package com.fangcang.grfp.core.cached;

import com.fangcang.grfp.core.entity.AttachmentFileEntity;
import com.fangcang.grfp.core.entity.ProjectEntity;
import com.fangcang.grfp.core.vo.AttachmentFileVO;
import com.fangcang.grfp.core.vo.response.common.AttachmentInfoResponse;

import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.List;

public interface CachedProjectService {

    ProjectEntity getById(Integer projectId);

    List<Long> queryProjectWhiteHotelIdList(Integer projectId, Integer hotelWhiteTypeId);

    public List<AttachmentFileVO> queryProjectEmailAttachmentList(Integer projectId) throws URISyntaxException, MalformedURLException;


}
