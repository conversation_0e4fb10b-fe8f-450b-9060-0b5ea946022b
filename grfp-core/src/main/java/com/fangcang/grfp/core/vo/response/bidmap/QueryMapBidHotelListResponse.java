package com.fangcang.grfp.core.vo.response.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@ApiModel("根据报价状态查询酒店报价列表返回VO")
@Getter
@Setter
public class QueryMapBidHotelListResponse extends BaseVO {

    /**
     * 项目意向ID
     */
    @ApiModelProperty("项目意向ID")
    private Integer projectIntentHotelId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Integer projectId;

    /**
     * 酒店ID
     */
    @ApiModelProperty("酒店ID")
    private Long hotelId;

    //城市编码
    @ApiModelProperty("城市编码")
    private String cityCode;

    //酒店名称
    @ApiModelProperty("酒店名称")
    private String hotelName;

    //星级
    @ApiModelProperty("星级")
    private String hotelStar;

    // 酒店星级名称
    @ApiModelProperty("酒店星级名称")
    private String hotelStarName;

    //评分
    @ApiModelProperty("评分")
    private String rating;


    //币种
    @ApiModelProperty("报价币种")
    private String currencyCode;


    //服务分
    @ApiModelProperty("服务分")
    private BigDecimal hotelServicePoint;

    //评分
    @ApiModelProperty("权重分")
    private BigDecimal bidWeight;

    //百度经度
    @ApiModelProperty("经度Google")
    private BigDecimal lngGoogle;

    //百度纬度
    @ApiModelProperty("维度Google")
    private BigDecimal latGoogle;

    //评分
    @ApiModelProperty("去年成交间夜数量")
    private Integer lastYearRoomNight;

    // 早餐
    @ApiModelProperty("早餐")
    private String breakfastNum;

    @ApiModelProperty("最低价格")
    private BigDecimal minPrice;

    // 签约参考价
    @ApiModelProperty("签约参考价")
    private BigDecimal referencePrice;

    @ApiModelProperty("报价状态 0:未报价，1:新标，2：议价中，3：议价中签，4：已否定，5，放弃报价，6：修订报价，7：拒绝报价")
    private Integer bidState;

    @ApiModelProperty("主图")
    private String mainPicUrl;

    @ApiModelProperty("房型描述")
    private String roomNameDesc;

    @ApiModelProperty("酒店地址")
    private String hotelAddress;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("国家/地区")
    private String countryName;

    @ApiModelProperty("国家编号")
    private String countryCode;

    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String nameEnUs;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String nameZhCn;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String addressEnUs;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String addressZhCn;

    //百度经度
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private BigDecimal lngBaidu;
    //百度纬度
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private BigDecimal latBaidu;





}
