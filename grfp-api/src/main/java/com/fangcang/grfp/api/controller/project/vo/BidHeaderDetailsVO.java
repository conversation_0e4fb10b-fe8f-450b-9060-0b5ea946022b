package com.fangcang.grfp.api.controller.project.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel("投标头部详情响应")
public class BidHeaderDetailsVO {
    @ApiModelProperty("项目ID")
    private Long projectId;
    @ApiModelProperty("项目名称")
    private String projectName;
    @ApiModelProperty("项目类型")
    private Integer projectType;
    @ApiModelProperty("项目类型值")
    private String projectTypeDesc;
    @ApiModelProperty("招标方式")
    private Integer tenderType;
    @ApiModelProperty("招标方式值")
    private String tenderTypeDesc;
    @ApiModelProperty("项目状态")
    private Integer projectState;
    @ApiModelProperty("项目状态值")
    private String projectStateDesc;
    @ApiModelProperty("预计签约酒店数")
    private Integer tenderCount;
    @ApiModelProperty("预算总金额")
    private BigDecimal budgetTotalAmount;
    @ApiModelProperty("招标机构ID")
    private Integer tenderOrgId;
    @ApiModelProperty("是否需要线上签约")
    private Integer needOnlineContracts;
    @ApiModelProperty("当前投标阶段")
    private Integer stage;
    @ApiModelProperty("第一轮开始时间")
    private Date firstBidStartTime;
    @ApiModelProperty("第一轮结束时间")
    private Date firstBidEndTime;
    @ApiModelProperty("第二轮开始时间")
    private Date secondBidStartTime;
    @ApiModelProperty("第二轮结束时间")
    private Date secondBidEndTime;
    @ApiModelProperty("第三轮开始时间")
    private Date thirdBidStartTime;
    @ApiModelProperty("第三轮结束时间")
    private Date thirdBidEndTime;
    @ApiModelProperty("权重总分")
    private BigDecimal whtTotalWeight;
    @ApiModelProperty("采购总价")
    private BigDecimal totalPurchasePrice;
    @ApiModelProperty("采购金额比率")
    private String purchaseAmountRatio;
    @ApiModelProperty("已投标酒店数")
    private Integer tenderedHotelCount;
    @ApiModelProperty("已投标完成率")
    private String tenderedHotelCountRatio;
    @ApiModelProperty("已中标酒店数")
    private Integer signingConfirmedHotelCount;
    @ApiModelProperty("已确认签约酒店完成率")
    private String signingConfirmedHotelCountRatio;
    @ApiModelProperty("投标总金额")
    private BigDecimal tenderTotalAmount;
    @ApiModelProperty("投标总金额同比增长率")
    private String tenderTotalGrowthRate;
} 