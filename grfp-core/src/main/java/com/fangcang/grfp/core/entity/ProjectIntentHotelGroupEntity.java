package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_intent_hotel_group")
public class ProjectIntentHotelGroupEntity extends BaseVO {


    /**
     * 意向酒店集团ID
     */
    @TableId(value = "project_intent_hotel_group_id", type = IdType.AUTO)
    private Integer projectIntentHotelGroupId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店集团机构ID
     */
    @TableField("hotel_group_org_id")
    private Integer hotelGroupOrgId;

    /**
     * 酒店集团指派销售跟进人id(招投标项目时指派的)
     */
    @TableField("hotel_group_contact_uid")
    private Integer hotelGroupContactUid;

    /**
     * 酒店集团指派销售跟进人姓名(招投标项目时指派的)
     */
    @TableField("hotel_group_contact_name")
    private Integer hotelGroupContactName;

    /**
     * 酒店集团销售联系人姓名
     */
    @TableField("hotel_group_bid_contact_name")
    private String hotelGroupBidContactName;

    /**
     * 酒店集团销售联系人电话
     */
    @TableField("hotel_group_bid_contact_mobile")
    private String hotelGroupBidContactMobile;

    /**
     * 酒店集团销售联系人邮箱
     */
    @TableField("hotel_group_bid_contact_email")
    private String hotelGroupBidContactEmail;


    /**
     * 邀约邮件发送状态(0：未发送邀约邮件，1：已发送邀约邮件)
     */
    @TableField("invite_send_email_status")
    private Integer inviteSendEmailStatus;

    /**
     * 邀约邮件发送状态(0：未发送邀约邮件，1：已发送邀约邮件)
     */
    @TableField("is_brand_limit")
    private Integer isBrandLimit;

    /**
     * 是否开启集团审核 1:开启，0:开启
     */
    @TableField("is_open_group_approve")
    private Integer isOpenGroupApprove;

    /**
     * 状态(1：有效，0：无效)
     */
    @TableField("is_active")
    private Integer isActive;

    /**
     * 酒店是否须支持员工到店付款: 1-是 0-否
     */
    @TableField("support_pay_at_hotel")
    private Integer supportPayAtHotel;

    /**
     * 酒店是否需支持 VCC 公司统一支付: 1-是 0-否
     */
    @TableField("support_vcc_pay")
    private Integer supportVccPay;

    /**
     * 酒店是否须支持提供入住明细信息: 1-是 0-否
     */
    @TableField("support_checkin_info")
    private Integer supportCheckinInfo;

    /**
     * 酒店是否须支持到店付免担保: 1-是 0-否
     */
    @TableField("support_no_guarantee")
    private Integer supportNoGuarantee;


    /**
     * 酒店是否须支持提前离店按实际入住金额收款: 1-是 0-否
     */
    @TableField("support_pay_early_checkout")
    private Integer supportPayEarlyCheckout;



    /**
     * 报价是否需要包括税费和服务费: 1-是 0-否
     */
    @TableField("support_include_tax_service")
    private Integer supportIncludeTaxService;

    /**
     * 酒店房间是否需提供免费 WIFI 服务: 1-是 0-否
     */
    @TableField("support_wifi")
    private Integer supportWifi;

    /**
     * 是否包含早餐: 1-是 0-否
     */
    @TableField("is_include_breakfast")
    private Integer isIncludeBreakfast;

    /**
     * 币种
     */
    @TableField("currency_code")
    private String currencyCode;

    /**
     * 到店付免担保最晚保留时间
     */
    @TableField("late_reserve_time")
    private String lateReserveTime;

    /**
     * 超出最晚保留时间采取措施：1-酒店直接与入住人或预订联系人联系，确认入住时间；0-取消订单
     */
    @TableField("do_after_late_reserve_time")
    private Integer doAfterLateReserveTime;

    /**
     * 免费取消限制天数
     */
    @TableField("support_cancel_day")
    private Integer supportCancelDay;

    /**
     * 免费取消限制时间
     */
    @TableField("support_cancel_time")
    private String supportCancelTime;

    /**
     * 有无佣金 1-有 0-否
     */
    @TableField("has_commission")
    private Integer hasCommission;

    /**
     * 佣金比例
     */
    @TableField("commission")
    private BigDecimal commission;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 创建时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
