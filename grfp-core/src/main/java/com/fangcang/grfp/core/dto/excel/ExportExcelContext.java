package com.fangcang.grfp.core.dto.excel;

import com.fangcang.grfp.core.usersession.UserSession;
import lombok.Getter;
import lombok.Setter;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class ExportExcelContext<R> {

    /**
     * 用户会话
     */
    private UserSession userSession;

    /**
     * 导出对象类
     */
    private Class<R> exportVOClass;

    /**
     * 语言
     */
    private int language;

    /**
     * 请求编号
     */
    private Long requestNo;


    /**
     * 导出逻辑
     */
    private List<R> exportData;

    /**
     * 导出 response
     */
    private HttpServletResponse httpServletResponse;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 动态表头映射
     */
    private Map<String, String> dynamicHeaderMap;

}
