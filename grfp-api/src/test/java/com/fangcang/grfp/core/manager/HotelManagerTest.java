package com.fangcang.grfp.core.manager;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

@SpringBootTest(classes = com.fangcang.grfp.api.GRfpApiApplication.class)
@Slf4j
public class HotelManagerTest {

    @Resource
    private HotelManager hotelManager;

    @Test
    void testQueryHotelBasicInfo() {
        hotelManager.queryHotelBasicInfo(Collections.singleton(131652L), true);
        try {
            TimeUnit.MINUTES.sleep(10L);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

}
