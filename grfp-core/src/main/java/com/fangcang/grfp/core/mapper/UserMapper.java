package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fangcang.grfp.core.entity.UserEntity;
import com.fangcang.grfp.core.vo.ListUserVO;
import com.fangcang.grfp.core.vo.request.QueryListUserPageRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface UserMapper extends BaseMapper<UserEntity> {


    default UserEntity selectByEmail(String email) {
        LambdaQueryWrapper<UserEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserEntity::getEmail, email);
        return this.selectOne(queryWrapper);
    }


    default int updateLoginInfo(UserEntity userEntity){
        LambdaUpdateWrapper<UserEntity>lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(UserEntity::getLastLoginTime, userEntity.getLastLoginTime());
        lambdaUpdateWrapper.set(UserEntity::getLastIpAddress, userEntity.getLastIpAddress());
        lambdaUpdateWrapper.eq(UserEntity::getUserId, userEntity.getUserId());
        return this.update(null, lambdaUpdateWrapper);

    }

    default List<UserEntity> queryUserListByOrgId(Integer orgId){
        LambdaQueryWrapper<UserEntity>lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(UserEntity::getOrgId, orgId);
        return this.selectList(lambdaQueryWrapper);

    }

    default int selectCount(Integer orgId){
        LambdaQueryWrapper<UserEntity>lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(UserEntity::getOrgId, orgId);
        return this.selectCount(lambdaQueryWrapper);

    }

    IPage<ListUserVO> queryUserVOPageList(IPage<ListUserVO> page, @Param("query") QueryListUserPageRequest query);



}
