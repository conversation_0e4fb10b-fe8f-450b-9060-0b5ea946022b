package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.ProjectHotelTaxSettingsEntity;
import com.fangcang.grfp.core.vo.BidHotelTaxSettingsVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 项目酒店报价税费设定 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface ProjectHotelTaxSettingsMapper extends BaseMapper<ProjectHotelTaxSettingsEntity> {

    BidHotelTaxSettingsVO queryBidHotelTaxSettingsVO(int projectIntentHotelId);

    BidHotelTaxSettingsVO queryBidHotelTaxSettingsVOByProjectHotelId(@Param("projectId") int projectId, @Param("hotelId") Long hotelId);

    /**
     * 根据项目 id 查询
     */
    default List<ProjectHotelTaxSettingsEntity> selectByProjectId(int projectId) {
        LambdaQueryWrapper<ProjectHotelTaxSettingsEntity> queryWrapper = Wrappers.lambdaQuery(ProjectHotelTaxSettingsEntity.class);
        queryWrapper.eq(ProjectHotelTaxSettingsEntity::getProjectId, projectId);
        return selectList(queryWrapper);
    }

    /**
     * 根据项目 id 和酒店 id 删除
     */
    default void deleteByProjectIdAndHotelId(int projectId, long hotelId) {
        LambdaQueryWrapper<ProjectHotelTaxSettingsEntity> queryWrapper = Wrappers.lambdaQuery(ProjectHotelTaxSettingsEntity.class);
        queryWrapper.eq(ProjectHotelTaxSettingsEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectHotelTaxSettingsEntity::getHotelId, hotelId);
        delete(queryWrapper);
    }

    /**
     * 根据项目 id 和酒店 id 查询
     */
    default ProjectHotelTaxSettingsEntity selectByProjectIdAndHotelId(int projectId, long hotelId) {
        LambdaQueryWrapper<ProjectHotelTaxSettingsEntity> queryWrapper = Wrappers.lambdaQuery(ProjectHotelTaxSettingsEntity.class);
        queryWrapper.eq(ProjectHotelTaxSettingsEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectHotelTaxSettingsEntity::getHotelId, hotelId);
        return selectOne(queryWrapper);
    }

}
