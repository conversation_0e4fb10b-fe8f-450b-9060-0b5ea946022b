package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 酒店集团默认报价自定义策略
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Getter
@Setter
@ApiModel("酒店集团模板其他承诺选项")
public class HGroupDefaultCusStrategyOptionVO extends BaseVO {

    @ApiModelProperty(value = "选项 id")
    @NotNull
    private Long optionId;

    @ApiModelProperty(value = "选项名称")
    @NotBlank
    private String optionName;

    @ApiModelProperty(value = "是否支持: 1-支持 0-不支持")
    private Integer isSupport = 0;

}
