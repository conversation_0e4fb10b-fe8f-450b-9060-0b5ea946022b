package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@ApiModel("酒店房档价格信息")
@Getter
@Setter
public class BidHotelPriceVO extends BaseVO {

    @ApiModelProperty("酒店价格ID 新增为null")
    private Integer hotelPriceId;

    @ApiModelProperty("价格类型 1:协议价,2:Season1价,3:Season2价")
    @NotNull
    private Integer priceType;

    @ApiModelProperty("单人价格")
    private BigDecimal onePersonPrice;

    @ApiModelProperty("单人价格包含税费")
    private BigDecimal onePersonIncludeTaxPrice;

    @ApiModelProperty("双人价格")
    private BigDecimal twoPersonPrice;

    @ApiModelProperty("双人价格包含税费")
    private BigDecimal twoPersonIncludeTaxPrice;

    @ApiModelProperty("单人价格 (最近议价价格)")
    private BigDecimal lastOnePersonPrice;

    @ApiModelProperty("单人价格包含税费 (最近议价价格)")
    private BigDecimal lastOnePersonIncludeTaxPrice;

    @ApiModelProperty("双人价格 (最近议价价格)")
    private BigDecimal lastTwoPersonPrice;

    @ApiModelProperty("双人价格包含税费 (最近议价价格)")
    private BigDecimal lastTwoPersonIncludeTaxPrice;

    @ApiModelProperty("单人价格税费信息")
    private HotelPriceTaxVO onePersonPriceTaxInfo;

    @ApiModelProperty("双人价格税费信息")
    private HotelPriceTaxVO twoPersonPriceTaxInfo;

    @ApiModelProperty("去年单人价格税费信息")
    private HotelPriceTaxVO lastOnePersonPriceTaxInfo;

    @ApiModelProperty("去年双人价格税费信息")
    private HotelPriceTaxVO lastTwoPersonPriceTaxInfo;

}
