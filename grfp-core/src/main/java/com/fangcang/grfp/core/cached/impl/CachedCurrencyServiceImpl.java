package com.fangcang.grfp.core.cached.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fangcang.grfp.core.cached.CachedCurrencyService;
import com.fangcang.grfp.core.entity.CurrencyExchangeRateEntity;
import com.fangcang.grfp.core.mapper.CurrencyExchangeRateMapper;
import com.fangcang.grfp.core.vo.CurrencyNameVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class CachedCurrencyServiceImpl implements CachedCurrencyService {

    @Autowired
    private CurrencyExchangeRateMapper currencyExchangeRateMapper;

    @Override
    @Cacheable(value="cachedCurrencyService.queryCurrencyNameList", key = "#currencyCode + '_' + #limitCount", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public List<CurrencyNameVO> queryCurrencyNameList(String currencyCode, Integer limitCount) {
        return currencyExchangeRateMapper.currencyNameList(currencyCode, limitCount);
    }

    @Override
    @Cacheable(value="cachedCurrencyService.getCurrencyRateInfo", key = "#currencyCode", condition = "#currencyCode != null", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public CurrencyExchangeRateEntity getCurrencyRateInfo(String currencyCode) {
        if (StringUtils.isEmpty(currencyCode)) {
            return null;
        }
        return currencyExchangeRateMapper.selectOne(new LambdaQueryWrapper<CurrencyExchangeRateEntity>()
                .eq(CurrencyExchangeRateEntity::getCurrencyCode, currencyCode));
    }
}
