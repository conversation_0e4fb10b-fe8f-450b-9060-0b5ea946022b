package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel("酒店集团列表信息")
@Getter
@Setter
public class ListHotelGroupDataVO extends BaseVO {
    /**
     * 酒店集团ID
     */
    @ApiModelProperty(value = "酒店集团Id")
    private Long hotelGroupId;

    /**
     * 英文名称
     */
    @ApiModelProperty(value = "英文名称")
    private String nameEnUs;

    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    private String nameZhCn;

    /**
     * 是否有效 1:是，0:否
     */
    @ApiModelProperty(value = "是否有效")
    private Integer isActive;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

}
