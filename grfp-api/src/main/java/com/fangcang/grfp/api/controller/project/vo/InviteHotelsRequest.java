package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.DestinationHotelVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@ApiModel(description = "邀请酒店请求")
public class InviteHotelsRequest extends BaseVO {

    private static final long serialVersionUID = 505362789934953794L;

    @ApiModelProperty(value = "项目 ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "酒店信息 (邀请酒店信息)")
    @NotNull
    private List<DestinationHotelVO> hotelInfoList;

    @ApiModelProperty(value = "邀请状态 邀请:1, 取消邀请：0", required = true)
    @NotNull
    private Integer inviteStatus;
}
