package com.fangcang.grfp.core.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * Page Response View Object
 * <AUTHOR>
 *
 * @param <T>
 */
@ApiModel("分页数据")
@Getter
@Setter
public class PageVO<T> extends BaseVO implements Serializable{

	// ---------------------------------------------------------------------------------------------------- Private Static Variables

	private static final long serialVersionUID = 1L;

	// ---------------------------------------------------------------------------------------------------- Private Variables
	@ApiModelProperty("总数")
	// Total record
	private int total;

	@ApiModelProperty("总页数")
	// Total Pages
	private int pages;

	@ApiModelProperty("当前页数据集合")
	// Current page data
	private List<T> list;
	
	// ---------------------------------------------------------------------------------------------------- Constructor
	
	public PageVO(int total, int pages, List<T> list) {
		this.total = total;
		this.pages = pages;
		this.list = list;
	}

	public PageVO(long total, long pages, List<T> list) {
		this.total = (int) total;
		this.pages = (int) pages;
		this.list = list;
	}

	// ---------------------------------------------------------------------------------------------------- Getter/Setter


	@Override
	public String toString() {
		return "PageVO{" +
				"total=" + total +
				", pages=" + pages +
				", list=" + list!=null ? String.valueOf(list.size()) : "" +
				'}';
	}
}

