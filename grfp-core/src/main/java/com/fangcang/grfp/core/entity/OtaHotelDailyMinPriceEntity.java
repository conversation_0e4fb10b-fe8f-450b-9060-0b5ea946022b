package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 酒店每日OTA价格
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_ota_hotel_daily_min_price")
public class OtaHotelDailyMinPriceEntity extends BaseVO {


    /**
     * 酒店id
     */
    @TableId(value = "hotel_id", type = IdType.ASSIGN_ID)
    private Long hotelId;

    /**
     * 销售日期
     */
    @TableField("sales_date")
    private Date salesDate;

    /**
     * 最低价格
     */
    @TableField("min_price")
    private BigDecimal minPrice;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
