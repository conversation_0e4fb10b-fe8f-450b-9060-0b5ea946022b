package com.fangcang.grfp.api.controller.hotel.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.core.manager.CityManager;
import com.fangcang.grfp.core.vo.ApproveStatusCountVO;
import com.fangcang.grfp.core.vo.BidStateCountVO;
import com.fangcang.grfp.core.vo.HotelGroupUserRelatedInfoVO;
import com.fangcang.grfp.core.vo.response.city.CityVO;
import com.fangcang.grfp.core.vo.response.hotel.HotelRoomTypeVO;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.entity.ProjectHotelBidStrategyEntity;
import com.fangcang.grfp.core.entity.ProjectIntentHotelEntity;
import com.fangcang.grfp.core.enums.BreakfastNumEnum;
import com.fangcang.grfp.core.enums.HotelBidStateEnum;
import com.fangcang.grfp.core.enums.HotelGroupApproveStatusEnum;
import com.fangcang.grfp.core.enums.OrgTypeEnum;
import com.fangcang.grfp.core.manager.BidMapManager;
import com.fangcang.grfp.core.manager.HotelManager;
import com.fangcang.grfp.core.mapper.HotelMapper;
import com.fangcang.grfp.core.mapper.ProjectHotelBidStrategyMapper;
import com.fangcang.grfp.core.mapper.ProjectHotelPriceMapper;
import com.fangcang.grfp.core.mapper.ProjectIntentHotelMapper;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.DateUtil;
import com.fangcang.grfp.core.util.GenericAppUtility;
import com.fangcang.grfp.core.vo.HotelOrgRelatedInfoVO;
import com.fangcang.grfp.core.vo.request.hotel.ProjectIntentHotelRequest;
import com.fangcang.grfp.core.vo.response.hotel.ProjectHotelTentResponse;
import com.fangcang.grfp.core.vo.response.hotel.RoomNameInfoVO;
import com.fangcang.grfp.core.vo.response.hotelgroup.QueryBidStatCountVO;
import com.fangcang.grfp.core.vo.response.hotelprice.HotelMinPriceResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class HotelService {


    @Autowired
    private ProjectIntentHotelMapper projectIntentHotelMapper;
    @Autowired
    private ProjectHotelBidStrategyMapper projectHotelBidStrategyMapper;
    @Autowired
    private ProjectHotelPriceMapper projectHotelPriceMapper;
    @Autowired
    private BidMapManager bidMapManager;

    @Resource
    private HotelManager hotelManager;

    @Resource
    private CityManager cityManager;

    public PageVO<ProjectHotelTentResponse> selectProjectHotelTentList(HttpServletRequest request, ProjectIntentHotelRequest projectIntentHotelRequest) {
        UserSession userSession = UserSession.get();
        dealLastYear(projectIntentHotelRequest);
        HotelOrgRelatedInfoVO hotelOrgRelatedInfoVO = AppUtility.getHotelOrgRelatedInfoVO(userSession);
        projectIntentHotelRequest.setHotelIdList(hotelOrgRelatedInfoVO.getHotelIdList());
        projectIntentHotelRequest.setHotelOrgId(userSession.getUserOrg().getOrgId());
        projectIntentHotelRequest.setSupplyOrgId(userSession.getUserOrg().getOrgId());
        IPage<ProjectHotelTentResponse> page = new Page<>(projectIntentHotelRequest.getPageIndex(), projectIntentHotelRequest.getPageSize());
        Page<ProjectHotelTentResponse> projectHotelTentResponsePage = projectIntentHotelMapper.selectHotelTentList(page, projectIntentHotelRequest);
        List<ProjectHotelTentResponse> projectHotelTentResponses = projectHotelTentResponsePage.getRecords();
        processRecords(request, projectIntentHotelRequest, projectHotelTentResponses);
        return new PageVO<>(page.getTotal(), page.getPages(), projectHotelTentResponses);

    }

    /**
     * 酒店集团查询报价统计数据
     */
    public QueryBidStatCountVO queryBidStatCount(HttpServletRequest request){
        UserSession userSession = UserSession.get();

        // 定义返回值
        QueryBidStatCountVO queryBidStatCountVO = new QueryBidStatCountVO();

        // 查询关联信息
        HotelOrgRelatedInfoVO hotelOrgRelatedInfoVO = AppUtility.getHotelOrgRelatedInfoVO(userSession);

        // 查询审核数量
        List<ApproveStatusCountVO> approveStatusCountVOList = projectIntentHotelMapper.queryHotelApprovalCount(hotelOrgRelatedInfoVO.getHotelIdList());
        for(ApproveStatusCountVO approveStatusCountVO : approveStatusCountVOList){
            if(approveStatusCountVO.getApproveStatus() == HotelGroupApproveStatusEnum.WAITING.key){
                queryBidStatCountVO.setWaitingApproveCount(approveStatusCountVO.getApproveStatusCount());
            }
            if(approveStatusCountVO.getApproveStatus() == HotelGroupApproveStatusEnum.REJECT_APPROVED.key){
                queryBidStatCountVO.setRejectedApproveCount(approveStatusCountVO.getApproveStatusCount());
            }
        }

        // 查询报价状态统计数据
        List<BidStateCountVO> bidStateCountList = projectIntentHotelMapper.queryHotelBidStatCount(hotelOrgRelatedInfoVO.getHotelIdList());
        for(BidStateCountVO bidStateCountVO : bidStateCountList){
            // 新标
            if(bidStateCountVO.getBidState() == HotelBidStateEnum.NEW_BID.bidState){
                queryBidStatCountVO.setNewBidCount(bidStateCountVO.getBidStateCount());
            }
            // 议价中
            if(bidStateCountVO.getBidState() == HotelBidStateEnum.UNDER_NEGOTIATION.bidState){
                queryBidStatCountVO.setUnderNegotiationCount(bidStateCountVO.getBidStateCount());
            }
            // 修订报价
            if(bidStateCountVO.getBidState() == HotelBidStateEnum.UPDATED_BID.bidState){
                queryBidStatCountVO.setUpdatedBidCount(bidStateCountVO.getBidStateCount());
            }
            // 拒绝议价
            if(bidStateCountVO.getBidState() == HotelBidStateEnum.REJECT_NEGOTIATION.bidState){
                queryBidStatCountVO.setRejectNegotiationCount(bidStateCountVO.getBidStateCount());
            }
            // 中签
            if(bidStateCountVO.getBidState() == HotelBidStateEnum.BID_WINNING.bidState){
                queryBidStatCountVO.setBidWinningCount(bidStateCountVO.getBidStateCount());
            }
            // 已否决
            if(bidStateCountVO.getBidState() == HotelBidStateEnum.REJECTED.bidState){
                queryBidStatCountVO.setRejectCount(bidStateCountVO.getBidStateCount());
            }

            // 放弃报价
            if(bidStateCountVO.getBidState() == HotelBidStateEnum.WITHDRAW_THE_QUOTATION.bidState){
                queryBidStatCountVO.setWaitingApproveCount(bidStateCountVO.getBidStateCount());
            }
        }

        // 未报价数量
        Date lastYearTime = DateUtil.dateFormat(DateUtil.getDate(new Date(),-730,0),DateUtil.defaultFormat);
        int noBidCount = projectIntentHotelMapper.selectHotelNoBidCount(hotelOrgRelatedInfoVO.getHotelIdList(), lastYearTime);
        queryBidStatCountVO.setNoBidCount(noBidCount);

        return queryBidStatCountVO;
    }

    private void processRecords(HttpServletRequest request, ProjectIntentHotelRequest projectIntentHotelRequest, List<ProjectHotelTentResponse> projectHotelTentResponses){
        // 查询项目意向酒店ID
        int languageId = GenericAppUtility.getRequestHeaderLanguage(request);
        Map<Long, ProjectIntentHotelEntity> projectIntentHotelMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(projectHotelTentResponses)) {
            for (ProjectHotelTentResponse projectHotelTentRespon : projectHotelTentResponses) {
                projectIntentHotelRequest.setProjectId(projectHotelTentRespon.getProjectId());
                if (projectIntentHotelRequest.getOrgType() != null && projectIntentHotelRequest.getOrgType().intValue() == OrgTypeEnum.HOTEL.key.intValue()) {
                    projectIntentHotelRequest.setLoopFilterHotelId(projectHotelTentRespon.getHotelId());
                }
                ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.queryByProjectAndHotelId(projectHotelTentRespon.getProjectId(), projectHotelTentRespon.getHotelId());
                if (Objects.nonNull(projectIntentHotel)) {
                    projectHotelTentRespon.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                    projectHotelTentRespon.setHotelContactName(projectIntentHotel.getHotelContactName());
                    projectIntentHotelMap.put(projectIntentHotel.getHotelId(), projectIntentHotel);
                }
            }
            if(projectIntentHotelMap.isEmpty()){
                return;
            }

            // 设置其他信息
            projectIntentHotelRequest.setLoopFilterHotelId(null);
            List<Integer> projectIntentHotelIdList = projectIntentHotelMap.values().stream().map(ProjectIntentHotelEntity::getProjectIntentHotelId).collect(Collectors.toList());
            List<ProjectHotelBidStrategyEntity> projectHotelBidStrategyList = projectHotelBidStrategyMapper.queryByProjectIntentHotelIds(projectIntentHotelIdList);
            Map<Integer, HotelMinPriceResponse> minBasePriceMap = bidMapManager.queryHotelMinPrice(GenericAppUtility.getRequestHeaderLanguage(request), projectIntentHotelIdList, false).stream().collect(Collectors.toMap(HotelMinPriceResponse::getProjectIntentHotelId, o->o, (o1, o2)-> o1));
            Map<Integer, ProjectHotelBidStrategyEntity> projectHotelBidStrategyMap = projectHotelBidStrategyList.stream().collect(Collectors.toMap(ProjectHotelBidStrategyEntity::getProjectIntentHotelId, Function.identity()));
            for (ProjectHotelTentResponse projectIntentHotelRespons : projectHotelTentResponses) {
                projectIntentHotelRequest.setProjectId(projectIntentHotelRespons.getProjectId());
                projectIntentHotelRespons.setHotelName(AppUtility.getHotelName(AppUtility.getRequestHeaderLanguage( request), projectIntentHotelRespons.getHotelId()));
                projectIntentHotelRespons.setCityName(AppUtility.getCityName(AppUtility.getRequestHeaderLanguage( request), projectIntentHotelRespons.getCityCode()));
                if (projectIntentHotelRequest.getOrgType() != null && projectIntentHotelRequest.getOrgType().intValue() == OrgTypeEnum.HOTEL.key.intValue()) {
                    projectIntentHotelRequest.setLoopFilterHotelId(projectIntentHotelRespons.getHotelId());
                }
                // 设置最低价格
                HotelMinPriceResponse hotelMinPriceResponse = minBasePriceMap.get(projectIntentHotelRespons.getProjectIntentHotelId());
                if(hotelMinPriceResponse != null){
                    projectIntentHotelRespons.setBasePrice(hotelMinPriceResponse.getMinPrice());
                    projectIntentHotelRespons.setIsIncludeBreakfast(hotelMinPriceResponse.getIsIncludeBreakfast());
                    projectIntentHotelRespons.setBreakfastNum(BreakfastNumEnum.getTextByKey(hotelMinPriceResponse.getBreakfastNum(), languageId));
                }
                // 设置佣金
                ProjectHotelBidStrategyEntity projectHotelBidStrategy = projectHotelBidStrategyMap.get(projectIntentHotelRespons.getProjectIntentHotelId());
                if (projectHotelBidStrategy != null) {
                    projectIntentHotelRespons.setSupportIncludeCommission(projectHotelBidStrategy.getHasCommission());
                    projectIntentHotelRespons.setTendCommission(projectHotelBidStrategy.getCommission());

                }
            }
        }

        dealTenderTimeAndPublishTime(projectHotelTentResponses);
    }

    // 处理投标起止时间、公布评标结果日
    public void dealTenderTimeAndPublishTime(List<ProjectHotelTentResponse> projectHotelTentRespons) {
        for (ProjectHotelTentResponse projectIntentHotelRespons : projectHotelTentRespons) {
            projectIntentHotelRespons.setTenderStartTime(projectIntentHotelRespons.getBidStartTime());


            projectIntentHotelRespons.setTenderEndTime(projectIntentHotelRespons.getThirdBidEndTime() != null?
                    projectIntentHotelRespons.getThirdBidEndTime():projectIntentHotelRespons.getSecondBidEndTime() != null?
                    projectIntentHotelRespons.getSecondBidEndTime():projectIntentHotelRespons.getFirstBidEndTime());
            Calendar c=Calendar.getInstance();
            c.setTime(projectIntentHotelRespons.getTenderEndTime());
            c.add(Calendar.DAY_OF_MONTH,1);
            Date tomorrow=c.getTime();//这是明天
            projectIntentHotelRespons.setPublishTenderResultTime(tomorrow);
        }
    }


    // 处理前一年时间

    public void dealLastYear(ProjectIntentHotelRequest projectIntentHotelRequest) {
        projectIntentHotelRequest.setLastYearTime(DateUtil.dateFormat(DateUtil.getDate(new Date(),-730,0),DateUtil.defaultFormat));
    }


    public List<ProjectHotelTentResponse> selectHotelOrgTentList(ProjectIntentHotelRequest projectIntentHotelRequest) {
        List<ProjectHotelTentResponse> joinProjectList = projectIntentHotelMapper.selectHotelOrgTentList(projectIntentHotelRequest);
        List<ProjectIntentHotelEntity> projectIntentHotelList = projectIntentHotelMapper.selectProjectIntentHotelList(projectIntentHotelRequest);
        Map<String, ProjectIntentHotelEntity> projectIntentHoteMap = new HashMap<>();
        if(projectIntentHotelRequest.getUserId() != null){
            for(ProjectIntentHotelEntity projectIntentHotel : projectIntentHotelList){
                projectIntentHoteMap.put(projectIntentHotel.getProjectId() + "_" + projectIntentHotel.getHotelId(), projectIntentHotel);
            }
        }
        if (Objects.equals(HotelBidStateEnum.NO_BID.bidState, projectIntentHotelRequest.getBidState())) {
            // 未报价 不包括等待酒店集团审核/拒绝状态报价
            List<ProjectIntentHotelEntity> hotelGroupApprovedProjectIntentHotelList = projectIntentHotelList.stream().filter(o -> o.getHotelGroupApproveStatus() != null &&
                    (Objects.equals(o.getHotelGroupApproveStatus(), HotelGroupApproveStatusEnum.REJECT_APPROVED.key) ||
                            Objects.equals(o.getHotelGroupApproveStatus(), HotelGroupApproveStatusEnum.WAITING.key))).collect(Collectors.toList());
            List<String> hotelGroupApprovedProjectIdHotelIdKeyList = hotelGroupApprovedProjectIntentHotelList.stream().map(o -> o.getProjectId() + "_" + o.getHotelId()).collect(Collectors.toList());

            projectIntentHotelList = projectIntentHotelList.stream().filter(o -> !Objects.equals(o.getBidState(), HotelBidStateEnum.NO_BID.bidState)).collect(Collectors.toList());
            // 已经报价的酒店项目
            List<String> projectIdHotelIdKeyList = projectIntentHotelList.stream().map(o -> o.getProjectId() + "_" + o.getHotelId()).collect(Collectors.toList());
            for (int i = joinProjectList.size() - 1; i >= 0; i--) {
                ProjectHotelTentResponse projectHotelTentResponse = joinProjectList.get(i);
                String projectHotelIdKey = projectHotelTentResponse.getProjectId() + "_" + projectHotelTentResponse.getHotelId();
                projectHotelTentResponse.setProjectIdHotelIdKey(projectHotelIdKey);
                if (projectHotelTentResponse.getTenderType() == 1 && projectIdHotelIdKeyList.contains(projectHotelIdKey)) { // 公开招标 过滤已经报价的酒店项目
                    joinProjectList.remove(i);
                    continue;
                }
                if (hotelGroupApprovedProjectIdHotelIdKeyList.contains(projectHotelIdKey)) { // 过滤申请酒店集团审核
                    joinProjectList.remove(i);
                    continue;
                }
                // 根据员工ID过滤
                if(projectIntentHotelRequest.getUserId() != null) {
                    if(!projectIntentHoteMap.containsKey(projectHotelIdKey)) {
                        joinProjectList.remove(i);
                        continue;
                    }
                    ProjectIntentHotelEntity projectIntentHotel = projectIntentHoteMap.get(projectHotelIdKey);
                    if(!Objects.equals(projectIntentHotel.getHotelContactUid(), projectIntentHotelRequest.getUserId())){
                        joinProjectList.remove(i);
                    }
                }
            }
            // 排序
            joinProjectList = joinProjectList.stream().sorted((o1, o2) -> {
                if(o1.getDisplayOrder() == null && o2.getDisplayOrder() == null){
                    if(o2.getCreateTime().getTime() - o1.getCreateTime().getTime() > 0L){
                        return 1;
                    }
                    return -1;
                } else if(o1.getDisplayOrder() != null && o2.getDisplayOrder() != null){
                    return o2.getDisplayOrder() - o1.getDisplayOrder();
                } else if(o1.getDisplayOrder() == null){
                    return 1;
                } else  {
                    return -1;
                }
            }).collect(Collectors.toList());
        } else {
            Map<String, ProjectIntentHotelEntity> hotelGroupApproveProjectIntentHotelMap = new HashMap<>();
            if(projectIntentHotelRequest.getHotelGroupApproveStatus() != null){
                projectIntentHotelList = projectIntentHotelList.stream().filter(o -> Objects.equals(o.getHotelGroupApproveStatus(), projectIntentHotelRequest.getHotelGroupApproveStatus())).collect(Collectors.toList());
                hotelGroupApproveProjectIntentHotelMap = projectIntentHotelList.stream().collect(Collectors.toMap(o -> o.getProjectId() + "_" + o.getHotelId(), Function.identity()));
            } else {
                projectIntentHotelList = projectIntentHotelList.stream().filter(o -> Objects.equals(o.getBidState(), projectIntentHotelRequest.getBidState())).collect(Collectors.toList());
            }
            List<String> projectIdHotelIdKeyList = projectIntentHotelList.stream().map(o -> o.getProjectId() + "_" + o.getHotelId()).collect(Collectors.toList());
            for (int i = joinProjectList.size() - 1; i >= 0; i--) {
                ProjectHotelTentResponse projectHotelTentResponse = joinProjectList.get(i);
                String projectHotelIdKey = projectHotelTentResponse.getProjectId() + "_" + projectHotelTentResponse.getHotelId();
                projectHotelTentResponse.setProjectIdHotelIdKey(projectHotelIdKey);
                if (!projectIdHotelIdKeyList.contains(projectHotelIdKey)) { // 过滤报价状态不相等的酒店项目
                    joinProjectList.remove(i);
                    continue;
                }
                // 设置酒店集团审批信息
                ProjectIntentHotelEntity hotelGroupApproveProjectIntentHotel = hotelGroupApproveProjectIntentHotelMap.get(projectHotelIdKey);
                if(hotelGroupApproveProjectIntentHotel != null){
                    projectHotelTentResponse.setHotelGroupApproveStatus(hotelGroupApproveProjectIntentHotel.getHotelGroupApproveStatus());
                    projectHotelTentResponse.setHotelGroupRejectApproveReason(hotelGroupApproveProjectIntentHotel.getHotelGroupRejectApproveReason());
                }
                // 根据员工ID过滤
                if(projectIntentHotelRequest.getUserId() != null) {
                    if(!projectIntentHoteMap.containsKey(projectHotelIdKey)) {
                        joinProjectList.remove(i);
                        continue;
                    }
                    ProjectIntentHotelEntity projectIntentHotel = projectIntentHoteMap.get(projectHotelIdKey);
                    if(!Objects.equals(projectIntentHotel.getHotelContactUid(), projectIntentHotelRequest.getUserId())){
                        joinProjectList.remove(i);
                    }
                }
            }
        }
        return joinProjectList;
    }

    /**
     * 查询酒店房型信息
     */
    public HotelRoomTypeVO queryHotelRoomTypeInfo(HttpServletRequest httpServletRequest, Long hotelId) {
        // 获取语言
        int languageId = GenericAppUtility.getRequestHeaderLanguage(httpServletRequest);

        // 查询酒店房型信息
        List<HotelRoomTypeVO> hotelRoomTypeList = hotelManager.selectHotelRoomTypeInfo(languageId, Collections.singletonList(hotelId));
        if (CollUtil.isEmpty(hotelRoomTypeList)) {
            return null;
        }
        HotelRoomTypeVO hotelRoomInfo = hotelRoomTypeList.get(0);

        // 查询城市信息
        Map<String, CityVO> cityCodeCityVOMap = cityManager.queryCityInfo(Collections.singletonList(hotelRoomInfo.getCityCode()));
        CityVO city = cityCodeCityVOMap.get(hotelRoomInfo.getCityCode());
        if (city != null) {
            hotelRoomInfo.setCityName(AppUtility.getName(languageId, city.getCityCode(), city.getCityNameEnUs(), city.getCityNameZhCn()));
            hotelRoomInfo.setProvinceName(AppUtility.getName(languageId, city.getProvinceCode(), city.getProvinceNameEnUs(), city.getProvinceNameZhCn()));
            hotelRoomInfo.setCountryName(AppUtility.getName(languageId, city.getCountryCode(), city.getCountryNameEnUs(), city.getCountryNameZhCn()));
        }

        return hotelRoomInfo;
    }
}
