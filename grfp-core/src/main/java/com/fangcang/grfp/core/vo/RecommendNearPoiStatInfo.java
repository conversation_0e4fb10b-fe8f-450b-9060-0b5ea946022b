package com.fangcang.grfp.core.vo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 按500以上，400-500，300-400，200-300，200以下 5个区间进行统计，显示百分比，最多1位小数。
 *  按五星、豪华型、四星/高档型、三星/舒适型、经济型、二星及以下/公寓 6组进行统计，显示百分比，最多1位小数。
 */
public class RecommendNearPoiStatInfo implements Serializable {

    private static final long serialVersionUID = 2802719435015814525L;

    // 500以上百分比
    private BigDecimal up500Percentage;

    // 400-500百分比
    private BigDecimal _400_500Percentage;

    // 300-400百分比
    private BigDecimal _300_400Percentage;

    // 200-300百分比
    private BigDecimal _200_300Percentage;

    // 200以下
    private BigDecimal down200Percentage;

   //     FIVESTAR(19, "五星"),
   //    QUISAFIVESTAR(29, "豪华型"),
   //    FOURSTAR(39, "四星"),
   //    QUISAFOURSTAR(49, "高档型"),
   //    THREESTAR(59, "三星"),
   //    QUISATHREESTAR(64, "舒适型"),
   //    TWOSTAR(69, "二星"),
   //    QUISATWOSTAR(66, "经济型"),
   //    BELOWTWOSTAR(79, "二星以下");
    // 五星级百分比 (19)
    private BigDecimal fiveStarPercentage;

    // 豪华型百分比 (29)
    private BigDecimal quisaFiveStarPercentage;

    // 四星级/高档型百分比 (39,49)
    private BigDecimal fourAndQuisaFourStarPercentage;

    // 三星/舒适型百分比 (59,64)
    public BigDecimal threeAndQuisaThreeStarPercentage;

    // 经济型、(66)
    public BigDecimal quisaTwoStarPercentage;

    //二星及以下/公寓、(69,79)
    public BigDecimal twoAndDownStarPercentage;


    public RecommendNearPoiStatInfo(){
        up500Percentage = BigDecimal.ZERO;
        _400_500Percentage = BigDecimal.ZERO;
        _300_400Percentage = BigDecimal.ZERO;
        _200_300Percentage = BigDecimal.ZERO;
        down200Percentage = BigDecimal.ZERO;
        fiveStarPercentage = BigDecimal.ZERO;
        quisaFiveStarPercentage = BigDecimal.ZERO;
        fourAndQuisaFourStarPercentage = BigDecimal.ZERO;
        threeAndQuisaThreeStarPercentage = BigDecimal.ZERO;
        quisaTwoStarPercentage = BigDecimal.ZERO;
        twoAndDownStarPercentage = BigDecimal.ZERO;


    }

    public BigDecimal getUp500Percentage() {
        return up500Percentage;
    }

    public void setUp500Percentage(BigDecimal up500Percentage) {
        this.up500Percentage = up500Percentage;
    }

    public BigDecimal get_400_500Percentage() {
        return _400_500Percentage;
    }

    public void set_400_500Percentage(BigDecimal _400_500Percentage) {
        this._400_500Percentage = _400_500Percentage;
    }

    public BigDecimal get_300_400Percentage() {
        return _300_400Percentage;
    }

    public void set_300_400Percentage(BigDecimal _300_400Percentage) {
        this._300_400Percentage = _300_400Percentage;
    }

    public BigDecimal get_200_300Percentage() {
        return _200_300Percentage;
    }

    public void set_200_300Percentage(BigDecimal _200_300Percentage) {
        this._200_300Percentage = _200_300Percentage;
    }

    public BigDecimal getDown200Percentage() {
        return down200Percentage;
    }

    public void setDown200Percentage(BigDecimal down200Percentage) {
        this.down200Percentage = down200Percentage;
    }

    public BigDecimal getFiveStarPercentage() {
        return fiveStarPercentage;
    }

    public void setFiveStarPercentage(BigDecimal fiveStarPercentage) {
        this.fiveStarPercentage = fiveStarPercentage;
    }

    public BigDecimal getQuisaFiveStarPercentage() {
        return quisaFiveStarPercentage;
    }

    public void setQuisaFiveStarPercentage(BigDecimal quisaFiveStarPercentage) {
        this.quisaFiveStarPercentage = quisaFiveStarPercentage;
    }

    public BigDecimal getFourAndQuisaFourStarPercentage() {
        return fourAndQuisaFourStarPercentage;
    }

    public void setFourAndQuisaFourStarPercentage(BigDecimal fourAndQuisaFourStarPercentage) {
        this.fourAndQuisaFourStarPercentage = fourAndQuisaFourStarPercentage;
    }

    public BigDecimal getThreeAndQuisaThreeStarPercentage() {
        return threeAndQuisaThreeStarPercentage;
    }

    public void setThreeAndQuisaThreeStarPercentage(BigDecimal threeAndQuisaThreeStarPercentage) {
        this.threeAndQuisaThreeStarPercentage = threeAndQuisaThreeStarPercentage;
    }

    public BigDecimal getQuisaTwoStarPercentage() {
        return quisaTwoStarPercentage;
    }

    public void setQuisaTwoStarPercentage(BigDecimal quisaTwoStarPercentage) {
        this.quisaTwoStarPercentage = quisaTwoStarPercentage;
    }

    public BigDecimal getTwoAndDownStarPercentage() {
        return twoAndDownStarPercentage;
    }

    public void setTwoAndDownStarPercentage(BigDecimal twoAndDownStarPercentage) {
        this.twoAndDownStarPercentage = twoAndDownStarPercentage;
    }
}
