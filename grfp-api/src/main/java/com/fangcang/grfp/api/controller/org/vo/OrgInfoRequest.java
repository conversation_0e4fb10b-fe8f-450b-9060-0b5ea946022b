package com.fangcang.grfp.api.controller.org.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("机构信息")
@Getter
@Setter
public class OrgInfoRequest extends BaseVO {

    @ApiModelProperty(value = "机构ID")
    @NotNull
    private Integer orgId;

}
