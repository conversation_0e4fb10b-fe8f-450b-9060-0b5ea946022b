package com.fangcang.grfp.core.vo.response.bidprice;

import com.fangcang.grfp.core.entity.PriceApplicableRoomEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("可用房型")
@Getter
@Setter
public class PriceApplicableRoomInfoResponse extends PriceApplicableRoomEntity {

    @ApiModelProperty("房型名称")
    private String roomTypeName;
}
