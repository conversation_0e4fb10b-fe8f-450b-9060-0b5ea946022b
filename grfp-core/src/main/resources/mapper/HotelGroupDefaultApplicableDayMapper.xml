<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HotelGroupDefaultApplicableDayMapper">

    <select id="queryDefaultApplicableDayInfo" resultType="com.fangcang.grfp.core.vo.BidApplicableDayVO">
        SELECT
            price_type AS priceType,
            start_date AS startDate,
            end_date AS endDate
        FROM t_hotel_group_default_applicable_day
        WHERE project_intent_hotel_group_id = #{projectIntentHotelGroupId}
        ORDER BY default_applicable_day_id ASC
    </select>

    <delete id="deleteHotelGroupDefaultApplicableDay">
        DELETE
        FROM t_hotel_group_default_applicable_day
        WHERE project_intent_hotel_group_id = #{projectIntentHotelGroupId}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_hotel_group_default_applicable_day (
            project_intent_hotel_group_id,
            project_id,
            price_type,
            start_date,
            end_date, creator)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.projectIntentHotelGroupId}, #{item.projectId}, #{item.priceType},
            #{item.startDate}, #{item.endDate}, #{item.creator})
        </foreach>
    </insert>
</mapper>
