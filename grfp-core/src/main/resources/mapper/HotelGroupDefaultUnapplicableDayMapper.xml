<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HotelGroupDefaultUnapplicableDayMapper">

    <select id="queryDefaultUnApplicableDayInfo" resultType="com.fangcang.grfp.core.vo.HotelGroupDefaultUnApplicableDayVO">
        SELECT
            project_intent_hotel_group_id AS projectIntentHotelGroupId,
            default_unapplicable_day_id AS defaultUnapplicableDayId,
            start_date AS startDate,
            end_date AS endDate
        FROM t_hotel_group_default_unapplicable_day
        WHERE project_intent_hotel_group_id = #{projectIntentHotelGroupId}
        ORDER BY default_unapplicable_day_id ASC
    </select>

    <select id="queryDefaultUnApplicableDayVOList" resultType="com.fangcang.grfp.core.vo.BidUnApplicableDayVO">
        SELECT
            start_date AS startDate,
            end_date AS endDate
        FROM t_hotel_group_default_unapplicable_day
        WHERE project_intent_hotel_group_id = #{projectIntentHotelGroupId}
        ORDER BY default_unapplicable_day_id ASC
    </select>

    <delete id="deleteByProjectIntentHotelGroupId">
        DELETE
        FROM t_hotel_group_default_unapplicable_day
        WHERE project_intent_hotel_group_id = #{projectIntentHotelGroupId}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_hotel_group_default_unapplicable_day (
            project_intent_hotel_group_id,
            project_id,
            start_date,
            end_date, creator)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.projectIntentHotelGroupId}, #{item.projectId},
            #{item.startDate}, #{item.endDate}, #{item.creator})
        </foreach>
    </insert>
</mapper>
