package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 项目权重配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_hotel_tend_weight")
public class ProjectHotelTendWeightEntity extends BaseVO {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 项目 id
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 间夜量权重（不少于120间夜）
     */
    @TableField("wht_room_night")
    private BigDecimal whtRoomNight;

    /**
     * 是否启用间夜量权重
     */
    @TableField("wht_room_night_state")
    private Integer whtRoomNightState;

    /**
     * 间夜量(额外加分项)权重（不少于600间夜）
     */
    @TableField("wht_room_night_ex")
    private BigDecimal whtRoomNightEx;

    /**
     * 是否启用间夜量(额外加分项)权重
     */
    @TableField("wht_room_night_ex_state")
    private Integer whtRoomNightExState;

    /**
     * 城市权重
     */
    @TableField("wht_city")
    private BigDecimal whtCity;

    /**
     * 是否启用城市权重
     */
    @TableField("wht_city_state")
    private Integer whtCityState;

    /**
     * 位置权重
     */
    @TableField("wht_location")
    private BigDecimal whtLocation;

    /**
     * 是否启用位置权重
     */
    @TableField("wht_location_state")
    private Integer whtLocationState;

    /**
     * 价格优势权重（低于15%）
     */
    @TableField("wht_price_advantage")
    private BigDecimal whtPriceAdvantage;

    /**
     * 是否启用价格优势权重
     */
    @TableField("wht_price_advantage_state")
    private Integer whtPriceAdvantageState;

    /**
     * 价格优势(额外加分项)权重（低于25%）
     */
    @TableField("wht_price_advantage_ex")
    private BigDecimal whtPriceAdvantageEx;

    /**
     * 是否启用价格优势(额外加分项)权重
     */
    @TableField("wht_price_advantage_ex_state")
    private Integer whtPriceAdvantageExState;

    /**
     * ota评分权重
     */
    @TableField("wht_ota_score")
    private BigDecimal whtOtaScore;

    /**
     * 是否启用ota评分权重
     */
    @TableField("wht_ota_score_state")
    private Integer whtOtaScoreState;

    /**
     * 公司统一支付权重
     */
    @TableField("wht_co_pay")
    private BigDecimal whtCoPay;

    /**
     * 是否启用公司统一支付权重
     */
    @TableField("wht_co_pay_state")
    private Integer whtCoPayState;

    /**
     * 早餐权重
     */
    @TableField("wht_breakfast")
    private BigDecimal whtBreakfast;

    /**
     * 是否启用早餐权重
     */
    @TableField("wht_breakfast_state")
    private Integer whtBreakfastState;

    /**
     * lra权重
     */
    @TableField("wht_lra")
    private BigDecimal whtLra;

    /**
     * 是否启用lra权重
     */
    @TableField("wht_lra_state")
    private Integer whtLraState;

    /**
     * 退改规则权重
     */
    @TableField("wht_cancel")
    private BigDecimal whtCancel;

    /**
     * 是否启用退改规则权重（注意字段名拼写一致性）
     */
    @TableField("wht_cancel_state")
    private Integer whtCancelState;

    /**
     * 总权重(满分)，各项启用权重总和
     */
    @TableField("wht_total_weight")
    private BigDecimal whtTotalWeight;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
