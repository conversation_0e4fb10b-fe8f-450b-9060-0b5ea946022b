<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HotelImageMapper">
    <insert id="batchUpsert">
        insert into t_hotel_image (hotel_id, data)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.hotelId}, #{item.data})
        </foreach>
        on duplicate key update data = values(data)
    </insert>
</mapper>
