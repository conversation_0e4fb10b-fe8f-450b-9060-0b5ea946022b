package com.fangcang.grfp.api.controller.userpermission.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel("新增用户权限VO")
@Getter
@Setter
public class AddUserPermission extends BaseVO {

    @ApiModelProperty("机构类型 机构类型 1：平台，2：酒店，3：企业，4酒店集团")
    @NotNull
    private Integer orgType;

    @ApiModelProperty("角色 角色编码类型 ADMIN：管理员，EMPLOYEE：员工")
    @NotBlank
    private String roleCode;

    @ApiModelProperty("权限")
    @NotBlank
    private String permission;

}
