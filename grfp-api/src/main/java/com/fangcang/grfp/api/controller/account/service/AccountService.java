package com.fangcang.grfp.api.controller.account.service;

import com.fangcang.grfp.api.controller.account.vo.*;
import com.fangcang.grfp.api.controller.org.vo.OrgInfoVO;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.bo.RedisService;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.entity.OrgEntity;
import com.fangcang.grfp.core.entity.UserEntity;
import com.fangcang.grfp.core.enums.OrgTypeEnum;
import com.fangcang.grfp.core.enums.RoleCodeEnum;
import com.fangcang.grfp.core.enums.SmsCodeTypeEnum;
import com.fangcang.grfp.core.mapper.OrgMapper;
import com.fangcang.grfp.core.mapper.UserMapper;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.StringUtil;
import com.fangcang.grfp.core.util.UserUtility;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@Service
public class AccountService {

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private OrgMapper orgMapper;

    public void findPswdByEmail(FindPswdByEmailRequest findPswdByEmailRequest){
        UserEntity user = userMapper.selectByEmail(findPswdByEmailRequest.getEmail());
        if(user != null){
            // 2、获取缓存中的验证码
            String key = UserUtility.getSmsCodeKey(findPswdByEmailRequest.getEmail(), SmsCodeTypeEnum.MODIFY_PWD.key);
            String randomNumOld = redisService.get(key);
            if(!StringUtil.isValidString(randomNumOld)){
                AppUtility.serviceError(ErrorCode.INVALIDATE_VERIFY_CODE_PLEASE_RESEND);
            }
            if (!randomNumOld.equals(findPswdByEmailRequest.getVerifyCode())) {
                AppUtility.serviceError(ErrorCode.VERIFY_CODE_ERROR);
            }

            String newMd5Pswd = UserUtility.md5Pswd(findPswdByEmailRequest.getEmail(), findPswdByEmailRequest.getNewPassword());
            UserEntity updatedUser = new UserEntity();
            updatedUser.setUserId(user.getUserId());
            updatedUser.setPassword(newMd5Pswd);
            updatedUser.setModifier(UserSession.SYSTEM_USER_NAME);
            updatedUser.setModifyTime(new Date());
            userMapper.updateById(updatedUser);
        } else {
            AppUtility.serviceError(ErrorCode.NOT_EXISTS_EMAIL);
        }
    }


    public void updateAccountOrgInfo(HttpServletRequest request, UpdateAccountOrgRequest updateAccountOrgRequest){
        // 获取用户Session
        UserSession userSession = UserSession.get();

        OrgEntity dbOrg = orgMapper.selectById(userSession.getUserOrg().getOrgId());
        if(dbOrg == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }
        if(dbOrg.getOrgType().equals(OrgTypeEnum.PLATFORM.key)){
            AppUtility.serviceError(ErrorCode.CANNOT_UPDATE_PLATFORM_ORG);
        }

        if(!userSession.getRoleCode().equals(RoleCodeEnum.ADMIN.key)){
            AppUtility.serviceError(ErrorCode.ONLY_ADMIN_HAS_PERMISSION_ADD);
        }

        // 新增机构
        Date now = new Date();
        OrgEntity org = new OrgEntity();
        org.setOrgId(dbOrg.getOrgId());
        BeanUtils.copyProperties(updateAccountOrgRequest, org);
        org.setLogoUrl(AppUtility.getFileKeyFromUrl(updateAccountOrgRequest.getLogoUrl()));
        org.setModifier(userSession.getUsername());
        org.setModifyTime(now);
        int updateCount = orgMapper.updateById(org);
        if(updateCount == 0){
            AppUtility.serviceError(ErrorCode.UPDATE_FAILED);
        }
    }

    public void resetPassword(HttpServletRequest request){
        // 获取用户Session
        UserSession userSession = UserSession.get();

        UserEntity user = new UserEntity();
        user.setUserId(userSession.getUserId());
        user.setPassword(UserUtility.md5Pswd(userSession.getEmail(), UserUtility.DEFAULT_PSWD));
        user.setModifier(userSession.getUsername());
        AppUtility.doUpdateOneRecord(userMapper.updateById(user));
    }
    public AccountInfoVO accountInfo(HttpServletRequest request){
        UserSession userSession = UserSession.get();

        UserEntity user = userMapper.selectById(userSession.getUserId());
        if(user == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }
        // 定义返回值
        AccountInfoVO accountInfoVO = new AccountInfoVO();
        BeanUtils.copyProperties(user, accountInfoVO);

        accountInfoVO.setMobileAreaCode(user.getMobileAreaCode());
        accountInfoVO.setMobile(user.getMobile());
        OrgEntity org = orgMapper.selectById(user.getOrgId());
        accountInfoVO.setOrgName(org.getOrgName());
        accountInfoVO.setOrgId(org.getOrgId());

        return accountInfoVO;
    }

    public void updateAccountInfo(HttpServletRequest request, UpdateAccountInfoRequest updateAccountInfoRequest){
        UserSession userSession = UserSession.get();

        UserEntity user = userMapper.selectById(userSession.getUserId());
        if(user == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }
        // 检查是否存在相同用户邮箱
        if(!user.getEmail().equals(updateAccountInfoRequest.getEmail())){
            UserEntity existUser = userMapper.selectByEmail(updateAccountInfoRequest.getEmail());
            if(existUser != null){
                AppUtility.serviceError(ErrorCode.EXIST_THE_SAME_USER_EMAIL);
            }
        }

        // 更新
        UserEntity userEntity = new UserEntity();
        userEntity.setUserId(user.getUserId());
        userEntity.setUserName(updateAccountInfoRequest.getUserName());
        userEntity.setMobileAreaCode(updateAccountInfoRequest.getMobileAreaCode());
        userEntity.setMobile(updateAccountInfoRequest.getMobile());
        userEntity.setEmail(updateAccountInfoRequest.getEmail());
        userMapper.updateById(userEntity);

    }

    public OrgInfoVO accountOrgInfo(HttpServletRequest request){
        UserSession userSession = UserSession.get();

        OrgEntity org = orgMapper.selectById(userSession.getUserOrg().getOrgId());
        if(org == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }
        // 定义返回值
        OrgInfoVO orgInfoVO = new OrgInfoVO();
        BeanUtils.copyProperties(org, orgInfoVO);

        return orgInfoVO;
    }

    public void updateSelfPassword(HttpServletRequest request, UpdateSelfPasswordRequest updateSelfPasswordRequest){
        UserSession userSession = UserSession.get();
        UserEntity user = new UserEntity();
        user.setUserId(userSession.getUserId());
        user.setPassword(UserUtility.md5Pswd(userSession.getEmail(), updateSelfPasswordRequest.getPassword()));
        user.setModifier(userSession.getUsername());
        AppUtility.doUpdateOneRecord(userMapper.updateById(user));
    }
}
