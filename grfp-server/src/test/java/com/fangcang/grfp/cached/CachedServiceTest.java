package com.fangcang.grfp.cached;

import com.fangcang.grfp.core.bo.RedisService;
import com.fangcang.grfp.core.constant.CacheDateType;
import com.fangcang.grfp.core.constant.TopicChannelName;
import com.fangcang.grfp.core.util.JsonUtil;
import com.fangcang.grfp.core.vo.ClearCacheMessageVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = com.fangcang.grfp.server.GRfpServerApplication.class)
@Slf4j
public class CachedServiceTest {

    @Autowired
    private RedisService redisService;

    @Test
    public void testClearHotelBrandCache(){
        ClearCacheMessageVO clearCacheMessageVO = new ClearCacheMessageVO();
        clearCacheMessageVO.setDataType(CacheDateType.HOTEL_BRAND);
        redisService.publishMessage(TopicChannelName.CLEAR_CACHE, JsonUtil.objectToJson(clearCacheMessageVO));
    }
}
