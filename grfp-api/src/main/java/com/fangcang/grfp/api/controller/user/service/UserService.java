package com.fangcang.grfp.api.controller.user.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.api.controller.user.vo.*;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.entity.UserEntity;
import com.fangcang.grfp.core.enums.OrgTypeEnum;
import com.fangcang.grfp.core.enums.StateEnum;
import com.fangcang.grfp.core.mapper.UserMapper;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.UserUtility;
import com.fangcang.grfp.core.vo.ListUserVO;
import com.fangcang.grfp.core.vo.request.QueryListUserPageRequest;
import com.fangcang.grfp.core.vo.request.SystemOperateRequest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserService {

    @Autowired
    private UserMapper userMapper;

    /**
     * 分页查询
     */
    public PageVO<ListUserVO> queryUserVoPageList(HttpServletRequest request, QueryListUserPageRequest query){
        UserSession userSession = UserSession.get();
        if(!userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.PLATFORM.key)){
            query.setRelatedOrgIdList(Lists.newArrayList(userSession.getUserOrg().getOrgId()));
        } else if(query.getOrgId() != null){
            query.setRelatedOrgIdList(Lists.newArrayList(query.getOrgId()));
        }
        IPage<ListUserVO> page = new Page<>(query.getPageIndex(), query.getPageSize());
        userMapper.queryUserVOPageList(page, query);
        return new PageVO<>((int) page.getTotal(), (int) page.getPages(), page.getRecords());
    }

    /**
     * 修改用户状态
     */
    public void updateUserState(HttpServletRequest request, UpdateUserStateRequest updateUserStateRequest) {
        UserEntity dbUser = userMapper.selectById(updateUserStateRequest.getUserId());
        UserSession userSession = UserSession.get();
        if(!userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.PLATFORM.key) && !Objects.equals(userSession.getUserOrg().getOrgId(), dbUser.getOrgId())){
            AppUtility.serviceError(ErrorCode.CANNOT_UPDATE_OTHER_ORG_USER);
        }
        UserEntity userEntity = new UserEntity();
        userEntity.setUserId(updateUserStateRequest.getUserId());
        userEntity.setState(updateUserStateRequest.getState());
        userEntity.setModifier(userSession.getUsername());
        AppUtility.doUpdateOneRecord(userMapper.updateById(userEntity));
    }

    /**
     * 新增用户
     */
    public void addUser(HttpServletRequest request, AddUserRequest addUserRequest) {
        UserSession userSession = UserSession.get();
        if(!userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.PLATFORM.key) && !Objects.equals(userSession.getUserOrg().getOrgId(), addUserRequest.getOrgId())){
            AppUtility.serviceError(ErrorCode.CANNOT_UPDATE_OTHER_ORG_USER);
        }
        // 检查电邮是否重复
        UserEntity dbUser = userMapper.selectByEmail(addUserRequest.getEmail());
        if(dbUser != null){
            AppUtility.serviceError(ErrorCode.USER_EMAIL_EXIST);
        }

        UserEntity user = new UserEntity();
        user.setOrgId(addUserRequest.getOrgId());
        user.setEmail(addUserRequest.getEmail());
        user.setRoleCode(addUserRequest.getRoleCode());
        user.setMobileAreaCode(addUserRequest.getMobileAreaCode());
        user.setMobile(addUserRequest.getMobile());
        user.setUserName(addUserRequest.getUserName());
        user.setPassword(null);
        user.setState(StateEnum.Effective.key);
        user.setCreator(userSession.getUsername());
        AppUtility.doInsert(userMapper.insert(user));
    }

    /**
     * 修改用户
     */
    public void updateUser(HttpServletRequest request, UpdateUserRequest updateUserRequest) {
        UserSession userSession = UserSession.get();
        UserEntity dbUser = userMapper.selectById(updateUserRequest.getUserId());
        if(!userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.PLATFORM.key) && !Objects.equals(userSession.getUserOrg().getOrgId(), dbUser.getOrgId())){
            AppUtility.serviceError(ErrorCode.CANNOT_UPDATE_OTHER_ORG_USER);
        }
        UserEntity emailCheckDbUser = userMapper.selectByEmail(updateUserRequest.getEmail());
        if(emailCheckDbUser != null && !Objects.equals(emailCheckDbUser.getUserId(), dbUser.getUserId())){
            AppUtility.serviceError(ErrorCode.USER_EMAIL_EXIST);
        }
        UserEntity user = new UserEntity();
        user.setUserId(updateUserRequest.getUserId());
        user.setEmail(updateUserRequest.getEmail());
        user.setRoleCode(updateUserRequest.getRoleCode());
        user.setMobileAreaCode(updateUserRequest.getMobileAreaCode());
        user.setMobile(updateUserRequest.getMobile());
        user.setUserName(updateUserRequest.getUserName());
        user.setModifier(userSession.getUsername());
        AppUtility.doUpdateOneRecord(userMapper.updateById(user));

    }

    /**
     * 删除用户
     */
    public void delete(HttpServletRequest request, DeleteUserRequest deleteUserRequest) {
        UserSession userSession = UserSession.get();
        UserEntity dbUser = userMapper.selectById(deleteUserRequest.getUserId());
        if(!userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.PLATFORM.key) && !Objects.equals(userSession.getUserOrg().getOrgId(), dbUser.getOrgId())){
            AppUtility.serviceError(ErrorCode.CANNOT_UPDATE_OTHER_ORG_USER);
        }
        AppUtility.doDelete(userMapper.deleteById(deleteUserRequest.getUserId()));

    }


    /**
     * 重置用户密码
     */
    public void resetPassword(HttpServletRequest request, ResetUserPasswordRequest resetUserPasswordRequest) {
        UserSession userSession = UserSession.get();
        UserEntity dbUser = userMapper.selectById(resetUserPasswordRequest.getUserId());
        if(!userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.PLATFORM.key) && !Objects.equals(userSession.getUserOrg().getOrgId(), dbUser.getOrgId())){
            AppUtility.serviceError(ErrorCode.CANNOT_UPDATE_OTHER_ORG_USER);
        }
        UserEntity user = new UserEntity();
        user.setUserId(dbUser.getUserId());
        user.setPassword(UserUtility.md5Pswd(dbUser.getEmail(), UserUtility.DEFAULT_PSWD));
        user.setModifier(userSession.getUsername());
        AppUtility.doUpdateOneRecord(userMapper.updateById(user));
    }

    /**
     * 修改用户密码
     */
    public void updatePassword(HttpServletRequest request, ResetUserPasswordRequest resetUserPasswordRequest) {
        UserSession userSession = UserSession.get();
        UserEntity dbUser = userMapper.selectById(resetUserPasswordRequest.getUserId());
        if(!userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.PLATFORM.key) && !Objects.equals(userSession.getUserOrg().getOrgId(), dbUser.getOrgId())){
            AppUtility.serviceError(ErrorCode.CANNOT_UPDATE_OTHER_ORG_USER);
        }
        UserEntity user = new UserEntity();
        user.setUserId(dbUser.getUserId());
        user.setPassword(UserUtility.md5Pswd(dbUser.getEmail(), UserUtility.DEFAULT_PSWD));
        user.setModifier(userSession.getUsername());
        AppUtility.doUpdateOneRecord(userMapper.updateById(user));
    }

    /**
     * 修改用户密码
     */
    public void resetAllPassword(HttpServletRequest request, SystemOperateRequest systemOperateRequest) {
        if(!systemOperateRequest.getOperateKey().equals("Abc123")){
            AppUtility.serviceError(ErrorCode.NO_PERMISSION_ACCESS);
        }
        LambdaQueryWrapper<UserEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(UserEntity::getPassword);
        List<UserEntity> userList = userMapper.selectList(queryWrapper);
        for(UserEntity userEntity : userList){
            UserEntity user = new UserEntity();
            user.setUserId(userEntity.getUserId());
            user.setPassword(UserUtility.md5Pswd(userEntity.getEmail(), UserUtility.DEFAULT_PSWD));
            user.setModifier("System");
            userMapper.updateById(user);
            log.info("Reset Pwd " + userEntity.getUserId());
        }
    }


    public List<ListUserVO> queryUserList(QueryUserRequest queryUserRequest) {
        UserSession userSession = UserSession.get();
       return userMapper.selectList(Wrappers.<UserEntity>lambdaQuery()
                .eq(UserEntity::getOrgId, userSession.getUserOrg().getOrgId())
                .eq(!ObjectUtils.isEmpty(queryUserRequest.getEmail()),UserEntity::getEmail, queryUserRequest.getEmail())
                .eq(!ObjectUtils.isEmpty(queryUserRequest.getMobile()),UserEntity::getMobile, queryUserRequest.getMobile())
                .like(!ObjectUtils.isEmpty(queryUserRequest.getUserName()),UserEntity::getUserName, queryUserRequest.getUserName())
                .in(!ObjectUtils.isEmpty(queryUserRequest.getRoleCodeList()),UserEntity::getUserName, queryUserRequest.getRoleCodeList())
        ).stream().map(u->BeanUtil.copyProperties(u,ListUserVO.class)).collect(Collectors.toList());

    }
}
