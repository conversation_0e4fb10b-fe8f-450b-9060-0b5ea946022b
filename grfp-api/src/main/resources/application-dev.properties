
# Datasource
spring.datasource.url=************************************************************************************************************************************************************
#spring.datasource.url=***********************************************************************************************************************************
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=ABcd@1234
#spring.datasource.password=mysql
spring.datasource.hikari.minimum-idle=1
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.read-only=false
spring.datasource.hikari.idle-timeout=60000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1

#################################################################################################################

spring.redis.host=*************
spring.redis.port=6379
spring.redis.password=ABcd1234
spring.redis.database=1

##################################################################################################################
# swagger
web.swagger.enabled=true

##################################################################################################################
xxl-job.adminAddresses=http://*************:80/fangcang-job-admin/
xxl-job.executorAppName=mps-server
xxl-job.executorLogPath=./data/applogs/grfp-server
xxl-job.logRetentionDays=5

###################################################################################################################

# ???????? cos ?????????????
grfp.cos.region=ap-guangzhou
grfp.cos.access-key-id=AKIDNo8Wo7LOH8uVtPsCMK5dzJURBh1FJApG
grfp.cos.access-key-secret=JZcS7czGPXUEntoY5OZfcMxLYYKwNadY
grfp.cos.bucket-public=dev-grfp-public-1305177846
# ?????, ?????, ???????
grfp.cos.bucket-public-domain=https://dev-grfp-public-1305177846.cos.ap-guangzhou.myqcloud.com
