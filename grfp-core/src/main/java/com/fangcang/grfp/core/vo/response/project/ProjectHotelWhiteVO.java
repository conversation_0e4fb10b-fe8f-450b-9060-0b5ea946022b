package com.fangcang.grfp.core.vo.response.project;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@ApiModel(description = "项目酒店白名单响应")
public class ProjectHotelWhiteVO extends BaseVO {

    private static final long serialVersionUID = 889926705495555367L;

    @ApiModelProperty(value = "酒店 ID")
    private Long hotelId;

    @ApiModelProperty(value = "酒店英文名称", hidden = true)
    @JsonIgnore
    private String hotelNameEnUs;

    @ApiModelProperty(value = "酒店中文名称", hidden = true)
    @JsonIgnore
    private String hotelNameZhCn;

    @ApiModelProperty(value = "酒店名称")
    private String hotelName;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "城市英文名称", hidden = true)
    @JsonIgnore
    private String cityNameEnUs;

    @ApiModelProperty(value = "城市中文名称", hidden = true)
    @JsonIgnore
    private String cityNameZhCn;

    @ApiModelProperty(value = "白名单类型: 1-报价免控白名单 2-周末节假日免履约监控白名单 3-仅每周一履约监控白名单")
    private Integer hotelWhiteType;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
