package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(description = "删除项目酒店白名单请求")
public class DeleteProjectHotelWhiteRequest extends BaseVO {

    private static final long serialVersionUID = 2257121514600630074L;

    @ApiModelProperty(value = "项目 ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "酒店 ID", required = true)
    @NotNull
    private Long hotelId;

    @ApiModelProperty(value = "白名单类型: 1-报价免控白名单 2-周末节假日免履约监控白名单 3-仅每周一履约监控白名单", required = true)
    @NotNull
    private Integer hotelWhiteType;
}
