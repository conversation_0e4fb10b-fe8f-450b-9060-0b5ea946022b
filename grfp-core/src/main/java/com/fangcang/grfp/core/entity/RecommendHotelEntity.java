package com.fangcang.grfp.core.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 酒店推荐表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_recommend_hotel")
public class RecommendHotelEntity extends BaseVO {


    /**
     * 酒店ID
     */
    @TableId(value = "hotel_id", type = IdType.ASSIGN_ID)
    private Long hotelId;

    /**
     * 早餐数量
     */
    @TableField("breakfast_num")
    private Integer breakfastNum;

    /**
     * 签约参考价
     */
    @TableField("reference_price")
    private BigDecimal referencePrice;

    /**
     * 酒店推荐值
     */
    @TableField("recommend_score")
    private Integer recommendScore;

    /**
     * 推荐亮点
     */
    @TableField("bright_spot")
    private String brightSpot;

    /**
     * 酒店联系人
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 酒店联系手机号码
     */
    @TableField("contact_mobile")
    private String contactMobile;

    /**
     * 酒店联系电邮
     */
    @TableField("contact_email")
    private String contactEmail;

    /**
     * 是否可以预订酒店最后一间未售出的客房(1:是，0：否)
     */
    @TableField("last_room_available")
    private Integer lastRoomAvailable;

    /**
     * 签约要求月均采购间夜数
     */
    @TableField("required_room_night")
    private String requiredRoomNight;

    /**
     * 平台跟进人
     */
    @TableField("platform_contact_uid")
    private Integer platformContactUid;

    /**
     * 平台运营跟进人姓名
     */
    @TableField("platform_contact_name")
    private String platformContactName;

    /**
     * 状态(1：有效，0：无效)
     */
    @TableField("state")
    private Integer state;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
