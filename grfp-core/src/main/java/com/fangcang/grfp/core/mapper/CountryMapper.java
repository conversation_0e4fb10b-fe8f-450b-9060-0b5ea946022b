package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fangcang.grfp.core.entity.CountryEntity;
import com.fangcang.grfp.core.vo.CountryNameVO;
import com.fangcang.grfp.core.vo.HotelGroupVO;
import com.fangcang.grfp.core.vo.ListCountryDataVO;
import com.fangcang.grfp.core.vo.ListHotelDataVO;
import com.fangcang.grfp.core.vo.request.ListCountryDataRequest;
import com.fangcang.grfp.core.vo.request.ListHotelDataRequest;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 国家 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface CountryMapper extends BaseMapper<CountryEntity> {

    void batchUpsert(@Param("list") Collection<CountryEntity> countryEntities);

    default CountryEntity getByCountryCode(@Param("countryCode") String countryCode){
        LambdaQueryWrapper<CountryEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CountryEntity::getCountryCode, countryCode);
        return selectOne(lambdaQueryWrapper);
    }

    List<CountryNameVO> selectCountryNameList(@Param("languageId") Integer languageId,
                                                 @Param("countryName") String countryName,
                                                 @Param("limitCount") Integer limitCount);

    /**
     * 分页查询数据
     */
    IPage<CountryEntity> listDataPage(IPage<CountryEntity> page, @Param("query") ListCountryDataRequest query);


}
