<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.CityMapper">
    <insert id="batchUpsert">
        insert into t_city(country_code, province_code, city_code, parent_city_code, name_zh_cn, name_en_us, creator, modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.countryCode}, #{item.provinceCode}, #{item.cityCode}, #{item.parentCityCode}, #{item.nameZhCn}, #{item.nameEnUs}, #{item.creator}, #{item.modifier})
        </foreach>
        on duplicate key update country_code     = values(country_code),
                                province_code    = values(province_code),
                                city_code        = values(city_code),
                                parent_city_code = values(parent_city_code),
                                name_zh_cn       = values(name_zh_cn),
                                name_en_us       = values(name_en_us),
                                modifier         = values(modifier)
    </insert>

    <select id="selectCityNameList" resultType="com.fangcang.grfp.core.vo.CityNameVO">
        SELECT
            city_code AS cityCode,
            country_code AS countryCode,
            province_code AS provinceCode,
            name_zh_cn AS nameZhCn,
            name_en_us AS nameEnUs
        FROM
            t_city
        <where>
            (`name_en_us` like LIKE CONCAT('%', #{cityName}, '%') OR `name_zh_cn` LIKE CONCAT('%', #{cityName}, '%'))
            <if test="countryCode != null and countryCode != ''">
                AND country_code = #{countryCode}
            </if>
        </where>
        <if test="languageId != null and languageId == 1">
            ORDER BY `name_en_us` ASC
        </if>
        <if test="languageId != null and languageId == 2">
            ORDER BY `name_zh_cn` ASC
        </if>
        <if test="limitCount != null and limitCount > 0">
            LIMIT #{limitCount}
        </if>
    </select>


    <select id="listDataPage" resultType="com.fangcang.grfp.core.entity.CityEntity">
        SELECT *
        FROM
        t_city
        <where>
            <if test="query.nameEnUs != null and query.nameEnUs != ''">
                AND `name_en_us` = #{query.nameEnUs}
            </if>
            <if test="query.nameZhCn != null and query.nameZhCn != ''">
                AND `name_zh_cn` = #{query.nameZhCn}
            </if>
            <if test="query.countryCode != null and query.countryCode != ''">
                AND `country_code` = #{query.countryCode}
            </if>
            <if test="query.provinceCode != null and query.provinceCode != ''">
                AND `province_code` = #{query.provinceCode}
            </if>
            <if test="query.parentCityCode != null and query.parentCityCode != ''">
                AND `parent_city_code` = #{query.parentCityCode}
            </if>
        </where>
        ORDER BY `name_en_us` ASC
    </select>

    <select id="selectCityVOList" resultType="com.fangcang.grfp.core.vo.response.city.CityVO">
        select t1.city_code,
               t1.name_zh_cn    as cityNameZhCn,
               t1.name_en_us    as cityNameEnUs,
               t2.province_code as provinceCode,
               t2.name_zh_cn    as provinceNameZhCn,
               t2.name_en_us    as provinceNameEnUs,
               t3.country_code  as countryCode,
               t3.name_zh_cn    as countryNameZhCn,
               t3.name_en_us    as countryNameEnUs
        from t_city t1
                 inner join t_province t2 on t1.province_code = t2.province_code and t1.country_code = t2.country_code
                 inner join t_country t3 on t1.country_code = t3.country_code
        <where>
            t1.city_code in
            <foreach collection="cityCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectByCityName" resultType="com.fangcang.grfp.core.entity.CityEntity">
        select *
        from t_city where (name_zh_cn in
        <foreach collection="cityNames" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        or name_en_us in
        <foreach collection="cityNames" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </select>
</mapper>
