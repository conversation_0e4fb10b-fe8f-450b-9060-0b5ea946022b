package com.fangcang.grfp.core.auditlog;

import org.apache.catalina.User;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 审计日志配置
 */
@Configuration
@ConditionalOnProperty(name="user.audit.log.record", havingValue = "true")
public class UserAuditLogAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean(UserAuditLogAspect.class)
    UserAuditLogAspect userAuditLogAspect() {
        return new UserAuditLogAspect();
    }

}
