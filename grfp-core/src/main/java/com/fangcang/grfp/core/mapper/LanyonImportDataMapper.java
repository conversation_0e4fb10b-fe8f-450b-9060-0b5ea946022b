package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fangcang.grfp.core.entity.LanyonImportDataEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * Lanyon导入数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface LanyonImportDataMapper extends BaseMapper<LanyonImportDataEntity> {

    default LanyonImportDataEntity getLanyonImportData(Integer projectId, Long hotelId, Integer dataType){
        LambdaQueryWrapper<LanyonImportDataEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LanyonImportDataEntity::getProjectId, projectId);
        lambdaQueryWrapper.eq(LanyonImportDataEntity::getHotelId, hotelId);
        lambdaQueryWrapper.eq(LanyonImportDataEntity::getDataType, dataType);
        return selectOne(lambdaQueryWrapper);
    }

    default Integer getLanyonImportDataCount(Integer projectId, Long hotelId){
        LambdaQueryWrapper<LanyonImportDataEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LanyonImportDataEntity::getProjectId, projectId);
        lambdaQueryWrapper.eq(LanyonImportDataEntity::getHotelId, hotelId);
        return selectCount(lambdaQueryWrapper);
    }

}
