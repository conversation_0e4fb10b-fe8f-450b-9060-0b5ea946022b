package com.fangcang.grfp.core.thread;


import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.mapper.ProjectHotelHistoryDataMapper;
import com.fangcang.grfp.core.mapper.ProjectIntentHotelMapper;
import com.fangcang.grfp.core.util.LocationUtil;
import com.fangcang.grfp.core.vo.response.project.BidHotelInfoQueryResponse;
import com.fangcang.grfp.core.vo.response.project.QueryHistoryProjectInfoResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

/**
 *  查询项目推荐酒店
 */
public class ProjectRecommendHotelQueryThread implements Callable<Map<Long, BidHotelInfoQueryResponse>> {

    private static final Logger logger = LoggerFactory.getLogger(ProjectRecommendHotelQueryThread.class);


    private Integer projectId;

    private Long baseCenterHotelId;

    private String cityCode;

    private ProjectHotelHistoryDataMapper projectHotelHistoryDataMapper;

    private ProjectIntentHotelMapper projectIntentHotelMapper;

    private BigDecimal lngGoogle;

    //百度纬度
    private BigDecimal latGoogle;

    // 距离
    private int distance;

    public ProjectRecommendHotelQueryThread() {
    }

    public ProjectRecommendHotelQueryThread(
                                            Integer projectId,
                                            Long baseCenterHotelId,
                                            int distance,
                                            BigDecimal lngGoogle,
                                            BigDecimal latGoogle,
                                            String cityCode,
                                            ProjectHotelHistoryDataMapper projectHotelHistoryDataMapper,
                                            ProjectIntentHotelMapper projectIntentHotelMapper) {
        this.projectId = projectId;
        this.baseCenterHotelId = baseCenterHotelId;
        this.distance = distance;
        this.lngGoogle = lngGoogle;
        this.latGoogle = latGoogle;
        this.cityCode = cityCode;
        this.projectHotelHistoryDataMapper = projectHotelHistoryDataMapper;
        this.projectIntentHotelMapper = projectIntentHotelMapper;
    }

    @Override
    public Map<Long, BidHotelInfoQueryResponse> call()  {
        Map<Long, BidHotelInfoQueryResponse> queryBidHotelInfoQueryResponseMap = new LinkedHashMap<>();
        // 查询城市推荐酒店
        List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoList = projectHotelHistoryDataMapper.queryRecommendHistoryProjectHotelList(projectId, cityCode);
        queryHistoryProjectInfoList = queryHistoryProjectInfoList.stream().filter(o ->
                    LocationUtil.isInLngLatInDistanceRange(distance, o.getLatGoogle(), o.getLngGoogle(), latGoogle, lngGoogle)).collect(Collectors.toList());

        // 查询项目邀请酒店
        int baseAvgPrice = 0;
        List<Long> invitedHotelIdList = projectIntentHotelMapper.queryInviteHotelList(projectId);
        for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : queryHistoryProjectInfoList) {
            // 计算均价
            if (Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), baseCenterHotelId)) {
                baseAvgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()), 2, RoundingMode.HALF_UP).intValue();
                // 转换返回对象
                BidHotelInfoQueryResponse bidHotelInfoQueryResponse = convertToBidHotelInfoQueryResponse(queryHistoryProjectInfoResponse, invitedHotelIdList);
                queryBidHotelInfoQueryResponseMap.put(queryHistoryProjectInfoResponse.getHotelId(), bidHotelInfoQueryResponse);
            }
        }

        // 设置是否为同档价位
        for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : queryHistoryProjectInfoList){
            if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), baseCenterHotelId)) {
                continue;
            }
            BidHotelInfoQueryResponse bidHotelInfoQueryResponse = convertToBidHotelInfoQueryResponse(queryHistoryProjectInfoResponse, invitedHotelIdList);
            queryBidHotelInfoQueryResponseMap.put(queryHistoryProjectInfoResponse.getHotelId(), bidHotelInfoQueryResponse);
            // 判断是否为同档
            if(queryHistoryProjectInfoResponse.getRoomNightCount() > 0 && queryHistoryProjectInfoResponse.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                int avgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()), 2, RoundingMode.HALF_UP).intValue();
                if (baseAvgPrice > 0 && Math.abs(baseAvgPrice - avgPrice) <= 50) {
                    queryBidHotelInfoQueryResponseMap.get(queryHistoryProjectInfoResponse.getHotelId()).setTheSameLevelPrice(true);
                }
            }
        }

        return queryBidHotelInfoQueryResponseMap;
    }

    private BidHotelInfoQueryResponse convertToBidHotelInfoQueryResponse(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse, List<Long> invitedHotelIdList){
        BidHotelInfoQueryResponse bidHotelInfoQueryResponse = new BidHotelInfoQueryResponse();
        bidHotelInfoQueryResponse.setHotelId(queryHistoryProjectInfoResponse.getHotelId());
        bidHotelInfoQueryResponse.setHotelName(queryHistoryProjectInfoResponse.getHotelName());
        bidHotelInfoQueryResponse.setHotelStar(queryHistoryProjectInfoResponse.getHotelStar());
        bidHotelInfoQueryResponse.setRating(queryHistoryProjectInfoResponse.getRating());
        bidHotelInfoQueryResponse.setLatBaiDu(queryHistoryProjectInfoResponse.getLatBaidu());
        bidHotelInfoQueryResponse.setLngBaiDu(queryHistoryProjectInfoResponse.getLngBaidu());
        if(queryHistoryProjectInfoResponse.getRoomNightCount() == 0 || queryHistoryProjectInfoResponse.getTotalAmount() == null || queryHistoryProjectInfoResponse.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
            bidHotelInfoQueryResponse.setLatestYearRoomNight(0);
            bidHotelInfoQueryResponse.setLastYearAvgPrice(BigDecimal.ZERO);
            bidHotelInfoQueryResponse.setLastYearCityOrder(0);
        } else {
            bidHotelInfoQueryResponse.setIsInvited(invitedHotelIdList.contains(queryHistoryProjectInfoResponse.getHotelId()) ? RfpConstant.constant_1 : RfpConstant.constant_0);
            bidHotelInfoQueryResponse.setRecommendLevel(queryHistoryProjectInfoResponse.getRecommendLevel());
            bidHotelInfoQueryResponse.setLatestYearRoomNight(queryHistoryProjectInfoResponse.getRoomNightCount());
            bidHotelInfoQueryResponse.setLastYearAvgPrice(queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()), 2, RoundingMode.HALF_UP));
            bidHotelInfoQueryResponse.setLastYearCityOrder(queryHistoryProjectInfoResponse.getCityOrder());
        }
        return bidHotelInfoQueryResponse;
    }


}
