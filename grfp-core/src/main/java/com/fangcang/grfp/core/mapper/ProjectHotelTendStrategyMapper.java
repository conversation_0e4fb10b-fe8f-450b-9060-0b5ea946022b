package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.ProjectHotelTendStrategyEntity;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelTendStrategyVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 项目采购策略表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface ProjectHotelTendStrategyMapper extends BaseMapper<ProjectHotelTendStrategyEntity> {

    /**
     * 新增或更新
     */
    void upsert(@Param("entity") ProjectHotelTendStrategyEntity entity);

    /**
     * 根据项目 ID 查询项目采购策略
     */
    ProjectHotelTendStrategyVO selectByProjectId(@Param("projectId") Integer projectId);


}
