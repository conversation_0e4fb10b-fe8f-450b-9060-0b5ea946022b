package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.ProjectHotelRemarkEntity;

import java.util.List;

/**
 * <p>
 * 项目酒店备注 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
public interface ProjectHotelRemarkMapper extends BaseMapper<ProjectHotelRemarkEntity> {

    default List<ProjectHotelRemarkEntity> selectByProjectId(Integer projectId, Long hotelId) {
        LambdaQueryWrapper<ProjectHotelRemarkEntity> queryWrapper = Wrappers.lambdaQuery(ProjectHotelRemarkEntity.class);
        queryWrapper.eq(ProjectHotelRemarkEntity::getProjectId, projectId);
        if(hotelId != null){
            queryWrapper.eq(ProjectHotelRemarkEntity::getHotelId, hotelId);
        }
        queryWrapper.orderByDesc(ProjectHotelRemarkEntity::getCreateTime);
        return selectList(queryWrapper);
    }
}
