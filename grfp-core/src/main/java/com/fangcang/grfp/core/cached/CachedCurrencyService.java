package com.fangcang.grfp.core.cached;

import com.fangcang.grfp.core.entity.CurrencyExchangeRateEntity;
import com.fangcang.grfp.core.vo.CurrencyNameVO;

import java.util.List;

public interface CachedCurrencyService {

    List<CurrencyNameVO> queryCurrencyNameList(String currencyCode, Integer limitCount);

    /**
     * 根据币种获取汇率信息
     */
    CurrencyExchangeRateEntity getCurrencyRateInfo(String currencyCode);
}
