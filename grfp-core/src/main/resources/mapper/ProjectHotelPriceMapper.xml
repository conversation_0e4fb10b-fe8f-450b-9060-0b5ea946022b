<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectHotelPriceMapper">

    <select id="selectMinPriceByProjectIntentHotelIds"
            resultType="com.fangcang.grfp.core.vo.response.hotelprice.HotelMinPriceResponse">
        SELECT t.project_intent_hotel_id as projectIntentHotelId,
                t.hotel_id as hotelId,
                t.hotel_price_level_id AS hotelPriceLevelId,
               min(t.one_person_price) as minPrice
        FROM T_PROJECT_HOTEL_PRICE t
        WHERE t.project_intent_hotel_id in
        <foreach collection="projectIntentHotelIds" open="(" close=")" item="projectIntentHotelId" separator="," index="index">
            #{projectIntentHotelId}
        </foreach>
        group by t.project_intent_hotel_id, t.hotel_id,t.hotel_price_level_id
        Order by t.hotel_price_level_id ASC
    </select>

    <select id="selectMinPriceByProjectIntentHotelId" resultType="java.math.BigDecimal">
        SELECT  min(t.one_person_price) AS minPrice
        FROM T_PROJECT_HOTEL_PRICE t
        WHERE t.project_intent_hotel_id = #{projectIntentHotelId}
    </select>


    <update id="recordLastPrice">
        UPDATE t_project_hotel_price
        SET last_one_person_price = one_person_price,
            last_two_person_price = two_person_price
        WHERE project_intent_hotel_id IN
        <foreach collection="projectIntentHotelIds" open="(" close=")" item="projectIntentHotelId" separator="," index="index">
            #{projectIntentHotelId}
        </foreach>
    </update>

    <insert id="batchInsert">
        insert into t_project_hotel_price (project_intent_hotel_id, project_id, hotel_id, hotel_price_level_id, hotel_price_group_id, price_type, one_person_price, two_person_price,
                                           last_one_person_price, last_two_person_price, creator, modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectIntentHotelId}, #{item.projectId}, #{item.hotelId}, #{item.hotelPriceLevelId}, #{item.hotelPriceGroupId}, #{item.priceType}, #{item.onePersonPrice}, #{item.twoPersonPrice},
             #{item.onePersonPrice}, #{item.twoPersonPrice}, #{item.creator}, #{item.modifier})
        </foreach>
    </insert>

    <delete id="deleteByGroupIdAndExcludePriceIds">
        DELETE FROM t_project_hotel_price
        WHERE hotel_price_group_id = #{hotelPriceGroupId,jdbcType=DECIMAL}
        <if test="excludePriceIdList != null and excludePriceIdList.size() > 0">
            AND hotel_price_id NOT IN
            <foreach collection="excludePriceIdList" open="(" close=")" item="priceId" separator="," index="index">
                #{priceId}
            </foreach>
        </if>
    </delete>

    <select id="selectPriceInfo" resultType="com.fangcang.grfp.core.vo.ProjectHotelPriceAndGroupInfoVO">
        SELECT
            t1.hotel_price_id AS hotelPriceId,
            t1.project_intent_hotel_id AS projectIntentHotelId,
            t1.project_id AS projectId,
            t1.hotel_id AS hotelId,
            t1.hotel_price_level_id AS hotelPriceLevelId,
            t1.hotel_price_group_id AS hotelPriceGroupId,
            t1.price_type AS priceType,
            t1.one_person_price AS onePersonPrice,
            t1.two_person_price AS twoPersonPrice,
            t2.lra AS lra,
            t2.is_include_breakfast AS isIncludeBreakfast
        FROM t_project_hotel_price t1
        LEFT JOIN t_project_hotel_price_group t2 ON t1.hotel_price_group_id = t2.hotel_price_group_id
        WHERE t1.project_intent_hotel_id IN
            <foreach collection="projectIntentHotelIds" open="(" close=")" item="projectIntentHotelId" separator="," index="index">
            #{projectIntentHotelId}
        </foreach>
        ORDER BY t1.project_intent_hotel_id ASC, t1.one_person_price ASC
    </select>
</mapper>
