package com.fangcang.grfp.core.vo.response.project;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(description = "项目策略统计信息")
public class ProjectStrategyStatsVO extends BaseVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目策略总数")
    private Integer strategyCount;

    @ApiModelProperty(value = "最大显示顺序")
    private Integer maxDisplayOrder;

    @ApiModelProperty(value = "策略名称是否存在")
    private Boolean strategyNameExists;

}
