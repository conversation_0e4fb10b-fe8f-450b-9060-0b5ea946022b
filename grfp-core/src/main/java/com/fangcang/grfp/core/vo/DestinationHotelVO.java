package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("目的地酒店信息")
@Getter
@Setter
public class DestinationHotelVO extends BaseVO {


    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;

    @ApiModelProperty(value = "酒店名称")
    private String hotelName;

    @ApiModelProperty(value = "国家编号")
    private String countryCode;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "省份编号")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "城市编号")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "google纬度")
    private String latGoogle;

    @ApiModelProperty(value = "google经度")
    private String lngGoogle;
}
