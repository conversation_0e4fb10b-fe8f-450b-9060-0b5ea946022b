package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.api.controller.org.vo.OrgInfoVO;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelTendStrategyVO;
import com.fangcang.grfp.core.vo.response.project.ProjectPoiVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("项目详情响应")
public class ProjectDetailsVO {
    @ApiModelProperty("项目基本信息")
    private ProjectBasicInfoVO projectBasicInfo;
    
    @ApiModelProperty("机构信息")
    private OrgInfoVO org;
    
    @ApiModelProperty("项目POI信息列表")
    private List<ProjectPoiVO> projectPoiInfoList;
    
    @ApiModelProperty("项目采购策略")
    private ProjectHotelTendStrategyVO projectHotelTendStrategy;
} 