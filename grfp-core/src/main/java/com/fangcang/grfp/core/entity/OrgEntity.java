package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 机构
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_org")
public class OrgEntity extends BaseVO {


    /**
     * 机构ID
     */
    @TableId(value = "org_id", type = IdType.AUTO)
    private Integer orgId;

    /**
     * 机构名称
     */
    @TableField("org_name")
    private String orgName;

    /**
     * 机构类型 1平台，2酒店，3企业，4酒店集团
     */
    @TableField("org_type")
    private Integer orgType;

    /**
     * 联系人
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 联系人电话区号
     */
    @TableField("contact_mobile_area_code")
    private String contactMobileAreaCode;

    /**
     * 联系人电话
     */
    @TableField("contact_mobile")
    private String contactMobile;

    /**
     * 联系人电邮
     */
    @TableField("contact_email")
    private String contactEmail;


    /**
     * 财务联系人
     */
    @TableField("financial_contact_name")
    private String financialContactName;

    /**
     * 财务联系人电话区号
     */
    @TableField("financial_contact_mobile_area_code")
    private String financialContactMobileAreaCode;

    /**
     * 财务联系人电话
     */
    @TableField("financial_contact_mobile")
    private String financialContactMobile;

    /**
     * 财务联系人电邮
     */
    @TableField("financial_contact_email")
    private String financialContactEmail;

    /**
     * 详细地址
     */
    @TableField("address_detail")
    private String addressDetail;

    /**
     * 公司简介
     */
    @TableField("company_profile")
    private String companyProfile;

    /**
     * 机构logo地址
     */
    @TableField("logo_url")
    private String logoUrl;

    /**
     * 状态(1：有效，0：无效)
     */
    @TableField("state")
    private Integer state;

    /**
     * 合作商渠道编码
     */
    @TableField("partner_channel_code")
    private String partnerChannelCode;

    /**
     * 渠道合作商ID
     */
    @TableField("channel_partner_id")
    private Integer channelPartnerId;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
