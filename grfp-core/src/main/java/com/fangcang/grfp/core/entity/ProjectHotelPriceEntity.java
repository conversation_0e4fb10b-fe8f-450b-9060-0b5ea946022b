package com.fangcang.grfp.core.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 项目酒店价格
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_hotel_price")
public class ProjectHotelPriceEntity extends BaseVO {


    /**
     * 项目酒店价格ID
     */
    @TableId(value = "hotel_price_id", type = IdType.AUTO)
    private Integer hotelPriceId;

    /**
     * 项目酒店意向ID
     */
    @TableField("project_intent_hotel_id")
    private Integer projectIntentHotelId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 项目酒店价格档次ID
     */
    @TableField("hotel_price_level_id")
    private Integer hotelPriceLevelId;

    /**
     * 项目酒店价格组ID
     */
    @TableField("hotel_price_group_id")
    private Integer hotelPriceGroupId;

    /**
     * 价格类型 1:协议价,2:Season1价,3:Season2价
     */
    @TableField("price_type")
    private Integer priceType;

    /**
     * 单人价格
     */
    @TableField("one_person_price")
    private BigDecimal onePersonPrice;

    /**
     * 双人价格
     */
    @TableField("two_person_price")
    private BigDecimal twoPersonPrice;

    /**
     * 单人价格 (最近议价价格)
     */
    @TableField("last_one_person_price")
    private BigDecimal lastOnePersonPrice;

    /**
     * 双人价格 (最近议价价格)
     */
    @TableField("last_two_person_price")
    private BigDecimal lastTwoPersonPrice;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
