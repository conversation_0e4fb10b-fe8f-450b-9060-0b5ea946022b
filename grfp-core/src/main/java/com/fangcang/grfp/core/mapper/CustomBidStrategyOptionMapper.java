package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.CustomBidStrategyOptionEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 自定义报价策略选项表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface CustomBidStrategyOptionMapper extends BaseMapper<CustomBidStrategyOptionEntity> {

    /**
     * 批量新增或更新
     */
    void batchUpsert(@Param("list") List<CustomBidStrategyOptionEntity> strategyOptionList);

    /**
     * 根据策略 id 查询
     */
    default List<CustomBidStrategyOptionEntity> selectByStrategyIdAndProjectIdAndHotelId(Collection<Long> strategyIds, Integer projectId, Long hotelId) {
        if (CollectionUtils.isEmpty(strategyIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CustomBidStrategyOptionEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CustomBidStrategyOptionEntity::getStrategyId, strategyIds);
        queryWrapper.eq(CustomBidStrategyOptionEntity::getProjectId, projectId);
        queryWrapper.eq(CustomBidStrategyOptionEntity::getHotelId, hotelId);
        return selectList(queryWrapper);
    }

    default void deleteByProjectIdAndHotelId(Integer projectId, Long hotelId) {
        LambdaQueryWrapper<CustomBidStrategyOptionEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomBidStrategyOptionEntity::getProjectId, projectId);
        queryWrapper.eq(CustomBidStrategyOptionEntity::getHotelId, hotelId);
        delete(queryWrapper);
    }

    /**
     * 根据项目 id 和策略 id 查询
     */
    default List<CustomBidStrategyOptionEntity> selectByProjectIdAndStrategyId(Integer projectId, Collection<Long> strategyIds) {
        if (CollectionUtils.isEmpty(strategyIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CustomBidStrategyOptionEntity> queryWrapper = Wrappers.lambdaQuery(CustomBidStrategyOptionEntity.class);
        queryWrapper.eq(CustomBidStrategyOptionEntity::getProjectId, projectId);
        queryWrapper.in(CustomBidStrategyOptionEntity::getStrategyId, strategyIds);
        return selectList(queryWrapper);
    }
}
