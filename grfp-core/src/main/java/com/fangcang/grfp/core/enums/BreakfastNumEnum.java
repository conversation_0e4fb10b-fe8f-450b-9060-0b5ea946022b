package com.fangcang.grfp.core.enums;

import com.fangcang.grfp.core.util.GenericAppUtility;

/**
 * <AUTHOR>
 * @date 2022/12/23 16:27
 */
public enum BreakfastNumEnum {

    MINUSONE(-1, "床位早"),
    Z<PERSON><PERSON>(0, "无早"),
    ONE(1, "单早"),
    TWO(2, "双早");

    public Integer key;
    public String value;

    BreakfastNumEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (BreakfastNumEnum breakfastNumEnum : BreakfastNumEnum.values()) {
            if (breakfastNumEnum.key.equals(key)) {
                value = breakfastNumEnum.value;
                break;
            }
        }
        return value;
    }

    public static BreakfastNumEnum getEnumByIncludeBreakfast(boolean includeBreakfast, int personCount) {
        if (includeBreakfast && personCount == 1) {
            return BreakfastNumEnum.ONE;
        } else if (includeBreakfast && personCount == 2) {
            return BreakfastNumEnum.TWO;
        } else if (includeBreakfast && personCount > 2) {
            return BreakfastNumEnum.MINUSONE;
        } else if(!includeBreakfast) {
            return BreakfastNumEnum.ZERO;
        } else {
            return null;
        }
    }

    public static String getTextByKey(BreakfastNumEnum breakfastNumEnum, int languageId) {
        if(breakfastNumEnum == null){
            return null;
        }
        return GenericAppUtility.getText(languageId, "BREAKFAST_TYPE_" + breakfastNumEnum.name());
    }
}
