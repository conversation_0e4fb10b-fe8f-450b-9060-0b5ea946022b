package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("上传文件结果")
@Getter
@Setter
public class UploadFileResultVO extends BaseVO {

    @ApiModelProperty("文件地址")
    private String fileUrl;

    @ApiModelProperty("文件Key")
    private String fileKey;

}
