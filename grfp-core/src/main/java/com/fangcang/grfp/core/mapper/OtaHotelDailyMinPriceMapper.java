package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.OtaHotelDailyMinPriceEntity;
import com.fangcang.grfp.core.vo.response.hotel.OtaHotelMinMaxPriceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 酒店每日OTA价格 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface OtaHotelDailyMinPriceMapper extends BaseMapper<OtaHotelDailyMinPriceEntity> {

    List<OtaHotelMinMaxPriceVO> queryOtaHotelMinMaxPriceVOList(@Param("hotelIds") List<Long> hotelIds,
                                                               @Param("salesDateFrom") String salesDateFrom, @Param("salesDateTo") String salesDateTo);
}
