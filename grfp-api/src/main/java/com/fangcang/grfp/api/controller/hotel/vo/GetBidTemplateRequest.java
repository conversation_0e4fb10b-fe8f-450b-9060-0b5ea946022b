package com.fangcang.grfp.api.controller.hotel.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("获取报价模板请求数据")
@Getter
@Setter
public class GetBidTemplateRequest extends BaseVO {

    @ApiModelProperty("项目意向酒店ID")
    @NotNull
    private Integer projectIntentHotelId;
}
