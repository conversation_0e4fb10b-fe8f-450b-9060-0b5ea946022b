package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 文件附件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_attachment_file")
public class AttachmentFileEntity extends BaseVO {


    /**
     * 文件ID
     */
    @TableId(value = "attachment_file_id", type = IdType.AUTO)
    private Long attachmentFileId;

    /**
     * 文件原名
     */
    @TableField("file_original_name")
    private String fileOriginalName;

    /**
     * 文件key
     */
    @TableField("file_key")
    private String fileKey;

    /**
     * 类型 1-机构logo 2-机构签约主体营业执照 3-正文合同模板(不含技术定位符)，供投标等预览时用  4-机构签约主体授权书  5-合同  6-合同正式模板
     */
    @TableField("business_type")
    private Integer businessType;

    /**
     * 是否有效 1-有效 0-无效
     */
    @TableField("is_active")
    private Integer isActive;

    /**
     * 关联ID
     */
    @TableField("external_id")
    private Long externalId;

    /**
     * 文件流
     */
    @TableField("file_stream")
    private byte[] fileStream;

    /**
     * 文件大小
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
