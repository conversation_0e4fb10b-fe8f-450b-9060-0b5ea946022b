package com.fangcang.grfp.core.enums;

import com.fangcang.grfp.core.util.GenericAppUtility;

/**
 * 项目类型
 *
 * <AUTHOR>
 * @date 2022/9/15 18:42
 */
public enum ProjectTypeEnum {

    HOTEL(1, "酒店", "Hotel");

    public int key;

    public String value;

    public String valueEn;

    ProjectTypeEnum(int key, String value, String valueEn) {
        this.key = key;
        this.value = value;
        this.valueEn = valueEn;
    }

    public static String getTextByKey(Integer key, int languageId) {
        String value = null;
        for (ProjectTypeEnum projectTypeEnum : ProjectTypeEnum.values()) {
            if (projectTypeEnum.key == key) {
                value = GenericAppUtility.getText(languageId, "PROJECT_TYPE_" + projectTypeEnum.name());
                break;
            }
        }
        return value;
    }
}
