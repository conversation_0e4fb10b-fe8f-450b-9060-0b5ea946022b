package com.fangcang.grfp.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("字段前后变化日志")
public class KvpLogBeforeAfterValuePairVO {

	// ---------------------------------------------------------------------------------------------------- Private Member Variables
	@ApiModelProperty("字段名")
	protected String field;
	@ApiModelProperty("变更前")
	protected String beforeValue;
	@ApiModelProperty("变更后")
	protected String afterValue;

	// ---------------------------------------------------------------------------------------------------- Constructor

	public KvpLogBeforeAfterValuePairVO() {
		super();
	}

	public KvpLogBeforeAfterValuePairVO(String field, String beforeValue, String afterValue) {
		super();

		this.field = field;
		this.beforeValue = beforeValue;
		this.afterValue = afterValue;
	}

	// ---------------------------------------------------------------------------------------------------- Getters/Setters

	public String getField() {
		return field;
	}

	public void setField(String field) {
		this.field = field;
	}

	public String getBeforeValue() {
		return beforeValue;
	}

	public void setBeforeValue(String beforeValue) {
		this.beforeValue = beforeValue;
	}

	public String getAfterValue() {
		return afterValue;
	}

	public void setAfterValue(String afterValue) {
		this.afterValue = afterValue;
	}

}
