package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel("修改项目请求")
@Getter
@Setter
public class UpdateProjectRequest extends BaseVO {

    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;

    @ApiModelProperty("项目名称")
    @NotNull
    private String projectName;

    @ApiModelProperty("排序 由于大到小")
    private Integer displayOrder;

    @ApiModelProperty("招标机构id")
    @NotNull
    private Integer tenderOrgId;

    @ApiModelProperty("招标方项目联系人")
    @NotBlank
    private String contactName;

    @ApiModelProperty("招标方项目人手机号码")
    @NotBlank
    private String contactMobile;

    @ApiModelProperty("招标方式(1-公开招标，2-邀请招标)")
    @NotNull
    private Integer tenderType;

    @ApiModelProperty("项目类型")
    @NotNull
    private Integer projectType;

    @ApiModelProperty("报价状态更新通知方式(0-手工，1-自动)")
    @NotNull
    private Integer bidStateUpdatedNotifyMode;

    @ApiModelProperty("招标开始日期 yyyy-MM-dd")
    @NotBlank
    private String bidStartTime;

    @ApiModelProperty("招标结束日期 yyyy-MM-dd")
    @NotBlank
    private String bidEndTime;

    @ApiModelProperty("第一轮报价开始时间 yyyy-MM-dd")
    @NotBlank
    private String firstBidStartTime;

    @ApiModelProperty("第一轮报价结束时间 yyyy-MM-dd")
    @NotBlank
    private String firstBidEndTime;

    @ApiModelProperty("第二轮报价开始时间 yyyy-MM-dd")
    private String secondBidStartTime;

    @ApiModelProperty("第二轮报价结束时间 yyyy-MM-dd")
    private String secondBidEndTime;

    @ApiModelProperty("第三轮报价开始时间 yyyy-MM-dd")
    private String thirdBidStartTime;

    @ApiModelProperty("第三轮报价结束时间 yyyy-MM-dd")
    private String thirdBidEndTime;

    @ApiModelProperty("预估采购数量，项目类型为酒店时即为酒店数量")
    @NotNull
    private Integer tenderCount;

    @ApiModelProperty("协议报价开始日期 yyyy-MM-dd")
    private String priceMonitorStartDate;

    @ApiModelProperty("协议报价结束日期 yyyy-MM-dd")
    private String priceMonitorEndDate;

}
