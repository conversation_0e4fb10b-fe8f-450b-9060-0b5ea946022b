package com.fangcang.grfp.core.vo.response.hotelprice;

import com.fangcang.grfp.core.enums.BreakfastNumEnum;
import com.fangcang.grfp.core.vo.response.bidprice.PriceApplicableRoomInfoResponse;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/24 16:34
 */

@Getter
@Setter
public class HotelMinPriceResponse {

    //酒店id
    private Long hotelId;

    //价格档次
    private Integer hotelPriceLevelId;

    //报价最低价
    private BigDecimal minPrice;

    //早餐
    private BreakfastNumEnum breakfastNum;

    //是否包含早餐
    private Integer isIncludeBreakfast;

    //酒店意向id
    private Integer projectIntentHotelId;

    // 房型描述
    private String roomTypeDesc;


    // 房型列表
    private List<PriceApplicableRoomInfoResponse> roomResponseList;



}
