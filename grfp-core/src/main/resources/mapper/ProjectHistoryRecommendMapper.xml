<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectHistoryRecommendMapper">

    <update id="resetRecommendPoiNearHotelInfo">
        UPDATE
        T_PROJECT_HISTORY_RECOMMEND
        SET
        IS_POI_NEAR_HOTEL = 0,
        IS_POI_NEAR_HOTEL_RECOMMEND = 0,
        POI_NEAR_HOTEL_RECOMMENDS = null,
        POI_NEAR_HOTEL_POI_ID = null
        WHERE
        PROJECT_ID = #{projectId}
    </update>

    <insert id="insertOrUpdate">
    INSERT INTO t_project_history_recommend (
        project_id,
        hotel_id,
        is_frequency,
        is_frequency_recommend,
        frequency_recommends,
        is_same_level_freq,
        is_same_level_freq_recommend,
        same_level_freq_hotel_id,
        same_level_freq_hotel_distance,
        same_level_freq_hotel_info,
        same_level_freq_recommends,
        is_poi_near_hotel,
        is_poi_near_hotel_recommend,
        poi_near_hotel_recommends,
        poi_near_hotel_poi_id,
        is_no_poi_hot_area,
        is_no_poi_hot_area_recommend,
        no_poi_hot_area_recommends,
        no_poi_hotel_id,
        no_poi_hotel_info,
        is_area_gather,
        is_area_gather_recommend,
        area_gather_hotel_id,
        area_gather_hotel_info,
        area_gather_recommends,
        is_high_quality,
        is_high_quality_recommend,
        is_saved_hotel,
        is_saved_hotel_recommend,
        price_level_room_night,
        creator,
        create_time,
        modifier,
        modify_time
    ) VALUES (
        #{projectId,jdbcType=BIGINT},
        #{hotelId,jdbcType=BIGINT},
        #{isFrequency,jdbcType=INTEGER},
        #{isFrequencyRecommend,jdbcType=INTEGER},
        #{frequencyRecommends,jdbcType=VARCHAR},
        #{isSameLevelFreq,jdbcType=INTEGER},
        #{isSameLevelFreqRecommend,jdbcType=INTEGER},
        #{sameLevelFreqHotelId,jdbcType=BIGINT},
        #{sameLevelFreqHotelDistance,jdbcType=DECIMAL},
        #{sameLevelFreqHotelInfo,jdbcType=VARCHAR},
        #{sameLevelFreqRecommends,jdbcType=VARCHAR},
        #{isPoiNearHotel,jdbcType=INTEGER},
        #{isPoiNearHotelRecommend,jdbcType=INTEGER},
        #{poiNearHotelRecommends,jdbcType=VARCHAR},
        #{poiNearHotelPoiId,jdbcType=BIGINT},
        #{isNoPoiHotArea,jdbcType=INTEGER},
        #{isNoPoiHotAreaRecommend,jdbcType=INTEGER},
        #{noPoiHotAreaRecommends,jdbcType=VARCHAR},
        #{noPoiHotelId,jdbcType=BIGINT},
        #{noPoiHotelInfo,jdbcType=VARCHAR},
        #{isAreaGather,jdbcType=INTEGER},
        #{isAreaGatherRecommend,jdbcType=INTEGER},
        #{areaGatherHotelId,jdbcType=BIGINT},
        #{areaGatherHotelInfo,jdbcType=VARCHAR},
        #{areaGatherRecommends,jdbcType=VARCHAR},
        #{isHighQuality,jdbcType=INTEGER},
        #{isHighQualityRecommend,jdbcType=VARCHAR},
        #{isSavedHotel,jdbcType=INTEGER},
        #{isSavedHotelRecommend,jdbcType=INTEGER},
        #{priceLevelRoomNight,jdbcType=INTEGER},
        #{creator,jdbcType=VARCHAR},
        NOW(),
        #{modifier,jdbcType=VARCHAR},
        NOW()
    )
    ON DUPLICATE KEY UPDATE
        <if test="isFrequency != null">
            is_frequency = #{isFrequency,jdbcType=INTEGER},
        </if>
        <if test="isFrequencyRecommend != null">
            is_frequency_recommend = #{isFrequencyRecommend,jdbcType=INTEGER},
        </if>
        <if test="frequencyRecommends != null and frequencyRecommends != ''">
            frequency_recommends = #{frequencyRecommends,jdbcType=VARCHAR},
        </if>
        <if test="isSameLevelFreq != null">
            is_same_level_freq = #{isSameLevelFreq,jdbcType=INTEGER},
        </if>
        <if test="isSameLevelFreqRecommend != null">
            is_same_level_freq_recommend = #{isSameLevelFreqRecommend,jdbcType=INTEGER},
        </if>
        <if test="sameLevelFreqHotelId != null">
            same_level_freq_hotel_id = #{sameLevelFreqHotelId,jdbcType=BIGINT},
        </if>
        <if test="sameLevelFreqHotelDistance != null">
            same_level_freq_hotel_distance = #{sameLevelFreqHotelDistance,jdbcType=DECIMAL},
        </if>
        <if test="sameLevelFreqHotelInfo != null and sameLevelFreqHotelInfo != ''">
            same_level_freq_hotel_info = #{sameLevelFreqHotelInfo,jdbcType=VARCHAR},
        </if>
        <if test="sameLevelFreqRecommends != null and sameLevelFreqRecommends != ''">
            same_level_freq_recommends = #{sameLevelFreqRecommends,jdbcType=VARCHAR},
        </if>
        <if test="isPoiNearHotel != null">
            is_poi_near_hotel = #{isPoiNearHotel,jdbcType=INTEGER},
        </if>
        <if test="isPoiNearHotelRecommend != null">
            is_poi_near_hotel_recommend = #{isPoiNearHotelRecommend,jdbcType=INTEGER},
        </if>
        <if test="poiNearHotelRecommends != null and poiNearHotelRecommends != ''">
            poi_near_hotel_recommends = #{poiNearHotelRecommends,jdbcType=VARCHAR},
        </if>
        <if test="poiNearHotelPoiId != null">
            poi_near_hotel_poi_id = #{poiNearHotelPoiId,jdbcType=BIGINT},
        </if>
        <if test="isNoPoiHotArea != null">
            is_no_poi_hot_area = #{isNoPoiHotArea,jdbcType=INTEGER},
        </if>
        <if test="isNoPoiHotAreaRecommend != null">
            is_no_poi_hot_area_recommend = #{isNoPoiHotAreaRecommend,jdbcType=INTEGER},
        </if>
        <if test="noPoiHotAreaRecommends != null and noPoiHotAreaRecommends != ''">
            no_poi_hot_area_recommends = #{noPoiHotAreaRecommends,jdbcType=VARCHAR},
        </if>
        <if test="noPoiHotelId != null">
            no_poi_hotel_id = #{noPoiHotelId,jdbcType=BIGINT},
        </if>
        <if test="noPoiHotelInfo != null and noPoiHotelInfo != ''">
            no_poi_hotel_info = #{noPoiHotelInfo,jdbcType=VARCHAR},
        </if>
        <if test="isAreaGather != null">
            is_area_gather = #{isAreaGather,jdbcType=INTEGER},
        </if>
        <if test="isAreaGatherRecommend != null">
            is_area_gather_recommend = #{isAreaGatherRecommend,jdbcType=INTEGER},
        </if>
        <if test="areaGatherHotelId != null">
            area_gather_hotel_id = #{areaGatherHotelId,jdbcType=BIGINT},
        </if>
        <if test="areaGatherHotelInfo != null and areaGatherHotelInfo != ''">
            area_gather_hotel_info = #{areaGatherHotelInfo,jdbcType=VARCHAR},
        </if>
        <if test="areaGatherRecommends != null and areaGatherRecommends != ''">
            area_gather_recommends = #{areaGatherRecommends,jdbcType=VARCHAR},
        </if>
        <if test="isHighQuality != null">
            is_high_quality = #{isHighQuality,jdbcType=INTEGER},
        </if>
        <if test="isHighQualityRecommend != null">
            is_high_quality_recommend = #{isHighQualityRecommend,jdbcType=VARCHAR},
        </if>
        <if test="isSavedHotel != null">
            is_saved_hotel = #{isSavedHotel,jdbcType=INTEGER},
        </if>
        <if test="isSavedHotelRecommend != null">
            is_saved_hotel_recommend = #{isSavedHotelRecommend,jdbcType=INTEGER},
        </if>
        <if test="priceLevelRoomNight != null">
            price_level_room_night = #{priceLevelRoomNight,jdbcType=INTEGER},
        </if>
        modifier    = #{modifier,jdbcType=VARCHAR}
    </insert>

    <update id="resetHighQuality">
        UPDATE
        T_PROJECT_HISTORY_RECOMMEND
        SET
        IS_HIGH_QUALITY = 0,
        IS_HIGH_QUALITY_RECOMMEND = 0,
        PRICE_LEVEL_ROOM_NIGHT = 0
        WHERE
        PROJECT_ID = #{projectId}
    </update>

    <update id="resetSavedHotel">
        UPDATE
        T_PROJECT_HISTORY_RECOMMEND
        SET
        IS_SAVED_HOTEL = 0,
        IS_SAVED_HOTEL_RECOMMEND = 0,
        PRICE_LEVEL_ROOM_NIGHT = 0
        WHERE
        PROJECT_ID = #{projectId}
    </update>

    <update id="resetRecommendFrequency">
        UPDATE
        T_PROJECT_HISTORY_RECOMMEND
        SET
        IS_FREQUENCY = 0,
        IS_FREQUENCY_RECOMMEND = 0,
        FREQUENCY_RECOMMENDS = null
        WHERE
        PROJECT_ID = #{projectId}
    </update>

    <update id="resetRecommendFrequencySameLevelInfo">
        UPDATE
        T_PROJECT_HISTORY_RECOMMEND
        SET
        IS_SAME_LEVEL_FREQ = 0,
        IS_SAME_LEVEL_FREQ_RECOMMEND = 0,
        SAME_LEVEL_FREQ_HOTEL_ID = null,
        SAME_LEVEL_FREQ_HOTEL_INFO = null,
        SAME_LEVEL_FREQ_RECOMMENDS = null
        WHERE
        PROJECT_ID = #{projectId}
    </update>
</mapper>
