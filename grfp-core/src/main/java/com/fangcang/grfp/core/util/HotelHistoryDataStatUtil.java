package com.fangcang.grfp.core.util;

import com.fangcang.grfp.core.vo.response.bidmap.QueryProjectHotelBidStatResponse;
import com.fangcang.grfp.core.vo.response.project.BidHotelInfoQueryResponse;
import com.fangcang.grfp.core.vo.response.project.QueryHistoryProjectInfoResponse;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class HotelHistoryDataStatUtil {

    /**
     * 结算服务分排名
     */
    public static QueryProjectHotelBidStatResponse calculateHotelServicePointOrder(Long hotelId, List<BidHotelInfoQueryResponse> bidHotelInfoQueryResponseList){
        QueryProjectHotelBidStatResponse queryProjectHotelBidStatResponse = new QueryProjectHotelBidStatResponse();
        bidHotelInfoQueryResponseList = bidHotelInfoQueryResponseList.stream().sorted(Comparator.comparing(BidHotelInfoQueryResponse::getHotelServicePoints).reversed()).collect(Collectors.toList());
        BigDecimal lastServicePoint = BigDecimal.ZERO;
        int servicePointOrder = 0;
        BigDecimal lastYearServicePoint = BigDecimal.ZERO;
        for (BidHotelInfoQueryResponse projectIntentHotel : bidHotelInfoQueryResponseList) {
            if (lastServicePoint.compareTo(BigDecimal.ZERO) == 0 || lastServicePoint.compareTo(projectIntentHotel.getHotelServicePoints()) != 0) {
                servicePointOrder = servicePointOrder + 1;
                lastServicePoint = projectIntentHotel.getHotelServicePoints();
            }
            if (Objects.equals(projectIntentHotel.getHotelId(), hotelId)) {
                lastYearServicePoint = projectIntentHotel.getHotelServicePoints();
                break;
            }
        }
        queryProjectHotelBidStatResponse.setLastYearServicePoint(lastYearServicePoint);
        queryProjectHotelBidStatResponse.setLastYearServicePointOrder(servicePointOrder);
        return queryProjectHotelBidStatResponse;
    }

    /**
     * 计算间夜数排名
     */
    public static QueryProjectHotelBidStatResponse calculateHotelNightRoomOrder(Long hotelId, List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoResponseList){
        QueryProjectHotelBidStatResponse queryProjectHotelBidStatResponse = new QueryProjectHotelBidStatResponse();
        List<QueryHistoryProjectInfoResponse> nightRoomStatList = queryHistoryProjectInfoResponseList.stream().sorted(Comparator.comparing(QueryHistoryProjectInfoResponse::getRoomNightCount).reversed()).collect(Collectors.toList());
        int nightRoomOrder = 0;
        int lastNightRoomCount = -1;
        int lastYearRoomNightCount = 0;
        for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : nightRoomStatList){
            if(lastNightRoomCount == -1 || lastNightRoomCount != queryHistoryProjectInfoResponse.getRoomNightCount()){
                nightRoomOrder = nightRoomOrder +1;
                lastNightRoomCount = queryHistoryProjectInfoResponse.getRoomNightCount();
            }

            if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), hotelId)){
                lastYearRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                break;
            }
        }
        queryProjectHotelBidStatResponse.setLastYearRoomNightCount(Math.max(0, lastYearRoomNightCount));
        queryProjectHotelBidStatResponse.setLastYearRoomNightOrder(nightRoomOrder);
        return queryProjectHotelBidStatResponse;
    }

    /**
     * 计算销售金额排名
     * @param hotelId
     * @param queryHistoryProjectInfoResponseList
     * @return
     */
    public static QueryProjectHotelBidStatResponse calculateSalesAmountOrder(Long hotelId, List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoResponseList){
        QueryProjectHotelBidStatResponse queryProjectHotelBidStatResponse = new QueryProjectHotelBidStatResponse();
        List<QueryHistoryProjectInfoResponse> amountDisplayList = queryHistoryProjectInfoResponseList.stream().sorted(Comparator.comparing(QueryHistoryProjectInfoResponse::getTotalAmount).reversed()).collect(Collectors.toList());
        BigDecimal lastHotelAmount = BigDecimal.ZERO;
        BigDecimal lastYearHotelAmount = BigDecimal.ZERO;
        int amountOrder = 0;
        for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : amountDisplayList){
            if(lastHotelAmount.compareTo(BigDecimal.ZERO) == 0 || lastHotelAmount.compareTo(queryHistoryProjectInfoResponse.getTotalAmount()) != 0){
                amountOrder = amountOrder +1;
            }
            lastHotelAmount = queryHistoryProjectInfoResponse.getTotalAmount();

            if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), hotelId)){
                lastYearHotelAmount = queryHistoryProjectInfoResponse.getTotalAmount();
                break;
            }
        }
        queryProjectHotelBidStatResponse.setLastYearAmount(lastYearHotelAmount);
        queryProjectHotelBidStatResponse.setLastYearAmountOrder(amountOrder);
        return queryProjectHotelBidStatResponse;
    }

    /**
     * 计算OTA排名
     * @return
     */
    public static int calculateOTAOrder(Long hotelId, List<BidHotelInfoQueryResponse> ratingHotelBidHotelInfoQueryResponses){
        String lastRatting = "";
        int rattingOrder = 0;
        List<BidHotelInfoQueryResponse> ratingHotelBidHotelInfoQueryOrderResponses = ratingHotelBidHotelInfoQueryResponses.stream().filter(o -> StringUtil.isValidString(o.getRating())).sorted(Comparator.comparing(BidHotelInfoQueryResponse::getRating).reversed()).collect(Collectors.toList());
        // 计算排名
        for (BidHotelInfoQueryResponse bidHotelInfoQueryResponse : ratingHotelBidHotelInfoQueryOrderResponses) {
            if (!StringUtil.isValidString(lastRatting) || lastRatting.compareTo(bidHotelInfoQueryResponse.getRating()) != 0) {
                rattingOrder = rattingOrder + 1;
            }
            lastRatting = bidHotelInfoQueryResponse.getRating();
            if (Objects.equals(bidHotelInfoQueryResponse.getHotelId(), hotelId)) {
                break;
            }
        }
        return rattingOrder;
    }



}
