package com.fangcang.grfp.core.vo.response.hotel;

import com.fangcang.grfp.core.entity.ProjectEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @auther cjh
 * @description 投标项目实体
 * @date 2022/10/8
 */
@ApiModel("投标项目实体")
@Getter
@Setter
public class ProjectHotelTentResponse extends ProjectEntity {

    // 投标开始时间
    @ApiModelProperty("投标开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date tenderStartTime;

    // 投标结束时间
    @ApiModelProperty("投标结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date tenderEndTime;

    // 公布评标结果日
    @ApiModelProperty("公布评标结果日")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date publishTenderResultTime;

    // 项目意向酒店
    @ApiModelProperty("项目意向酒店")
    private Integer projectIntentHotelId;

    // 投标价
    @ApiModelProperty("投标价")
    private BigDecimal basePrice;

    @ApiModelProperty("早餐类型")
    private String breakfastNum;

    @ApiModelProperty("是否包含早餐")
    private Integer isIncludeBreakfast;

    // 酒店投标报价是否含佣金：1-是，0-否
    @ApiModelProperty("酒店投标报价是否含佣金：1-是，0-否")
    private Integer supportIncludeCommission;

    // 所有投标价佣金率(大于等于0小于等于100)
    @ApiModelProperty("所有投标价佣金率(大于等于0小于等于100)")
    private BigDecimal tendCommission;

    //酒店指派销售跟进人id(招投标项目时指派的)
    @ApiModelProperty("酒店指派销售跟进人id(招投标项目时指派的)")
    private Long hotelContactUid;

    //酒店指派销售跟进人姓名(招投标项目时指派的)
    @ApiModelProperty("酒店指派销售跟进人姓名(招投标项目时指派的)")
    private String hotelContactName;

    //机构名称
    @ApiModelProperty("机构名称")
    private String orgName;


    // 城市名称
    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("城市编号")
    private String cityCode;

    // 酒店ID
    @ApiModelProperty("酒店ID")
    private Long hotelId;

    // 酒店名称
    @ApiModelProperty("酒店名称")
    private String hotelName;

    // 唯一标识  projectId_hotelId
    @ApiModelProperty("唯一标识  projectId_hotelId")
    private String projectIdHotelIdKey;

    /** 排序 （大到小） */
    @ApiModelProperty("排序 （大到小）")
    private Integer displayOrder;

    // 集团审核状态
    @ApiModelProperty("集团审核状态")
    private Integer hotelGroupApproveStatus;

    // 集团审核驳回原因
    @ApiModelProperty("集团审核驳回原因")
    private String hotelGroupRejectApproveReason;

}
