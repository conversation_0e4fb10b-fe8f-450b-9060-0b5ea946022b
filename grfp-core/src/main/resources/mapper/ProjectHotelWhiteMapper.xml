<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectHotelWhiteMapper">
    <insert id="upsert">
        insert into t_project_hotel_white(project_id, hotel_id, hotel_white_type, creator) value
            (#{entity.projectId}, #{entity.hotelId}, #{entity.hotelWhiteType}, #{entity.creator})
        on duplicate key update modifier = #{entity.modifier}
    </insert>

    <select id="selectByCondition" resultType="com.fangcang.grfp.core.vo.response.project.ProjectHotelWhiteVO">
        select t2.hotel_id,
               t2.name_zh_cn as hotelNameZhCn,
               t2.name_en_us as hotelNameEnUs,
               t2.city_code,
               t1.hotel_white_type,
               t1.creator,
               t1.create_time,
               t3.name_en_us as cityNameEnUs,
               t3.name_zh_cn as cityNameZhCn
        from t_project_hotel_white t1
                 inner join t_hotel t2 on t1.hotel_id = t2.hotel_id and t2.is_active = 1
                 inner join t_city t3 on t2.city_code = t3.city_code
        <where>
            t1.project_id = #{req.projectId}
            <if test="req.hotelWhiteType != null">
                and t1.hotel_white_type = #{req.hotelWhiteType}
            </if>
            <if test="req.cityCode != null and req.cityCode != ''">
                and t2.city_code = #{req.cityCode}
            </if>
            <if test="req.hotelId != null">
                and t1.hotel_id = #{req.hotelId}
            </if>
            <if test="req.hotelName != null and req.hotelName != ''">
                and (t2.name_en_us like concat('%', #{req.hotelName}, '%') or t2.name_zh_cn like concat('%', #{req.hotelName}, '%'))
            </if>
        </where>
    </select>

    <select id="queryProjectWhiteHotelIdList" resultType="java.lang.Long">
        select
            hotel_id
        from t_project_hotel_white
        <where>
            project_id = #{projectId}
            <if test="hotelWhiteType != null">
                and hotel_white_type = #{hotelWhiteType}
            </if>
        </where>
    </select>
</mapper>
