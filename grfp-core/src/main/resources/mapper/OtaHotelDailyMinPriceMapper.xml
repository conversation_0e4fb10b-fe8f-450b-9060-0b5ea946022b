<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.OtaHotelDailyMinPriceMapper">
    <select id="queryOtaHotelMinMaxPriceVOList" resultType="com.fangcang.grfp.core.vo.response.hotel.OtaHotelMinMaxPriceVO">
        SELECT hotel_id       AS hotelId,
               MIN(min_price) AS minPrice,
               MAX(min_price) AS maxPrice
        FROM t_ota_hotel_daily_min_price
        WHERE HOTEL_ID IN
        <foreach collection="hotelIds" item="hotelId" open="(" close=")" separator=",">
            #{hotelId}
        </foreach>
        AND SALES_DATE >= date(#{salesDateFrom,jdbcType=VARCHAR})
        AND SALES_DATE &lt;= date(#{salesDateTo,jdbcType=VARCHAR})
        GROUP BY HOTEL_ID
    </select>
</mapper>
