package com.fangcang.grfp.api.controller.hotelgroup.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.HGroupDefaultCusStrategyVO;
import com.fangcang.grfp.core.vo.HotelGroupDefaultApplicableDayVO;
import com.fangcang.grfp.core.vo.HotelGroupDefaultUnApplicableDayVO;
import com.fangcang.grfp.core.vo.response.project.ProjectBidCustomTendStrategyVO;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelTendStrategyVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@ApiModel("保存酒店集团项目报价模板信息")
@Getter
@Setter
public class UpdateHotelGroupBidTemplateRequest extends BaseVO {

    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;

    /**
     * 意向酒店集团ID
     */
    @ApiModelProperty("意向酒店集团ID")
    @NotNull
    private Integer projectIntentHotelGroupId;

    /**
     * 酒店集团销售联系人姓名
     */
    @ApiModelProperty("酒店集团销售联系人姓名")
    private String hotelGroupBidContactName;

    /**
     * 酒店集团销售联系人电话
     */
    @ApiModelProperty("酒店集团销售联系人电话")
    private String hotelGroupBidContactMobile;

    /**
     * 酒店集团销售联系人邮箱
     */
    @ApiModelProperty("酒店集团销售联系人邮箱")
    private String hotelGroupBidContactEmail;

    /**
     * 酒店是否须支持员工到店付款: 1-是 0-否
     */
    @ApiModelProperty("酒店是否须支持员工到店付款: 1-是 0-否")
    private Integer supportPayAtHotel;

    /**
     * 酒店是否需支持 VCC 公司统一支付: 1-是 0-否
     */
    @ApiModelProperty("店是否需支持 VCC 公司统一支付: 1-是 0-否")
    private Integer supportVccPay;

    /**
     * 酒店是否须支持提供入住明细信息: 1-是 0-否
     */
    @ApiModelProperty("酒店是否须支持提供入住明细信息: 1-是 0-否")
    private Integer supportCheckinInfo;

    /**
     * 酒店是否须支持到店付免担保: 1-是 0-否
     */
    @ApiModelProperty("酒店是否须支持到店付免担保: 1-是 0-否")
    private Integer supportNoGuarantee;


    /**
     * 酒店需支持提前离店按实际入住金额收款: 1-是 0-否
     */
    @ApiModelProperty("酒店需支持提前离店按实际入住金额收款: 1-是 0-否")
    private Integer supportPayEarlyCheckout;

    /**
     * 报价是否需要包括税费和服务费: 1-是 0-否
     */
    @ApiModelProperty("报价是否需要包括税费和服务费: 1-是 0-否")
    private Integer supportIncludeTaxService;

    /**
     * 酒店房间是否需提供免费 WIFI 服务: 1-是 0-否
     */
    @ApiModelProperty("酒店房间是否需提供免费 WIFI 服务: 1-是 0-否")
    private Integer supportWifi;

    /**
     * 到店付免担保最晚保留时间
     */
    @ApiModelProperty("到店付免担保最晚保留时间")
    private String lateReserveTime;

    /**
     * 超出最晚保留时间采取措施：1-酒店直接与入住人或预订联系人联系，确认入住时间；0-取消订单
     */
    @ApiModelProperty("超出最晚保留时间采取措施：1-酒店直接与入住人或预订联系人联系，确认入住时间；0-取消订单")
    private Integer doAfterLateReserveTime;

    /**
     * 免费取消限制天数
     */
    @ApiModelProperty("免费取消限制天数")
    private Integer supportCancelDay;

    /**
     * 免费取消限制时间
     */
    @ApiModelProperty("免费取消限制时间")
    private String supportCancelTime;

    /**
     * 有无佣金 1-有 0-否
     */
    @ApiModelProperty("有无佣金 1-有 0-否")
    private Integer hasCommission;

    /**
     * 佣金比例
     */
    @ApiModelProperty("佣金比例")
    private BigDecimal commission;

    @ApiModelProperty("酒店集团其他承诺")
    private List<HGroupDefaultCusStrategyVO> hotelGroupDefaultCusStrategyList;

    @ApiModelProperty("适用日期列表")
    private List<HotelGroupDefaultApplicableDayVO> applicableDayList;

    @ApiModelProperty("不适应日期列表")
    private List<HotelGroupDefaultUnApplicableDayVO> unapplicableDayList;

    @ApiModelProperty("币种")
    private String currencyCode;



}
