package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.ProjectHotelPriceEntity;
import com.fangcang.grfp.core.vo.ProjectHotelPriceAndGroupInfoVO;
import com.fangcang.grfp.core.vo.response.hotelprice.HotelMinPriceResponse;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 项目酒店价格 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface ProjectHotelPriceMapper extends BaseMapper<ProjectHotelPriceEntity> {

    default List<ProjectHotelPriceEntity> selectByProjectIntentHotelId(int projectIntentHotelId){
        LambdaQueryWrapper<ProjectHotelPriceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHotelPriceEntity::getProjectIntentHotelId, projectIntentHotelId);
        queryWrapper.orderByAsc(ProjectHotelPriceEntity::getPriceType);
        return selectList(queryWrapper);
    }

    default List<ProjectHotelPriceEntity> selectByProjectPriceGroupId(int projectPriceGroupId){
        LambdaQueryWrapper<ProjectHotelPriceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHotelPriceEntity::getHotelPriceGroupId, projectPriceGroupId);
        return selectList(queryWrapper);
    }

    default List<ProjectHotelPriceEntity> selectByProjectPriceLevelId(int projectPriceLevelId){
        LambdaQueryWrapper<ProjectHotelPriceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHotelPriceEntity::getHotelPriceLevelId, projectPriceLevelId);
        return selectList(queryWrapper);
    }

    default int deleteByProjectPriceGroupId(int projectPriceGroupId){
        LambdaQueryWrapper<ProjectHotelPriceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHotelPriceEntity::getHotelPriceGroupId, projectPriceGroupId);
        return delete(queryWrapper);
    }

    default List<ProjectHotelPriceEntity> selectByProjectIdAndHotelId(Integer projectId, Long hotelId) {
        LambdaQueryWrapper<ProjectHotelPriceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHotelPriceEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectHotelPriceEntity::getHotelId, hotelId);
        return selectList(queryWrapper);
    }


    default List<ProjectHotelPriceEntity> selectByProjectId(Integer projectId) {
        LambdaQueryWrapper<ProjectHotelPriceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHotelPriceEntity::getProjectId, projectId);
        return selectList(queryWrapper);
    }

    default void deleteByProjectAndHotelId(Integer projectId, Long hotelId) {
        LambdaQueryWrapper<ProjectHotelPriceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHotelPriceEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectHotelPriceEntity::getHotelId, hotelId);
        delete(queryWrapper);
    }

    int recordLastPrice(@Param("projectIntentHotelIds") List<Integer> projectIntentHotelIds);

    BigDecimal selectMinPriceByProjectIntentHotelId(@Param("projectIntentHotelId") Integer projectIntentHotelId);


    void batchInsert(@Param("list") List<ProjectHotelPriceEntity> newPrices);


    int deleteByGroupIdAndExcludePriceIds(@Param("hotelPriceGroupId") Integer hotelPriceGroupId, @Param("excludePriceIdList") List<Integer> excludePriceIdList);

    List<ProjectHotelPriceAndGroupInfoVO> selectPriceInfo(@Param("projectIntentHotelIds") Collection<Integer> projectIntentHotelIds);

}
