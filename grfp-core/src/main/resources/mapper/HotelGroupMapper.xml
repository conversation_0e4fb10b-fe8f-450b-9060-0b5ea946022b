<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HotelGroupMapper">

    <insert id="batchUpsert">
        insert into t_hotel_group(hotel_group_id, name_zh_cn, name_en_us, is_active, creator, modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.hotelGroupId}, #{item.nameZhCn}, #{item.nameEnUs},  #{item.isActive}, #{item.creator}, #{item.modifier})
        </foreach>
        on duplicate key update hotel_group_id = values(hotel_group_id),
                                name_zh_cn     = values(name_zh_cn),
                                name_en_us     = values(name_en_us),
                                is_active      = values(is_active),
                                modifier       = values(modifier)
    </insert>

    <select id="selectHotelGroupNameList" resultType="com.fangcang.grfp.core.vo.HotelGroupVO">
        SELECT
            `hotel_group_id` AS hotelGroupId,
            `name_en_us` AS nameEnUs,
            `name_zh_cn` AS nameZhCn
        FROM
            `t_hotel_group`
        WHERE `name_en_us` LIKE CONCAT('%', #{hotelGroupName}, '%') OR `name_zh_cn` LIKE CONCAT('%', #{hotelGroupName}, '%')
        <if test="languageId != null and languageId == 1">
            ORDER BY  `name_en_us` ASC
        </if>
        <if test="languageId != null and languageId == 2">
            ORDER BY `name_zh_cn` ASC
        </if>
        <if test="limitCount != null and limitCount > 0">
            LIMIT #{limitCount}
        </if>
    </select>

    <select id="listDataPage" parameterType="com.fangcang.grfp.core.vo.request.ListHotelGroupDataRequest"
            resultType="com.fangcang.grfp.core.vo.ListHotelGroupDataVO">
        SELECT
            `hotel_group_id` AS hotelGroupId,
            `name_en_us` AS nameEnUs,
            `name_zh_cn` AS nameZhCn,
            `is_active` AS isActive,
            `creator` AS creator,
            `modifier` AS modifier,
            `create_time` AS createTime,
            `modify_time` AS modifyTime
        FROM
            `t_hotel_group`
        <where>
            <if test="query.hotelGroupId != null and query.hotelGroupId > 0">
                AND `hotel_group_id` = #{query.hotelGroupId}
            </if>
            <if test="query.isActive != null">
                AND `is_active` = #{query.isActive}
            </if>
            <if test="query.hotelGroupName != null and query.hotelGroupName != ''">
                AND (`name_en_us` LIKE CONCAT('%', #{query.hotelGroupName}, '%') OR
                    `name_zh_cn` LIKE CONCAT('%', #{query.hotelGroupName}, '%'))
            </if>
        </where>
        ORDER BY `hotel_group_id` ASC
    </select>


</mapper>
