package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.api.controller.common.vo.FindHotelListByDestinationRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("查询项目邀请酒店列表")
public class QueryProjectInvitedHotelList extends FindHotelListByDestinationRequest {

    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;


}
