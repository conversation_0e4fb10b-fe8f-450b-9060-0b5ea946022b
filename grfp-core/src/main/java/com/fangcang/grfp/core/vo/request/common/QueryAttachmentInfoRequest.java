package com.fangcang.grfp.core.vo.request.common;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


@ApiModel("查询附件信息请求")
@Getter
@Setter
public class QueryAttachmentInfoRequest extends BaseVO {

    //附件id
    @ApiModelProperty("附件id")
    private Long fileId;

    //外部业务id
    @ApiModelProperty("外部业务id")
    private Long externalId;

    //类型 1 机构logo 2.签约主体 20：项目电邮附件
    @ApiModelProperty("1 机构logo 2.签约主体 20：项目电邮附件")
    private Integer businessType;

}
