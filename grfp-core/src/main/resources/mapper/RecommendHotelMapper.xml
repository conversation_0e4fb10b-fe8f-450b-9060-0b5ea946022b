<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.RecommendHotelMapper">

    <select id="selectRecommendHotelByHotelIds" resultType="com.fangcang.grfp.core.entity.RecommendHotelEntity">
        select
            *
        from T_RECOMMEND_HOTEL
        where STATE=1 and HOTEL_ID in
        <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
            #{hotelId}
        </foreach>
    </select>

</mapper>
