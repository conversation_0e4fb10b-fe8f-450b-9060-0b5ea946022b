package com.fangcang.grfp.api.controller.currencyexchangerate.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@ApiModel("修改 美元兑币种汇率请求")
@Getter
@Setter
public class UpdateCurrencyExchangeRequest extends BaseVO {

    @ApiModelProperty("币种参数集合")
    private List<UpdateCurrencyExchangeVO>  updateCurrencyList;


}
