package com.fangcang.grfp.core.vo.request.hotelgroup;

import com.fangcang.grfp.core.base.BaseVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/14 18:30
 */
@Getter
@Setter
public class QueryHotelGroupBidPriceCountRequest extends BaseVO {

    //酒店集团机构id
    private Integer hotelGroupOrgId;

    private List<Integer> projectIds;

    //1查询总报价数 ，2议价中，3已中签
    private Integer queryType;

    // 酒店集团品牌id集合
    private Collection<Long> hotelGroupBrandIdList;

}
