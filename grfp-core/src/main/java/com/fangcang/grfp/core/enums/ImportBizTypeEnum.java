package com.fangcang.grfp.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导入业务类型枚举
 */
@Getter
@AllArgsConstructor
public enum ImportBizTypeEnum {

    /**
     * 导入 POI
     */
    IMPORT_POI("IMPORT_POI", "导入 POI"),
    IMPORT_HOTEL_ORG("IMPORT_HOTEL_ORG", "导入酒店机构"),
    IMPORT_HOTEL_GROUP_INTENT_HOTEL("IMPORT_HOTEL_GROUP_INTENT_HOTEL", "导入酒店集团意向酒店"),
    IMPORT_PROJECT_HOTEL_HISTORY_TRADE_DATA("IMPORT_PROJECT_HOTEL_HISTORY_TRADE_DATA", "导入项目酒店历史交易数据"),
    IMPORT_HOTEL_INTENT_HOTEL("IMPORT_HOTEL_INTENT_HOTEL", "导入邀请酒店"),
    IMPORT_CONTRACT_STATUS("IMPORT_CONTRACT_STATUS", "导入签约状态"),
    IMPORT_LANYON_BID("IMPORT_LANYON_BID", "Lanyon导入报价"),
    ;

    private final String key;

    private final String value;

}
