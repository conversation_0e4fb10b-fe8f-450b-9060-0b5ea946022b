<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HotelViolationsMonitorMapper">

    <select id="queryHotelViolationCountStat" resultType="com.fangcang.grfp.core.vo.response.hotel.HotelViolationCountStatDto">
        SELECT hotel_id AS hotelId,
               count(1) AS violationCount
        FROM t_hotel_violations_monitor
        WHERE project_id = #{projectId,jdbcType=BIGINT}
          AND hotel_id IN
        <foreach collection="hotelIdList" item="hotelId" separator="," open="(" close=")">
            #{hotelId,jdbcType=BIGINT}
        </foreach>
        AND VIOLATION_TYPE IN (1, 2)
        GROUP BY hotel_id
    </select>
</mapper>
