 package com.fangcang.grfp.core.enums;

 /**
  * 高频预订酒店推荐原因
  */
 public enum RecommendFrequencyEnum {

    ROOM_NIGHT_100_199(1, "高产酒店，建议邀约酒店进行报价"),
    ROOM_NIGHT_200_599(2, "重点高产酒店，建议与酒店议价拿到较好价格"),
    ROOM_NIGHT_600(3, "超高产酒店，建议签约LRA，保证协议房间供应");

    public int key;

    public String value;
    RecommendFrequencyEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (RecommendFrequencyEnum recommendFrequencyEnum : RecommendFrequencyEnum.values()) {
            if (recommendFrequencyEnum.key == key) {
                value = recommendFrequencyEnum.value;
                break;
            }
        }
        return value;
    }
}
