package com.fangcang.grfp.api.controller.currencyexchangerate.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@ApiModel("查询 美元兑币种汇率")
@Getter
@Setter
public class QueryCurrencyExchangeRequest extends BaseVO {

    @ApiModelProperty("币种code")
    private String currencyCode;

}
