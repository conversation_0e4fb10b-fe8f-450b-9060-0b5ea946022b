<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ImportBidTaskMapper">
    <select id="selectByCondition" resultType="com.fangcang.grfp.core.vo.ImportBidTaskListVO">
        select t1.id,
               t1.hotel_id,
               t1.project_id,
               t1.create_time,
               t1.modifier,
               t1.modify_time,
               t2.project_name,
               t2.tender_org_id,
               t3.city_code,
               t1.prop_name,
               t1.prop_code,
               t1.prop_phone,
               t1.prop_add,
               t3.address_zh_cn,
               t3.address_en_us,
               t3.telephone,
               t1.is_validate_error,
               t1.validate_error_detail,
               t1.generate_bid_status
        from t_import_bid_task t1
                 inner join t_project t2 on t1.project_id = t2.project_id
                 left join t_hotel t3 on t1.hotel_id = t3.hotel_id
        <include refid="SELECT_BID_TASK_WHERE"/>
        order by id desc
    </select>

    <select id="selectDataDetailByCondition" resultType="com.fangcang.grfp.core.entity.ImportBidTaskEntity">
        select t1.hotel_id, t1.data_detail from t_import_bid_task t1
            inner join t_project t2 on t1.project_id = t2.project_id
            left join t_hotel t3 on t1.hotel_id = t3.hotel_id
        <include refid="SELECT_BID_TASK_WHERE"/>
    </select>

    <sql id="SELECT_BID_TASK_WHERE">
        <where>
            <if test="request.projectName != null and request.projectName != ''">
                AND t2.project_name like CONCAT('%', #{request.projectName}, '%')
            </if>
            <if test="request.hotelId != null">
                AND t3.hotel_id = #{request.hotelId}
            </if>
            <if test="request.cityCode != null and request.cityCode != ''">
                AND t3.city_code = #{request.cityCode}
            </if>
            <if test="request.propName != null and request.propName != ''">
                AND t1.prop_name LIKE CONCAT('%', #{request.propName}, '%')
            </if>
            <if test="request.createDateStart != null">
                AND t1.create_time >= #{request.createDateStart}
            </if>
            <if test="request.createDateEnd != null">
                AND t1.create_time &lt;= #{request.createDateEnd}
            </if>
            <if test="request.isValidateError != null">
                AND t1.is_validate_error = #{request.isValidateError}
            </if>
            <if test="request.generateBidStatus != null">
                AND t1.generate_bid_status = #{request.generateBidStatus}
            </if>
        </where>
    </sql>
</mapper>
