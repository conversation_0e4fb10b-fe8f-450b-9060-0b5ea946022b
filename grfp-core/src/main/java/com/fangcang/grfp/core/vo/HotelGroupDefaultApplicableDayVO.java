package com.fangcang.grfp.core.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 项目意向酒店集团默认适用日期
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@ApiModel("项目意向酒店集团适用日期")
@Getter
@Setter
public class HotelGroupDefaultApplicableDayVO extends BaseVO {


    /**
     * 默认适用日期ID
     */
    @ApiModelProperty(hidden = true)
    private Integer defaultApplicableDayId;

    /**
     * 酒店集团意向ID
     */
    @ApiModelProperty(value = "酒店集团意向ID")
    private Integer projectIntentHotelGroupId;


    /**
     * 价格类型
     */
    @ApiModelProperty(value = "价格类型 1:协议价,2:Season1价 3:Season2价")
    private Integer priceType;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone=RfpConstant.TIME_ZONE)
    private Date startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone=RfpConstant.TIME_ZONE)
    private Date endDate;



}
