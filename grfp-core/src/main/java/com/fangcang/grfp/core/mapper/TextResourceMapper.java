package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.core.entity.TextResourceEntity;
import com.fangcang.grfp.core.util.StringUtility;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 文字资源表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface TextResourceMapper extends BaseMapper<TextResourceEntity> {

    default TextResourceEntity getByCode(int textResourceType, String textResourceCode){
        return this.selectOne(new LambdaQueryWrapper<TextResourceEntity>()
                .eq(TextResourceEntity::getTextResourceType, textResourceType)
                .eq(TextResourceEntity::getTextResourceCode, textResourceCode)
        );
    }

    default int deleteByCode(int textResourceType, String textResourceCode){
        return this.delete(new LambdaQueryWrapper<TextResourceEntity>()
                .eq(TextResourceEntity::getTextResourceType, textResourceType)
                .eq(TextResourceEntity::getTextResourceCode, textResourceCode)
        );
    }

    /**
     * 根据 code 和 type 批量查询
     */
    default List<TextResourceEntity> selectByCodeAndType(int type, Collection<String> codes){
        return this.selectList(new LambdaQueryWrapper<TextResourceEntity>()
                .eq(TextResourceEntity::getTextResourceType, type)
                .in(TextResourceEntity::getTextResourceCode, codes)
        );
    }

    /**
     * Query list by updateDateTime, Used in cachedTextResourceService
     */
    default List<TextResourceEntity> getListByUpdatedTime(Date updateTimeForm, Date updateTimeTo, Integer textResourceType, String textResourceCodeLikeRight){
        LambdaQueryWrapper<TextResourceEntity>queryWrapper = new LambdaQueryWrapper<>();
        if(updateTimeForm != null) {
            queryWrapper.ge(TextResourceEntity::getModifyTime, updateTimeForm);
        }
        if(updateTimeTo != null) {
            queryWrapper.le(TextResourceEntity::getModifyTime, updateTimeTo);
        }
        if(textResourceType != null){
            queryWrapper.eq(TextResourceEntity::getTextResourceType, textResourceType);
        }
        if(StringUtility.notNullAndNotEmpty(textResourceCodeLikeRight)) {
            queryWrapper.likeRight(TextResourceEntity::getTextResourceCode, textResourceCodeLikeRight);
        }
        return this.selectList(queryWrapper);
    }

    /**
     * Query last updatedTime
     */
    public Date getLastUpdatedTime(@Param("textResourceType") Integer textResourceType);

    Page<TextResourceEntity> queryPageList(Page<TextResourceEntity> page, @Param("searchTextResourceType") Integer searchTextResourceType, @Param("searchTextResourceCodeLike") String searchTextResourceCodeLike, @Param("searchTextResourceValueLike") String searchTextResourceValueLike);

    List<TextResourceEntity> queryListForExport(@Param("searchTextResourceType") Integer searchTextResourceType, @Param("searchTextResourceCodeLike") String searchTextResourceCodeLike, @Param("searchTextResourceValueLike") String searchTextResourceValueLike);

}
