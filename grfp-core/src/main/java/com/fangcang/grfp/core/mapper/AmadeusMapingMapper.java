package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fangcang.grfp.core.entity.AmadeusMapingEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * amadeus酒店映射 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
public interface AmadeusMapingMapper extends BaseMapper<AmadeusMapingEntity> {

    default Long queryHotelIdBySpHotelId(String spHotelId) {
        LambdaQueryWrapper<AmadeusMapingEntity>queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmadeusMapingEntity::getSpHotelId, spHotelId);
        List<AmadeusMapingEntity> list = this.selectList(queryWrapper);
        if (!list.isEmpty()) {
            return list.get(0).getHotelId();
        }
        return null;
    }


}
