package com.fangcang.grfp.core.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.entity.HotelEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel("酒店数据列表信息")
@Getter
@Setter
public class ListHotelDataVO extends BaseVO {


    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;

    /**
     * 国家编号
     */
    @ApiModelProperty(value = "国家编号")
    private String countryCode;

    /**
     * 省/州/邦编码
     */
    @ApiModelProperty(value = "省/州/邦编码")
    private String provinceCode;

    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    /**
     * 英文名称
     */
    @ApiModelProperty(value = "英文名称")
    private String nameEnUs;

    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    private String nameZhCn;


    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String telephone;

    /**
     * 电邮
     */
    @ApiModelProperty(value = "email")
    private String email;

    /**
     * 星级
     */
    @ApiModelProperty(value = "星级")
    private String hotelStar;

    @ApiModelProperty(value = "星级名称")
    private String hotelStarName;

    /**
     * 酒店评分
     */
    @ApiModelProperty(value = "酒店评分")
    private String rating;

    /**
     * 开业日期
     */

    @ApiModelProperty(value = "开业日期")
    private Date openingDate;

    /**
     * 装修日期
     */
    @ApiModelProperty(value = "装修日期")
    private Date fitmentDate;

    /**
     * 酒店集团ID
     */
    @ApiModelProperty(value = "酒店集团ID")
    private Long hotelGroupId;

    /**
     * 酒店品牌ID
     */
    @ApiModelProperty(value = "酒店品牌ID")
    private Long hotelBrandId;

    /**
     * 主图URL地址
     */
    @ApiModelProperty(value = "主图URL地址")
    private String mainPicUrl;

    /**
     * 酒店入住时间
     */
    @ApiModelProperty(value = "酒店入住时间")
    private String checkInTime;

    /**
     * 酒店离店时间
     */
    @ApiModelProperty(value = "酒店离店时间")
    private String checkOutTime;

    /**
     * 客房总数
     */
    @ApiModelProperty(value = "客房总数")
    private Integer roomNum;

    /**
     * 是否包含会议室 1:是，0:否
     */
    @ApiModelProperty(value = "是否包含会议室 1:是，0:否")
    private Integer hasMeetingRoom;

    /**
     * 是否包含游泳池 1:是，0:否
     */
    @ApiModelProperty(value = "是否包含游泳池 1:是，0:否")
    private Integer hasSwimmingPool;

    /**
     * 是否包含健身房 1:是，0:否
     */
    @ApiModelProperty(value = "是否包含健身房 1:是，0:否")
    private Integer hasGym;

    /**
     * 是否包含洗衣房 1:是，0:否
     */
    @ApiModelProperty(value = "是否包含洗衣房 1:是，0:否")
    private Integer hasLaundryRoom;

    /**
     * 百度经度
     */
    @ApiModelProperty(value = "百度经度")
    private BigDecimal lngBaidu;

    /**
     * 百度纬度
     */
    @ApiModelProperty(value = "百度纬度")
    private BigDecimal latBaidu;

    /**
     * Google经度
     */
    @ApiModelProperty(value = "Google经度")
    private BigDecimal lngGoogle;

    /**
     * Google纬度
     */
    @ApiModelProperty(value = "Google纬度")
    private BigDecimal latGoogle;

    /**
     * 火星经度
     */
    @ApiModelProperty(value = "火星经度")
    private BigDecimal lngMars;

    /**
     * 火星纬度
     */
    @ApiModelProperty(value = "火星纬度")
    private BigDecimal latMars;

    /**
     * 是否有效 1:是，0:否
     */
    @ApiModelProperty(value = "是否有效 1:是，0:否")
    private Integer isActive;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    @ApiModelProperty(value = "酒店集团名称")
    private String hotelGroupName;

    @ApiModelProperty(value = "酒店品牌名称")
    private String hotelBrandName;



}
