package com.fangcang.grfp.api.controller.login.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel("登录请求")
@Data
public class LoginByVerifyCodeRequest extends BaseVO {

    @ApiModelProperty(value="登录账号", required = true)
    @NotBlank
    protected String account;
    @ApiModelProperty(value="验证码", required = true)
    @NotBlank
    protected String verifyCode;

}
