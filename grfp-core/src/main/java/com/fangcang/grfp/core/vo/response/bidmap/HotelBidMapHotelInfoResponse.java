package com.fangcang.grfp.core.vo.response.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@ApiModel("酒店地图信息")
@Getter
@Setter
public class HotelBidMapHotelInfoResponse extends BaseVO {

    // 酒店名称
    @ApiModelProperty("酒店名称")
    private String hotelName;

    //标书状态(0：未投标，1：新标(酒店提交报价，企业还未处理)，2：议价中，3：已中标，4：已否决)
    @ApiModelProperty("标书状态(0：未投标，1：新标(酒店提交报价，企业还未处理)，2：议价中，3：已中标，4：已否决)")
    private Integer bidState;

    /**
     * 百度经度
     */
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private BigDecimal lngBaiDu;

    /**
     * 百度纬度
     */
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private BigDecimal latBaiDu;


    /**
     * Google经度
     */
    @ApiModelProperty("Google经度")
    private BigDecimal lngGoogle;

    /**
     * Google纬度
     */
    @ApiModelProperty("Google纬度")
    private BigDecimal latGoogle;


}
