package com.fangcang.grfp.core.manager;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.entity.OrgRelatedHotelBrandEntity;
import com.fangcang.grfp.core.enums.RoleCodeEnum;
import com.fangcang.grfp.core.mapper.OrgRelatedHotelBrandMapper;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.GenericAppUtility;
import com.fangcang.grfp.core.vo.HotelGroupUserRelatedInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class HotelGroupManager {


}
