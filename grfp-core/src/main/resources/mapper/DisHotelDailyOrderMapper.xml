<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.DisHotelDailyOrderMapper">
    <select id="selectHotelSavedAmountStatList" resultType="com.fangcang.grfp.core.vo.response.hotel.DisHotelDailyOrderResponse">
        select t.savedAmount,
               t.hotelId,
               t.hasOtaPriceOrderAmount,
               t.totalOtaPrice,
               t.roomNightCount,
               t.orderAmount
        from (
        select o.hotel_id                        hotelId,
               sum(o.SAVED_AMOUNT)               savedAmount,
               sum(o.HAS_OTA_PRICE_ORDER_AMOUNT) hasOtaPriceOrderAmount,
               sum(o.TOTAL_OTA_PRICE)            totalOtaPrice,
               sum(o.ROOM_NIGHT_COUNT)           roomNightCount,
               sum(o.ORDER_AMOUNT)               orderAmount
        from t_dis_hotel_daily_order o
        <where>
            #{req.startTime} &lt;= o.BOOK_DATE
              and o.BOOK_DATE &lt;= #{req.endTime}
              AND o.TOTAL_OTA_PRICE IS NOT NULL
              AND o.HAS_OTA_PRICE_ORDER_AMOUNT IS NOT NULL
            <if test="req.orgId != null and req.orgId > 0">
                and exists (select 1
                            from t_order_monitor_config c
                            where o.DISTRIBUTOR_CODE = c.distributor_code
                              and c.STATE = 1
                              and c.org_id = #{req.orgId})
            </if>
            <if test="req.hotelIdList != null and req.hotelIdList.size() > 0">
                and o.HOTEL_ID in
                <foreach item="item" index="index" collection="req.hotelIdList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by o.hotel_id
        )t
        order by t.savedAmount desc
    </select>

    <select id="selectHotelRoomNights" resultType="com.fangcang.grfp.core.vo.HotelRoomNightCountDto">
        SELECT HOTEL_ID                        AS hotelId,
               sum(nvl(t.room_night_count, 0)) AS roomNightCount
        FROM t_dis_hotel_daily_order t
        WHERE t.hotel_id IN
        <foreach collection="hotelIds" item="hotelId" separator="," open="(" close=")">
            #{hotelId}
        </foreach>
        AND date(#{startTime}) &lt;= BOOK_DATE
        and BOOK_DATE &lt;= date(#{endTime})
        GROUP BY HOTEL_ID
    </select>

    <select id="selectHotelSavedAmountGroupByOrg" resultType="com.fangcang.grfp.core.vo.HotelSavedAmountDto">
        SELECT
        o.hotel_id hotelId ,
        omc.ORG_ID orgId,
        NVL(sum(CASE WHEN o.TOTAL_OTA_PRICE IS NULL OR o.TOTAL_OTA_PRICE = 0 THEN 0 ELSE o.SAVED_AMOUNT END) ,0) savedAmount,
        NVL(sum(o.TOTAL_OTA_PRICE),0) totalOtaPrice
        FROM T_DIS_HOTEL_DAILY_ORDER o
        LEFT JOIN T_ORDER_MONITOR_CONFIG omc ON o.DISTRIBUTOR_CODE = omc.distributor_code
        <where>
            <if test="startTime != null and startTime != ''  and endTime != null and endTime != ''">
                and date(#{startTime}) &lt;= o.BOOK_DATE and  o.BOOK_DATE &lt;= date(#{endTime})
            </if>
            <if test="hotelId != null ">
                and o.hotel_id = #{hotelId}
            </if>
            <if test="hotelIdList != null and hotelIdList.size() > 0">
                and o.hotel_id in
                <foreach collection="hotelIdList" item="hotelId" separator="," open="（" close=")">
                    #{hotelId}
                </foreach>
            </if>
            AND omc.STATE = 1
        </where>
        group by o.HOTEL_ID, omc.ORG_ID
    </select>

    <select id="selectHotelSavedAmount" resultType="com.fangcang.grfp.core.vo.HotelSavedAmountDto">
        SELECT o.hotel_id                                                                                               hotelId,
               o.DISTRIBUTOR_CODE                                                                                       distributorCode,
               NVL(sum(CASE WHEN o.TOTAL_OTA_PRICE IS NULL OR o.TOTAL_OTA_PRICE = 0 THEN 0 ELSE o.SAVED_AMOUNT END), 0) savedAmount,
               NVL(sum(o.TOTAL_OTA_PRICE), 0)                                                                           totalOtaPrice
        FROM T_DIS_HOTEL_DAILY_ORDER o
        <where>
            <if test="startTime != null and startTime != ''  and endTime != null and endTime != ''">
                and date(#{startTime}) <![CDATA[   <=  ]]> o.BOOK_DATE
                and o.BOOK_DATE <![CDATA[   <=  ]]> date(#{endTime})
            </if>
            <if test="hotelId != null">
                and o.hotel_id = #{hotelId}
            </if>
            AND o.TOTAL_OTA_PRICE IS NOT NULL
            AND o.HAS_OTA_PRICE_ORDER_AMOUNT IS NOT NULL
        </where>
        group by o.HOTEL_ID, o.DISTRIBUTOR_CODE
    </select>
</mapper>
