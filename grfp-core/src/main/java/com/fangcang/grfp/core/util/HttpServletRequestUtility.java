package com.fangcang.grfp.core.util;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

public class HttpServletRequestUtility {

	// ---------------------------------------------------------------------------------------------------- Public Static Method
	
	public static String extractRequestIpAddress(HttpServletRequest request) {
		return extractRequestIpAddress(request, true);
	}
	
	public static String extractRequestIpAddress(HttpServletRequest request, boolean tryLookupForInternalIpAddress) {
		String result;
		String xForwardedFor = request.getHeader("x-forwarded-for");
		
		// Supporting CloudFlare
		if (xForwardedFor == null) {
			result = request.getRemoteAddr();
		} else {
			StringTokenizer st = new StringTokenizer(xForwardedFor, " ,");
			
			if (st.hasMoreTokens()) {
				result = st.nextToken();
			} else {
				result = request.getRemoteAddr();
			}
		}
		
		// For Development Purpose
		if (
				(tryLookupForInternalIpAddress == true) &&
				(result.startsWith("0:0:0:0") || result.startsWith("192."))
		){
        	List<java.net.URL> urlList = new ArrayList<java.net.URL>();
        	try {
	        	urlList.add(new java.net.URL("http://icanhazip.com"));				// Fast
	        	urlList.add(new java.net.URL("http://ifconfig.me/ip"));				// Fast
	        	urlList.add(new java.net.URL("http://api.my-ip.io/ip"));			// OK
	        	urlList.add(new java.net.URL("http://wtfismyip.com/text"));			// OK
	        	urlList.add(new java.net.URL("http://checkip.amazonaws.com"));		// Slow
	        	urlList.add(new java.net.URL("http://ident.me"));					// Slow
        	} catch (Exception ex) {
        		ex.printStackTrace();
        	}
        	
        	for (java.net.URL url : urlList) {
        		try {
    	            java.net.HttpURLConnection conn = (java.net.HttpURLConnection)url.openConnection();
    	            java.io.InputStream stream = conn.getInputStream();
    	            java.io.InputStreamReader reader = new java.io.InputStreamReader(stream);
    	            java.io.BufferedReader bReader = new java.io.BufferedReader(reader);
    	            
    	            String newIpAddress = bReader.readLine();
    	            System.out.println("Development IP address " + result + " is replaced by " + newIpAddress);
    	            result = newIpAddress;
    	            
    	            try {
    		            bReader.close();
    	            } catch (Exception ex2) {
    	            	ex2.printStackTrace();
    	            }
    	            
    	            try {
    		            reader.close();
    	            } catch (Exception ex2) {
    	            	ex2.printStackTrace();
    	            }
    	            
    	            try {
    		            stream.close();
    	            } catch (Exception ex2) {
    	            	ex2.printStackTrace();
    	            }
    	            
    	            break;						// Break the loop if IP has been found
        		} catch (Exception ex) {
    	            ex.printStackTrace();
        		}
        	}
		}
		
		return result;
	}
	
	public static String extractRequestUserAgent(HttpServletRequest request) {
		String result = request.getHeader("User-Agent");
		
		if (result != null) {
			result = result.substring(0, Math.min(result.length(), 200));
		}
		
		return result;
	}

}
