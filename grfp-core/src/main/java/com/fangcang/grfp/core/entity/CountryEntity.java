package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 国家
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_country")
public class CountryEntity extends BaseVO {


    /**
     * 国家ID
     */
    @TableField("country_id")
    private String countryId;

    /**
     * 国家编号
     */
    @TableId(value = "country_code", type = IdType.ASSIGN_ID)
    private String countryCode;

    /**
     * 英文名称
     */
    @TableField("name_en_us")
    private String nameEnUs;

    /**
     * 英文名称简称
     */
    @TableField("short_name_en_us")
    private String shortNameEnUs;

    /**
     * 中文名称
     */
    @TableField("name_zh_cn")
    private String nameZhCn;

    /**
     * 中文简称名称
     */
    @TableField("short_name_zh_cn")
    private String shortNameZhCn;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
