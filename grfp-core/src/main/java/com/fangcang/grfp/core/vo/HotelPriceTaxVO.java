package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@ApiModel("酒店价格税费")
@Getter
@Setter
public class HotelPriceTaxVO extends BaseVO {

    @ApiModelProperty("报价")
    private BigDecimal bidPrice;

    @ApiModelProperty("净房价")
    private BigDecimal basePrice;

    @ApiModelProperty("税费信息")
    private BidHotelPriceTaxValueVO taxInfo;

    @ApiModelProperty("总包含税费值")
    private BigDecimal totalIncludeTaxAmount;

    @ApiModelProperty("总未包含税费值")
    private BigDecimal totalUnIncludeTaxAmount;

    @ApiModelProperty("企业总成本")
    private BigDecimal totalCostAmount;

}
