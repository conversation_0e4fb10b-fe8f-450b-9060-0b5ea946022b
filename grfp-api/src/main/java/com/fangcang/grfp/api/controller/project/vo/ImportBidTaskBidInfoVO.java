package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(description = "导入报价任务报价信息响应")
public class ImportBidTaskBidInfoVO extends BaseVO {

    @ApiModelProperty(value = "项目ID")
    private Integer projectId;

    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;

    @ApiModelProperty(value = "项目酒店ID")
    private Integer projectIntentHotelId;



}
