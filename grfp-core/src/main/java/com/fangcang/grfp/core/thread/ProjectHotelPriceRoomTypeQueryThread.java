package com.fangcang.grfp.core.thread;


import com.fangcang.grfp.core.entity.PriceApplicableRoomEntity;
import com.fangcang.grfp.core.manager.BidMapManager;
import com.fangcang.grfp.core.manager.HotelManager;
import com.fangcang.grfp.core.mapper.PriceApplicableRoomMapper;
import com.fangcang.grfp.core.mapper.ProjectHotelPriceMapper;
import com.fangcang.grfp.core.vo.response.bidprice.PriceApplicableRoomInfoResponse;
import com.fangcang.grfp.core.vo.response.hotel.RoomNameInfoVO;
import com.fangcang.grfp.core.vo.response.hotelprice.HotelMinPriceResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/7 14:54
 */
public class ProjectHotelPriceRoomTypeQueryThread implements Callable<List<HotelMinPriceResponse>> {

    private static final Logger logger = LoggerFactory.getLogger(ProjectHotelPriceRoomTypeQueryThread.class);

    private CountDownLatch countDownLatch;

    private List<Integer> projectIntentHotelIds;

    private BidMapManager bidMapManager;

    private int languageId;

    private HotelManager hotelManager;

    private PriceApplicableRoomMapper priceApplicableRoomMapper;

    public ProjectHotelPriceRoomTypeQueryThread() {
    }

    public ProjectHotelPriceRoomTypeQueryThread(CountDownLatch countDownLatch,
                                                List<Integer> projectIntentHotelIds,
                                                BidMapManager bidMapManager,
                                                HotelManager hotelManager,
                                                PriceApplicableRoomMapper priceApplicableRoomMapper,
                                                int languageId) {
        this.countDownLatch = countDownLatch;
        this.projectIntentHotelIds = projectIntentHotelIds;
        this.bidMapManager = bidMapManager;
        this.hotelManager = hotelManager;
        this.languageId = languageId;
        this.priceApplicableRoomMapper = priceApplicableRoomMapper;
    }

    @Override
    public List<HotelMinPriceResponse> call()  {
        try {
            if (CollectionUtils.isEmpty(projectIntentHotelIds)) {
                logger.error("查询报价参数异常，项目意向酒店ID为空");
                return null;
            }

            Map<Integer, HotelMinPriceResponse> hotelMinPriceResponseMap = bidMapManager.queryHotelMinPrice(languageId, projectIntentHotelIds, true).stream().collect(Collectors.toMap(HotelMinPriceResponse::getProjectIntentHotelId, o->o, (o1,o2)->o1));
            return new ArrayList<>(hotelMinPriceResponseMap.values());


        } finally {
            countDownLatch.countDown();
        }
    }




}
