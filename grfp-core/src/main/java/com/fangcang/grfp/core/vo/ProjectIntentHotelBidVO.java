package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@ApiModel("项目意向酒店报价 VO")
@Getter
@Setter
public class ProjectIntentHotelBidVO extends BaseVO {

    /**
     * 项目酒店意向ID
     */
    @ApiModelProperty("项目酒店意向ID")
    private Integer projectIntentHotelId;

    /**
//     * 项目ID
     */
    @ApiModelProperty("项目ID")
    private Integer projectId;

    /**
     * 酒店ID
     */
    @ApiModelProperty("酒店ID")
    private Long hotelId;


    /**
     * 酒店报价联系人
     */
    @ApiModelProperty("酒店报价联系人")
    private String hotelBidContactName;

    /**
     * 酒店报价联系人手机号码
     */
    @ApiModelProperty("酒店报价联系人手机号码")
    private String hotelBidContactMobile;

    /**
     * 酒店报价联系人电邮
     */
    @ApiModelProperty("酒店报价联系人电邮")
    private String hotelBidContactEmail;

    /**
     * 酒店报价联系人
     */
    @ApiModelProperty("酒店集团报价联系人")
    private String hotelGroupBidContactName;

    /**
     * 酒店报价联系人手机号码
     */
    @ApiModelProperty("酒店报价联系人手机号码")
    private String hotelGroupBidContactMobile;

    /**
     * 酒店报价联系人电邮
     */
    @ApiModelProperty("酒店报价联系人电邮")
    private String hotelGroupBidContactEmail;


    @ApiModelProperty("邀约邮件发送状态(0：未发送邀约邮件，1：已发送邀约邮件)")
    private Integer sendMailStatus;

    /**
     * 邀约状态(0：未邀请，1：已邀请)，企业主动邀请的记录默认就是已邀请，非企业邀请的酒店指派销售人时默认就是未邀请
     */
    @ApiModelProperty("邀约状态(0：未邀请，1：已邀请)，企业主动邀请的记录默认就是已邀请，非企业邀请的酒店指派销售人时默认就是未邀请")
    private Integer inviteStatus;

    @ApiModelProperty("标书状态(0：未投标，1：新标(酒店提交报价，企业还未处理)，2：议价中，3：已中标，4：已否决，5：放弃报价)")
    private Integer bidState;

    @ApiModelProperty("近一年采购间夜数")
    private Integer lastYearRoomNight;

    /**
     * 采购均价
     */
    @ApiModelProperty("采购均价")
    private BigDecimal tenderAvgPrice;

    /**
     * 投标总权重(投标时根据项目权重和酒店投标策略计算生成)
     */
    @ApiModelProperty("投标总权重(投标时根据项目权重和酒店投标策略计算生成)")
    private BigDecimal bidWeight;

    /**
     * 酒店服务分
     */
    @ApiModelProperty("酒店服务分")
    private BigDecimal hotelServicePoints;

    /**
     * 是否上传报价(0：否，1：是)
     */
    @ApiModelProperty("是否上传报价(0：否，1：是)")
    private Integer isUpload;

    /**
     * 报价导入源数据 1:加力,2:Lanyon
     */
    @ApiModelProperty("报价导入源数据 1:加力,2:Lanyon")
    private Integer bidUploadSource;

    /**
     * 报价机构ID
     */
    @ApiModelProperty("报价机构ID")
    private Integer bidOrgId;

    /**
     * 报价机构类型 2:酒店，4:酒店集团
     */
    @ApiModelProperty("报价机构类型 2:酒店，4:酒店集团")
    private Integer bidOrgType;

    /**
     * 员工权益
     */
    @ApiModelProperty("员工权益")
    private String employRight;

    /**
     * 员工权益上传文件
     */
    @ApiModelProperty("员工权益上传文件")
    private List<UploadFileVO> employRightFileUrl;



}
