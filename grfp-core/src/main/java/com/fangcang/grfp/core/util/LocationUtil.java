package com.fangcang.grfp.core.util;

import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class LocationUtil {

    private static final double EARTH_RADIUS = 6371;
    private static final double R = 6371000; // 地球半径，单位：米

    /**
     * 计算两点之间的距离, 单位为米
     */
    public static double getDistance(double startLat, double startLong, double endLat, double endLong) {
        double latDistance = Math.toRadians(endLat - startLat);
        double lonDistance = Math.toRadians(endLong - startLong);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(startLat)) * Math.cos(Math.toRadians(endLat))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c; // 单位：米
    }

    /**
     * 计算两点之间的距离, 单位为米
     */
    public static BigDecimal getDistance(BigDecimal startLat, BigDecimal startLong, BigDecimal endLat, BigDecimal endLong) {
        if (startLat == null || startLong == null || endLat == null || endLong == null) {
            throw new IllegalArgumentException("经纬度参数不能为 null");
        }

        double lat1 = startLat.doubleValue();
        double lon1 = startLong.doubleValue();
        double lat2 = endLat.doubleValue();
        double lon2 = endLong.doubleValue();

        double distance = getDistance(lat1, lon1, lat2, lon2);
        return BigDecimal.valueOf(distance);
     }

     /**
     * 获取两点之间的距离，单位为千米
     */
    public static BigDecimal getKmDistance(BigDecimal startLat, BigDecimal startLong, BigDecimal endLat, BigDecimal endLong) {
        if (startLat == null || startLong == null || endLat == null || endLong == null) {
            throw new IllegalArgumentException("经纬度参数不能为 null");
        }
        BigDecimal distance = getDistance(startLat, startLong, endLat, endLong);
        System.out.println("distance " + distance);
        return distance.multiply(new BigDecimal("0.001"));
    }

    /**
     * 计算经纬度是否在范围内 (千米)
     */
    public static boolean isInLngLatInDistanceRange(Integer distanceK, BigDecimal startLat, BigDecimal startLong, BigDecimal endLat, BigDecimal endLong){
        if (startLat == null || startLong == null || endLat == null || endLong == null) {
            return false;
        }
        BigDecimal distance = getDistance(startLat, startLong, endLat, endLong);
        return distance.compareTo(new BigDecimal(distanceK).multiply(new BigDecimal("1000"))) <= 0;
    }

    /**
     * 给定中心点和半径，计算经纬度范围
     */
    private static double[] calculateGeoRangeHaversine(double latitude, double longitude, double radius) {
        // 计算纬度范围（直接使用弧长公式，因为纬度变化不受经度影响）
        double deltaLat = Math.toDegrees(radius / EARTH_RADIUS);
        double minLat = latitude - deltaLat;
        double maxLat = latitude + deltaLat;

        // 计算经度范围（考虑纬度影响）
        double deltaLon = Math.toDegrees(Math.asin(Math.sin(radius / EARTH_RADIUS) / Math.cos(Math.toRadians(latitude))));
        double minLon = longitude - deltaLon;
        double maxLon = longitude + deltaLon;

        return new double[]{minLat, maxLat, minLon, maxLon};
    }

    /**
     * 给定中心点和半径，计算经纬度范围
     */
    private static BigDecimal[] calculateGeoRangeHaversine(BigDecimal latitude, BigDecimal longitude, BigDecimal radius) {
        double[] doubles = calculateGeoRangeHaversine(latitude.doubleValue(), longitude.doubleValue(), radius.doubleValue());
        return new BigDecimal[]{BigDecimal.valueOf(doubles[0]), BigDecimal.valueOf(doubles[1]), BigDecimal.valueOf(doubles[2]), BigDecimal.valueOf(doubles[3])};
    }

    /**
     * 判断点是否在多边形内
     */
    public static boolean isInPolygon(BigDecimal lng, BigDecimal lat, List<String[]> lanlatList){
        return isInPolygonJTS(lng.doubleValue(), lat.doubleValue(), lanlatList);
    }

    /**
     * 判断点是否在多边形内
     */
    private static boolean isInPolygonJTS(double lng, double lat, List<String[]> lanlatList) {
        GeometryFactory factory = new GeometryFactory();
        // 创建多边形, 注意最后一个点要与第一个点相同
        if(lanlatList.size() == 6) { // 避免list集合多次执行，导致size变大
            lanlatList.add(new String[]{lanlatList.get(0)[0], lanlatList.get(0)[1]});
        }
        Coordinate[] coords = lanlatList.stream().map(p -> new Coordinate(Double.parseDouble(p[0]), Double.parseDouble(p[1]))).toArray(Coordinate[]::new);
        Polygon polygon = factory.createPolygon(factory.createLinearRing(coords), null);
        Point point = factory.createPoint(new Coordinate(lng, lat));
        return polygon.contains(point); // 或 polygon.covers(point) 包含边上点
    }

    //

    public static void main(String[] args) {
//        System.out.println(getDistance(new BigDecimal("22.53369616"), new BigDecimal("114.06757402"),new BigDecimal("22.53369616"), new BigDecimal("114.06757002")));
        /**
        boolean inPolygon = isInPolygon(new BigDecimal("114.109869"), new BigDecimal("22.557309"), new ArrayList<>(
            Arrays.asList(
                new String[]{"114.115707", "22.558888"},
                new String[]{"114.113457", "22.562785"},
                new String[]{"114.108957", "22.562785"},
                new String[]{"114.106707", "22.558888"},
                new String[]{"114.108957", "22.554990"},
                new String[]{"114.113457", "22.554990"}
            )));
        System.out.println(inPolygon);
         **/
        // 150655

        // 150842
        System.out.println(getKmDistance( new BigDecimal("22.2849520245"), new BigDecimal("114.1569063297"),
                new BigDecimal("22.2864743300"), new BigDecimal("114.1569203900")
        ));

        double dis = getDistance(22.2849520245, 114.1569063297, 22.2864743300, 114.1569203900);
        System.out.println(dis);
        BigDecimal dis1 = BigDecimal.valueOf(dis);
        System.out.println(dis1.multiply(new BigDecimal("0.001")));







    }
}
