package com.fangcang.grfp.api.controller.userpermission.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel("用户权限VO")
@Getter
@Setter
public class UserPermissionVO extends BaseVO {

    /**
     * 用户权限ID
     */
    @ApiModelProperty("用户权限ID")
    private Integer userPermissionId;

    /**
     * 机构类型 1：平台，2：酒店，3：企业，4酒店集团
     */
    @ApiModelProperty("机构类型")
    private Integer orgType;

    @ApiModelProperty("机构类型名称")
    private String orgTypeName;

    /**
     * 角色编码类型 ADMIN：管理员，EMPLOYEE：员工，HEAD_ORGANIZATION：机构负责人，CHANNEL_ADMIN:渠道合作管理员
     */
    @ApiModelProperty("角色 角色编码类型 ADMIN：管理员，EMPLOYEE：员工，HEAD_ORGANIZATION")
    private String roleCode;

    @ApiModelProperty("角色名称")
    private String roleName;

    /**
     * 权限
     */
    @ApiModelProperty("权限")
    private String permission;

    @ApiModelProperty("权限名称")
    private String permissionName;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;
}
