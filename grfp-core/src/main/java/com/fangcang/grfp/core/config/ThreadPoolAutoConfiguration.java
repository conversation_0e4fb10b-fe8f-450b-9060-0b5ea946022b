package com.fangcang.grfp.core.config;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration(proxyBeanMethods = false)
@EnableAsync(proxyTargetClass = true)
@Slf4j
public class ThreadPoolAutoConfiguration {

    /**
     * 通用线程池
     */
    public final static String EXECUTOR_NAME = "generalExecutor";

    /**
     * 同步 GLINK专用线程池
     */
    public final static String SYNC_GLINK_EXECUTOR_NAME = "syncGlinkExecutor";

    /**
     * 查询酒店信息线程池
     */
    public final static String QUERY_BID_HOTEL_INFO_EXECUTOR_NAME = "queryBidHotelInfoExecutor";

    /**
     * 审计日志线程池
     */
    public final static String AUDIT_LOG_EXECUTOR_NAME = "auditLogExecutor";

    @Bean(EXECUTOR_NAME)
    public Executor generalExecutor() {
        // 线程池配置先写死, 后续可以从配置文件读取
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("general-async-");
        executor.setKeepAliveSeconds(300);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.setTaskDecorator(new MdcTaskDecorator());
        return executor;
    }

    @Bean(SYNC_GLINK_EXECUTOR_NAME)
    public Executor syncGlinkExecutor() {
        // 线程池配置先写死, 后续可以从配置文件读取
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("sync-glink-async-");
        executor.setRejectedExecutionHandler((r, executor1) -> log.error("同步 GLink 数据线程池已满, 任务被拒绝, 任务: {}", r));
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.setTaskDecorator(new MdcTaskDecorator());
        return executor;
    }

    @Bean(QUERY_BID_HOTEL_INFO_EXECUTOR_NAME)
    public Executor queryBidHotelInfoExecutor() {
        // 线程池配置先写死, 后续可以从配置文件读取
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(100);
        executor.setQueueCapacity(2000);
        executor.setThreadNamePrefix("queryBidHotelInfoExecutor-async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setKeepAliveSeconds(60);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.setTaskDecorator(new MdcTaskDecorator());
        return executor;
    }

    @Bean(AUDIT_LOG_EXECUTOR_NAME)
    public Executor auditLogExecutor() {
        // 线程池配置先写死, 后续可以从配置文件读取
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(2000); // 审计日志对实时性要求不是很高, 可以慢慢去插入
        executor.setThreadNamePrefix("audit-log-async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setKeepAliveSeconds(600);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.setTaskDecorator(new MdcTaskDecorator());
        return executor;
    }

    /**
     * MDC 任务装饰器, 用于传递 MDC 上下文
     */
    public static class MdcTaskDecorator implements TaskDecorator {

        @Override
        public Runnable decorate(Runnable runnable) {
            Map<String, String> contextMap = MDC.getCopyOfContextMap();
            return () -> {
                try {
                    if (contextMap != null) {
                        MDC.setContextMap(contextMap);
                    }
                    runnable.run();
                } finally {
                    MDC.clear();
                }
            };
        }
    }

}
