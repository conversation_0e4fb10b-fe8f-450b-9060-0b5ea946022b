package com.fangcang.grfp.core.vo.response.project;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel(description = "酒店项目招标采购策略响应")
public class ProjectHotelTendStrategyVO extends BaseVO {

    private static final long serialVersionUID = -3021727511400107023L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "项目 ID")
    private Integer projectId;

    @ApiModelProperty(value = "酒店是否需支持 VCC 公司统一支付: 1-是 0-否")
    private Integer supportVccPay;

    @ApiModelProperty(value = "酒店是否须支持员工到店付款: 1-是 0-否")
    private Integer supportPayAtHotel;

    @ApiModelProperty(value = "酒店是否须支持提供入住明细信息: 1-是 0-否")
    private Integer supportCheckinInfo;

    @ApiModelProperty(value = "酒店是否须支持到店付免担保: 1-是 0-否")
    private Integer supportNoGuarantee;

    @ApiModelProperty(value = "酒店是否须支持提前离店按实际入住金额收款: 1-是 0-否")
    private Integer supportPayEarlyCheckout;

    @ApiModelProperty(value = "报价是否需要包括税费和服务费: 1-是 0-否")
    private Integer supportIncludeTaxService;

    @ApiModelProperty(value = "酒店房间是否需提供免费 WIFI 服务: 1-是 0-否")
    private Integer supportWifi;

    @ApiModelProperty(value = "酒店是否全部报价金额限制范围内报价：1-是 0-否")
    private Boolean supportPriceLimit;

    @ApiModelProperty(value = "限制最低报价")
    private BigDecimal limitMinPrice;

    @ApiModelProperty(value = "限制最高报价，有报价限制时必须有值")
    private BigDecimal limitMaxPrice;

    @ApiModelProperty(value = "限制报价币种")
    private String limitPriceCurrencyCode;

    @ApiModelProperty(value = "酒店全部报价中是否支持N天M点前免费取消: 1-是 0-否")
    private Integer supportCancel;

    @ApiModelProperty(value = "免费取消限制天数")
    private Integer supportCancelDay;

    @ApiModelProperty(value = "免费取消限制时间")
    private String supportCancelTime;

    @ApiModelProperty(value = "酒店投标是否支持lra的报价: 1-是 0-否")
    private Integer supportLra;

    @ApiModelProperty(value = "酒店投标是否须支持报价中产品不适用日期数总和不能超过N天: 1-是 0-否")
    private Integer supportMaxNotApplicableDay;

    @ApiModelProperty(value = "酒店报价中产品不适用日期数总和不能超过N天")
    private Integer maxNotApplicableDay;

    @ApiModelProperty(value = "酒店是否支持房档最大数限制: 1-是 0-否")
    private Integer supportMaxRoomTypeCount;

    @ApiModelProperty(value = "酒店房档最大数限制")
    private Integer maxRoomTypeCount;

    @ApiModelProperty(value = "是否支持SeasonDay限制: 1-是 0-否")
    private Integer supportSeasonDayLimit;

    @ApiModelProperty(value = "最大 Season 日期数")
    private Integer maxSeasonDay;

    @ApiModelProperty(value = "是否必须提供无早单人价: 1-是 0-否")
    private Integer isRequireNoBreakfastSingle;

    @ApiModelProperty(value = "是否必须提供无早双人价: 1-是 0-否")
    private Integer isRequireNoBreakfastDouble;

    @ApiModelProperty(value = "是否必须提供含早单人价: 1-是 0-否")
    private Integer isRequireWithBreakfastSingle;

    @ApiModelProperty(value = "是否必须提供含早双人价: 1-是 0-否")
    private Integer isRequireWithBreakfastDouble;

    @ApiModelProperty(value = "是否建议提供无早单人价: 1-是 0-否")
    private Integer isRecommendNoBreakfastSingle;

    @ApiModelProperty(value = "是否建议提供无早双人价: 1-是 0-否")
    private Integer isRecommendNoBreakfastDouble;

    @ApiModelProperty(value = "是否建议提供含早单人价: 1-是 0-否")
    private Integer isRecommendWithBreakfastSingle;

    @ApiModelProperty(value = "是否建议提供含早双人价: 1-是 0-否")
    private Integer isRecommendWithBreakfastDouble;

    @ApiModelProperty(value = "酒店报价是否必须填写税费明细")
    private Integer isRequireTaxDetails;

    @ApiModelProperty(value = "酒店报价是否必须包含全部税费")
    private Integer isRequireIncludeAllTaxes;

    @ApiModelProperty(value = "酒店报价是否建议包含全部税费")
    private Integer isRecommendIncludeAllTaxes;

    @ApiModelProperty(hidden = true)
    private String creator;

    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(hidden = true)
    private String modifier;

    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

}
