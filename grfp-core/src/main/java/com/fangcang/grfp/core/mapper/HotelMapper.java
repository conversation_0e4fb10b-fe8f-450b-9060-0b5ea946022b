package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.HotelEntity;
import com.fangcang.grfp.core.enums.YesOrNoEnum;
import com.fangcang.grfp.core.vo.HotelNameVO;
import com.fangcang.grfp.core.vo.ListHotelDataVO;
import com.fangcang.grfp.core.vo.request.ListHotelDataRequest;
import com.fangcang.grfp.core.vo.response.HotelResponse;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 酒店 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface HotelMapper extends BaseMapper<HotelEntity> {

    /**
     * 根据查询有效的酒店 id
     */
    Set<Long> selectActiveHotelIds(@Param("hotelIds") Collection<Long> hotelIds);

    default List<HotelEntity> selectByHotelIdList(@Param("hotelIds") Collection<Long> hotelIds){
        return selectList(new LambdaQueryWrapper<HotelEntity>().in(HotelEntity::getHotelId, hotelIds));
    }

    /**
     * 根据酒店 id 更新酒店状态
     */
    default void updateActiveByHotelIds(Collection<Long> hotelIds, Integer active, String modifier) {
        LambdaUpdateWrapper<HotelEntity> updateWrapper = Wrappers.lambdaUpdate(HotelEntity.class)
            .set(HotelEntity::getIsActive, active)
            .set(HotelEntity::getModifier, modifier)
            .in(HotelEntity::getHotelId, hotelIds);
        update(null, updateWrapper);
    }

    default List<HotelEntity> selectHotelIdList(){
            LambdaQueryWrapper<HotelEntity>lambdaQueryWrapper = new LambdaQueryWrapper();
            lambdaQueryWrapper.select(HotelEntity::getHotelId);
            return selectList(lambdaQueryWrapper);
    }

    /**
     * 批量插入
     */
    int batchUpsert(@Param("hotelEntities") Collection<HotelEntity> hotelEntities);

    /**
     * 分页查询数据
     */
    IPage<ListHotelDataVO> listDataPage(IPage<ListHotelDataVO> page, @Param("languageId") Integer languageId, @Param("query") ListHotelDataRequest query);



    /**
     * Select local hotel  list  name like
     */
    List<HotelNameVO> selectHotelNameList(@Param("languageId") Integer languageId,
                                                  @Param("hotelName") String hotelName,
                                                  @Param("limitCount") Integer limitCount);

    List<HotelResponse> selectHotelInfoByIds(@Param("hotelIds") List<Long> hotelIds);


    List<HotelResponse> selectHotelInfoByCityAndRating(@Param("city") String city, @Param("rating") String rating);

    /**
     * 根据酒店 id 查询有效酒店
     */
    default List<HotelEntity> selectActiveHotelById(Collection<Long> hotelIds) {
        LambdaQueryWrapper<HotelEntity> queryWrapper = Wrappers.lambdaQuery(HotelEntity.class);
        queryWrapper.in(HotelEntity::getHotelId, hotelIds);
        queryWrapper.eq(HotelEntity::getIsActive, YesOrNoEnum.YES.getKey());
        return selectList(queryWrapper);
    }

    List<Long> queryNoDetailHotelIds();
}
