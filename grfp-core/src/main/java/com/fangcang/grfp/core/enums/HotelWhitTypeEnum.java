package com.fangcang.grfp.core.enums;

/**
 * 酒店项目白名单类型
 */
public enum HotelWhitTypeEnum {

    BID_NO_VALIDATE(1, "报价免控白名单"),
    HOLIDAY_NO_MONITOR(2, "周末节假日免履约监控白名单"),
    MONDAY_MONITOR(3, "仅每周一履约监控白名单");

    public int key;

    public String value;

    HotelWhitTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static HotelWhitTypeEnum getEnumByKey(Integer key) {
        if (key == null) {
            return null;
        }
        for (HotelWhitTypeEnum hotelWhitTypeEnum : HotelWhitTypeEnum.values()) {
            if (hotelWhitTypeEnum.key == key.intValue()) {
                return hotelWhitTypeEnum;
            }
        }
        return null;
    }
}
