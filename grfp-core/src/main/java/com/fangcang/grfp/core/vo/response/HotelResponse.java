package com.fangcang.grfp.core.vo.response;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class HotelResponse implements java.io.Serializable{

    /** 酒店ID **/
    private Long hotelId;

    /** 酒店名称 **/
    private String hotelName;

    /** 酒店星级 **/
    private String hotelStar;

    private String hotelNameZhCn;

    private String hotelNameEnUs;

    /** 酒店星级 **/
    private String hotelStarName;

    /** 国家 **/
    private String country;


    /** 国家 **/
    private String countryName;


    /** 省份 **/
    private String province;

    /** 省份 **/
    private String provinceName;

    /** 城市 **/
    private String city;

    /** 城市名称 **/
    private String cityName;

    /** 酒店地址 **/
    private String chnAddress;

    /** 百度经度  **/
    private BigDecimal lngBaidu;

    /** 百度维度 **/
    private BigDecimal latBaidu;

    /**  经度 **/
    private BigDecimal longitude;

    /**  维度 **/
    private BigDecimal latitude;

    /** 推荐亮点*/
    private String brightSpot;

    /** OTA 评分**/
    private String ratting;

    /**
     * 酒店品牌
     */
    private String hotelBrand;

    /**
     * 就带你集团
     */
    private String hotelGroup;

    // 房间数
    private String roomCount;

    // 装修时间
    private Date fitmentDate;

    // 开业日期
    private Date praciceDate;

    // 酒店联系电话
    private String telephone;

    // 是否有效
    private Integer isActive;

    /**
     * 酒店中文地址
     */
    private String addressZhCn;

    /**
     * 酒店英文地址
     */
    private String addressEnUs;

    private BigDecimal lngGoogle;

    private BigDecimal latGoogle;
}
