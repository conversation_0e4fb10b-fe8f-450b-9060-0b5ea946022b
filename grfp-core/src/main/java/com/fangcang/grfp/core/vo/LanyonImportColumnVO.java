package com.fangcang.grfp.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Param
 * @return
 */
@ApiModel("Lanyon导入列")
@Getter
@Setter
public class LanyonImportColumnVO {

    /**
     * COLUMN_INDEX 列序号
     */
    @ApiModelProperty("列序号")
    private Integer columnIndex;

    /**
     * DATA_TYPE 数据类型
     */
    @ApiModelProperty("数据类型")
    private String dataType;

    /**
     * DATA_TYPE_CODE 数据类型编号
     */
    @ApiModelProperty("数据类型编号")
    private String dataTypeCode;



    /**
     * DATA_TYPE 数据分类
     */
    @ApiModelProperty("数据分类")
    private String dataCategory;

    /**
     * COLUMN_CODE 列编号
     */
    @ApiModelProperty("列编号")
    private String columnCode;

    /**
     * COLUMN_NAME 列名称
     */
    @ApiModelProperty("列名称")
    private String columnName;

    /**
     * DISPLAY_ORDER 排序
     */
    @ApiModelProperty("排序")
    private Integer displayOrder;


}
