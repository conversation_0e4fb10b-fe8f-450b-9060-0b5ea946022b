package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 酒店关联机构联系方式
 */
@Getter
@Setter
public class HotelRelatedOrgContactInfoVO {

    /**
     * 酒单ID
     */
    private Long hotelId;

    /**
     * 机构ID
     */
    private Integer orgId;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 联系人电话区号
     */
    private String contactMobileAreaCode;

    /**
     * 联系人电话
     */
    private String contactMobile;

    /**
     * 联系人电邮
     */
    private String contactEmail;


}
