package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@ApiModel(description = "导入报价任务列表响应")
public class ImportBidTaskListVO extends BaseVO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "项目ID")
    private Integer projectId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目管理机构 ID", hidden = true)
    private Integer tenderOrgId;

    @ApiModelProperty(value = "项目机构名称")
    private String projectOrgName;

    @ApiModelProperty(value = "城市 code")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "导入集团内部酒店编码")
    private String propCode;

    @ApiModelProperty(value = "导入酒店名称")
    private String propName;

    @ApiModelProperty(value = "导入酒店电话")
    private String propPhone;

    @ApiModelProperty(value = "导入酒店地址")
    private String propAdd;

    @ApiModelProperty(value = "房仓酒店 ID")
    private Long hotelId;

    @ApiModelProperty(value = "房仓酒店名称")
    private String hotelName;

    @ApiModelProperty(value = "房仓酒店地址")
    private String address;

    @ApiModelProperty(value = "酒店地址英文")
    private String addressEnUs;

    @ApiModelProperty(value = "酒店地址中文")
    private String addressZhCn;

    @ApiModelProperty(value = "酒店联系人电话")
    private String telephone;

    @ApiModelProperty(value = "是否校验错误: 1-是 0-否")
    private Integer isValidateError;

    @ApiModelProperty(value = "异常内容")
    private String validateErrorDetail;

    @ApiModelProperty(value = "报价生成状态: 0-待生成 1-已生成")
    private Integer generateBidStatus;

    @ApiModelProperty(value = "更新人")
    private String modifier;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

}
