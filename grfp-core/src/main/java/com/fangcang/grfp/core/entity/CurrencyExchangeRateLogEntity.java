package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 币种汇率日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_currency_exchange_rate_log")
public class CurrencyExchangeRateLogEntity extends BaseVO {


    /**
     * 币种汇率日志ID
     */
    @TableId(value = "currency_exchange_rate_log_id", type = IdType.AUTO)
    private Long currencyExchangeRateLogId;

    /**
     * 币种编码
     */
    @TableField("currency_code")
    private String currencyCode;

    /**
     * 修改前值
     */
    @TableField("before_kv_json")
    private String beforeKvJson;

    /**
     * 修改后值
     */
    @TableField("after_kv_json")
    private String afterKvJson;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;


}
