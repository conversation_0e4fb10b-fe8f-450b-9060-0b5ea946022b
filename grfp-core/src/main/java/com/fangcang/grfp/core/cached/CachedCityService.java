package com.fangcang.grfp.core.cached;

import com.fangcang.grfp.core.entity.CityEntity;

import java.util.Map;

public interface CachedCityService {
	
	// ---------------------------------------------------------------------------------------------------- Public Methods
	
	public CityEntity getByCityCode(String cityCode);

	Map<String, String> getNameMap(int languageId);

	void clearNameMap(int languageId);



}
