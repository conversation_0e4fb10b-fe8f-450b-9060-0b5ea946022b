package com.fangcang.grfp.core.vo.request.project;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(description = "查询项目酒店白名单详情请求")
public class QueryProjectHotelWhiteRequest extends PageQuery {

    private static final long serialVersionUID = 8807116919274177507L;

    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "酒店id")
    private Long hotelId;

    @ApiModelProperty(value = "酒店名称")
    private String hotelName;

    @ApiModelProperty(value = "白名单类型: 1-报价免控白名单 2-周末节假日免履约监控白名单 3-仅每周一履约监控白名单")
    private Integer hotelWhiteType;
}
