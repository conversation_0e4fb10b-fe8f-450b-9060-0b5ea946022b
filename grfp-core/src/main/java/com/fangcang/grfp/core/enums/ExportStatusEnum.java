package com.fangcang.grfp.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExportStatusEnum {

    /**
     * 导出中
     */
    EXPORTING("1", "EXPORTING"),

    /**
     * 导出完成
     */
    EXPORTED("2", "EXPORTED"),
    /**
     * 导出失败
     */
    EXPORT_FAILED("3", "EXPORT_FAILED"),

    ;

    /**
     * 导出状态 code
     */
    private final String code;

    /**
     * 导出状态 desc
     */
    private final String desc;

}
