package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel("酒店信息VO")
@Getter
@Setter
public class HotelInfoVO extends BaseVO {

    @ApiModelProperty("酒店ID")
    private Long hotelId;

    @ApiModelProperty("酒店名称")
    private String hotelName;

    @ApiModelProperty("酒店星级")
    private String hotelStar;

    @ApiModelProperty("酒店星级名称")
    private String hotelStarName;

    @ApiModelProperty("国家编号")
    private String countryCode;

    @ApiModelProperty("国家名称")
    private String countryName;

    @ApiModelProperty("省份编号")
    private String provinceCode;

    @ApiModelProperty("省份名称")
    private String provinceName;

    @ApiModelProperty("城市编号")
    private String cityCode;

    @ApiModelProperty("币种")
    private String currencyCode;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("酒店地址")
    private String hotelAddress;

    @ApiModelProperty("google经度")
    private BigDecimal lngGoogle;

    @ApiModelProperty("google纬度")
    private BigDecimal latGoogle;

    @ApiModelProperty("评分")
    private String rating;

    @ApiModelProperty("酒店品牌ID")
    private Long hotelBrandId;

    @ApiModelProperty("酒店品牌名称")
    private String hotelBrandName;

    @ApiModelProperty("酒店集团ID")
    private Long hotelGroupId;

    @ApiModelProperty("房价数量")
    private Integer roomCount;

    @ApiModelProperty("开业时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd")
    private Date openingDate;

    @ApiModelProperty("装修")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd")
    private Date fitmentDate;

    @ApiModelProperty("电话")
    private String telephone;

    @ApiModelProperty("是否可用")
    private Integer isActive;


}
