<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.AttachmentFileMapper">

    <select id="queryAttachment" parameterType="com.fangcang.grfp.core.vo.request.common.QueryAttachmentInfoRequest"
            resultType="com.fangcang.grfp.core.entity.AttachmentFileEntity">
        SELECT *
        FROM t_attachment_file
        WHERE IS_ACTIVE = 1
        <if test="fileId != null">
            and FILE_ID = #{fileId}
        </if>
        <if test="externalId != null">
            and EXTERNAL_ID = #{externalId}
        </if>
        <if test="businessType != null">
            and BUSINESS_TYPE = #{businessType}
        </if>
    </select>
</mapper>
