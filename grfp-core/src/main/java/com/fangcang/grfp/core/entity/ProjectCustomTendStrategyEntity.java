package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 项目自定义采购策略表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_custom_tend_strategy")
public class ProjectCustomTendStrategyEntity extends BaseVO {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 项目 ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * POI ID
     */
    @TableField("strategy_name")
    private String strategyName;

    /**
     * 策略类型: 1-是或否 2-文本
     */
    @TableField("strategy_type")
    private Integer strategyType;

    /**
     * 排序(小到大)
     */
    @TableField("display_order")
    private Integer displayOrder;

    /**
     * 是否支持策略: 1-是 0-否
     */
    @TableField("support_strategy_name")
    private Integer supportStrategyName;

    /**
     * 是否启用权重: 1-是 0-否
     */
    @TableField("wht_strategy_name_state")
    private Integer whtStrategyNameState;

    /**
     * 权重分值
     */
    @TableField("wht_strategy_name")
    private BigDecimal whtStrategyName;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
