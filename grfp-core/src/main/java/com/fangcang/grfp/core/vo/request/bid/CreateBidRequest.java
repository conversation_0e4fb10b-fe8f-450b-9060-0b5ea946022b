package com.fangcang.grfp.core.vo.request.bid;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.Range;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 创建报价请求
 */
@Getter
@Setter
public class CreateBidRequest {

    /**
     * 项目 ID
     */
    private Integer projectId;

    /**
     * FC 酒店 ID
     */
    private Long hotelId;

    /**
     * 项目酒店报价 ID
     */
    private Integer projectIntentHotelId;

    /**
     * 是否上传报价
     */
    private Integer isUpload;

    /**
     * 酒店销售联系人姓名(默认取推荐酒店表数据)
     */
    private String hotelSalesContactName;

    /**
     * 酒店销售联系人电话
     */
    private String hotelSalesContactMobile;

    /**
     * 酒店销售联系人电邮
     */
    private String hotelSalesContactEmail;

    /**
     * 酒店报价联系人
     */
    private String hotelBidContactName;

    /**
     * 酒店报价联系人手机号码
     */
    private String hotelBidContactMobile;

    /**
     * 酒店报价联系人电邮
     */
    private String hotelBidContactEmail;



    /**
     * 酒店集团报价联系人
     */
    private String hotelGroupBidContactName;

    /**
     * 酒店集团报价联系人手机号码
     */
    private String hotelGroupBidContactMobile;

    /**
     * 酒店集团报价联系人电邮
     */
    private String hotelGroupBidContactEmail;

    /**
     * 机构类型
     */
    private Integer bidOrgType;

    /**
     * 报价机构 id
     */
    private Integer bidOrgId;

    /**
     * 酒店机构 id
     */
    private Integer hotelOrgId;


    /**
     * 报价币种
     */
    private String currencyCode;

    /**
     * 报价开始时间
     */
    private Date bidStartTime;

    /**
     * 报价结束时间
     */
    private Date bidEndTime;

    /**
     * 是否包含早餐 1:是，2:否
     */
    private Integer isIncludeBreakfast;

    /**
     * season1 日期
     */
    private List<Range<Date>> season1Dates;

    /**
     * season2 日期
     */
    private List<Range<Date>> season2Dates;


    /**
     * 价格不可用日期
     */
    private List<Range<Date>> unapplicableDates;

    /**
     * 可用房型 Map, key: 房档, value: 房型 ID
     */
    private Map<Integer, List<CreateApplicableRoomRequest>> roomTypeMap;

    /**
     * 房档房间数量
     */
    private Map<Integer, CreatePriceLevelRequest> roomCountMap;

    /**
     * 房档价格组 Map
     */
    private Map<Integer, List<CreatePriceGroupRequest>> priceGroupMap;

    /**
     * 创建税费设置请求
     */
    private CreateTaxSettingRequest taxSetting;

    /**
     * 自定义报价策略列表
     */
    private List<CreateCustomBidStrategyRequest> customBidStrategies;

    /**
     * 项目酒店报价策略
     */
    private CreateBidStrategyRequest bidStrategy;

    // 导入来源 2 lanyon, 3 stand
    private Integer bidUploadSource;

}
