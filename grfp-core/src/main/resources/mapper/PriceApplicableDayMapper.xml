<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.PriceApplicableDayMapper">

    <select id="selectVOListByProjectIntentHotelId" resultType="com.fangcang.grfp.core.vo.BidApplicableDayVO">
        SELECT
            price_type AS priceType,
            start_date AS startDate,
            end_date AS endDate
        FROM
            t_price_applicable_day
        WHERE
            project_intent_hotel_id = #{projectIntentHotelId}
        ORDER BY price_applicable_day_id ASC
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO t_price_applicable_day (project_intent_hotel_id, project_id, hotel_id, price_type, start_date, end_date, creator) VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.projectIntentHotelId}, #{item.projectId}, #{item.hotelId},  #{item.priceType}, #{item.startDate}, #{item.endDate}, #{item.creator})
        </foreach>
    </insert>
</mapper>
