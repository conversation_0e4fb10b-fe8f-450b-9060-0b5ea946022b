package com.fangcang.grfp.api.controller.project.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("修改酒店报价人信息请求")
@Getter
@Setter
public class UpdateProjectIntentHotelBidContactInfoRequest extends BaseVO {

    @ApiModelProperty("项目酒店ID")
    @NotNull
    private Integer projectIntentHotelId;

    /**
     * 酒店报价联系人
     */
    @ApiModelProperty("酒店报价联系人")
    private String hotelBidContactName;

    /**
     * 酒店报价联系人手机号码
     */
    @ApiModelProperty("酒店报价联系人手机号码")
    private String hotelBidContactMobile;

    /**
     * 酒店报价联系人电邮
     */
    @ApiModelProperty("酒店报价联系人电邮")
    private String hotelBidContactEmail;
}
