package com.fangcang.grfp.core.enums;

import com.fangcang.grfp.core.util.GenericAppUtility;

/**
 * 酒店报价通知状态枚举
 */
public enum HotelBidNotifyStatusEnum {

    PENDING_NOTIFY(0, "待通知", "Pending Notify"),
    NOTIFIED(1, "已通知", "Notified");

    public Integer key;
    public String value;
    public String valueEn;

    HotelBidNotifyStatusEnum(Integer key, String value, String valueEn) {
        this.key = key;
        this.value = value;
        this.valueEn = valueEn;
    }

    public static String getTextByKey(Integer key, int languageId) {
        String value = null;
        for (HotelBidNotifyStatusEnum item : HotelBidNotifyStatusEnum.values()) {
            if (item.key.equals(key)) {
                value = GenericAppUtility.getText(languageId, "NOTIFY_STATUS_" + item.key);
                break;
            }
        }
        return value;
    }

    public static HotelBidNotifyStatusEnum getEnumByKey(Integer key) {
        for (HotelBidNotifyStatusEnum item : HotelBidNotifyStatusEnum.values()) {
            if (item.key.equals(key)) {
                return item;
            }
        }
        return null;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (HotelBidNotifyStatusEnum item : HotelBidNotifyStatusEnum.values()) {
            if (item.key.equals(key)) {
                value = item.value;
                break;
            }
        }
        return value;
    }
} 