package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 机构 POI
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_org_poi")
public class OrgPoiEntity extends BaseVO {


    /**
     * POI ID
     */
    @TableId(value = "poi_id", type = IdType.AUTO)
    private Long poiId;

    /**
     * POI 名称
     */
    @TableField("poi_name")
    private String poiName;

    /**
     * POI 地址
     */
    @TableField("poi_address")
    private String poiAddress;

    /**
     * 机构ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 省份编码
     */
    @TableField("province_code")
    private String provinceCode;

    /**
     * 国家编码
     */
    @TableField("country_code")
    private String countryCode;

    /**
     * 地图 POI ID
     */
    @TableField("map_poi_id")
    private String mapPoiId;

    /**
     * Google 经度
     */
    @TableField("lng_google")
    private BigDecimal lngGoogle;

    /**
     * Google 纬度
     */
    @TableField("lat_google")
    private BigDecimal latGoogle;

    /**
     * 状态(1：有效，0：无效)
     */
    @TableField("state")
    private Integer state;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
