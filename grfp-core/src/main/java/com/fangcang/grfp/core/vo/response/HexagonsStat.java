package com.fangcang.grfp.core.vo.response;

import com.fangcang.grfp.core.util.JsonUtil;

import java.util.List;

public class HexagonsStat {

    // 六边形经纬度坐标
    private List<String[]> lanlatList;

    // 统计值
    private int stat;

    public List<String[]> getLanlatList() {
        return lanlatList;
    }

    public void setLanlatList(List<String[]> lanlatList) {
        this.lanlatList = lanlatList;
    }

    public int getStat() {
        return stat;
    }

    public void setStat(int stat) {
        this.stat = stat;
    }

    @Override
    public String toString() {
        return JsonUtil.objectToJson(this);

    }
}
