package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 酒店集团默认报价自定义策略
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Getter
@Setter
@ApiModel("酒店集团模板其他承诺")
public class BidCustomStrategyVO extends BaseVO {

    /**
     * 自定义策略ID
     */
    @ApiModelProperty("自定义策略ID")
    private Integer customTendStrategyId;

    /**
     * 项目意向酒店集团ID
     */
    @ApiModelProperty("项目意向酒店集团ID")
    private Integer projectIntentHotelGroupId;

    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    private Integer projectId;

    /**
     * 策略类型: 1-是或否 2-文本
     */
    @ApiModelProperty("策略类型: 1-是或否 2-文本 3-选项")
    private Integer strategyType;

    /**
     * 策略名称
     */
    @ApiModelProperty("策略名称")
    private String strategyName;

    /**
     * 是否支持策略: 1-是 0-否
     */
    @ApiModelProperty("是否支持策略: 1-是 0-否")
    private Integer supportStrategyName;

    /**
     * 策略回复文本
     */
    @ApiModelProperty("策略回复文本")
    private String supportStrategyText;

    @ApiModelProperty("策略选项, 策略类型为回复选项时有值")
    private List<CustomStrategyBidOption> options;

    @Getter
    @Setter
    @ApiModel("报价策略选项")
    public static class CustomStrategyBidOption extends BaseVO {

        @ApiModelProperty(value = "选项 id")
        private Long optionId;

        @ApiModelProperty(value = "选项名称")
        private String optionName;

        @ApiModelProperty(value = "是否支持: 1-支持 0-不支持")
        private Integer isSupport = 0;


    }

}
