package com.fangcang.grfp.core.mapper;

import com.fangcang.grfp.core.entity.AttachmentFileEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.vo.request.common.QueryAttachmentInfoRequest;

import java.util.List;

/**
 * <p>
 * 文件附件 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface AttachmentFileMapper extends BaseMapper<AttachmentFileEntity> {

    List<AttachmentFileEntity> queryAttachment(QueryAttachmentInfoRequest queryAttachmentInfoRequest);
}
