package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 项目酒店价格档次
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_hotel_price_level")
public class ProjectHotelPriceLevelEntity extends BaseVO {


    /**
     * 项目酒店价格档次ID
     */
    @TableId(value = "hotel_price_level_id", type = IdType.AUTO)
    private Integer hotelPriceLevelId;

    /**
     * 项目酒店意向ID
     */
    @TableField("project_intent_hotel_id")
    private Integer projectIntentHotelId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 房档编号 1,2,3,4
     */
    @TableField("room_level_no")
    private Integer roomLevelNo;

    /**
     * 大床房数量
     */
    @TableField("big_bed_room_count")
    private Integer bigBedRoomCount;

    /**
     * 双床房数量
     */
    @TableField("double_bed_room_count")
    private Integer doubleBedRoomCount;

    /**
     * 总房数量
     */
    @TableField("total_room_count")
    private Integer totalRoomCount;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * Lanyon 房档描述
     */
    @TableField("lanyon_room_desc")
    private String lanyonRoomDesc;

}
