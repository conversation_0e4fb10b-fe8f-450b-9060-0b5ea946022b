package com.fangcang.grfp.core.oss;

import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import com.volcengine.tos.comm.HttpMethod;
import com.volcengine.tos.model.object.*;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

public class TosClient implements IOssClient {

    private final TOSV2 tos;

    public TosClient(String endpoint, String region, String accessKey, String secretKey) {
        tos = new TOSV2ClientBuilder().build(region, endpoint, accessKey, secretKey);
    }

    @Override
    public void putObject(String bucket, String key, byte[] object, String contentType, String fileName) {
        // 统一封装成 ByteArrayInputStream
        ByteArrayInputStream stream = new ByteArrayInputStream(object);
        PutObjectInput putObjectInput = new PutObjectInput().setBucket(bucket).setKey(key).setContent(stream);
        tos.putObject(putObjectInput);
    }

    @Override
    public void putObject(String bucket, String key, InputStream inputStream, String contentType, String fileName) {
        PutObjectInput putObjectInput = new PutObjectInput().setBucket(bucket).setKey(key).setContent(inputStream);
        tos.putObject(putObjectInput);
    }

    @Override
    public InputStream getObject(String bucket, String key) {
        GetObjectV2Input input = new GetObjectV2Input().setBucket(bucket).setKey(key);
        return tos.getObject(input).getContent();
    }

    @Override
    public void copyObject(String sourceBucket, String sourceKey, String destinationBucket, String destinationKey) {
        CopyObjectV2Input input = new CopyObjectV2Input().setBucket(destinationBucket).setKey(destinationKey).setSrcBucket(sourceBucket).setSrcKey(sourceKey);
        tos.copyObject(input);
    }

    @Override
    public String generatePresignedUrl(String bucket, String key, int expirationInMinutes) {
        long expires = expirationInMinutes * 60L;
        PreSignedURLInput input = new PreSignedURLInput().setBucket(bucket).setKey(key).setHttpMethod(HttpMethod.GET).setExpires(expires);
        PreSignedURLOutput output = tos.preSignedURL(input);
        return output.getSignedUrl();
    }
}
