package com.fangcang.grfp.core.vo.response.project;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@ApiModel(description = "查询酒店项目招标权重配置响应")
public class ProjectHotelTendWeightVO extends BaseVO {

    private static final long serialVersionUID = -409058033322167531L;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "项目 id")
    private Integer projectId;

    @ApiModelProperty(value = "间夜量权重（不少于120间夜）")
    private BigDecimal whtRoomNight;

    @ApiModelProperty(value = "是否启用间夜量权重")
    private Integer whtRoomNightState;

    @ApiModelProperty(value = "间夜量(额外加分项)权重（不少于600间夜）")
    private BigDecimal whtRoomNightEx;

    @ApiModelProperty(value = "是否启用间夜量(额外加分项)权重")
    private Integer whtRoomNightExState;

    @ApiModelProperty(value = "城市权重")
    private BigDecimal whtCity;

    @ApiModelProperty(value = "是否启用城市权重")
    private Integer whtCityState;

    @ApiModelProperty(value = "位置权重")
    private BigDecimal whtLocation;

    @ApiModelProperty(value = "是否启用位置权重")
    private Integer whtLocationState;

    @ApiModelProperty(value = "价格优势权重（低于15%）")
    private BigDecimal whtPriceAdvantage;

    @ApiModelProperty(value = "是否启用价格优势权重")
    private Integer whtPriceAdvantageState;

    @ApiModelProperty(value = "价格优势(额外加分项)权重（低于25%）")
    private BigDecimal whtPriceAdvantageEx;

    @ApiModelProperty(value = "是否启用价格优势(额外加分项)权重")
    private Integer whtPriceAdvantageExState;

    @ApiModelProperty(value = "ota评分权重")
    private BigDecimal whtOtaScore;

    @ApiModelProperty(value = "是否启用ota评分权重")
    private Integer whtOtaScoreState;

    @ApiModelProperty(value = "公司统一支付权重")
    private BigDecimal whtCoPay;

    @ApiModelProperty(value = "是否启用公司统一支付权重")
    private Integer whtCoPayState;

    @ApiModelProperty(value = "早餐权重")
    private BigDecimal whtBreakfast;

    @ApiModelProperty(value = "是否启用早餐权重")
    private Integer whtBreakfastState;

    @ApiModelProperty(value = "lra权重")
    private BigDecimal whtLra;

    @ApiModelProperty(value = "是否启用lra权重")
    private Integer whtLraState;

    @ApiModelProperty(value = "退改规则权重")
    private BigDecimal whtCancel;

    @ApiModelProperty(value = "是否启用退改规则权重（注意字段名拼写一致性）")
    private Integer whtCancelState;

    @ApiModelProperty(value = "总权重(满分)，各项启用权重总和")
    private BigDecimal whtTotalWeight;

}
