package com.fangcang.grfp.api.interceptor;

import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.usersession.Anon;
import com.fangcang.grfp.core.usersession.Logical;
import com.fangcang.grfp.core.usersession.RequiresPermissions;
import com.fangcang.grfp.core.usersession.UserSession;
import org.springframework.util.CollectionUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.Set;

/**
 * 权限校验拦截器
 */
public class PreAuthorizeInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        final Method method = ((HandlerMethod) handler).getMethod();

        // 添加了 @Anon 注解的,不需要鉴权
        Anon anon = method.getAnnotation(Anon.class);
        if (anon == null) {
            anon = method.getDeclaringClass().getAnnotation(Anon.class);
        }
        if (Objects.nonNull(anon)) {
            return true;
        }

        // 超管直接放过
        final UserSession userSession = UserSession.get();

        // 校验权限注解
        RequiresPermissions annotation = method.getAnnotation(RequiresPermissions.class);
        if (Objects.isNull(annotation)) {
            annotation = method.getDeclaringClass().getAnnotation(RequiresPermissions.class);
        }
        if (Objects.nonNull(annotation)) {
            // 获取接口配置的权限 code
            final String[] perms = annotation.value();
            // 获取当前登录用户的权限列表
            final Set<String> permissions = userSession.getPermissions();
            if (CollectionUtils.isEmpty(permissions)) {
                AppUtility.serviceError(ErrorCode.NO_PERMISSION_ACCESS);
            }
            // 按不同逻辑判断
            if (annotation.logical() == Logical.AND) {
                // 其中一个没有就返回没有权限
                for (String perm : perms) {
                    if (!permissions.contains(perm)) {
                        AppUtility.serviceError(ErrorCode.NO_PERMISSION_ACCESS);
                    }
                }
            } else {
                // 只要有一个就返回成功
                int countInclude = 0;
                for (String perm : perms) {
                    if (permissions.contains(perm)) {
                        countInclude++;
                    }
                }
                if (countInclude <= 0) {
                    AppUtility.serviceError(ErrorCode.NO_PERMISSION_ACCESS);
                }
            }
        }
        return true;
    }
}
