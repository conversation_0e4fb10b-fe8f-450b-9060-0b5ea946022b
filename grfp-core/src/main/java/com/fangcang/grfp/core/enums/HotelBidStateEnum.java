package com.fangcang.grfp.core.enums;

import com.fangcang.grfp.core.util.GenericAppUtility;

import java.util.Arrays;

/**
 * 酒店的标书状态
 *
 * <AUTHOR>
 * @date 2022/9/9 15:45
 */
public enum HotelBidStateEnum {

    NO_BID(0, "未报价", "No Bid"),

    NEW_BID(1, "新标", "New"),

    UNDER_NEGOTIATION(2, "议价中", "Under Negotiation"),

    BID_WINNING(3, "已中签", "Bid Winning"),

    REJECTED(4, "已否决", "Rejected"),

    WITHDRAW_THE_QUOTATION(5,"放弃报价", "Witharaw The Quotation"),

    UPDATED_BID(6,"修订报价", "Updated Bid"),

    REJECT_NEGOTIATION(7,"保持原价", "Reject Negotiation");

    public Integer bidState;

    public String value;

    public String valueEn;

    HotelBidStateEnum(Integer bidState, String value, String valueEn) {
        this.bidState = bidState;
        this.value = value;
        this.valueEn = valueEn;
    }

    public static HotelBidStateEnum getHotelBidStateEnum(Integer bidState) {
        for(HotelBidStateEnum hotelBidStateEnum : HotelBidStateEnum.values()) {
            if(hotelBidStateEnum.bidState.equals(bidState)) {
                return hotelBidStateEnum;
            }
        }
        return null;
    }

    public static String getTextByKey(Integer key, int languageId) {
        String value = null;
        for(HotelBidStateEnum hotelBidStateEnum : HotelBidStateEnum.values()) {
            if(hotelBidStateEnum.bidState.equals(key)) {
                value =  GenericAppUtility.getText(languageId, "BID_STATE_" + hotelBidStateEnum.bidState);
                break;
            }
        }
        return value;
    }

    public static HotelBidStateEnum getEnumByDesc(String valueEn) {
        return Arrays.stream(HotelBidStateEnum.values()).filter(e -> e.valueEn.equals(valueEn)).findFirst().orElse(null);
    }

    public static String getValueByName(int language, String name) {
        for(HotelBidStateEnum hotelBidStateEnum : HotelBidStateEnum.values()) {
            if(hotelBidStateEnum.name().equals(name)) {
                return GenericAppUtility.getText(language, "BID_STATE_" + hotelBidStateEnum.bidState);
            }
        }
        return null;
    }

}
