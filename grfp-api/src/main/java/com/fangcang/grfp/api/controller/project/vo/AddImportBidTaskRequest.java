package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(description = "添加导入报价任务请求")
public class AddImportBidTaskRequest extends BaseVO {

    @ApiModelProperty(value = "项目 ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "原始文件名称", required = true)
    @NotBlank
    private String fileName;

    @ApiModelProperty(value = "上传文件 Key", required = true)
    @NotBlank
    private String fileKey;

}
