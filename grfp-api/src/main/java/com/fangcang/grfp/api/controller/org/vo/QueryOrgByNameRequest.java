package com.fangcang.grfp.api.controller.org.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.util.List;

@ApiModel("查询机构名称查询机构")
@Getter
@Setter
public class QueryOrgByNameRequest extends BaseVO {

    @ApiModelProperty(value="机构名称", required = true)
    @NotBlank
    private String orgName;

    @ApiModelProperty(value="机构类型")
    private List<Integer> orgTypeIdList;

    @ApiModelProperty(value = "返回数量 默认20")
    private Integer limitCount = 20;
}
