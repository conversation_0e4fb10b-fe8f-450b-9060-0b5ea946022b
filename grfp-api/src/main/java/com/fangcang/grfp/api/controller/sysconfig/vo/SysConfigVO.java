package com.fangcang.grfp.api.controller.sysconfig.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("系统配置")
@Getter
@Setter
public class SysConfigVO extends BaseVO {

    @ApiModelProperty("系统配置ID")
    private Long sysConfigId;

    @ApiModelProperty("系统配置编号")
    private String sysConfigCode;

    @ApiModelProperty("系统配置值")
    private String sysConfigValue;

    @ApiModelProperty("备注")
    private String remark;
}
