package com.fangcang.grfp.core.config;

import lombok.extern.slf4j.Slf4j;
import net.sf.ehcache.CacheException;
import net.sf.ehcache.Ehcache;
import net.sf.ehcache.Element;
import net.sf.ehcache.event.CacheEventListener;
import net.sf.ehcache.event.CacheEventListenerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;


@Slf4j
public class EhCacheCacheEventListenerFactory extends CacheEventListenerFactory implements CacheEventListener {

	private static CacheEventListener cacheEventListener;
	
	// ---------------------------------------------------------------------------------------------------- Public Methods
	
	public EhCacheCacheEventListenerFactory() {
		cacheEventListener = cacheEventListener == null ? this : cacheEventListener;
	}

	// ---------------------------------------------------------------------------------------------------- Public Methods

	@Override
	public void notifyElementPut(Ehcache cache, Element element) throws CacheException {
		log.debug("notifyElementPut key: {}, value: {}", element.getObjectKey(), element.getObjectValue());
	}

	@Override
	public void notifyElementUpdated(Ehcache cache, Element element) throws CacheException {
		log.debug("notifyElementUpdated key: {}, value: {}", element.getObjectKey(), element.getObjectValue());
	}

	@Override
	public void notifyElementExpired(Ehcache cache, Element element) {
		log.debug("notifyElementExpired key: {}, value: {}", element.getObjectKey(), element.getObjectValue());
	}

	@Override
	public void notifyElementEvicted(Ehcache cache, Element element) {
		log.debug("notifyElementEvicted key: {}, value: {}", element.getObjectKey(), element.getObjectValue());
	}
	
	@Override
	public void notifyElementRemoved(Ehcache cache, Element element) throws CacheException {
		log.debug("notifyElementRemoved key: {}, value: {}", element.getObjectKey(), element.getObjectValue());
	}

	@Override
	public void notifyRemoveAll(Ehcache cache) {
		log.debug("notifyRemoveAll ");
	}
	

	@Override
	public void dispose() {
		log.debug("dispose ");
	}
	
	@Override
	public Object clone() throws CloneNotSupportedException{
		return super.clone();
	}
	
	// ----------------------------------------------------------------------------------------------------
	
	@Override
	public CacheEventListener createCacheEventListener(Properties properties) {
		return cacheEventListener;
	}
	
}
