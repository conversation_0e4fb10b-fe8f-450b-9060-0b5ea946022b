package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("地图代酒店报价请求")
@Getter
@Setter
public class GoToHotelBidRequest extends BaseVO {

    @ApiModelProperty("项目Id")
    private Integer projectId;

    @ApiModelProperty("酒店ID")
    private Long hotelId;
}
