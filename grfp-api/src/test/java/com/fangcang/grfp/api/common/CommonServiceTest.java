package com.fangcang.grfp.api.common;

import com.fangcang.grfp.api.BaseTest;
import com.fangcang.grfp.api.controller.common.service.CommonService;
import com.fangcang.grfp.api.controller.common.vo.SendSmsCodeRequest;
import com.fangcang.grfp.core.enums.SmsCodeTypeEnum;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CommonServiceTest extends BaseTest {

    @Autowired
    private CommonService commonService;


    @Test
    public void testSendSMSCode(){
        SendSmsCodeRequest sendSmsCodeRequest = new SendSmsCodeRequest();
        sendSmsCodeRequest.setEmail("<EMAIL>");
        sendSmsCodeRequest.setType(SmsCodeTypeEnum.LOGIN.key);
        commonService.sendSMSCode(sendSmsCodeRequest);
    }
}
