package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.PriceUnapplicableDayEntity;
import com.fangcang.grfp.core.vo.BidUnApplicableDayVO;

import java.util.List;

/**
 * <p>
 * 价格不可用日期 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface PriceUnapplicableDayMapper extends BaseMapper<PriceUnapplicableDayEntity> {

    default List<PriceUnapplicableDayEntity> selectByProjectIntentHotelId(int projectIntentHotelId){
        LambdaQueryWrapper<PriceUnapplicableDayEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PriceUnapplicableDayEntity::getProjectIntentHotelId, projectIntentHotelId);
        return selectList(lambdaQueryWrapper);
    }

    List<BidUnApplicableDayVO> selectVOListByProjectIntentHotelId(int projectIntentHotelId);

    int batchInsert(List<PriceUnapplicableDayEntity> priceUnapplicableDayEntities);

    /**
     * 根据项目 id 查询
     */
    default List<PriceUnapplicableDayEntity> selectByProjectId(Integer projectId) {
        LambdaQueryWrapper<PriceUnapplicableDayEntity> queryWrapper = Wrappers.lambdaQuery(PriceUnapplicableDayEntity.class);
        queryWrapper.eq(PriceUnapplicableDayEntity::getProjectId, projectId);
        return selectList(queryWrapper);
    }

    default void deleteByProjectIdAndHotelId(Integer projectId, Long hotelId) {
        LambdaQueryWrapper<PriceUnapplicableDayEntity> queryWrapper = Wrappers.lambdaQuery(PriceUnapplicableDayEntity.class);
        queryWrapper.eq(PriceUnapplicableDayEntity::getProjectId, projectId);
        queryWrapper.eq(PriceUnapplicableDayEntity::getHotelId, hotelId);
        delete(queryWrapper);
    }
}
