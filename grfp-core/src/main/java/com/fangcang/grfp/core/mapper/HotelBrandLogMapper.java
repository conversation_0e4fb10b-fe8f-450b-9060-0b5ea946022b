package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.HotelBrandLogEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 酒店品牌日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface HotelBrandLogMapper extends BaseMapper<HotelBrandLogEntity> {

    /**
     * 根据酒店集团ID查询每个集团 ID 最新的日志
     */
    List<HotelBrandLogEntity> selectLatestByBrandIds(@Param("hotelBrandIds") Collection<Long> hotelBrandIds);

    /**
     * batch insert
     */
    void batchInsert(@Param("list") List<HotelBrandLogEntity> list);

}
