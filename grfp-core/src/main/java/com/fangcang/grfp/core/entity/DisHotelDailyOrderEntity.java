package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 分销商酒店每日订单统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_dis_hotel_daily_order")
public class DisHotelDailyOrderEntity extends BaseVO {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分销商编码
     */
    @TableField("distributor_code")
    private String distributorCode;

    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 预订日期
     */
    @TableField("book_date")
    private Date bookDate;

    /**
     * 订单数量
     */
    @TableField("book_count")
    private Integer bookCount;

    /**
     * 间夜数量
     */
    @TableField("room_night_count")
    private Integer roomNightCount;

    /**
     * 订单金额
     */
    @TableField("order_amount")
    private BigDecimal orderAmount;

    /**
     * 节省金额
     */
    @TableField("saved_amount")
    private BigDecimal savedAmount;

    /**
     * 酒店集团
     */
    @TableField("hotel_group")
    private Long hotelGroup;

    /**
     * 有节省对比数据的订单间夜数
     */
    @TableField("saved_room_night_count")
    private Integer savedRoomNightCount;

    /**
     * 有OTA价格的总预订金额
     */
    @TableField("has_ota_price_order_amount")
    private BigDecimal hasOtaPriceOrderAmount;

    /**
     * OTA的总价格
     */
    @TableField("total_ota_price")
    private BigDecimal totalOtaPrice;

    /**
     * 有节省金额的订单数量
     */
    @TableField("saved_amount_order_count")
    private Integer savedAmountOrderCount;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
