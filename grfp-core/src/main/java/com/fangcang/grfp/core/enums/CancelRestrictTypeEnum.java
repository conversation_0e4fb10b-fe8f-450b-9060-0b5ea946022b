package com.fangcang.grfp.core.enums;

/**
 * 退款政策：0-免费退改，2-预订后规定时间前可取消或收取全额费用 3 收取首晚房费的退订费
 * @auther chenjianhua
 * @date 2022/11/3
*/
public enum CancelRestrictTypeEnum {

    FREE(0,"免费退改"),PAY(2,"预订后规定时间前可取消或收取全额费用"), PAY_FIRST_NIGHT(3,"预订后规定时间前可取消或之后收取首晚房费的退订费");

    public int key;
    public String value;


    private CancelRestrictTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static int getKeyByValue(String value) {
        int key = 0;
        for (CancelRestrictTypeEnum assureStateEnum : CancelRestrictTypeEnum.values()) {
            if (assureStateEnum.value.equals(value)) {
                key = assureStateEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        for (CancelRestrictTypeEnum assureStateEnum : CancelRestrictTypeEnum.values()) {
            if (assureStateEnum.key == key) {
                value = assureStateEnum.value;
                break;
            }
        }
        return value;
    }

    public static CancelRestrictTypeEnum getEnumByKey(int key) {
        CancelRestrictTypeEnum assureStateEnum = null;
        for (CancelRestrictTypeEnum assureState : CancelRestrictTypeEnum.values()) {
            if (assureState.key == key) {
                assureStateEnum = assureState;
                break;
            }
        }
        return assureStateEnum;
    }

    public int getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

}
