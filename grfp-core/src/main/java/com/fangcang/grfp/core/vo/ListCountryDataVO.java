package com.fangcang.grfp.core.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.entity.CountryEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;

@ApiModel("国家列表信息")
@Getter
@Setter
public class ListCountryDataVO extends BaseVO {


    public ListCountryDataVO() {
    }

    public ListCountryDataVO(CountryEntity countryEntity) {
        BeanUtils.copyProperties(countryEntity, this);
    }

    /**
     * 国家ID
     */
    @ApiModelProperty("国家ID")
    private String countryId;

    /**
     * 国家编号
     */
    @ApiModelProperty("国家编号")
    private String countryCode;

    /**
     * 英文名称
     */
    @ApiModelProperty("英文名称")
    private String nameEnUs;

    /**
     * 英文名称简称
     */
    @ApiModelProperty("英文名称简称")
    private String shortNameEnUs;

    /**
     * 中文名称
     */
    @ApiModelProperty("中文名称")
    private String nameZhCn;

    /**
     * 中文简称名称
     */
    @ApiModelProperty("中文简称名称")
    private String shortNameZhCn;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改人")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

}
