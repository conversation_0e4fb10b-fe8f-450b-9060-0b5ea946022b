package com.fangcang.grfp.core.enums;

public enum StateEnum {


    Effective(1,"有效"),Invalid(0,"无效"),Auditing(2,"待审核"),PMS_Invalid(3,"强制关闭"),No_Pass(4,"不通过");

    public int key;
    public String value;

    //供应商权限状态注释
    // 0.未提交审核状态（未申请）
    // 1.已开通供应权限状态（已开通）
    // 2.已提交审核申请状态（待审核）
    // 3.已关闭供应权限状态（已关闭）
    // 4.审核不通过状态（不通过）

    private StateEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static int getKeyByValue(String value) {
        int key = 0;
        for(StateEnum assureStateEnum : StateEnum.values()) {
            if(assureStateEnum.value .equals(value)) {
                key = assureStateEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        for(StateEnum assureStateEnum : StateEnum.values()) {
            if(assureStateEnum.key == key) {
                value = assureStateEnum.value;
                break;
            }
        }
        return value;
    }

    public static StateEnum  getEnumByKey(int key){
        StateEnum assureStateEnum = null;
        for(StateEnum assureState : StateEnum.values()) {
            if(assureState.key == key) {
                assureStateEnum = assureState;
                break;
            }
        }
        return assureStateEnum;
    }

    public static String getStateTextCodeByKey(Integer key) {
        StateEnum stateEnum = getEnumByKey(key);
        return "STATE_" + stateEnum.key;
    }

    public int getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static boolean between(Integer type, StateEnum... types) {
        if (type == null) {
            return false;
        }

        if (types == null) {
            return false;
        }

        for (StateEnum t : types) {
            if (type.equals(t.key)) {
                return true;
            }
        }
        return false;
    }
}
