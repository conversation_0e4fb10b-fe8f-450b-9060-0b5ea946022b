package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.util.Set;

@Getter
@Setter
@ApiModel(description = "删除报价任务请求")
public class DeleteImportBidTaskRequest extends BaseVO {

    @ApiModelProperty(value = "任务 ID 列表", required = true)
    @NotEmpty
    private Set<Long> importBidTaskIds;

}
