package com.fangcang.grfp.core.vo.request.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("删除酒店集团下意向酒店请求")
public class DeleteHotelGroupInviteHotelRequest {

    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "酒店ID列表", required = true)
    @NotNull
    private List<Long> hotelIds;
}