package com.fangcang.grfp.core.base;

import com.fangcang.grfp.core.util.GenericAppUtility;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jodd.net.HttpStatus;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * Web interface comm
 * <AUTHOR>
 *
 */
@ApiModel("Web 接口通用响应")
public class Result<T> extends BaseVO {

	// ------------------------------------------------------------------------------Public static variables
	
	public static final String SUCCESS_CODE = "200";
	
	public static final String SUCCESS_MESSAGE = "SUCCEEDED";
	
	public static final String SYSTEM_ERROR_CODE = "500";
	
	public static final String SYSTEM_ERROR_MESSAGE = "SYSTEM_ERROR";


	public static final String VALIDATE_PARAMETER_ERROR_CODE = "400";

	public static final String VALIDATE_PARAMETER_ERROR_MESSAGE  = "VALIDATE_PARAMETER_ERROR";
	
	public static final String UNAUTHENTICATED_CODE = "401"; // Invalidate session
	
	public static final String UNAUTHENTICATED_MESSAGE = "INVALIDATE_SESSION";
	
	public static final String CONCURRENT_LOGIN_CODE = "409";
	
	public static final String CONCURRENT_LOGIN_MESSAGE = "CONCURRENT_LOGIN";
	
	public static final String UNAUTHORIZED_CODE = "403"; // no permisssion
	
	public static final String UNAUTHORIZED_MESSAGE = "NO_PERMISSION";
	
	//------------------------------------------------------------------------------------ Private Member Variables

	@ApiModelProperty("响应码, 200 成功, 500 服务异常，400 请求参数校验错误, 401 Session无效, 403 没有权限")
	private String code;

	@ApiModelProperty("响应结果描述")
	private String message;

	@ApiModelProperty("响应数据")
	private T data;

	@ApiModelProperty("请求是否成功")
	private boolean successful;
	
	//------------------------------------------------------------------------------------ Constructor
	
    public Result() {
    }

    public Result(String code, String message) {
        this.code = code;
        this.message = message;
        this.successful = (SUCCESS_CODE.equals(code));
    }

    public boolean isSuccessful() {
        return successful;
    }

	//------------------------------------------------------------------------------------ Public Static Method

	private static String getMsgText(String msg) {
		HttpServletRequest httpServletRequest = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
		int languageId = GenericAppUtility.getRequestHeaderLanguage(httpServletRequest);
		return GenericAppUtility.getText(languageId,msg);
	}

	public static <K> Result<K> ok() {
	     return new Result<>(SUCCESS_CODE, getMsgText(SUCCESS_MESSAGE));
	}

	public static <K> Result<K> ok(K data) {
		Result<K> response = new Result<>(SUCCESS_CODE, getMsgText(SUCCESS_MESSAGE));
		response.setData(data);
		return response;
	 }

	public static <K> Result<K> error() {
	    return new Result<>(SYSTEM_ERROR_CODE, getMsgText(SYSTEM_ERROR_MESSAGE));
	}

	public static <K> Result<K> error(String message) {
	    return new Result<>(SYSTEM_ERROR_CODE, message);
	}

	public static <K> Result<K> unauthenticated() {
	    return new Result<>(UNAUTHENTICATED_CODE, getMsgText(UNAUTHENTICATED_MESSAGE));
	}

	public static <K> Result<K> concurrentLogin() {
	    return new Result<>(CONCURRENT_LOGIN_CODE, CONCURRENT_LOGIN_MESSAGE);
	}

	public static <K> Result<K> unauthorized() {
	    return new Result<>(UNAUTHORIZED_CODE, getMsgText(UNAUTHORIZED_MESSAGE));
	}

	public static <K> Result<K> validateParameterError(String message) {return new Result<>(VALIDATE_PARAMETER_ERROR_CODE, message);}


	//------------------------------------------------------------------------------------ Getter/Setter
	
	public String getCode() {
	    return code;
	}

	public Result<T> setCode(String code) {
	    this.code = code;
	    this.successful = (SUCCESS_CODE.equals(code));
	    return this;
	}

	public String getMessage() {
	    return message;
	}

	public Result<T> setMessage(String message) {
	    this.message = message;
	    return this;
	}

	public T getData() {
	    return data;
	}

	public Result<T> setData(T data) {
	    this.data = data;
	    return this;
	}
}
