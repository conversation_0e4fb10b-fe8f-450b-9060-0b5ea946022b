package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@ApiModel(description = "查询导出状态响应")
public class QueryExportStatusResponse extends BaseVO {

    @ApiModelProperty(value = "导出状态: 1-导出中 2-导出完成 3-导出失败")
    private Integer status;

}
