package com.fangcang.grfp.core.cached.impl;

import com.fangcang.grfp.core.cached.CachedSysConfigService;
import com.fangcang.grfp.core.entity.SysConfigEntity;
import com.fangcang.grfp.core.mapper.SysConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CachedSysConfigServiceImpl implements CachedSysConfigService {

	// ---------------------------------------------------------------------------------------------------- Private Member Variables
	
	@Autowired
	private SysConfigMapper sysConfigMapper;
	
	// ---------------------------------------------------------------------------------------------------- Public Methods
	
	@Override
	@Cacheable(value="cachedSysConfigService.getValue", key = "#sysConfigCode", unless = "#result == null", cacheManager = "redisCacheManager")
	public String getValue(String sysConfigCode) {
		
		// Core logic
		String result;
		SysConfigEntity sysInfo = sysConfigMapper.getByCode(sysConfigCode);
		
		if (sysInfo == null) {
			result = null;
		} else {
			result = sysInfo.getSysConfigValue();
		}

		return result;
	}

	@Override
	@CacheEvict(value="cachedSysConfigService.getValue", key = "#sysConfigCode", cacheManager = "redisCacheManager")
	public void clearValue(String sysConfigCode) {
		log.info("clearValue {}", sysConfigCode);
	}
	
}
