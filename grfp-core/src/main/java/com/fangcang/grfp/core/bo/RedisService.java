package com.fangcang.grfp.core.bo;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RedisService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    // -------------------------------------------------------------

    /**
     * 发布消息
     */
    public void publishMessage(String topic, String message) {
        redisTemplate.convertAndSend(topic, message);
    }

    public String get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    public void set(String key, String value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public void setex(String key, String value, int seconds){
        redisTemplate.opsForValue().set(key, value, seconds, TimeUnit.SECONDS);
    }

    public Long delete(List<String> keys) {
        return redisTemplate.delete(keys);
    }

    public Boolean delete(String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 向 Set 添加值
     */
    public void sadd(String key, String value) {
        redisTemplate.opsForSet().add(key, value);
    }

    /**
     * 从 Set 中随机弹出一个元素
     */
    public String spop(String key) {
        return redisTemplate.opsForSet().pop(key);
    }

    /**
     * 获取 Set 中元素数量
     */
    public Long scard(String key) {
        return redisTemplate.opsForSet().size(key);
    }

}
