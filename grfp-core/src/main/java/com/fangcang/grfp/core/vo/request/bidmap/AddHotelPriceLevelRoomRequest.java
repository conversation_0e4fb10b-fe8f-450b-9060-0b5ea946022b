package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.BidApplicableRoomVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("新增或者修改房档房型")
@Getter
@Setter
public class AddHotelPriceLevelRoomRequest extends BaseVO {

    @ApiModelProperty("价格房档ID, 新增传ID，修改传null")
    @NotNull
    private Integer hotelPriceLevelId;

    @ApiModelProperty("大床房数量")
    private Integer bigBedRoomCount;

    @ApiModelProperty("双床房数量")
    private Integer doubleBedRoomCount;

    @ApiModelProperty("总房数量")
    private Integer totalRoomCount;

    @ApiModelProperty("房档房型列表")
    @NotNull
    private List<BidApplicableRoomVO> roomList;
}
