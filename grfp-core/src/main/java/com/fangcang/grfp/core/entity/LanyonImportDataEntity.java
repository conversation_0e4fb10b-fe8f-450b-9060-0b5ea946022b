package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.sql.Blob;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * Lanyon导入数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_lanyon_import_data")
public class LanyonImportDataEntity extends BaseVO {


    /**
     * 导入数据ID
     */
    @TableId(value = "lanyon_import_data_id", type = IdType.AUTO)
    private Integer lanyonImportDataId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 数据类型， 1:Lanyon导入数据，2:Lanyon导入数据（如果存在两条显示有早
     */
    @TableField("data_type")
    private Integer dataType;


    /**
     * 导入JSON数据
     */
    @TableField("json_data")
    private String jsonData;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
