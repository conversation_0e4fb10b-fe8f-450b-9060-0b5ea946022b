package com.fangcang.grfp.core.jackson;

import com.fangcang.grfp.core.oss.OssManager;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.web.context.support.SpringBeanAutowiringSupport;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * cos url 序列化注解. xxx.png -> https://dev-grfp-public-**********.cos.ap-guangzhou.myqcloud.com/xxx.png
 * 公开桶
 *
 */
public class OssPublicJsonSerializer extends JsonSerializer<String> {

    @Resource
    protected OssManager ossManager;

    public OssPublicJsonSerializer() {
        SpringBeanAutowiringSupport.processInjectionBasedOnCurrentContext(this);
    }

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }

        if (value.isEmpty() || value.startsWith("http") || value.startsWith("https")) {
            gen.writeString(value);
            return;
        }
        String publicUrl = ossManager.generateUrlPublic(value);
        gen.writeString(publicUrl);
    }


}
