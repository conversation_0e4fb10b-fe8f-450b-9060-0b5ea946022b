package com.fangcang.grfp.core.vo.project;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fangcang.grfp.core.base.ImportVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImportContractStatusVO extends ImportVO {

    @ExcelProperty(value = "HotelId")
    private Long hotelId;

    @ExcelProperty(value = "Contract Status")
    private String bidStateName;

    @ExcelProperty(value = "Remark")
    private String remark;
} 