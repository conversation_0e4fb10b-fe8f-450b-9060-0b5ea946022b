<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.SysConfigMapper">
    <select id="querySysConfigVOPageList" parameterType="com.fangcang.grfp.core.vo.request.ListSysConfigRequest"
            resultType="com.fangcang.grfp.core.vo.ListSysConfigVO">
        SELECT
            `sys_config_id` AS sysConfigId,
            `sys_config_code` AS sysConfigCode,
            `sys_config_value` AS sysConfigValue,
            `creator` AS creator,
            `create_time` AS createTime,
            `modifier` AS modifier,
            `modify_time` AS modifyTime
        FROM
        `t_sys_config`
        <where>
            <if test="query.sysConfigCode != null and query.sysConfigCode != ''">
                AND sys_config_code LIKE concat('%',#{query.sysConfigCode},'%')
            </if>
            <if test="query.sysConfigValue != null and query.sysConfigValue != ''">
                AND sys_config_value LIKE concat('%',#{query.sysConfigValue},'%')
            </if>
        </where>
    </select>
</mapper>
