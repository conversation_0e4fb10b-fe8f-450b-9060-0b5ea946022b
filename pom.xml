<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.10.RELEASE</version>
        <relativePath/>
    </parent>

    <groupId>com.fangcang</groupId>
    <artifactId>grfp-project</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>grfp-core</module>
        <module>grfp-api</module>
        <module>grfp-server</module>
    </modules>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <hutool.version>5.7.5</hutool.version>
        <pagehelper.version>4.1.2</pagehelper.version>
        <knife4j.version>3.0.2</knife4j.version>
        <mybatis.version>2.1.4</mybatis.version>
        <mysql.connector.version>8.0.33</mysql.connector.version>
        <mybatis-plus.version>3.4.3</mybatis-plus.version>
        <xxl-job.version>2.1.1</xxl-job.version>
        <easyexcel.version>3.3.4</easyexcel.version>
        <jjwt.version>0.9.1</jjwt.version>
        <hibernate-validator.version>6.1.7.Final</hibernate-validator.version>
        <commons.io.version>2.13.0</commons.io.version>
        <mybatis-plus-generator.version>3.4.1</mybatis-plus-generator.version>
        <velocity.version>2.3</velocity.version>
        <nexus.url>192.168.2.254</nexus.url>
        <lombok.version>1.18.24</lombok.version>
        <ehcache.version>2.10.6</ehcache.version>
        <com.baomidou.version>>3.4.1</com.baomidou.version>
        <gson.version>2.9.1</gson.version>
        <redisson.version>3.16.0</redisson.version>

    </properties>
    <dependencyManagement>
        <dependencies>
            <!-- hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-json</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-http</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- lombok-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!-- swagger / springfox / knife4j  -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- Java JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- Mybatis Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>

            <!-- XxJob -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <!--校验 -->
            <dependency>
                <artifactId>hibernate-validator</artifactId>
                <groupId>org.hibernate</groupId>
                <version>${hibernate-validator.version}</version>
            </dependency>

            <!-- Mybatis Plus Code Generator -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus-generator.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- Ehcache, optional -->
            <dependency>
                <groupId>net.sf.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>${ehcache.version}</version>
            </dependency>


            <!-- Gson -->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>

            <!-- swagger / springfox / knife4j  -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <profiles>
        <!--开发环境通用打包 profile -->
        <profile>
            <id>development</id>
            <properties>
                <package.env>dev</package.env>
            </properties>

            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>

            <repositories>
                <repository>
                    <id>sonatype</id>
                    <name>dev</name>
                    <url>http://${nexus.url}/content/repositories/dev/</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>3rdparties</id>
                    <name>3rdparties</name>
                    <url>http://${nexus.url}/content/repositories/3rdparties</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>Maven Central</id>
                    <name>Maven Central</name>
                    <layout>default</layout>
                    <url>http://${nexus.url}/content/repositories/central</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <distributionManagement>
                <repository>
                    <id>sonatype</id>
                    <name>dev</name>
                    <url>http://${nexus.url}/content/repositories/dev/</url>
                </repository>
            </distributionManagement>
        </profile>
        <profile>
            <id>test02</id>
            <properties>
                <package.env>test02</package.env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <repositories>
                <repository>
                    <id>sonatype</id>
                    <name>test02</name>
                    <url>http://${nexus.url}/content/repositories/test02/</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>3rdparties</id>
                    <name>3rdparties</name>
                    <url>http://${nexus.url}/content/repositories/3rdparties</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>Maven Central</id>
                    <name>Maven Central</name>
                    <layout>default</layout>
                    <url>http://${nexus.url}/content/repositories/central</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <distributionManagement>
                <repository>
                    <id>sonatype</id>
                    <name>test02</name>
                    <url>http://${nexus.url}/content/repositories/test02/</url>
                </repository>
            </distributionManagement>
        </profile>
        <!--生产环境通用打包 profile -->
        <profile>
            <id>production</id>
            <properties>
                <package.env>prod</package.env>
            </properties>
            <repositories>
                <repository>
                    <id>sonatype</id>
                    <name>libs-releases</name>
                    <url>http://${nexus.url}/content/repositories/releases/</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>3rdparties</id>
                    <name>3rdparties</name>
                    <url>http://${nexus.url}/content/repositories/3rdparties</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>Maven Central</id>
                    <name>Maven Central</name>
                    <layout>default</layout>
                    <url>http://${nexus.url}/content/repositories/central</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <distributionManagement>
                <repository>
                    <id>sonatype</id>
                    <name>libs-releases</name>
                    <url>http://${nexus.url}/content/repositories/releases/</url>
                </repository>
            </distributionManagement>
        </profile>
    </profiles>

</project>