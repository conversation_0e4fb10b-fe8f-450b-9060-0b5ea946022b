package com.fangcang.grfp.core.config;


import net.sf.ehcache.CacheManager;
import org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers;
import org.springframework.boot.autoconfigure.cache.CacheProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ResourceCondition;
import org.springframework.cache.ehcache.EhCacheCacheManager;
import org.springframework.cache.ehcache.EhCacheManagerUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;

/**
 * 
 * Ehcache cache autoConfiguration
 * Reference org.springframework.boot.autoconfigure.cache.EhCacheCacheConfiguration
 *  
 *  <AUTHOR>
 *
 * 2022-10-28
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty("spring.cache.ehcache.config")
public class EhCacheCacheAutoConfiguration {
	
	@Bean
	@Primary
    @ConditionalOnMissingBean
	EhCacheCacheManager ehCacheCacheManager(CacheManagerCustomizers customizers, CacheManager ehCacheManager) {
		EhCacheCacheManager ehCacheCacheManager = new EhCacheCacheManager(ehCacheManager);
		return customizers.customize(ehCacheCacheManager);
	}

	@Bean
	@ConditionalOnMissingBean
	CacheManager ehCacheManager(CacheProperties cacheProperties) {
		Resource location = cacheProperties.resolveConfigLocation(cacheProperties.getEhcache().getConfig());
		if (location != null) {
			return EhCacheManagerUtils.buildCacheManager(location);
		}
		CacheManager cacheManager = EhCacheManagerUtils.buildCacheManager();
		return cacheManager;
	}
	
	@Bean
	@ConditionalOnMissingBean
	EhCacheCacheEventListenerFactory ehCacheCacheEventListenerFactory() {
		return new EhCacheCacheEventListenerFactory();
	}
	/**
	 * Determine if the EhCache configuration is available. This either kick in if a
	 * default configuration has been found or if property referring to the file to use
	 * has been set.
	 */
	static class ConfigAvailableCondition extends ResourceCondition {

		ConfigAvailableCondition() {
			super("EhCache", "spring.cache.ehcache.config", "classpath:/ehcache.xml");
		}

	}

}
