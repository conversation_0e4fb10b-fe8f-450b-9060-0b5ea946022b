package com.fangcang.grfp.core.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 系统配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sys_config")
public class SysConfigEntity extends BaseVO {


    /**
     * 系统配置ID
     */
    @TableField("sys_config_id")
    private Integer sysConfigId;

    /**
     * 配置code
     */
    @TableId(value = "sys_config_code")
    private String sysConfigCode;

    /**
     * 配置Value
     */
    @TableField("sys_config_value")
    private String sysConfigValue;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
