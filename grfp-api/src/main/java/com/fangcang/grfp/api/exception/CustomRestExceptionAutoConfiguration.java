package com.fangcang.grfp.api.exception;

import com.fangcang.grfp.core.cached.CachedTextResourceService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(CachedTextResourceService.class)
public class CustomRestExceptionAutoConfiguration {

	// -------------------------------------------------------------------------------------------------- Public Method
	
	@Bean
	@ConditionalOnMissingBean
	public CustomRestExceptionHandler customRestExceptionHandler() {return new CustomRestExceptionHandler();}
}
