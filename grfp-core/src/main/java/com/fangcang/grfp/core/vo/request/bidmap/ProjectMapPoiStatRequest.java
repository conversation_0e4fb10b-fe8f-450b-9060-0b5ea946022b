package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @description POI排名统计
 */
@ApiModel("POI排名统计查询")
@Getter
@Setter
public class ProjectMapPoiStatRequest extends BaseVO {

    // 项目ID
    @ApiModelProperty("项目ID")
    private Integer projectId;

    // POI ID
    @ApiModelProperty("POI ID")
    private Integer poiId;

    // 距离
    @ApiModelProperty("距离")
    private int distance;

}
