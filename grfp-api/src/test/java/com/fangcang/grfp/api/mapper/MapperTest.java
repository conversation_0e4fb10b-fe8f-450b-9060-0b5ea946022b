package com.fangcang.grfp.api.mapper;

import com.fangcang.grfp.api.BaseTest;
import com.fangcang.grfp.core.manager.MailManager;
import com.fangcang.grfp.core.mapper.HGroupDefaultCusStrategyMapper;
import com.fangcang.grfp.core.vo.HGroupDefaultCusStrategyVO;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.mail.MessagingException;
import java.util.ArrayList;
import java.util.List;

public class MapperTest extends BaseTest {

    @Autowired
    private HGroupDefaultCusStrategyMapper hGroupDefaultCusStrategyMapper;

    @Test
    void testBatchMergeHotelGroupDefaultCustomStrategy() throws MessagingException {
        List<HGroupDefaultCusStrategyVO> hGroupDefaultCusStrategyList = new ArrayList<>();
        HGroupDefaultCusStrategyVO hGroupDefaultCusStrategyVO = new HGroupDefaultCusStrategyVO();
        hGroupDefaultCusStrategyVO.setProjectIntentHotelGroupId(1);
        hGroupDefaultCusStrategyVO.setProjectId(2);
        hGroupDefaultCusStrategyVO.setCustomTendStrategyId(100);
        hGroupDefaultCusStrategyVO.setStrategyName("测试1003");
        hGroupDefaultCusStrategyVO.setSupportStrategyName(1);
        hGroupDefaultCusStrategyVO.setStrategyType(1);
        hGroupDefaultCusStrategyVO.setCreator("gang");
        hGroupDefaultCusStrategyVO.setModifier("gang");
        hGroupDefaultCusStrategyList.add(hGroupDefaultCusStrategyVO);
      HGroupDefaultCusStrategyVO hGroupDefaultCusStrategyVO1 = new HGroupDefaultCusStrategyVO();
        hGroupDefaultCusStrategyVO1.setProjectIntentHotelGroupId(1);
        hGroupDefaultCusStrategyVO1.setProjectId(2);
        hGroupDefaultCusStrategyVO1.setCustomTendStrategyId(101);
        hGroupDefaultCusStrategyVO1.setStrategyName("测试1014");
        hGroupDefaultCusStrategyVO1.setSupportStrategyName(1);
        hGroupDefaultCusStrategyVO1.setStrategyType(1);
        hGroupDefaultCusStrategyVO1.setCreator("gang");
        hGroupDefaultCusStrategyVO1.setModifier("gang");
        hGroupDefaultCusStrategyList.add(hGroupDefaultCusStrategyVO1);
        hGroupDefaultCusStrategyMapper.batchMergeHotelGroupDefaultCustomStrategy(hGroupDefaultCusStrategyList);
    }
}
