package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("酒店集团信息")
@Getter
@Setter
public class HotelGroupVO extends BaseVO {

    @ApiModelProperty(value = "酒店集团ID")
    private Long hotelGroupId;

    @ApiModelProperty(value = "酒店集团名称")
    private String name;

    @ApiModelProperty(value = "英文名称")
    @JsonIgnore
    private String nameEnUs;

    @ApiModelProperty(value = "中文名称")
    @JsonIgnore
    private String nameZhCn;
}
