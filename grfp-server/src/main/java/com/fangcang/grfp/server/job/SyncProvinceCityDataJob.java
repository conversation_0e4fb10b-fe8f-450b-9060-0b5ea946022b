package com.fangcang.grfp.server.job;

import cn.hutool.core.collection.CollUtil;
import com.fangcang.grfp.core.constant.SysConfig;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.QueryCityListRequest;
import com.fangcang.grfp.core.dto.dhub.response.Response;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.City;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.Province;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.QueryCityListResponse;
import com.fangcang.grfp.core.entity.CityEntity;
import com.fangcang.grfp.core.entity.CountryEntity;
import com.fangcang.grfp.core.entity.ProvinceEntity;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.mapper.CityMapper;
import com.fangcang.grfp.core.mapper.CountryMapper;
import com.fangcang.grfp.core.mapper.ProvinceMapper;
import com.fangcang.grfp.core.remote.tmchub.manager.TmcHubApiManager;
import com.fangcang.grfp.core.remote.tmchub.request.SignatureHelpRequestDto;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.server.util.AppUtility;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;


@Component
@Slf4j
@JobHandler(value = SyncProvinceCityDataJob.TASK_NAME)
public class SyncProvinceCityDataJob extends IJobHandler {

    public static final String TASK_NAME = "SyncProvinceCityDataJob";

    @Resource
    private TmcHubApiManager tmcHubApiManager;

    @Resource
    private CountryMapper countryMapper;

    @Resource
    private ProvinceMapper provinceMapper;

    @Resource
    private CityMapper cityMapper;

    /**
     * Synchronize province and city data to the database
     */
    @Override
    public ReturnT<String> execute(String param) {
        log.info("Sync province city data job start");
        try {
            // Query all country data
            List<CountryEntity> countryEntityList = countryMapper.selectList(null);

            // Synchronize province data
            countryEntityList.forEach(country -> {
                // Cache province and city data
                Map<String, ProvinceEntity> provinceIdProvinceMap = new HashMap<>();
                Map<String, CityEntity> cityIdCityMap = new HashMap<>();

                Stream.of(LanguageEnum.values()).forEach(language -> {
                    // Query province city data
                    QueryCityListRequest queryCityListRequest = QueryCityListRequest.builder()
                        .language(language.value)
                        .countryCode(country.getCountryCode())
                        .build();
                    Response<QueryCityListResponse> response = tmcHubApiManager.queryCityList(queryCityListRequest, signatureParam());
                    if (Objects.isNull(response) || Objects.isNull(response.getBussinessResponse()) || CollUtil.isEmpty(response.getBussinessResponse().getProvinces())) {
                        log.debug("Query province city data from DHUB is empty, country: {}, language: {}", country.getCountryCode(), language);
                        return;
                    }

                    // Convert data
                    convertData(language.value, response.getBussinessResponse().getProvinces(), provinceIdProvinceMap, cityIdCityMap);
                });

                // Insert or update province data
                saveData(new ArrayList<>(provinceIdProvinceMap.values()), new ArrayList<>(cityIdCityMap.values()));

                provinceIdProvinceMap.clear();
                cityIdCityMap.clear();
            });
        } catch (Exception e) {
            log.error("Sync province city data job error", e);
            return ReturnT.FAIL;
        }
        log.info("Sync province city data end");
        return ReturnT.SUCCESS;
    }

    /**
     * Create a signature parameter
     */
    private SignatureHelpRequestDto signatureParam() {
        SignatureHelpRequestDto signatureParam = new SignatureHelpRequestDto();
        signatureParam.setPartnerCode(AppUtility.getSysInfo(SysConfig.TMC_HUB_PARTNER_CODE));
        signatureParam.setSecurityKey(AppUtility.getSysInfo(SysConfig.TMC_HUB_SECURITY_KEY));
        return signatureParam;
    }

    /**
     * Convert province and city data
     */
    private void convertData(String language, List<Province> sourceProvinces,
                             Map<String, ProvinceEntity> provinceIdProvinceMap, Map<String, CityEntity> cityIdCityMap) {
        sourceProvinces.forEach(province -> {
            // Convert province data
            String provinceKey = String.format("%s-%s", province.getCountryCode(), province.getProvinceCode());
            ProvinceEntity provinceEntity = provinceIdProvinceMap.computeIfAbsent(provinceKey, k -> new ProvinceEntity());
            convertProvince(language, province, provinceEntity);

            // Convert city data
            if (CollUtil.isEmpty(province.getCitys())) {
                return;
            }
            province.getCitys().forEach(city -> {
                String cityKey = String.format("%s-%s-%s", province.getCountryCode(), province.getProvinceCode(), city.getCityCode());
                CityEntity cityEntity = cityIdCityMap.computeIfAbsent(cityKey, k -> new CityEntity());
                convertCity(language, province, city, cityEntity);
            });
        });
    }

    /**
     * Convert province and city data to entity
     */
    private void convertProvince(String language, Province province, ProvinceEntity provinceEntity) {
        provinceEntity.setCountryCode(province.getCountryCode());
        provinceEntity.setProvinceCode(province.getProvinceCode());
        provinceEntity.setCreator(UserSession.SYSTEM_USER_NAME);
        provinceEntity.setModifier(UserSession.SYSTEM_USER_NAME);
        if (LanguageEnum.ZH_CN.value.equals(language)) {
            provinceEntity.setNameZhCn(StringUtils.defaultString(province.getProvinceName()));
            provinceEntity.setNameEnUs(StringUtils.defaultString(provinceEntity.getNameEnUs()));
        } else if (LanguageEnum.EN_US.value.equals(language)) {
            provinceEntity.setNameEnUs(StringUtils.defaultString(province.getProvinceName()));
            provinceEntity.setNameZhCn(StringUtils.defaultString(provinceEntity.getNameZhCn()));
        }
    }

    /**
     * Convert city data to entity
     */
    private void convertCity(String language, Province province, City city, CityEntity cityEntity) {
        cityEntity.setCountryCode(province.getCountryCode());
        cityEntity.setProvinceCode(province.getProvinceCode());
        cityEntity.setCityCode(city.getCityCode());
        cityEntity.setParentCityCode(StringUtils.defaultString(city.getParentCityCode()));
        cityEntity.setCreator(UserSession.SYSTEM_USER_NAME);
        cityEntity.setModifier(UserSession.SYSTEM_USER_NAME);
        if (LanguageEnum.ZH_CN.value.equals(language)) {
            cityEntity.setNameZhCn(StringUtils.defaultString(city.getCityName()));
            cityEntity.setNameEnUs(StringUtils.defaultString(cityEntity.getNameEnUs()));
        } else if (LanguageEnum.EN_US.value.equals(language)) {
            cityEntity.setNameEnUs(StringUtils.defaultString(city.getCityName()));
            cityEntity.setNameZhCn(StringUtils.defaultString(cityEntity.getNameZhCn()));
        }
    }

    /**
     * Save data to DB
     */
    private void saveData(List<ProvinceEntity> provinceEntityList, List<CityEntity> cityEntityList) {
        if (CollUtil.isNotEmpty(provinceEntityList)) {
            provinceMapper.batchUpsert(provinceEntityList);
        }
        if (CollUtil.isNotEmpty(cityEntityList)) {
            cityMapper.batchUpsert(cityEntityList);
        }
    }
}
