package com.fangcang.grfp.core.util;

import cn.hutool.core.io.IoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
public class ControllerLoggingUtility {

	// ---------------------------------------------------------------------------------------------------- Private Static Members

	private final static String SESSION_ATTRIBUTE_KEY_START_TIME = "START_TIME";

	private final static String SESSION_ATTRIBUTE_KEY_HAS_LOGGED_FINISH = "HAS_LOGGED_FINISH";

	private final static String ALPHA_NUMERIC = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
	
	// Header key
	private final static String HEADER_SIGNATURE = "Signature";
	public static String HEADERS_LANGUAGE = "language";
	public static String HEADERS_AUTHORIZATION = "Authorization";

	// ---------------------------------------------------------------------------------------------------- Protected Methods
	
	public static boolean hasLoggedFinish(HttpServletRequest request) {
		Boolean hasLoggedFinish = (Boolean)request.getAttribute(SESSION_ATTRIBUTE_KEY_HAS_LOGGED_FINISH);
		
		if (hasLoggedFinish == null) {
			return false;
		} else {
			return hasLoggedFinish;
		}
	}

	public static void logBegin(HttpServletRequest request, HttpServletResponse response) {
		try {
			// Mark the request pre-handle time
			request.setAttribute(SESSION_ATTRIBUTE_KEY_START_TIME, System.currentTimeMillis());
			
			// Generate a random ID and put it in the MDC
//			String traceId = UUID.randomUUID().toString();
			String traceId = StringUtility.randomString(ALPHA_NUMERIC, 6);
			MDC.put("traceId", traceId);
			
			// Prepare the logging information
	        String queryString = (request.getQueryString() != null) ? " with queryString=" + request.getQueryString() : "";
	        String requestBody = IoUtil.read(request.getInputStream(), StandardCharsets.UTF_8);
	        if ((requestBody != null) && (requestBody.length() > 0)) {
	        	requestBody = " with requestBody=" + StringUtility.truncate(requestBody, 10000);
	        }
	        
	        HttpHeaders headers = new ServletServerHttpRequest(request).getHeaders();
	        String headerInfo = getHeaderInfo(headers, HEADER_SIGNATURE, HEADERS_LANGUAGE, HEADERS_AUTHORIZATION);
	        // Logging
			log.info(
					request.getMethod() + " " +
					request.getRequestURI().substring(request.getContextPath().length()) + " " +
					headerInfo + 
					" BEGIN" +
					queryString +
					requestBody
	        		);
			
		} catch (Exception ex) {
			log.error("Exception occurs at ControllerLoggingUtility::logBegin()", ex);
		}
		
	}
	
	// ---------------------------------------------------------------------------------------------------- Public Methods
	
	/**
	 * This method use use to log the Controller return object in a more efficient way rather than wrapping the ResponseBody in the filter.
	 */
	public static <T> T logFinish(HttpServletRequest request, HttpServletResponse response, T object) {
		try {
			// Mark the request has logged finished
			request.setAttribute(SESSION_ATTRIBUTE_KEY_HAS_LOGGED_FINISH, Boolean.TRUE);
			
			// Compute the total execution time
			Object startTimeObject = request.getAttribute(SESSION_ATTRIBUTE_KEY_START_TIME);
			long execTime = -1;
			if (startTimeObject != null) {
				long startTime = (Long)request.getAttribute(SESSION_ATTRIBUTE_KEY_START_TIME);
				execTime = System.currentTimeMillis() - startTime;
			}
			
			// Logging information
			String returning = (object != null) ? " returning " + String.valueOf(object) : "";
			
			// Logging
			log.info(
	        		request.getMethod() + " " +
	        		request.getRequestURI().substring(request.getContextPath().length()) +
					" FINISH in " + execTime + "ms" +
					StringUtils.abbreviate(returning, 1000)
	        		);
			
			// Clear the information stored in the MDC
			MDC.clear();
			
		} catch (Exception ex) {
			log.error("Exception occurs at ControllerLoggingUtility::logFinish()", ex);
		}
		
		return object;
	}
	
	private static String getHeaderInfo(HttpHeaders headers, String...headerNames) {
		StringBuffer headerInfo = new StringBuffer();
		for(String headerName : headerNames) {
			List<String> headerValueList = headers.get(headerName);
			if(CollectionUtils.isEmpty(headerValueList)) {
				continue;
			}
			headerInfo.append(" ");
			headerInfo.append(headerName);
			headerInfo.append(": ");
			headerInfo.append(headerValueList);
		}
		return headerInfo.toString();
	}
	
}
