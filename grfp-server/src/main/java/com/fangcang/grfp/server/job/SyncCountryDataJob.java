package com.fangcang.grfp.server.job;

import cn.hutool.core.collection.CollUtil;
import com.fangcang.grfp.core.constant.SysConfig;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.QueryCountryListRequest;
import com.fangcang.grfp.core.dto.dhub.response.Response;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.Country;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.QueryCountryListResponse;
import com.fangcang.grfp.core.entity.CountryEntity;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.mapper.CountryMapper;
import com.fangcang.grfp.core.remote.tmchub.manager.TmcHubApiManager;
import com.fangcang.grfp.core.remote.tmchub.request.SignatureHelpRequestDto;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.server.util.AppUtility;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;


@Component
@Slf4j
@JobHandler(value = SyncCountryDataJob.TASK_NAME)
public class SyncCountryDataJob extends IJobHandler {

    public static final String TASK_NAME = "SyncCountryDataJob";

    @Resource
    private TmcHubApiManager tmcHubApiManager;

    @Resource
    private CountryMapper countryMapper;

    /**
     * Synchronize country data to the database
     */
    @Override
    public ReturnT<String> execute(String s) {
        log.info("Sync country data job start");
        try {
            Map<String, CountryEntity> countryIdCountryMap = new HashMap<>(250);

            Stream.of(LanguageEnum.values()).forEach(language -> {
                // Get country data from DHUB
                QueryCountryListRequest queryCountryListRequest = QueryCountryListRequest.builder()
                    .language(language.value)
                    .build();
                Response<QueryCountryListResponse> response = tmcHubApiManager.queryCountryList(queryCountryListRequest, signatureParam());
                if (Objects.isNull(response) || Objects.isNull(response.getBussinessResponse()) || CollUtil.isEmpty(response.getBussinessResponse().getCountries())) {
                    log.error("Query country data from DHUB is empty, language: {}", language);
                    return;
                }

                // Convert data
                convertData(language.value, response.getBussinessResponse().getCountries(), countryIdCountryMap);
            });

            // Insert or update data into the database
            saveData(new ArrayList<>(countryIdCountryMap.values()));

        } catch (Exception e) {
            log.error("Sync country data job error", e);
            return ReturnT.FAIL;
        }
        log.info("Sync country data end");
        return ReturnT.SUCCESS;
    }

    /**
     * Create a signature parameter
     */
    private SignatureHelpRequestDto signatureParam() {
        SignatureHelpRequestDto signatureParam = new SignatureHelpRequestDto();
        signatureParam.setPartnerCode(AppUtility.getSysInfo(SysConfig.TMC_HUB_PARTNER_CODE));
        signatureParam.setSecurityKey(AppUtility.getSysInfo(SysConfig.TMC_HUB_SECURITY_KEY));
        return signatureParam;
    }

    /**
     * Convert QueryCountryListResponse to List<CountryEntity>
     */
    private void convertData(String language, List<Country> countries, Map<String, CountryEntity> countryIdCountryMap) {
        countries.forEach(country -> {
            CountryEntity countryEntity = countryIdCountryMap.computeIfAbsent(country.getCountryId(), k -> new CountryEntity());
            countryEntity.setCountryId(country.getCountryId());
            countryEntity.setCountryCode(country.getCountryCode());
            countryEntity.setCreator(UserSession.SYSTEM_USER_NAME);
            countryEntity.setModifier(UserSession.SYSTEM_USER_NAME);
            if (LanguageEnum.ZH_CN.value.equals(language)) {
                countryEntity.setNameZhCn(StringUtils.defaultString(country.getCountryName()));
                countryEntity.setShortNameZhCn(StringUtils.defaultString(country.getAcronymPinyin()));
                countryEntity.setNameEnUs(StringUtils.defaultString(countryEntity.getNameEnUs()));
                countryEntity.setShortNameEnUs(StringUtils.defaultString(countryEntity.getShortNameEnUs()));
            } else if (LanguageEnum.EN_US.value.equals(language)) {
                countryEntity.setNameEnUs(StringUtils.defaultString(country.getCountryName()));
                countryEntity.setShortNameEnUs(StringUtils.defaultString(country.getAcronymPinyin()));
                countryEntity.setNameZhCn(StringUtils.defaultString(countryEntity.getNameZhCn()));
                countryEntity.setShortNameZhCn(StringUtils.defaultString(countryEntity.getShortNameZhCn()));
            }
        });
    }

    /**
     * Insert or update data into the database
     */
    private void saveData(List<CountryEntity> countryEntityList) {
        if (CollUtil.isNotEmpty(countryEntityList)) {
            countryMapper.batchUpsert(countryEntityList);
        }
    }
}
