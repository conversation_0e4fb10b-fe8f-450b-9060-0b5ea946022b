package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.CurrencyExchangeRateEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.vo.CurrencyNameVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 币种汇率表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
public interface CurrencyExchangeRateMapper extends BaseMapper<CurrencyExchangeRateEntity> {

    void batchUpsert(@Param("list") Collection<CurrencyExchangeRateEntity> currencyExchangeRateList);

    /**
     *  币种列表
     */
    List<CurrencyNameVO> currencyNameList(@Param("currencyCode") String currencyCode, @Param("limitCount") Integer limitCount);

    /**
     *  查询2个币种的美元汇率。
     */
    List<CurrencyExchangeRateEntity> getCurrencyRateInfoList(@Param("fromCurrencyCode") String fromCurrencyCode,
                                             @Param("toCurrencyCode") String toCurrencyCode);

    /**
     * 根据币种编码查询币种信息
     */
    default List<CurrencyExchangeRateEntity> selectByCurrencyCodes(Collection<String> currencyCodes) {
        LambdaQueryWrapper<CurrencyExchangeRateEntity> queryWrapper = Wrappers.lambdaQuery(CurrencyExchangeRateEntity.class);
        queryWrapper.select(CurrencyExchangeRateEntity::getCurrencyCode, CurrencyExchangeRateEntity::getCurrencyName);
        queryWrapper.in(CurrencyExchangeRateEntity::getCurrencyCode, currencyCodes);
        return selectList(queryWrapper);
    }
}
