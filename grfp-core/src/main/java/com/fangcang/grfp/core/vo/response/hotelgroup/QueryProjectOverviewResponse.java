package com.fangcang.grfp.core.vo.response.hotelgroup;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/8/14 16:33
 */
@ApiModel("签约列表VO")
@Getter
@Setter
public class QueryProjectOverviewResponse extends BaseVO {

    //项目id
    @ApiModelProperty("项目ID")
    private Integer projectId;

    //项目名称
    @ApiModelProperty("项目名称")
    private String projectName;

    //企业名称
    @ApiModelProperty("机构名称")
    private String orgName;

    //项目类型(1：酒店招标)
    @ApiModelProperty("项目类型(1：酒店招标)")
    private Integer projectType;

    //早餐类型
    @ApiModelProperty("早餐类型")
    private String breakfastNum;


    //招标方式(1-公开招标，2-邀请招标)
    @ApiModelProperty("招标方式(1-公开招标，2-邀请招标)")
    private Integer tenderType;

    @JsonIgnore
    private Date firstBidStartTime;

    @JsonIgnore
    private Date firstBidEndTime;

    @JsonIgnore
    private Date secondBidStartTime;

    @JsonIgnore
    private Date secondBidEndTime;

    @JsonIgnore
    private Date thirdBidStartTime;

    @JsonIgnore
    private Date thirdBidEndTime;

    //预估采购数量，项目类型为酒店时即为酒店数量
    @ApiModelProperty("估采购数量，项目类型为酒店时即为酒店数量")
    private Integer tenderCount;

    //创建人
    private String creator;

    //创建时间
    private String createTime;

    //招标开始时间 yyyy-MM-dd
    @ApiModelProperty("招标开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date bidStartTime;

    //招标结束时间 yyyy-MM-dd
    @ApiModelProperty("招标结束时间 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date bidEndTime;

    //评标结果时间 yyyy-MM-dd
    @ApiModelProperty("评标结果时间 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date bidResultTime;

    //
    @ApiModelProperty("酒店集团销售人")
    private String hotelGroupContactName;

    //总报价数
    @ApiModelProperty("总报价数")
    private int totalBidPriceCount;

    //议价中数
    @ApiModelProperty("议价中数")
    private int underNegotiationPriceCount;

    //已中签数
    @ApiModelProperty("已中签数")
    private int bidWinningPriceCount;

    //项目状态(1：签约中，2：签约结束，3：已废除)
    @ApiModelProperty("项目状态(1：签约中，2：签约结束，3：已废除)")
    private Integer projectState;

    //城市名称
    @ApiModelProperty("城市名称")
    private String cityName;

    //城市名称
    @ApiModelProperty("城市编号")
    private String cityCode;

    //酒店名称
    @ApiModelProperty("酒店名称")
    private String hotelName;

    //酒店id
    @ApiModelProperty("酒店id")
    private Long hotelId;

    //早餐数量
    @ApiModelProperty("是否包含早餐")
    private Integer isIncludeBreakfast;

    //早餐数量
    @ApiModelProperty("起价")
    private BigDecimal basePrice;

    //酒店意向id
    @ApiModelProperty("酒店意向id")
    private Integer projectIntentHotelId;


    //酒店集团意向id
    @ApiModelProperty("酒店集团意向id")
    private Integer projectIntentHotelGroupId;

    // 报价来源 2:酒店，4酒店集团
    @ApiModelProperty("报价来源 2:酒店，4酒店集团")
    private Integer bidOrgType;

    //报价监控开始日期
    @ApiModelProperty("报价监控开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date priceMonitorStartDate;

    //
    @ApiModelProperty("报价监控结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date priceMonitorEndDate;

    /** 排序 */
    @ApiModelProperty("排序")
    private Integer displayOrder;

    /**品牌ID**/
    @ApiModelProperty("品牌ID")
    private Long hotelBrandId;

    /**品牌名称**/
    @ApiModelProperty("品牌名称")
    private String hotelBrandName;

    /** 是否开启酒店集团审核 **/
    @ApiModelProperty("是否开启酒店集团审核")
    private Integer isOpenGroupApprove;

    /** 酒店投标联系人 **/
    @ApiModelProperty("酒店投标联系人")
    private String bidContactName;


    /** 酒店投标联系手机号码 **/
    @ApiModelProperty("酒店投标联系手机号码")
    private String hotelBidContactMobile;

    /** 酒店投标联系电邮 **/
    @ApiModelProperty("酒店投标联系电邮")
    private String hotelBidContactEmail;

    /** 酒店集团投标联系人 **/
    @ApiModelProperty("酒店集团投标联系人")
    private String hotelGroupBidContactName;


    /** 酒店集团投标联系手机号码 **/
    @ApiModelProperty("酒店集团投标联系手机号码")
    private String hotelGroupBidContactMobile;

    /** 酒店集团投标联系电邮 **/
    @ApiModelProperty("酒店集团投标联系电邮")
    private String hotelGroupBidContactEmail;

    /** 销售联系人 **/
    @ApiModelProperty("销售联系人")
    private String hotelSalesContactName;


    /** 销售联系手机号码 **/
    @ApiModelProperty("销售联系手机号码")
    private String hotelSalesContactMobile;

    /** 销售联系电邮 **/
    @ApiModelProperty("销售联系电邮")
    private String hotelSalesContactEmail;

    // 报价状态
    @ApiModelProperty("报价状态")
    private Integer bidState;

    // 集团审批状态
    @ApiModelProperty("集团审批状态")
    private Integer hotelGroupApproveStatus;

    // 是否品牌限制
    @ApiModelProperty("是否品牌限制")
    private Integer isBrandLimit;

}
