package com.fangcang.grfp.core.util;

import com.fangcang.grfp.core.vo.LngLatGoogleVO;

import java.math.BigDecimal;
import java.util.Arrays;

public class CoordinateUtils {
    private static final double PI = 3.1415926535897932384626;
    private static final double X_PI = PI * 3000.0 / 180.0;
    private static final double EARTH_RADIUS = 6378245.0;
    private static final double EE = 0.00669342162296594323;

    // ------------------------- 公有方法 -------------------------

    /**
     * 百度坐标系 (BD-09) 转 火星坐标系 (GCJ-02)
     */
    public static double[] bd09ToGcj02(double bdLng, double bdLat) {
        double x = bdLng - 0.0065;
        double y = bdLat - 0.006;
        double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI);
        double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI);
        return new double[]{z * Math.cos(theta), z * Math.sin(theta)};
    }

    /**
     * 火星坐标系 (GCJ-02) 转 百度坐标系 (BD-09)
     */
    public static double[] gcj02ToBd09(double gcjLng, double gcjLat) {
        double z = Math.sqrt(gcjLng * gcjLng + gcjLat * gcjLat) + 0.00002 * Math.sin(gcjLat * X_PI);
        double theta = Math.atan2(gcjLat, gcjLng) + 0.000003 * Math.cos(gcjLng * X_PI);
        return new double[]{z * Math.cos(theta) + 0.0065, z * Math.sin(theta) + 0.006};
    }

    /**
     * WGS-84 转 火星坐标系 (GCJ-02)
     */
    public static double[] wgs84ToGcj02(double wgsLng, double wgsLat) {
        if (isOutOfChina(wgsLat, wgsLng)) {
            return new double[]{wgsLng, wgsLat};
        }
        double dLat = transformLat(wgsLng - 105.0, wgsLat - 35.0);
        double dLng = transformLng(wgsLng - 105.0, wgsLat - 35.0);
        double radLat = wgsLat / 180.0 * PI;
        double magic = Math.sin(radLat);
        magic = 1 - EE * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / (EARTH_RADIUS * (1 - EE) / (magic * sqrtMagic) * PI);
        dLng = (dLng * 180.0) / (EARTH_RADIUS / sqrtMagic * Math.cos(radLat) * PI);
        return new double[]{wgsLng + dLng, wgsLat + dLat};
    }

    /**
     * 火星坐标系 (GCJ-02) 转 WGS-84（近似计算，存在误差）
     */
    public static double[] gcj02ToWgs84(double gcjLng, double gcjLat) {
        if (isOutOfChina(gcjLat, gcjLng)) {
            return new double[]{gcjLng, gcjLat};
        }
        double[] gcj02 = wgs84ToGcj02(gcjLng, gcjLat);
        return new double[]{gcjLng * 2 - gcj02[0], gcjLat * 2 - gcj02[1]};
    }

    // ------------------------- 私有方法 -------------------------

    /**
     * 判断坐标是否在中国境外
     */
    private static boolean isOutOfChina(double lat, double lng) {
        return lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271;
    }

    /**
     * 计算纬度偏移量（内部方法）
     */
    private static double transformLat(double x, double y) {
        double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(y * PI) + 40.0 * Math.sin(y / 3.0 * PI)) * 2.0 / 3.0;
        ret += (160.0 * Math.sin(y / 12.0 * PI) + 320.0 * Math.sin(y * PI / 30.0)) * 2.0 / 3.0;
        return ret;
    }

    /**
     * 计算经度偏移量（内部方法）
     */
    private static double transformLng(double x, double y) {
        double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(x * PI) + 40.0 * Math.sin(x / 3.0 * PI)) * 2.0 / 3.0;
        ret += (150.0 * Math.sin(x / 12.0 * PI) + 300.0 * Math.sin(x / 30.0 * PI)) * 2.0 / 3.0;
        return ret;
    }

    public static LngLatGoogleVO calculateNullGoogleLngLat(BigDecimal lngGoogle, BigDecimal latGoogle, BigDecimal lngBaidu, BigDecimal latBaidu){
        LngLatGoogleVO result = new LngLatGoogleVO();
        if(lngGoogle != null && latGoogle != null){
            result.setLngGoogle(lngGoogle);
            result.setLatGoogle(latGoogle);
            return result;
        }
        if(lngBaidu != null && latBaidu != null) {
            double[] lngLatBaiduArrays = CoordinateUtils.bd09ToGcj02(lngBaidu.doubleValue(), latBaidu.doubleValue());
            result.setLngGoogle(BigDecimal.valueOf(lngLatBaiduArrays[0]));
            result.setLatGoogle(BigDecimal.valueOf(lngLatBaiduArrays[1]));

        }
        return result;
    }

    // ------------------------- 示例用法 -------------------------
    public static void main(String[] args) {
        // 示例：BD09 → WGS84
//        double[] gcj02 = bd09ToGcj02(116.40337,39.923955);
//        System.out.println(Arrays.toString(gcj02));
//        double[] wsg84 = gcj02ToWgs84(gcj02[0], gcj02[1]);
//        System.out.println(Arrays.toString(wsg84));

        // 示例：WGS84 → BD09
        double[] gcj02 = wgs84ToGcj02(113.918006,22.540628);
        System.out.println(Arrays.toString(gcj02));
        double[] bd09 = gcj02ToBd09(gcj02[0], gcj02[1]);
        System.out.println(Arrays.toString(bd09));
    }

}
