package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 机构关联酒店品牌
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_org_related_hotel_brand")
public class OrgRelatedHotelBrandEntity extends BaseVO {


    @TableId(value = "org_related_hotel_brand_id", type = IdType.AUTO)
    private Integer orgRelatedHotelBrandId;

    /**
     * 机构ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 酒店集团ID
     */
    @TableField("hotel_group_id")
    private Long hotelGroupId;

    /**
     * 酒店品牌ID
     */
    @TableField("hotel_brand_id")
    private Long hotelBrandId;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;


}
