package com.fangcang.grfp.api.controller.org.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.jackson.OssPublicJsonSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("机构信息")
@Getter
@Setter
public class OrgInfoVO extends BaseVO {

    @ApiModelProperty(value = "机构Id")
    private Integer orgId;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "机构类型 1平台，2酒店，3企业，4酒店集团")
    private Integer orgType;

    @ApiModelProperty(value = "联系人")
    private String contactName;

    @ApiModelProperty(value = "联系人电话区号")
    private String contactMobileAreaCode;

    @ApiModelProperty(value = "联系人电话")
    private String contactMobile;

    @ApiModelProperty(value = "联系人电邮")
    private String contactEmail;

    @ApiModelProperty(value = "财务联系人")
    private String financialContactName;

    @ApiModelProperty(value = "财务联系人电话区号")
    private String financialContactMobileAreaCode;

    @ApiModelProperty(value = "财务联系人电话")
    private String financialContactMobile;

    @ApiModelProperty(value = "财务联系人电邮")
    private String financialContactEmail;

    @ApiModelProperty(value = "详细地址")
    private String addressDetail;

    @ApiModelProperty(value = "公司简介")
    private String companyProfile;

    @ApiModelProperty(value = "机构logo地址")
    @JsonSerialize(using = OssPublicJsonSerializer.class)
    private String logoUrl;

}
