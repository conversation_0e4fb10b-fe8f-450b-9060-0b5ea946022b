package com.fangcang.grfp.core.vo.response.project;


import com.fangcang.grfp.core.dto.dhub.response.product.HotelPriceItem;
import com.fangcang.grfp.core.vo.response.bidprice.PriceApplicableRoomInfoResponse;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel("项目报价意向酒店信息")
public class BidHotelInfoQueryResponse implements java.io.Serializable{

    //项目意向酒店表id
    @ApiModelProperty("项目意向酒店表id")
    private Integer projectIntentHotelId;

    /**
     * 项目id
     */
    @ApiModelProperty("项目ID")
    private Long projectId;

    /**
     * 酒店ID
     */
    @ApiModelProperty("酒店ID")
    private Long hotelId;

    //酒店图片
    @ApiModelProperty("酒店图片")
    private String hotelImageUrl;

    //名牌名称
    @ApiModelProperty("名牌名称")
    private String hotelBrandName;

    //酒店名称
    @ApiModelProperty("酒店名称")
    private String hotelName;


    //酒店名称
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String hotelNameEnUs;


    //酒店名称
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String hotelNameZhCn;

    //星级
    private String hotelStar;

    //星级
    private String hotelStarName;

    //酒店地址
    private String hotelAddress;

    //酒店地址
    @JsonIgnore
    private String hotelAddressEnUs;

    //酒店地址
    @JsonIgnore
    private String hotelAddressZhCn;


    //城市名称
    private String cityName;

    //城市编码
    private String cityCode;

    //评分
    private String rating;

    //推荐亮点
    private String brightSpot;

    //企业(分销商)跟进人姓名
    private String distributorContactName;

    //企业(分销商)跟进人id
    private Long distributorContactUid;

    //标书状态(0：未投标，1：新标(酒店提交报价，企业还未处理)，2：议价中，3：已中标，4：已否决)
    private Integer bidState;

    //近一年采购间夜数
    private int latestYearRoomNight;

    //采购均价
    private BigDecimal tenderAvgPrice;

    //1：推荐酒店 ，其它值为未推荐酒店
    private Integer recommendHotelState;

    //百度经度
    @JsonIgnore
    private BigDecimal lngBaiDu;

    //百度纬度
    @JsonIgnore
    private BigDecimal latBaiDu;

    //Google经度
    private BigDecimal lngGoogle;

    //Google纬度
    private BigDecimal latGoogle;

    //开业时间
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingDate;

    //装修时间
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date fitmentDate;

    //客房总数
    private Integer roomNum;

    //投标总权重
    private BigDecimal bidWeight;

    //最低产品投标价
    private BigDecimal minPrice;

    //距离项目poi距离
    private String poiNameDistance;

    //酒店提供发票类型：1-普票，2-专票
    private Integer invoiceType;

    //酒店提供发票税点((大于等于0小于等于100)
    private BigDecimal provideInvoiceTaxRate;

    //酒店投标报价是否含佣金：1-是，0-否
    private Integer supportIncludeCommission;

    //所有投标价佣金率(大于等于0小于等于100)
    private BigDecimal tendCommission;

    //合同状态状态：0-待签章，1-已完成 ,2-生成中；
    private Integer contractState;

    //签约状态 0未签约，1已签约
    private Integer signingStatus;
    /**
     * 每日起价信息
     */
    private List<HotelPriceItem> priceItems;

    //poi名称
    private String poiName;

    //未报价备注
    private String unPriceRemark;

    // 运营平台报价跟进人
    private String hotelPriceFollowName;

    // 平台线上跟进人姓名
    private String onlineFollowName;

    // 平台履约跟进人姓名
    private String monitorFollowName;

    // 是否为同档价位
    private boolean isTheSameLevelPrice;

    // 去年报价均价
    private BigDecimal lastYearAvgPrice;

    // 去年城市排名
    private int lastYearCityOrder;

    // 去年服务分
    private BigDecimal lastYearServicePoints;
    // 房型列表
    List<PriceApplicableRoomInfoResponse> roomResponseList;

    // 房型描述(多个房型拼接)
    private String roomTypeDesc;

    //酒店服务分
    private BigDecimal hotelServicePoints;

    // 酒店拒绝议价备注
    private String rejectNegotiationRemark;

    // 通知状态
    private Integer notifyStatus;

    // 通知人
    private String notifyOperator;

    // 酒店集团ID
    private String hotelGroup;

    // 酒店集团名称
    private String hotelGroupName;

    // 是否邀请
    private Integer isInvited;

    // 推荐等级 1:SSS,2:SS,3:S,4:A,5:B,6:C
    private Integer recommendLevel;

    // 通知时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date notifyTime;

    //签约参考价格
    private BigDecimal referencePrice;

    //报价来源 2:酒店，4酒店集团
    private Integer bidOrgType;

    //报价机构ID
    private Integer bidOrgId;

    //报价机构名称
    private String bidOrgName;

    private Integer isIncludeBreakfast;



}
