<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectLastYearCityStatMapper">

    <select id="selectByProjectIdAndCityCode" resultType="com.fangcang.grfp.core.entity.ProjectLastYearCityStatEntity">
        SELECT
            *
        FROM T_PROJECT_LAST_YEAR_CITY_STAT
        WHERE PROJECT_ID = #{projectId}
        AND CITY_CODE = #{cityCode}
    </select>

    <insert id="upsert">
        INSERT INTO t_project_last_year_city_stat (project_id,
                                                   city_code,
                                                   total_room_night,
                                                   total_sales_amount,
                                                   total_room_night_500,
                                                   total_room_night_400_to_500,
                                                   total_room_night_300_to_400,
                                                   total_room_night_200_to_300,
                                                   total_room_night_100_to_200,
                                                   total_room_night_100,
                                                   creator)
        VALUES (#{projectId,jdbcType=BIGINT},
                #{cityCode,jdbcType=VARCHAR},
                #{totalRoomNight,jdbcType=BIGINT},
                #{totalSalesAmount,jdbcType=DECIMAL},
                #{totalRoomNight500,jdbcType=BIGINT},
                #{totalRoomNight400To500,jdbcType=BIGINT},
                #{totalRoomNight300To400,jdbcType=BIGINT},
                #{totalRoomNight200To300,jdbcType=BIGINT},
                #{totalRoomNight100To200,jdbcType=BIGINT},
                #{totalRoomNight100,jdbcType=BIGINT},
                #{creator,jdbcType=VARCHAR})
        ON DUPLICATE KEY UPDATE total_room_night            = values(total_room_night),
                                total_sales_amount          = values(total_sales_amount),
                                total_room_night_500        = values(total_room_night_500),
                                total_room_night_400_to_500 = values(total_room_night_400_to_500),
                                total_room_night_300_to_400 = values(total_room_night_300_to_400),
                                total_room_night_200_to_300 = values(total_room_night_200_to_300),
                                total_room_night_100_to_200 = values(total_room_night_100_to_200),
                                total_room_night_100        = values(total_room_night_100),
                                modifier                    = #{modifier}
    </insert>
</mapper>
