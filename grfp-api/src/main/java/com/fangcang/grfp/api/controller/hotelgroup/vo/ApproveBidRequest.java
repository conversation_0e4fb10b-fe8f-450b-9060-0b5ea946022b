package com.fangcang.grfp.api.controller.hotelgroup.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 *  审核通过报价
 */
@ApiModel("审核通过报价请求")
@Getter
@Setter
public class ApproveBidRequest extends BaseVO {

    // 酒店报价ID
    @ApiModelProperty("酒店报价ID")
    @NotNull
    private Integer projectIntentHotelId;

}
