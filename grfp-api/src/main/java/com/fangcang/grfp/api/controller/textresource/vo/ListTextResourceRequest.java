package com.fangcang.grfp.api.controller.textresource.vo;


import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("文字资源列表查询")
@Getter
@Setter
public class ListTextResourceRequest extends PageQuery {

	private static final long serialVersionUID = 1L;
	
	// ---------------------------------------------------------------------------------------------------- Private Member Variables
	
	@ApiModelProperty(value = "文字资源编码")
	private String searchTextResourceCodeLike;


	@ApiModelProperty(value = "文字资源类型 1:网页文本,2:消息文本")
	private Integer searchTextResourceType;

	@ApiModelProperty(value = "文字资源值")
	private String searchTextResourceValueLike;


}
