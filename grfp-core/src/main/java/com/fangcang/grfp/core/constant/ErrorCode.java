package com.fangcang.grfp.core.constant;

/**
 * 提示信息编号
 */
public class ErrorCode {

    // 参数校验
    // 参数不能为空字符串
    public static final String VALIDATE_REQUEST_PARAMETER_CANNOT_BE_EMPTY = "VALIDATE_REQUEST_PARAMETER_CANNOT_BE_EMPTY";

    // 参数不能为空值
    public static final String VALIDATE_REQUEST_PARAMETER_CANNOT_BE_NULL = "VALIDATE_REQUEST_PARAMETER_CANNOT_BE_NULL";

    // 参数长度必须大于x小于Y
    public static final String VALIDATE_REQUEST_PARAMETER_LENGTH_MUST_BE_BETWEEN_X_AND_Y = "VALIDATE_REQUEST_PARAMETER_LENGTH_MUST_BE_BETWEEN_X_AND_Y";

    // 参数长度必须为X
    public static final String VALIDATE_REQUEST_PARAMETER_LENGTH_MUST_BE_X = "VALIDATE_REQUEST_PARAMETER_LENGTH_MUST_BE_X";

    // 参数枚举类型错误
    public static final String VALIDATE_REQUEST_ENUM_PARAMETER_ERROR =  "VALIDATE_REQUEST_ENUM_PARAMETER_ERROR";

    // 参数必须大于等于X
    public static final String VALIDATE_REQUEST_PARAMETER_MUST_BE_GREATER_THAN_OR_EQUAL_TO_X =  "VALIDATE_REQUEST_PARAMETER_MUST_BE_GREATER_THAN_OR_EQUAL_TO_X";

    // 参数必须小于等X
    public static final String VALIDATE_REQUEST_PARAMETER_MUST_BE_LESS_THAN_OR_EQUAL_TO_X =  "VALIDATE_REQUEST_PARAMETER_MUST_BE_LESS_THAN_OR_EQUAL_TO_X";

    // 参数必须匹配正则
    public static final String VALIDATE_REQUEST_PARAMETER_MUST_MATCH_PATTERN =  "VALIDATE_REQUEST_PARAMETER_MUST_MATCH_PATTERN";

    // 参数值范围必须在X和Y之间
    public static final String VALIDATE_REQUEST_PARAMETER_RANGE_MUST_BE_BETWEEN_X_AND_Y =  "VALIDATE_REQUEST_PARAMETER_RANGE_MUST_BE_BETWEEN_X_AND_Y";

    // 参数值必须为X
    public static final String VALIDATE_REQUEST_PARAMETER_MUST_BE_BETWEEN_X =  "VALIDATE_REQUEST_PARAMETER_MUST_BE_BETWEEN_X";


    //系统错误
    public static final String SYSTEM_ERROR =  "SYSTEM_ERROR";

    // 请求参数错误
    public static final String REQUEST_PARAMETER_ERROR =  "REQUEST_PARAMETER_ERROR";

    // 文件大小超出限制
    public static final String SIZE_IS_TOO_LARGE =  "SIZE_IS_TOO_LARGE";

    public static final String INVALIDATE_USER_SESSION_PLEASE_LOGIN = "INVALIDATE_USER_SESSION_PLEASE_LOGIN"; // 登录信息已失效, 请重新登录
    public static final String USER_SESSION_IS_NULL = "USER_SESSION_IS_NULL"; // Session会话为空
    public static final String NO_PERMISSION_ACCESS = "NO_PERMISSION_ACCESS"; // 没有权限访问

    // 公共
    public static final String RECORD_ALREADY_EXISTED = "RECORD_ALREADY_EXISTED";
    public static final String ADD_FAILED = "ADD_FAILED";
    public static final String DELETE_FAILED = "DELETE_FAILED";
    public static final String UPDATE_FAILED = "UPDATE_FAILED";
    public static final String DATA_NOT_FOUND = "DATA_NOT_FOUND";
    public static final String OPERATE_TOO_FREQUENCY = "OPERATE_TOO_FREQUENCY"; // 您操作的频率过快，请稍后再操作
    public static final String ONLY_ADMIN_HAS_PERMISSION_UPDATE= "ONLY_ADMIN_HAS_PERMISSION_UPDATE"; // 只有管理员有全新修改
    public static final String ONLY_ADMIN_HAS_PERMISSION_ADD= "ONLY_ADMIN_HAS_PERMISSION_ADD"; // 只有管理员有全新修改
    public static final String IMPORT_RECORD_NOT_EXIST = "IMPORT_RECORD_NOT_EXIST"; // 导入记录不存在

    // 发送验证码
    public static final String EMAIL_IS_REGISTERED = "EMAIL_IS_REGISTERED"; // 邮箱已经注册
    public static final String EMAIL_NOT_REGISTERED = "EMAIL_NOT_REGISTERED"; // 电邮未注册
    public static final String AUDITING_USER = "AUDITING_USER"; // 用户审核中
    public static final String NOT_SUPPORT_BUSSINESS_TYPE = "NOT_SUPPORT_BUSSINESS_TYPE"; // 不支持的业务类型
    public static final String SEND_SMS_CODE_EXCEPTION = "SEND_SMS_CODE_EXCEPTION"; // 发送验证码异常
    public static final String SEND_SMS_CODE_ERROR = "SEND_SMS_CODE_ERROR"; // 发送验证码失败
    public static final String INVALIDATE_VERIFY_CODE_PLEASE_RESEND  = "INVALIDATE_VERIFY_CODE_PLEASE_RESEND";// 验证码失效，请重新发送
    public static final String VERIFY_CODE_ERROR = "VERIFY_CODE_ERROR"; // 验证码不正确

    // 登录提示
    public static final String LOGIN_PASSWORD_CANNOT_BE_EMPTY = "LOGIN_PASSWORD_CANNOT_BE_EMPTY"; // 登录密码不能为空
    public static final String VERIFY_CODE_CANNOT_BE_EMPTY = "VERIFY_CODE_CANNOT_BE_EMPTY"; // 验证码不能为空
    public static final String LOGIN_ACCOUNT_FORMAT_ERROR = "LOGIN_ACCOUNT_FORMAT_ERROR"; // 登录账号格式不正确
    public static final String NOT_EXISTS_USER = "NOT_EXISTS_USER"; // 用户不存在
    public static final String LOGIN_PASSWORD_ERROR = "LOGIN_PASSWORD_ERROR"; // 登录密码错误

    public static final String LOGIN_PASSWORD_FAILED = "LOGIN_PASSWORD_FAILED"; // 密码登录失败
    public static final String INVALID_USER = "INVALID_USER";  // 无效用户


    // 找回密码
    public static final String NOT_EXISTS_EMAIL = "NOT_EXISTS_EMAIL"; // 电邮不存在

    // 机构
    public static final String NOT_EXIST_ORG = "NOT_EXIST_ORG";  // 机构不存在
    public static final String INVALID_ORG = "INVALID_ORG"; // 无效机构
    public static final String ORG_EXIST_USER = "ORG_EXIST_USER";

    // 文字资源
    public static final String INSERT_TEXT_RESOURCE_FAILED = "INSERT_TEXT_RESOURCE_FAILED"; // 新增文字资源失败
    public static final String UPDATE_TEXT_RESOURCE_FAILED = "UPDATE_TEXT_RESOURCE_FAILED"; // 修改文字资源失败
    public static final String DELETE_TEXT_RESOURCE_FAILED = "DELETE_TEXT_RESOURCE_FAILED"; // 删除失败

    // 机构管理
    public static final String PLEASE_SELECT_HOTEL_GROUP_BRAND = "PLEASE_SELECT_HOTEL_GROUP_BRAND"; // 至少选择一个品牌权限
    public static final String CONTACT_EMAIL_INVALIDATED = "CONTACT_EMAIL_INVALIDATED"; // 机构联系电邮不正确
    public static final String FINANCIAL_CONTACT_NAME_CANNOT_BE_EMPTY = "FINANCIAL_CONTACT_NAME_CANNOT_BE_EMPTY"; // 机构财务联系人不能为空
    public static final String FINANCIAL_CONTACT_MOBILE_CANNOT_BE_EMPTY = "FINANCIAL_CONTACT_MOBILE_CANNOT_BE_EMPTY"; // 机构财务联系人不能为空
    public static final String FINANCIAL_CONTACT_EMAIL_INVALIDATED = "FINANCIAL_CONTACT_EMAIL_INVALIDATED"; // 机构财务联系人不能为空
    public static final String EXIST_THE_SAME_ORG_NAME = "EXIST_THE_SAME_ORG_NAME"; // 存在相同名称的机构
    public static final String ADD_ORG_FAILED_DUE_TO_NULL_HOTEL_ID = "ADD_ORG_FAILED_DUE_TO_NULL_HOTEL_ID"; // 酒店id为空新增机构失败
    public static final String HOTEL_EXIST_RELATED_ORG = "HOTEL_EXIST_RELATED_ORG"; //酒店已经存在关联机构
    public static final String CANNOT_VIEW_OTHER_ORG_INFO = "CANNOT_VIEW_OTHER_ORG_INFO"; // 不能查看其他机构信息
    public static final String CANNOT_UPDATE_PLATFORM_ORG = "CANNOT_UPDATE_PLATFORM_ORG"; // 平台机构不能编辑
    public static final String ORG_MUST_BE_HOTEL_GROUP = "ORG_MUST_BE_HOTEL_GROUP"; // 机构必须为酒店集团
    public static final String ORG_MUST_BE_HOTEL = "ORG_MUST_BE_HOTEL"; // 机构必须为酒店
    public static final String HOTEL_INFO_IS_NULL = "HOTEL_INFO_IS_NULL"; //酒店信息为空
    public static final String HOTEL_ID_IS_NOT_NUMBER = "HOTEL_ID_IS_NOT_NUMBER"; // 酒店ID不为数字
    public static final String HOTEL_ID_HAS_RELATED_ORG = "HOTEL_ID_HAS_RELATED_ORG"; // 酒店ID已经关联机构
    public static final String ORG_CONTACT_NAME_CANNOT_BE_EMPTY = "ORG_CONTACT_NAME_CANNOT_BE_EMPTY"; //机构联系人不能为空
    public static final String CONTACT_MOBILE_CANNOT_BE_EMPTY = "CONTACT_MOBILE_CANNOT_BE_EMPTY"; //联系电话不能为空
    public static final String HOTEL_ID_NOT_EXIST = "HOTEL_ID_NOT_EXIST"; // 酒店ID不存在
    public static final String ADD_ORG_FAILED = "ADD_ORG_FAILED"; // 新增机构失败
    public static final String HOTEL_NOT_EXIST = "HOTEL_NOT_EXIST"; // 酒店不存在
    public static final String HOTEL_ID_INVALID = "HOTEL_ID_INVALID"; // 酒店 ID 无效
    public static final String HOTEL_NOT_BELONG_HOTEL_GROUP = "HOTEL_NOT_BELONG_HOTEL_GROUP"; // 酒店不属于酒店集团

    // 上传
    public static final String UPLOAD_FILE_CANNOT_BE_NULL = "UPLOAD_FILE_CANNOT_BE_NULL"; // 上传文件不能为空
    public static final String UPLOAD_FILE_FILED = "UPLOAD_FILE_FILED"; // 上传文件失败
    public static final String UPLOAD_FILE_SIZE_LIMIT = "UPLOAD_FILE_SIZE_LIMIT"; // 上传文件大小限制
    public static final String UPLOAD_FILE_BUSSINESS_TYPE_ERROR = "UPLOAD_FILE_BUSSINESS_TYPE_ERROR"; //上传业务类型不能为空

    // 用户
    public static final String EXIST_THE_SAME_USER_EMAIL = "EXIST_THE_SAME_USER_EMAIL"; // 存在相同用户邮箱
    public static final String CANNOT_UPDATE_OTHER_ORG_USER = "CANNOT_UPDATE_OTHER_ORG_USER"; // 不能操作其他机构用户

    public static final String USER_EMAIL_EXIST = "USER_EMAIL_EXIST"; // 用户电邮已经存在

    // 项目
    public static final String PROJECT_NOT_EXIST = "PROJECT_NOT_EXIST"; // 项目不存在
    public static final String THRID_BID_END_TIME_CANNOT_ARTER_BID_END_TIME = "THRID_BID_END_TIME_CANNOT_ARTER_BID_END_TIME"; // 报名开始时间不能晚于第三轮结束时间
    public static final String THRID_BID_END_TIME_CANNOT_BEFORE_BID_START_TIME = "THRID_BID_END_TIME_CANNOT_BEFORE_BID_START_TIME"; // 报名开始时间不能晚于第三轮开始时间
    public static final String SECOND_BID_END_TIME_RANG_UNPASS_VALIDATE = "SECOND_BID_END_TIME_RANG_UNPASS_VALIDATE"; // 第二轮结束时间范围校验不通过
    public static final String FIRST_BID_END_TIME_RANG_UNPASS_VALIDATE = "FIRST_BID_END_TIME_RANG_UNPASS_VALIDATE"; // 第一轮结束时间范围检查不通过
    public static final String FIRST_BID_START_END_TIME_UNPASS_VALIDATE = "FIRST_BID_START_END_TIME_UNPASS_VALIDATE"; // 第一轮报价时间输入错误
    public static final String SECOND_BID_START_AND_FIRST_END_TIME_UNPASS_VALIDATE = "SECOND_BID_START_AND_FIRST_END_TIME_UNPASS_VALIDATE";
    public static final String SECOND_BID_START_AND_END_TIME_UNPASS_VALIDATE = "SECOND_BID_START_AND_END_TIME_UNPASS_VALIDATE";
    public static final String SECOND_BID_END_TIME_AND_THIRD_BID_START_TIME_UNPASS_VALIDATE = "SECOND_BID_END_TIME_AND_THIRD_BID_START_TIME_UNPASS_VALIDATE";
    public static final String THIRD_BID_START_END_TIME_UNPASS_VALIDATE = "THIRD_BID_START_END_TIME_UNPASS_VALIDATE";
    public static final String ORG_CANNOT_ADD_PROJECT = "ORG_CANNOT_ADD_PROJECT"; // 机构不能添加项目
    public static final String CANNOT_UPDATE_OTHER_ORG_PROJECT = "CANNOT_UPDATE_OTHER_ORG_PROJECT"; // 机构不能修改其他机构项目
    public static final String UPDATE_PROJECT_STATE_PARAMETER_INVALIDATE = "UPDATE_PROJECT_STATE_PARAMETER_INVALIDATE";// 修改项目状态参数不可用
    public static final String BIDDING_COMPLETED_FAILED_DUE_TO_HAS_NEW_OR_UNDER_NEGOTIATION_BID = "BIDDING_COMPLETED_FAILED_DUE_TO_HAS_NEW_OR_UNDER_NEGOTIATION_BID"; // 请先处理完新标及议价状态的报价才能终止签约

    public static final String PROJECT_ALREADY_THE_SAME_STATE = "PROJECT_ALREADY_THE_SAME_STATE";// 项目已经是相同状态
    public static final String PROJECT_CUSTOM_TEND_STRATEGY_COUNT_EXCEED_LIMIT = "PROJECT_CUSTOM_TEND_STRATEGY_COUNT_EXCEED_LIMIT"; // 项目自定义策略数量超出限制
    public static final String PROJECT_CUSTOM_TEND_STRATEGY_NAME_EXIST = "PROJECT_CUSTOM_TEND_STRATEGY_NAME_EXIST"; // 项目自定义策略名称已存在
    public static final String PROJECT_TEND_STRATEGY_LIMIT_PRICE_IS_NULL = "PROJECT_TEND_STRATEGY_LIMIT_PRICE_IS_NULL"; // 报价范围为空
    public static final String PROJECT_TEND_STRATEGY_PRICE_INVALID = "PROJECT_TEND_STRATEGY_PRICE_INVALID"; // 报价范围错误
    public static final String PROJECT_TEND_MAX_NOT_APPLICABLE_DAY_IS_NULL = "PROJECT_TEND_MAX_NOT_APPLICABLE_DAY_IS_NULL"; // 酒店报价中产品不适用日期数总和不能超过天数必填
    public static final String PROJECT_TEND_MAX_NOT_APPLICABLE_DAY_IS_INVALID = "PROJECT_TEND_MAX_NOT_APPLICABLE_DAY_IS_INVALID"; // 酒店报价中产品不适用日期数总和不能超过天数无效
    public static final String PROJECT_TEND_MAX_ROOM_TYPE_COUNT_IS_NULL = "PROJECT_TEND_MAX_ROOM_TYPE_COUNT_IS_NULL"; // 酒店报价最多不超过房档个数必填
    public static final String PROJECT_TEND_MAX_ROOM_TYPE_COUNT_IS_INVALID = "PROJECT_TEND_MAX_ROOM_TYPE_COUNT_IS_INVALID"; // 酒店报价最多不超过房档个数无效
    public static final String PROJECT_TEND_MAX_SEASON_DAY_IS_NULL = "PROJECT_TEND_MAX_SEASON_DAY_IS_NULL"; // 最大 Season 日期数不能为空
    public static final String PROJECT_TEND_MAX_SEASON_DAY_IS_INVALID = "PROJECT_TEND_MAX_SEASON_DAY_IS_INVALID"; // 最大 Season 日期数无效
    public static final String HOTEL_HAS_BID_CANNOT_DELETE = "HOTEL_HAS_BID_CANNOT_DELETE"; // 酒店已提供报价，无法删除
    public static final String GENERATE_PROJECT_HISTORY_STAT_NOT_FINISH = "GENERATE_PROJECT_HISTORY_STAT_NOT_FINISH"; // 项目上一次统计未完成
    public static final String GENERATE_PROJECT_RECOMMEND_LEVEL_NOT_FINISH = "GENERATE_PROJECT_RECOMMEND_LEVEL_NOT_FINISH"; // 项目上一次生成推荐等级未完成
    public static final String GENERATE_PROJECT_HISTORY_STAT_ERROR = "GENERATE_PROJECT_HISTORY_STAT_ERROR"; // 生成项目 POI 统计失败
    public static final String GENERATE_PROJECT_RECOMMEND_LEVEL_ERROR = "GENERATE_PROJECT_RECOMMEND_LEVEL_ERROR"; // 更新推荐酒店列表错误
    public static final String PROJECT_INTENT_HOTEL_SALES_CONTACT_EMAIL = "PROJECT_INTENT_HOTEL_SALES_CONTACT_EMAIL"; // 酒店销售联系人邮箱不能为空
    public static final String PROJECT_INTENT_HOTEL_DISTRIBUTOR_CONTACT_NAME = "PROJECT_INTENT_HOTEL_DISTRIBUTOR_CONTACT_NAME"; //  企业(分销商)跟进人姓名不能为空
    public static final String PROJECT_INTENT_HOTEL_DISTRIBUTOR_CONTACT_ID = "PROJECT_INTENT_HOTEL_SALES_CONTACT_EMAIL"; //  企业(分销商)企业(分销商)跟进人id(招投标项目时指派的)不能为空
    public static final String PROJECT_INTENT_HOTEL_PLATFORM_CONTACT_NAME = "PROJECT_INTENT_HOTEL_PLATFORM_CONTACT_NAME"; //  平台跟进人跟进人跟进人姓名不能为空
    public static final String PROJECT_INTENT_HOTEL_PLATFORM_CONTACT_UID = "PROJECT_INTENT_HOTEL_SALES_CONTACT_EMAIL"; //  平台跟进人跟进人跟进人id不能为空
    public static final String PROJECT_STRATEGY_OPTION_CANNOT_EMPTY = "PROJECT_STRATEGY_OPTION_CANNOT_EMPTY"; // 自定义采购策略选项不能为空


    // POI
    public static final String POI_ORG_ID_ILLEGAL = "POI_ORG_ID_ILLEGAL"; // 机构ID不合法
    public static final String POI_NOT_EXIST = "POI_NOT_EXIST"; // POI 不存在
    public static final String POI_NO_PERMISSION = "POI_NO_PERMISSION"; // 没有权限操作该 POI
    public static final String POI_NAME_VALID = "POI_NAME_EMPTY"; // POI 名称不能为空
    public static final String POI_ADDRESS_VALID = "POI_NAME_CAN_NOT_BE_EMPTY"; // POI 名称不能为空
    public static final String POI_LONGITUDE_INVALID = "POI_LONGITUDE_EMPTY"; // POI 经度不能为空
    public static final String POI_LATITUDE_INVALID = "POI_LATITUDE_EMPTY"; // POI 名称不能为空
    public static final String POI_CITY_NOT_EXIST = "POI_CITY_NOT_EXIST"; // POI 城市不存在

    // 酒店
    public static final String ROOM_NIGHT_COUNT_INVALID = "ROOM_NIGHT_COUNT_INVALID"; // 间夜数数字无效
    public static final String AMOUNT_INVALID = "AMOUNT_INVALID"; // 金额无效

    // 国家省市
    public static final String CITY_NOT_EXIST = "CITY_NOT_EXIST"; // 城市不存在

    // 导入
    public static final String IMPORT_EXCEL_TEMPLATE_NOT_MATCH = "IMPORT_EXCEL_TEMPLATE_NOT_MATCH"; //导入模板不匹配

    public static final String CURRENCY_CODE_EXIST = "CURRENCY_CODE_EXIST"; // 币种已存在
    public static final String CURRENCY_CODE_NOT_EXIST = "CURRENCY_CODE_NOT_EXIST"; // 币种不存在

    public static final String USD_CURRENCY_CAN_NOT_UPDATE = "USD_CURRENCY_CAN_NOT_UPDATE"; // 美元基准货币汇率不允许修改 1usd = 1 usd


    // 酒店集团
    public static final String CANNOT_FOUND_RELATED_HOTEL_GROUP_BY_ORG = "CANNOT_FOUND_HOTEL_GROUP_BY_ORG"; // 没有找到关联酒店集团
    public static final String CANNOT_FOUND_RELATED_HOTEL_BRAND_BY_ORG = "CANNOT_FOUND_HOTEL_BRAND_BY_ORG"; // 没有找到关联酒店集团品牌
    public static final String HOTEL_INTENT_GROUP_NOT_EXIST = "HOTEL_INTENT_GROUP_NOT_EXIST";
    public static final String PERMISSION_DENIED = "PERMISSION_DENIED"; // 没有操作权限

    // 酒店报价

    public static final String SYNC_HOTEL_INFO_FAILED = "SYNC_HOTEL_INFO_FAILED"; //同步酒店信息失败
    public static final String HOTEL_ALREADY_BID_CANNOT_REPEAT_BID = "HOTEL_ALREADY_BID_CANNOT_REPEAT_BID"; //酒店已经报价，不可重复报价
    public static final String CANNOT_BID_DUE_TO_ALREADY_SUBMIT_BID = "CANNOT_BID_DUE_TO_ALREADY_SUBMIT_BID"; //酒店已经是新标，不能继续报价
    public static final String ADD_OR_UPDATE_HOTEL_PRICE_LEVEL_FAILED = "ADD_OR_UPDATE_HOTEL_PRICE_LEVEL_FAILED"; // 新增或者修改价格房档失败
    public static final String ADD_OR_UPDATE_HOTEL_PRICE_GROUP_FAILED = "ADD_OR_UPDATE_HOTEL_PRICE_GROUP_FAILED"; // 新增或者修改价格组失败
    public static final String ADD_OR_UPDATE_PRICE_APPLICABLE_ROOM_FAILED = "ADD_OR_UPDATE_PRICE_APPLICABLE_ROOM_FAILED"; //新增或者修改房档房型失败
    public static final String ADD_OR_UPDATE_HOTEL_PRICE_FAILED = "ADD_OR_UPDATE_HOTEL_PRICE_FAILED"; // 新增或者修改价格组败
    public static final String ROOM_TYPE_EXIST_IN_OTHER_PRICE_LEVEL = "ROOM_TYPE_EXIST_IN_OTHER_PRICE_LEVEL"; // 房型存在其他房档

    public static final String CANNOT_BID_DUE_TO_AFTER_BID_END_DATE_TIME = "CANNOT_BID_DUE_TO_AFTER_BID_END_DATE_TIME"; // 报价时间已过，不能报价
    public static final String ADD_HOTEL_PRICE_LEVEL_FAILED = "ADD_HOTEL_PRICE_LEVEL_FAILED"; // 新增价格房档失败
    public static final String ADD_HOTEL_PRICE_GROUP_FAILED = "ADD_HOTEL_PRICE_GROUP_FAILED"; // 新增价格组失败
    public static final String ADD_HOTEL_PRICE_GROUP_FAILED_DUE_TO_NULL_HOTEL_PRICE = "ADD_HOTEL_PRICE_GROUP_FAILED_DUE_TO_NULL_HOTEL_PRICE"; // 新增价格组失败
    public static final String ADD_HOTEL_PRICE_LEVEL_FAILED_DUE_TO_MAX_LEVEL_COUNT_LIMIT = "ADD_HOTEL_PRICE_LEVEL_FAILED_DUE_TO_MAX_LEVEL_COUNT_LIMIT"; //新增房档失败，超过最大房档限制
    public static final String ADD_PRICE_APPLICABLE_ROOM_FAILED = "ADD_PRICE_APPLICABLE_ROOM_FAILED"; //新增房档房型失败
    public static final String ADD_HOTEL_PRICE_FAILED = "ADD_HOTEL_PRICE_FAILED"; // 新增修改价格组败
    public static final String CANNOT_FOUND_PROJECT_INVITED_HOTEL_GROUP = "CANNOT_FOUND_PROJECT_INVITED_HOTEL_GROUP"; // 不能找到项目邀请酒店集团
    public static final String CANNOT_FOUND_PROJECT_INTENT_HOTEL = "CANNOT_FOUND_PROJECT_INTENT_HOTEL"; //不能找到酒店意向报价

    // 酒店报价
    public static final String CANNOT_SUBMIT_BID_DUE_TO_BID_WINNING = "CANNOT_SUBMIT_BID_DUE_TO_BID_WINNING"; // 已中签状态不允许重新提交报价
    public static final String CANNOT_SUBMIT_BID_DUE_TO_BID_REJECTED = "CANNOT_SUBMIT_BID_DUE_TO_BID_REJECTED"; // 已否决状态不允许重新提交报价
    public static final String CANNOT_SUBMIT_BID_DUE_TO_REJECT_NEGOTIATION = "CANNOT_SUBMIT_BID_DUE_TO_REJECT_NEGOTIATION"; // 保持原价状态不允许重新提交报价
    public static final String CANNOT_SUBMIT_BID_DUE_TO_HOTEL_GROUP_APPROVE_SATUS_WAITING = "CANNOT_SUBMIT_BID_DUE_TO_HOTEL_GROUP_APPROVE_SATUS_WAITING"; // 待审核报价不允许重新提交报价
    public static final String CANNOT_SUBMIT_BID_DUE_TO_UNAPPLICABLE_DAYS_LIMIT = "CANNOT_SUBMIT_BID_DUE_TO_UNAPPLICABLE_DAYS_LIMIT"; // 不可以日期超过报价限制
    public static final String CANNOT_SUBMIT_BID_DUE_TO_SEASON_DAYS_LIMIT = "CANNOT_SUBMIT_BID_DUE_TO_SEASON_DAYS_LIMIT"; // Season总日期超过限制
    public static final String EXIST_PROJECT_HOTEL_BID_STRATEGY = "EXIST_PROJECT_HOTEL_BID_STRATEGY"; // 已经存在报价策略主键
    public static final String PROJECT_NOT_INVITE_HOTEL_GROUP = "PROJECT_NOT_INVITE_HOTEL_GROUP"; // 项目没有邀请
    public static final String BID_NOT_EXIST = "BID_NOT_EXIST"; //报价不存在
    public static final String HOTEL_GROUP_NO_PERMISSION = "HOTEL_GROUP_NO_PERMISSION"; //酒店集团没有权限查看
    public static final String DISTRIBUTOR_NO_PERMISSION = "DISTRIBUTOR_NO_PERMISSION"; // 企业没有权限
    public static final String HOTEL_NO_PERMISSION = "HOTEL_NO_PERMISSION"; // 酒店没有权限
    public static final String PROJECT_POI_NOT_EXIST = "PROJECT_POI_NOT_EXIST"; // 项目POI不存在
    public static final String QUERY_TIME_OUT = "QUERY_TIME_OUT";
    public static final String NOTIFY_BIDDER_CONTAINS_NO_BID_HOTEL = "NOTIFY_BIDDER_CONTAINS_NO_BID_HOTEL"; // 列表中包含未报价酒店，通知失败
    public static final String USER_PERMISSION_EXIST = "USER_PERMISSION_EXIST";
    public static final String HOTEL_PRICE_GROUP_WEEK_DAY_REPEAT = "HOTEL_PRICE_GROUP_WEEK_DAY_REPEAT";
    public static final String CANNOT_SUBMIT_BID_DUE_TO_HOTEL_PRICE_LEVEL_IS_EMPTY = "CANNOT_SUBMIT_BID_DUE_TO_HOTEL_PRICE_LEVEL_IS_EMPTY"; // 空价房档价格,不能报价，
    public static final String CANNOT_SUBMIT_BID_DUE_TO_HOTEL_PRICE_GROUP_IS_EMPTY = "CANNOT_SUBMIT_BID_DUE_TO_HOTEL_PRICE_GROUP_IS_EMPTY"; //  空价格组,不能报价
    public static final String CANNOT_SUBMIT_BID_DUE_TO_HOTEL_PRICE_IS_EMPTY = "CANNOT_SUBMIT_BID_DUE_TO_HOTEL_PRICE_IS_EMPTY"; // 空价格,不能报价
    public static final String CANNOT_SUBMIT_BID_DUE_TO_EXIST_NO_SEASON_DATE_PRICE = "CANNOT_SUBMIT_BID_DUE_TO_EXIST_NO_SEASON_DATE_PRICE"; // 空价格,不能报价
    public static final String CANNOT_SUBMIT_BID_DUE_TO_CUSTOM_STRATEGY_IS_EMPTY = "CANNOT_SUBMIT_BID_DUE_TO_CUSTOM_STRATEGY_IS_EMPTY"; // 空价格,不能报价

    public static final String BID_STATE_NOT_UNDER_NEGOTIATION = "BID_STATE_NOT_UNDER_NEGOTIATION";
    public static final String INVALIDATE_BID_APPROVE_STATUS = "INVALIDATE_BID_APPROVE_STATUS";
    public static final String CANNOT_UPDATE_BID_DUE_TO_BID_STATE_NOT_UNDER_NEGOTIATION = "CANNOT_UPDATE_BID_DUE_TO_BID_STATE_NOT_UNDER_NEGOTIATION"; //  当前报价状态下不允许修改报价
    public static final String CANNOT_UPDATE_BID_DUE_TO_BID_ORG_NOT_HOTEL_GROUP = "CANNOT_UPDATE_BID_DUE_TO_BID_ORG_NOT_HOTEL_GROUP"; //  当前报价所属机构不是酒店集团, 不允许修改报价
    public static final String HOTEL_NAME_CANNOT_BE_EMPTY = "HOTEL_NAME_CANNOT_BE_EMPTY"; // 酒店名称不能为空
    public static final String CURRENCY_CODE_CANNOT_BE_EMPTY = "CURRENCY_CODE_CANNOT_BE_EMPTY"; // 币种不能为空
    public static final String ROOM_TYPE_1_DEFINE_CANNOT_BE_EMPTY = "ROOM_TYPE_1_DEFINE_CANNOT_BE_EMPTY"; // 第一档房型不可为空
    public static final String CONTRACT_START_DATE_CANNOT_BE_EMPTY = "CONTRACT_START_DATE_CANNOT_BE_EMPTY"; // 合同开始时间不可为空
    public static final String CONTRACT_END_DATE_CANNOT_BE_EMPTY = "CONTRACT_END_DATE_CANNOT_BE_EMPTY"; // 合同结束时间不可为空
    public static final String CONTRACT_START_DATE_CANNOT_AFTER_END_DATE = "CONTRACT_START_DATE_CANNOT_AFTER_END_DATE"; // 合同开始时间不能大于结束时间
    public static final String CONTRACT_DATE_FORMAT_INVALID = "CONTRACT_DATE_FORMAT_INVALID"; // 合同开始结束时间格式错误
    public static final String ROOM_TYPE_1_SGL_PRICE_CANNOT_BE_EMPTY = "ROOM_TYPE_1_SGL_PRICE_CANNOT_BE_EMPTY"; // 房档1单人报价不可为空
    public static final String ROOM_TYPE_1_DBL_PRICE_CANNOT_BE_EMPTY = "ROOM_TYPE_1_DBL_PRICE_CANNOT_BE_EMPTY"; // 房档1双人报价不可为空
    public static final String RATE_TYPE_INVALID = "RATE_TYPE_INVALID"; // 是否LRA,必录项，值必须是LRA,NLRA
    public static final String CANCEL_POLICY_CANNOT_BE_EMPTY = "CANCEL_POLICY_CANNOT_BE_EMPTY"; // 取消条款不能为空
    public static final String IS_INCLUDE_BREAKFAST_INVALID = "IS_INCLUDE_BREAKFAST_INVALID"; // 是否含早必须录入,值只能是Y N
    public static final String SEASON_START_END_DATE_FORMAT_INVALID = "SEASON_START_END_DATE_FORMAT_INVALID"; // 淡旺季{0}开始时间-结束时间格式错误
    public static final String SEASON_START_DATE_NOT_IN_CONTRACT_DATE_RANGE = "SEASON_START_DATE_NOT_IN_CONTRACT_DATE_RANGE"; // 淡旺季{0}开始时间没在协议日期内
    public static final String SEASON_END_DATE_NOT_IN_CONTRACT_DATE_RANGE = "SEASON_END_DATE_NOT_IN_CONTRACT_DATE_RANGE"; // 淡旺季{0}结束时间没在协议日期内
    public static final String SEASON_1_AND_2_DATE_CROSS = "SEASON_1_AND_2_DATE_CROSS"; // 淡旺季日期存在交叉
    public static final String CUSTOM_STRATEGY_INVALID = "CUSTOM_STRATEGY_INVALID"; // 自定义策略{0}无效
    public static final String BD_START_END_DATE_FORMAT_INVALID = "BD_START_END_DATE_FORMAT_INVALID"; // 价格不适用时段{0}开始或结束时间格式错误
    public static final String BD_START_DATE_NOT_IN_CONTRACT_DATE_RANGE = "BD_START_DATE_NOT_IN_CONTRACT_DATE_RANGE"; // 价格不适用时段{0}开始日期不能超过协议开始日期
    public static final String BD_END_DATE_NOT_IN_CONTRACT_DATE_RANGE = "BD_END_DATE_NOT_IN_CONTRACT_DATE_RANGE"; // 价格不适用时段{0}结束日期不能超过协议结束日期
    public static final String BD_DATE_CROSS = "BD_DATE_CROSS"; // 价格不适用时段{0}和价格不适用时段{0}日期存在交叉
    public static final String VATGSTRM_INCLUDE_INVALID = "VATGSTRM_INCLUDE_INVALID"; // 是否含客房增值税字段不合法
    public static final String VATGSTRM_UOM_INVALID = "VATGSTRM_UOM_INVALID"; // 客房增值税收费方式字段不合法
    public static final String VATGSTFB_INCLUDE_INVALID = "VATGSTFB_INCLUDE_INVALID"; // 是否含餐饮增值税字段不合法
    public static final String VATGSTFB_UOM_INVALID = "VATGSTFB_UOM_INVALID"; // 餐饮增值税收费方式字段不合法
    public static final String SERVICE_INCLUDE_INVALID = "SERVICE_INCLUDE_INVALID"; // 是否含服务费字段不合法
    public static final String SERVICE_UOM_INVALID = "SERVICE_UOM_INVALID"; // 服务费收费方式字段不合法
    public static final String OCC_INCLUDE_INVALID = "OCC_INCLUDE_INVALID"; // 是否含占用费字段不合法
    public static final String OCC_UOM_INVALID = "OCC_UOM_INVALID"; // 占用费收费方式字段不合法
    public static final String OTHER_TX_FEE_1_INCLUDE_INVALID = "OTHER_TX_FEE_1_INCLUDE_INVALID"; // 是否含其他税费1字段不合法
    public static final String OTHER_TX_FEE_1_UOM_INVALID = "OTHER_TX_FEE_1_UOM_INVALID"; // 其他税费1收费方式字段不合法
    public static final String OTHER_TX_FEE_2_INCLUDE_INVALID = "OTHER_TX_FEE_2_INCLUDE_INVALID"; // 是否含其他税费2字段不合法
    public static final String OTHER_TX_FEE_2_UOM_INVALID = "OTHER_TX_FEE_2_UOM_INVALID"; // 其他税费2收费方式字段不合法
    public static final String OTHER_TX_FEE_3_INCLUDE_INVALID = "OTHER_TX_FEE_3_INCLUDE_INVALID"; // 是否含其他税费3字段不合法
    public static final String OTHER_TX_FEE_3_UOM_INVALID = "OTHER_TX_FEE_3_UOM_INVALID"; // 其他税费3收费方式字段不合法
    public static final String ROOM_TYPE_ID_REPEAT = "ROOM_TYPE_ID_REPEAT"; // 房档{0}存在重复的房型 ID
    public static final String ROOM_TYPE_NOT_EXIST = "ROOM_TYPE_NOT_EXIST"; // 房档{0}存在酒店不包含的房型
    public static final String HOTEL_NOT_EXIST_ORG = "HOTEL_NOT_EXIST_ORG"; // 酒店不存在机构

    public static final String CANNOT_UPDATE_LOCKED_PRICE = "CANNOT_UPDATE_LOCKED_PRICE"; // 不能更新锁定价格

    // 导入报价任务
    public static final String IMPORT_DATA_CANNOT_BE_EMPTY = "IMPORT_DATA_CANNOT_BE_EMPTY"; // 导入数据不能为空
    public static final String IMPORT_DATA_EXCEED_LIMIT = "IMPORT_DATA_EXCEED_LIMIT"; // 导入数据超过限制
    public static final String IMPORT_BID_TASK_NOT_EXIST = "IMPORT_BID_TASK_NOT_EXIST"; // 导入报价任务不存在
    public static final String IMPORT_BID_TASK_CANNOT_BE_UPDATE = "IMPORT_BID_TASK_CANNOT_BE_UPDATE"; // 报价任务已生成报价，不允许修改
    public static final String IMPORT_BID_TASK_VALIDATE_NO_PASSED = "IMPORT_BID_TASK_VALIDATE_NO_PASSED"; // 报价任务校验不通过
    public static final String BID_TASK_HAS_GENERATED = "BID_TASK_HAS_GENERATED"; // 报价任务{0}已经生成报价
    public static final String IMPORT_BID_TASK_NOT_GENERATED = "IMPORT_BID_TASK_NOT_GENERATED"; // 报价任务未生成报价
    public static final String PROJECT_INTENT_HOTEL_NOT_EXIST = "PROJECT_INTENT_HOTEL_NOT_EXIST"; // 未找到任务对应的报价


    public static final String IMPORT_ROW_COLUMN_DATA_CANNOT_BE_EMPTY = "IMPORT_ROW_COLUMN_DATA_CANNOT_BE_EMPTY"; //导入行对应列名不能为空
    public static final String IMPORT_ROW_HAS_DUPLICATE_HOTEL_BREAKFAST_DATA = "IMPORT_ROW_HAS_DUPLICATE_HOTEL_BREAKFAST_DATA"; //导入用重复酒店早餐数据
    public static final String IMPORT_ROW_DATA_INVALIDATED_1 = "IMPORT_ROW_DATA_INVALIDATED_1"; //导入行数据不合法
    public static final String IMPORT_ROW_DATA_INVALIDATED_2 = "IMPORT_ROW_DATA_INVALIDATED_2";
    public static final String IMPORT_ROW_EXCEPTION = "IMPORT_ROW_EXCEPTION";
    public static final String IMPORT_DATA_EXCEPTION = "IMPORT_DATA_EXCEPTION";

}
