package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;


@ApiModel("删除文件请求")
@Getter
@Setter
public class DeleteFileDtoRequest extends BaseVO {

    //文件id
    @ApiModelProperty("文件id")
    @NotNull
    private Long fileId;

}
