package com.fangcang.grfp.api.controller.currencyexchangerate.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@ApiModel("新增 美元兑新币种汇率")
@Getter
@Setter
public class AddCurrencyExchangeRequest extends BaseVO {

    @ApiModelProperty("新币种code")
    @NotBlank
    private String newCurrencyCode;

    @ApiModelProperty("新币种名称")
    private String newCurrencyName;

    @ApiModelProperty("美元兑新币种汇率")
    @NotNull
    private BigDecimal exchangeRate;

    @ApiModelProperty("排序")
    private Integer displayOrder;


}
