package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 酒店
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_hotel")
public class HotelEntity extends BaseVO {


    /**
     * 酒店ID
     */
    @TableId(value = "hotel_id", type = IdType.ASSIGN_ID)
    private Long hotelId;

    /**
     * 国家编号
     */
    @TableField("country_code")
    private String countryCode;

    /**
     * 省/州/邦编码
     */
    @TableField("province_code")
    private String provinceCode;

    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 英文名称
     */
    @TableField("name_en_us")
    private String nameEnUs;

    /**
     * 中文名称
     */
    @TableField("name_zh_cn")
    private String nameZhCn;

    /**
     * 地址（英文）
     */
    @TableField("address_en_us")
    private String addressEnUs;

    /**
     * 地址（中文）
     */
    @TableField("address_zh_cn")
    private String addressZhCn;

    /**
     * 介绍（英文）
     */
    @TableField("introduce_en_us")
    private String introduceEnUs;

    /**
     * 介绍（中文）
     */
    @TableField("introduce_zh_cn")
    private String introduceZhCn;

    /**
     * 电话
     */
    @TableField("telephone")
    private String telephone;

    /**
     * 电邮
     */
    @TableField("email")
    private String email;

    /**
     * 星级
     */
    @TableField("hotel_star")
    private String hotelStar;

    /**
     * 酒店评分
     */
    @TableField("rating")
    private String rating;

    /**
     * 开业日期
     */
    @TableField("opening_date")
    private Date openingDate;

    /**
     * 装修日期
     */
    @TableField("fitment_date")
    private Date fitmentDate;

    /**
     * 酒店集团ID
     */
    @TableField("hotel_group_id")
    private Long hotelGroupId;

    /**
     * 酒店品牌ID
     */
    @TableField("hotel_brand_id")
    private Long hotelBrandId;

    /**
     * 主图URL地址
     */
    @TableField("main_pic_url")
    private String mainPicUrl;

    /**
     * 酒店入住时间
     */
    @TableField("check_in_time")
    private String checkInTime;

    /**
     * 酒店离店时间
     */
    @TableField("check_out_time")
    private String checkOutTime;

    /**
     * 客房总数
     */
    @TableField("room_num")
    private Integer roomNum;

    /**
     * 是否包含会议室 1:是，0:否
     */
    @TableField("has_meeting_room")
    private Integer hasMeetingRoom;

    /**
     * 是否包含游泳池 1:是，0:否
     */
    @TableField("has_swimming_pool")
    private Integer hasSwimmingPool;

    /**
     * 是否包含健身房 1:是，0:否
     */
    @TableField("has_gym")
    private Integer hasGym;

    /**
     * 是否包含洗衣房 1:是，0:否
     */
    @TableField("has_laundry_room")
    private Integer hasLaundryRoom;

    /**
     * 百度经度
     */
    @TableField("lng_baidu")
    private BigDecimal lngBaidu;

    /**
     * 百度纬度
     */
    @TableField("lat_baidu")
    private BigDecimal latBaidu;

    /**
     * Google经度
     */
    @TableField("lng_google")
    private BigDecimal lngGoogle;

    /**
     * Google纬度
     */
    @TableField("lat_google")
    private BigDecimal latGoogle;

    /**
     * 火星经度
     */
    @TableField("lng_mars")
    private BigDecimal lngMars;

    /**
     * 火星纬度
     */
    @TableField("lat_mars")
    private BigDecimal latMars;

    /**
     * 币种
     */
    @TableField("currency_code")
    private String currencyCode;

    /**
     * 是否有效 1:是，0:否
     */
    @TableField("is_active")
    private Integer isActive;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;


}
