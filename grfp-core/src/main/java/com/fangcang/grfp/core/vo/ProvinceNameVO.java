package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("省份名称信息")
@Getter
@Setter
public class ProvinceNameVO extends BaseVO {


    @ApiModelProperty(value = "国家编码")
    private String countryCode;

    @ApiModelProperty(value = "省份编号")
    private String provinceCode;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "英文名称")
    @JsonIgnore
    private String nameEnUs;
    @ApiModelProperty(value = "中文名称")
    @JsonIgnore
    private String nameZhCn;


}
