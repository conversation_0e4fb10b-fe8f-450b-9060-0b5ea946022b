<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HotelBrandLogMapper">
    <select id="selectLatestByBrandIds" resultType="com.fangcang.grfp.core.entity.HotelBrandLogEntity">
        SELECT t1.*
        FROM t_hotel_brand_log t1
            JOIN (
        SELECT hotel_brand_id, MAX(hotel_brand_log_id) AS max_id
        FROM t_hotel_brand_log
        WHERE hotel_brand_id IN
        <foreach collection="hotelBrandIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY hotel_brand_id
        ) t2 ON t1.hotel_brand_id = t2.hotel_brand_id AND t1.hotel_brand_log_id = t2.max_id
    </select>

    <insert id="batchInsert">
        INSERT INTO t_hotel_brand_log(hotel_brand_id, before_kv_json, after_kv_json, creator)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.hotelBrandId}, #{item.beforeKvJson}, #{item.afterKvJson}, #{item.creator})
        </foreach>
    </insert>
</mapper>
