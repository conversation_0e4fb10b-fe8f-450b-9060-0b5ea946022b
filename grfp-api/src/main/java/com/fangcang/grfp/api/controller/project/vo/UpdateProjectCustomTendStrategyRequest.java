package com.fangcang.grfp.api.controller.project.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(description = "更新自定义采购策略请求")
public class UpdateProjectCustomTendStrategyRequest {

    private static final long serialVersionUID = -1901502293867818999L;

    @ApiModelProperty(value = "自定义采购策略 ID", required = true)
    @NotNull
    private Long customTendStrategyId;

    @ApiModelProperty(value = "是否支持策略: 1-是 0-否", required = true)
    @NotNull
    private Integer supportStrategyName;

}
