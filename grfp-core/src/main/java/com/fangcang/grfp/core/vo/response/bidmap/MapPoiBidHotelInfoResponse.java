package com.fangcang.grfp.core.vo.response.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.response.project.BidHotelInfoQueryResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel("地图POI酒店信息")
@Getter
@Setter
public class MapPoiBidHotelInfoResponse extends BaseVO {

    /**
     * 项目id
     */
    @ApiModelProperty("项目ID")
    private Integer projectId;

    /**
     * 范围内酒店信息
     */
    @ApiModelProperty("范围内酒店信息")
    private List<BidHotelInfoQueryResponse> bidHotelInfoQueryResponseList;

    /**
     * 推荐酒店信息列表
     */
    @ApiModelProperty("推荐酒店信息列表")
    private List<BidHotelInfoQueryResponse> commendHotelInfoQueryResponseList;

    /**
     * 范围内POI信息
     */
    @ApiModelProperty("范围内POI信息")
    private List<ProjectPoiInfoResponse> hotelProjectPoiInfoResponseList;

    /**
     * 3公里统计信息
     */
    @ApiModelProperty("3公里统计信息")
    private QueryProjectHotelBidStatResponse threeQueryProjectHotelBidStatResponse;

    /**
     * 城市同档统计信息
     */
    @ApiModelProperty("城市同档统计信息")
    private QueryProjectHotelBidStatResponse cityTheSameLevelQueryProjectHotelBidStatResponse;

    /**
     * 城市统计信息
     */
    @ApiModelProperty("城市统计信息")
    private QueryProjectHotelBidStatResponse cityQueryProjectHotelBidStatResponse;



}
