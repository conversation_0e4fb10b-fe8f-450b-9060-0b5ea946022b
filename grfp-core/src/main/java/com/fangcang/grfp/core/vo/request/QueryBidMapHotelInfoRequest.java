package com.fangcang.grfp.core.vo.request;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
  地图酒店报价信息
 */
@ApiModel("地图酒店报价信息")
@Getter
@Setter
public class QueryBidMapHotelInfoRequest extends BaseVO {

    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;

    @ApiModelProperty("酒店ID")
    @NotNull
    private Long hotelId;


}
