package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.core.entity.UserPermissionEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.vo.request.userpermission.QueryUserPermissionRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 权限管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface UserPermissionMapper extends BaseMapper<UserPermissionEntity> {


    default List<String> queryPermissions(Integer orgTypeId, String roleCode){
        LambdaQueryWrapper<UserPermissionEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPermissionEntity::getOrgType, orgTypeId)
                .eq(UserPermissionEntity::getRoleCode, roleCode);
        return this.selectList(queryWrapper).stream().map(UserPermissionEntity::getPermission).collect(Collectors.toList());
    }

    Page<UserPermissionEntity> queryUserPermissionPage(IPage<?> page, @Param("query") QueryUserPermissionRequest req);

}
