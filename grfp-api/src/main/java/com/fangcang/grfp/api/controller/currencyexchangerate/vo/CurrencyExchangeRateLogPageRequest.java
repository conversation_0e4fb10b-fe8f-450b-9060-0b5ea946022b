package com.fangcang.grfp.api.controller.currencyexchangerate.vo;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@ApiModel("币种汇率变化记录列表分页查询")
@Getter
@Setter
public class CurrencyExchangeRateLogPageRequest extends PageQuery {

    @ApiModelProperty("币种code")
    @NotBlank
    private String currencyCode;

}
