package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(description = "通知报价人请求")
public class NotifyBidderRequest extends BaseVO {

    @ApiModelProperty(value = "项目意向酒店列表", required = true)
    @NotEmpty(message = "项目意向酒店列表不能为空")
    @Valid
    private List<NotifyBidderItem> projectIntentHotels;

    @Data
    @ApiModel(description = "通知报价人项目")
    public static class NotifyBidderItem {
        
        @ApiModelProperty(value = "项目ID", required = true)
        @NotNull(message = "项目ID不能为空")
        private Long projectId;

        @ApiModelProperty(value = "项目意向酒店ID", required = true)
        @NotNull(message = "项目意向酒店ID不能为空")
        private Integer projectIntentHotelId;

        @ApiModelProperty(value = "报价状态", required = true)
        @NotNull(message = "报价状态不能为空")
        private Integer bidState;
    }
} 