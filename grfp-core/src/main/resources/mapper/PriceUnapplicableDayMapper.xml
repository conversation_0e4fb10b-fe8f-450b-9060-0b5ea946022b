<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.PriceUnapplicableDayMapper">

    <select id="selectVOListByProjectIntentHotelId" resultType="com.fangcang.grfp.core.vo.BidUnApplicableDayVO">
        SELECT
            start_date AS startDate,
            end_date AS endDate
        FROM
            t_price_unapplicable_day
        WHERE
            project_intent_hotel_id = #{projectIntentHotelId}
        ORDER BY price_unapplicable_day_id ASC
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_price_unapplicable_day (project_intent_hotel_id, project_id,  hotel_id, start_date, end_date, creator) VALUES
        <foreach item="item" index="index" collection="list" separator="," >
            (#{item.projectIntentHotelId}, #{item.projectId}, #{item.hotelId},  #{item.startDate}, #{item.endDate}, #{item.creator})
        </foreach>
    </insert>
</mapper>
