<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invitation Email</title>
    <style>
        .content-box img {
            display: block;
            max-width:960px;
            height: auto;
        }
    </style>
</head>

<body style="background: rgb(232, 232, 237);">
<div style="background: rgb(232, 232, 237);width: 100% ;padding: 10px;">
    <div class="content" style="width: 1040px;border-radius: 5px;margin: 20px auto;">
        <div class="headInfo"
             style="height: 64px;background-color: #008489;display: flex;align-items: center;padding-left: 40px;border-radius: 5px 5px 0 0;">
            <div class="tit1"
                 style="font-weight: 900;font-style: normal;font-size: 24px;letter-spacing: 1px;color: #FFFFFF;">
                RFP Invitation
            </div>
        </div>
        <div class="titInfo"
             style="height: 94px;background-color: rgba(235, 244, 255, 1);padding: 20px 40px;font-size: 13px;box-sizing: border-box;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div style="color: #444444;">
                    Dear <span style="color: #0D529C;" th:text="${hotelName}"></span>:
                    <div style="margin-top:0px;text-indent: 26px;">
                        <span style="color: #FF4200;" th:text="${orgName}"></span> cordially invites you to participate in
                        <span style="color:#FF4200;" th:text="${projectName}"></span> bidding.
                        <span th:text="${recommendLevelInfo}"></span>
                    </div>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="margin-right:30px;">
                        <span style="color:gray">Copy</span>
                        <a href="javaScript:0" onclick="copyText()">https://www.agree-ease.com/</a>
                        <span style="color:gray">Open in browser or</span>
                    </div>
                    <div>
                        <a th:href="${goBidUrl}"
                           style="width: 140px;text-decoration:none;height: 40px;line-height: 40px;text-align: center;color: #fff;background-color: rgb(251, 126, 51);border-radius: 3px; cursor: pointer; border: 0px; display: block;">Go to RFP</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="contactInfo"
             style="padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);">
            <div style="font-size: 18px;">
                Tendering Organization Profile
            </div>
            <div style="margin-top:30px;display: flex;align-items: center;justify-content: space-between;">
                <div style="font-size: 13px;">
                    <div style="display:flex;">
                        <div style="width:130px;color: #A1A1A1;">Organization Name</div>
                        <div th:text="${orgName}"></div>
                    </div>
                    <div style="margin-top:20px;display:flex;">
                        <div style="width:130px;color: #A1A1A1;">Profile</div>
                        <div style="width:525px;" th:text="${companyProfile}"></div>
                    </div>
                </div>
                <div style="width: 237px;height:147px; border:1px solid rgba(222, 224, 229, 1);">
                    <img th:src="${logoUrl}" alt="Logo" style="width: 100%;height:130px;object-fit: contain;">
                </div>
            </div>
        </div>
        <div class="contactInfo"
             style="padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);">
            <div style="font-size: 18px;">
                Project Basic Information
            </div>
            <div style="margin-top:30px;display: flex;align-items: center;justify-content: space-between;">
                <div style="font-size: 13px;">
                    <div style="display:flex;">
                        <div style="width:130px;color: #A1A1A1;">Project Name</div>
                        <div style="display: flex;align-items: center;">
                            <span th:text="${projectName}"></span>
                            <div style="width:56px;height:20px;line-height: 20px;text-align: center;color: rgb(161, 161, 161);border:1px solid rgba(222, 224, 229, 1);margin: 0 5px 0 10px;"
                                 th:text="${projectTypeValue} + ' RFP'"></div>
                            <div style="width:45px;height:20px;line-height: 20px;text-align: center;color: #fff;background-color: rgb(251, 126, 51);"
                                 th:text="${projectStateValue}"></div>
                        </div>
                    </div>
                    <div style="margin-top:20px;display:flex;">
                        <div style="width:130px;color: #A1A1A1;">Organization Name</div>
                        <div th:text="${orgName}"></div>
                    </div>
                    <div style="margin-top:20px;display:flex;">
                        <div style="width:130px;color: #A1A1A1;">Tender Type</div>
                        <div th:text="${tenderValue}"></div>
                    </div>
                    <div style="margin-top:20px;display:flex;">
                        <div style="width:130px;color: #A1A1A1;">Enrollment Period</div>
                        <div>
                            <span th:text="${enrollStartTime}"></span> — <span th:text="${enrollEndTime}"></span>
                        </div>
                    </div>
                    <div style="margin-top:20px;display:flex;">
                        <div style="width:130px;color: #A1A1A1;">Bid Deviation Range</div>
                        <div>
                            <span th:text="${diffMinAmount}"></span> — <span th:text="${diffMaxAmount}"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="contactInfo"
             style="padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);">
            <div style="font-size: 18px;">
                Project Introduction
            </div>
            <div id="content" class="content-box" style="margin-top:20px;" th:utext="${introduction}"></div>
        </div>
        <div class="footer"
             style="height: 60px;line-height: 60px;background-color: rgba(235, 244, 255, 1);;font-size: 13px;padding-left: 40px;color: #A0A7BB;border-radius:0 0 5px 5px;">
            <a href="http://www.fangcang.com/RFP/login.html#/login"
               style="color: rgb(2, 167, 240);text-decoration:underline;">Login to RFP</a> to participate in the bidding process.
            <span>No account? Please contact support: <span style="color: rgb(255, 96, 0);">400-1866-919</span></span>
        </div>
    </div>
</div>
</body>
<footer style="margin-top: 3%">
    <br/>
    <div style="margin-top: 20px;">
        <strong>AMT Global RFP team</strong><br><br>
        <img src="https://grfp-prod-public.tos-cn-hongkong.volces.com/org/org/logo.png" alt="AMT Logo" width="260"><br><br>
        <strong>Wuhan Tian Xia Fang Cang Technology Co., Ltd.（AMT）</strong><br>
        <strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a><br>
        <strong>Address:</strong> Floor 2, 1278 Heping Avenue, Qingshan District, Wuhan
    </div>
</footer>

<script type="text/javascript">
    function copyText() {
        const textToCopy = 'www.fangcang.com/RFP';
        const tempInput = document.createElement('input');
        tempInput.value = textToCopy;
        document.body.appendChild(tempInput);
        tempInput.select();
        document.execCommand('copy');
        document.body.removeChild(tempInput);
        alert('Copied successfully');
    }
</script>

</html>