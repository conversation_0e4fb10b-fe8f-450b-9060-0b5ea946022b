package com.fangcang.grfp.api.controller.common.vo;

import cn.hutool.core.collection.CollectionUtil;
import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@ApiModel("系统信息")
@Getter
@Setter
public class AppInfoVO extends BaseVO {
	
	// ---------------------------------------------------------------------------------------------------- Private Member
	
	@ApiModelProperty("版本号")
	protected String version;

	@ApiModelProperty("文字资源更新时间")
	protected String textUpdatedTime;
	
	@ApiModelProperty("当前时间")
	protected String nowDateTime;

	@ApiModelProperty("发送验证码频率 秒")
	protected Integer smsFrequency;

	@ApiModelProperty("文字资源集合 Map<String,String>")
	protected Map<String, String> textResources;

	@ApiModelProperty("电话区号")
	private List<String> areaCodes;


	@Override
	public String toString() {
		return "AppInfoVO [version=" + version + ", textUpdatedTime=" + textUpdatedTime + ",nowDateTime=" + nowDateTime
				+ ", textResources=" + CollectionUtil.size(textResources) + "]" ;
	}
}
