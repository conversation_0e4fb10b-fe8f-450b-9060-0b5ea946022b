package com.fangcang.grfp.core.mapper;

import com.fangcang.grfp.core.entity.LanyonImportColumnEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.vo.LanyonImportColumnVO;

import java.util.List;

/**
 * <p>
 * Lanyon导入列 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface LanyonImportColumnMapper extends BaseMapper<LanyonImportColumnEntity> {

    public List<LanyonImportColumnVO> queryAll();

    public List<LanyonImportColumnVO> queryByDisplayOrder();
}
