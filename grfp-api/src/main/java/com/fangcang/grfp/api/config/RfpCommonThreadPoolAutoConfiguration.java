package com.fangcang.grfp.api.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration(proxyBeanMethods = false)
@EnableAsync(proxyTargetClass = true)
public class RfpCommonThreadPoolAutoConfiguration {

    /**
     * 生成酒店六边形坐标热力图线程池
     */
    public final static String EXECUTOR_NAME = "rfpCommonExecutor";

    @Bean(EXECUTOR_NAME)
    public Executor rfpCommonExecutor() {
        // 线程池配置先写死, 后续可以从配置文件读取
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(100);
        executor.setQueueCapacity(2000);
        executor.setThreadNamePrefix("rfpCommonExecutor-async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setKeepAliveSeconds(60);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        return executor;
    }

}
