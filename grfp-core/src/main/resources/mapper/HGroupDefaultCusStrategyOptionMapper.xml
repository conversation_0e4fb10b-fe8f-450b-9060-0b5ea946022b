<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HGroupDefaultCusStrategyOptionMapper">
    <insert id="batchUpsert">
        insert into t_h_group_default_cus_strategy_option (option_id, strategy_id, project_intent_hotel_group_id, project_id, option_name, is_support, creator)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.optionId}, #{item.strategyId}, #{item.projectIntentHotelGroupId}, #{item.projectId}, #{item.optionName}, #{item.isSupport}, #{item.creator})
        </foreach>
        on duplicate key update option_name = values(option_name),
                                is_support  = values(is_support),
                                modifier    = values(modifier)
    </insert>

    <select id="selectByProjectIntentHotelGroupIdAndStrategyIds" resultType="com.fangcang.grfp.core.entity.HGroupDefaultCusStrategyOptionEntity">
        SELECT *
        FROM t_h_group_default_cus_strategy_option
        WHERE project_intent_hotel_group_id = #{projectIntentHotelGroupId}
        AND strategy_id in
        <foreach collection="strategyIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>
