package com.fangcang.grfp.core.vo.response.hotelgroup;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("酒店集团酒店报价统计")
public class QueryHotelGroupHotelBidStatusResponse  extends BaseVO {

    // 项目ID
    @ApiModelProperty("项目ID")
    private Integer projectId;

    // 酒店ID
    @ApiModelProperty("酒店ID")
    private Long hotelId;

    // 是否报价 1：已经报价 0：未报价
    @ApiModelProperty("是否报价 1：已经报价 0：未报价")
    private Integer isBid;

    // 是否能代报价 1：显示代报价 0：不显示
    @ApiModelProperty("是否能代报价 1：显示代报价 0：不显示")
    private Integer canDoBid;

    // 是否能通知酒店 1：显示通知酒店，0：不显示
    @ApiModelProperty("是否能通知酒店 1：显示通知酒店，0：不显示")
    private Integer canDoNotifyHotel;

    // 是否再次通知酒店   1：显示再次通知酒店，0：不显示
    @ApiModelProperty("是否再次通知酒店   1：显示再次通知酒店，0：不显示")
    private Integer canDoNotifyHotelAgain;

    // 酒店报价ID
    @ApiModelProperty("酒店报价ID")
    private Integer projectIntentHotelId;

    // 酒店集团是否品牌限制 1:是， 0：否
    @ApiModelProperty(" 酒店集团是否品牌限制 1:是， 0：否")
    private Integer isBrandLimit;

    // 是否酒店集团品牌  1:有，0：无
    @ApiModelProperty(" 是否酒店集团品牌  1:有，0：无")
    private Integer isHotelGroupBrand;



}
