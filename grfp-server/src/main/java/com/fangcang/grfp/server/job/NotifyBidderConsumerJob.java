package com.fangcang.grfp.server.job;

import com.fangcang.grfp.core.bo.RedisService;
import com.fangcang.grfp.core.cached.CachedProjectService;
import com.fangcang.grfp.core.constant.RedisConstant;
import com.fangcang.grfp.core.manager.BidMapManager;
import com.fangcang.grfp.core.manager.MailManager;
import com.fangcang.grfp.core.mapper.HotelMapper;
import com.fangcang.grfp.core.mapper.OrgMapper;
import com.fangcang.grfp.core.mapper.ProjectIntentHotelMapper;
import com.fangcang.grfp.core.mapper.ProjectMapper;
import com.fangcang.grfp.core.util.ExceptionUtility;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 通知报价人消费
 */
@Component
@Slf4j
@JobHandler(value = NotifyBidderConsumerJob.TASK_NAME)
public class NotifyBidderConsumerJob extends IJobHandler {

    public static final String TASK_NAME = "NotifyBidderConsumerJob";

    @Resource
    private RedisService redisService;
    @Resource
    private BidMapManager bidMapManager;


    @Override
    public ReturnT<String> execute(String s) {
        log.info("启动通知报价人消费...");

        Integer notifyConsumberCount = 20;
        String key = RedisConstant.NOTIFY_BIDDER;

        if (redisService.scard(key) == null || redisService.scard(key) <= 0) {
            XxlJobLogger.log("通知报价人队列为空，无需发送");
            return SUCCESS;
        }
        for (int i = 0; i < notifyConsumberCount; i++) {
            // spop = 项目ID_项目意向酒店ID_操作人
            String spop = redisService.spop(key);
            if (!StringUtil.isEmpty(spop)) {
                String[] arr = spop.split("_");
                try {
                    String response = bidMapManager.notifyBidderConsumer(Long.valueOf(arr[1]).intValue(), arr[2]);
                    if (!"SUCCESS".equals(response)) {
                        log.error("启动通知报价人队列失败,项目ID：{} 酒店ID：{}, response:{}", arr[0], arr[1], response);
                    }
                } catch (Exception e) {
                    log.error(ExceptionUtility.getDetailedExceptionString(e));
                    log.error("启动通知报价人队列失败,项目ID：{} 酒店ID：{}", arr[0], arr[1], e);
                }
            }
        }
        log.info("结束消费通知报价人..");
        return ReturnT.SUCCESS;
    }



}
