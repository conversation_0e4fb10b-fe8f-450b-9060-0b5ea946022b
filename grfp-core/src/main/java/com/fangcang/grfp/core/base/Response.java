package com.fangcang.grfp.core.base;

import java.io.Serializable;

/**
 * 返回对象
 *
 * <AUTHOR>
 */
public class Response implements Serializable {

    private static final long serialVersionUID = 6899493106722037546L;

    /**
     * 结果(1：成功 0：失败) 枚举类ReturnResultEnum
     */
    protected Integer result;

    /**
     * 失败原因
     */
    protected String msg;

    /**
     * 返回对象
     */
    protected Object data;

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
