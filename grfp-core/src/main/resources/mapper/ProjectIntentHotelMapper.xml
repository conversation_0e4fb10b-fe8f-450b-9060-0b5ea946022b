<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectIntentHotelMapper">

    <insert id="batchInsert">
        insert into t_project_intent_hotel
            (project_id,
            hotel_id,
            hotel_contact_uid,
            hotel_contact_name,
            distributor_contact_uid,
            distributor_contact_name,
            platform_contact_uid,
            platform_contact_name,
            hotel_sales_contact_name,
            hotel_sales_contact_mobile,
            hotel_sales_contact_email,
            hotel_bid_contact_name,
            hotel_bid_contact_mobile,
            hotel_bid_contact_email,
            hotel_group_bid_contact_name,
            hotel_group_bid_contact_mobile,
            hotel_group_bid_contact_email,
            send_mail_status,
            invite_status,
            last_invite_time,
            bid_state,
            last_year_room_night,
            tender_avg_price,
            bid_weight,
            hotel_service_points,
            is_upload,
            bid_upload_source,
            bid_org_id,
            bid_org_type,
            hotel_group_approve_status,
            hotel_group_reject_approve_reason,
            creator
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.projectId},
            #{item.hotelId},
            #{item.hotelContactUid},
            #{item.hotelContactName},
            #{item.distributorContactUid},
            #{item.distributorContactName},
            #{item.platformContactUid},
            #{item.platformContactName},
            #{item.hotelSalesContactName},
            #{item.hotelSalesContactMobile},
            #{item.hotelSalesContactEmail},
            #{item.hotelBidContactName},
            #{item.hotelBidContactMobile},
            #{item.hotelBidContactEmail},
            #{item.hotelGroupBidContactName},
            #{item.hotelGroupBidContactMobile},
            #{item.hotelGroupBidContactEmail},
            #{item.sendMailStatus},
            #{item.inviteStatus},
            #{item.lastInviteTime},
            #{item.bidState},
            #{item.lastYearRoomNight},
            #{item.tenderAvgPrice},
            #{item.bidWeight},
            #{item.hotelServicePoints},
            #{item.isUpload},
            #{item.bidUploadSource},
            #{item.bidOrgId},
            #{item.bidOrgType},
            #{item.hotelGroupApproveStatus},
            #{item.hotelGroupRejectApproveReason},
            #{item.creator})
        </foreach>
    </insert>

    <select id="selectProjectRelatedCountList" resultType="com.fangcang.grfp.core.vo.ProjectRelatedCount">
        SELECT
            project_id AS projectId,
            SUM(IFNULL(`invite_status`,0)) AS invitedCount,
            SUM( CASE WHEN bid_state > 0 THEN 1 ELSE 0 END ) AS bidCount
        FROM t_project_intent_hotel
        WHERE project_id IN 
        <foreach collection="projectIdList" item="projectId" separator="," open="(" close=")">
            #{projectId}
        </foreach>
        GROUP BY project_id
    </select>

    <select id="queryHotelGroupBidPriceCount" resultType="com.fangcang.grfp.core.vo.response.hotelgroup.ProjectHotelCountResponse"
            parameterType="com.fangcang.grfp.core.vo.request.hotelgroup.QueryHotelGroupBidPriceCountRequest">
        select
            ph.project_id AS projectId ,ifnull(count(ph.hotel_id),0) AS hotelCount
        from t_project_intent_hotel ph
        left join t_hotel h on ph.hotel_id = h.hotel_id
        where ph.project_id in
        <foreach collection="projectIds" open="(" close=")" item="projectId" separator="," index="index">
            #{projectId}
        </foreach>
        <if test="queryType == 1">
            and ph.bid_state = 1
        </if>
        <if test="queryType == 2">
            and ph.bid_state = 2
        </if>
        <if test="queryType == 3">
            and ph.bid_state = 3
        </if>
        <if test="queryType > 0 and hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
            AND ((ph.BID_ORG_ID = #{hotelGroupOrgId} AND ph.BID_ORG_TYPE = 4)
            OR (h.hotel_brand_id IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach> AND ph.BID_ORG_TYPE = 2)
            )
        </if>
        group by ph.project_id
    </select>

    <select id="queryHotelGroupRecommendHotelInfo"
            resultType="com.fangcang.grfp.core.vo.response.recommendhotel.BidRecommendHotelInfoQueryResponse"
            parameterType="com.fangcang.grfp.core.vo.request.QueryHotelGroupBidMapHotelListRequest">
        SELECT
            phhd.project_id as projectId,
            phhd.hotel_id as hotelId,
            ih.city_code as cityCode,
            ih.lng_google as lngGoogle,
            ih.lat_google as latGoogle,
            ih.lng_baidu as lngBaidu,
            ih.lat_baidu as latBaidu,
            ih.name_en_us as nameEnUs,
            ih.name_zh_cn as nameZhCn,
            ih.hotel_star as hotelStar,
            ih.rating as rating,
            phhd.RECOMMEND_LEVEL as recommendLevel,
            rh.REQUIRED_ROOM_NIGHT as requiredRoomNight,
            rh.LAST_ROOM_AVAILABLE as lra,
            rh.BREAKFAST_NUM as breakfastNum,
            rh.REFERENCE_PRICE as referencePrice,
            pih.BID_STATE as bidState,
            pih.invite_status as isInvited,
        CASE WHEN pih.PROJECT_INTENT_HOTEL_ID IS NULL THEN 0 ELSE IFNULL(pih.INVITE_STATUS,0) END AS isInvited,
        CASE WHEN pih.PROJECT_INTENT_HOTEL_ID IS NULL OR pih.BID_STATE = 0 THEN 0 ELSE 1 END AS isBid
        FROM T_PROJECT_HOTEL_HISTORY_DATA phhd
        LEFT JOIN t_hotel ih on phhd.hotel_id = ih.hotel_id and ih.is_active = 1
        LEFT JOIN t_recommend_hotel rh on phhd.hotel_id = rh.hotel_id and rh.state = 1
        LEFT JOIN T_PROJECT_INTENT_HOTEL pih ON phhd.project_id = pih.project_id and phhd.hotel_id = pih.hotel_id
        WHERE phhd.project_id = #{query.projectId}
        <if test="query.orgId != null">
            AND ((pih.BID_ORG_ID = #{query.orgId} AND pih.BID_ORG_TYPE = 4)
            OR (ih.hotel_brand_id IN
            <foreach collection="query.hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach> AND (pih.BID_STATE = 0 OR pih.PROJECT_INTENT_HOTEL_ID IS NULL))
            )
        </if>
        <if test="query.hotelName != null and query.hotelName != ''">
            and (ih.name_en_us like concat('%',#{query.hotelName},'%') OR ih.name_zh_cn like concat('%',#{query.hotelName},'%'))
        </if>

        <if test="query.hotelId != null">
            and phhd.hotel_id = #{query.hotelId}
        </if>
        <if test="query.cityCode != null and query.cityCode != ''">
            and phhd.CITY_CODE = #{query.cityCode}
        </if>
        ORDER BY isBid ASC, isInvited ASC, recommendLevel ASC
    </select>

    <select id="queryNoRecommendHotelInfo"
            resultType="com.fangcang.grfp.core.vo.response.recommendhotel.BidRecommendHotelInfoQueryResponse">
        SELECT
            ih.HOTEL_ID as hotelId,
            ih.city_code as cityCode,
            ih.lng_baidu as lngBaiDu,
            ih.lat_baidu as latBaiDu,
            ih.lng_google as lngGoogle,
            ih.lat_google as latGoogle,
            ih.name_en_us as nameEnUs,
            ih.name_zh_cn as nameZhCn,
            ih.hotel_star as hotelStar,
            ih.rating as rating,
            NULL as recommendLevel,
            CASE WHEN pih.PROJECT_INTENT_HOTEL_ID IS NULL OR pih.BID_STATE = 0 THEN 0 ELSE 1 END AS isBid
        FROM
            t_hotel ih
       LEFT JOIN T_PROJECT_INTENT_HOTEL pih ON pih.project_id = #{projectId} and ih.HOTEL_ID = pih.hotel_id
        WHERE
            ih.hotel_id = #{hotelId} AND ih.is_active = 1
    </select>

    <select id="queryHotelGroupBidMapInvitedHotelList"
            resultType="com.fangcang.grfp.core.vo.response.recommendhotel.BidRecommendHotelInfoQueryResponse"
            parameterType="com.fangcang.grfp.core.vo.request.QueryHotelGroupBidMapHotelListRequest">
        SELECT
            ph.project_id as projectId,
            ph.hotel_id as hotelId,
            ih.city_code as cityCode,
            ih.lng_baidu as lngBaiDu,
            ih.lat_baidu as latBaiDu,
            ih.lng_google as lngGoogle,
            ih.lat_google as latGoogle,
            ih.name_en_us as nameEnUs,
            ih.name_zh_cn as nameZhCn,
            ih.hotel_star as hotelStar,
            ih.rating as rating,
            rh.REQUIRED_ROOM_NIGHT as requiredRoomNight,
            rh.LAST_ROOM_AVAILABLE as lra,
            rh.BREAKFAST_NUM as breakfastNum,
            rh.REFERENCE_PRICE as referencePrice,
            pih.BID_STATE  AS bidState,
            pih.invite_status as isInvited,
        CASE WHEN pih.PROJECT_INTENT_HOTEL_ID IS NULL OR pih.BID_STATE = 0 THEN 0 ELSE 1 END AS isBid
        FROM T_PROJECT_INVITE_HOTEL ph
        LEFT JOIN t_hotel ih on ph.hotel_id = ih.hotel_id and ih.is_active = 1
        LEFT JOIN t_recommend_hotel rh on ph.hotel_id = rh.hotel_id and rh.state = 1
        LEFT JOIN T_PROJECT_INTENT_HOTEL pih ON ph.project_id = pih.project_id and ph.hotel_id = pih.hotel_id
        WHERE ph.project_id = #{query.projectId}
        AND ((pih.BID_ORG_ID = #{query.orgId} AND hotel_group_approve_status IS NULL)
        OR (ih.hotel_brand_id IN
        <foreach collection="query.hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
            #{hotelGroupBrandId}
        </foreach> AND ((pih.BID_STATE = 0 AND hotel_group_approve_status IS NULL) OR pih.PROJECT_INTENT_HOTEL_ID IS NULL))
        )
        <if test="query.hotelId == null">
            <if test="query.hotelName != null and query.hotelName != ''">
                and (ih.name_en_us like concat('%',#{query.hotelName},'%') OR ih.name_zh_cn like concat('%',#{query.hotelName},'%'))
            </if>
        </if>
        <if test="query.hotelId != null">
            and ih.hotel_id = #{query.hotelId}
        </if>
        <if test="query.cityCode != null and query.cityCode != ''">
            and ih.CITY_CODE = #{query.cityCode}
        </if>
        ORDER BY isBid ASC
    </select>

    <select id="queryProjectBidHotelInfo"
            resultType="com.fangcang.grfp.core.vo.response.project.BidHotelInfoQueryResponse">
        SELECT
            ph.project_intent_hotel_id AS projectIntentHotelId,
            ph.project_id AS projectId ,
            ph.hotel_id AS hotelId,
            ph.BID_STATE AS bidState,
            ph.HOTEL_SERVICE_POINTS AS hotelServicePoints,
            ih.city_code AS cityCode,
            ih.lng_baidu AS lngBaiDu,
            ih.lat_baidu AS latBaiDu,
            ih.lng_google AS lngGoogle,
            ih.lat_google AS latGoogle,
            ih.name_en_us AS hotelNameEnUs,
            ih.name_zh_cn AS hotelNameZhCn,
            ih.hotel_star AS hotelStar,
            ih.rating AS rating,
            ih.address_en_us AS hotelAddressEnUs,
            ih.address_zh_cn AS hotelAddressZhCn,
            ih.opening_date AS openingDate,
            ih.fitment_date AS fitmentDate,
            ih.main_pic_url AS hotelImageUrl,
            ih.room_num AS roomNum,
            ph.BID_WEIGHT AS bidWeight
        FROM t_project_intent_hotel ph
        LEFT JOIN t_hotel ih ON ph.hotel_id = ih.hotel_id AND ih.is_active = 1
        <if test="cityCode != '' and cityCode != null">
            AND ih.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        WHERE ph.project_id = #{projectId,jdbcType=BIGINT}
        AND ph.BID_STATE >= 1
        <if test="cityCode != '' and cityCode != null">
            AND ih.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="queryProjectIntentHotelList" resultType="com.fangcang.grfp.core.vo.response.project.ProjectIntentHotelVO">
        SELECT
            ph.project_intent_hotel_id AS projectIntentHotelId,
            ph.project_id AS projectId,
            ph.hotel_id AS hotelId,
            h.name_en_us AS hotelNameEnUs,
            h.name_zh_cn AS hotelNameZhCn,
            h.main_pic_url AS hotelImageUrl,
            h.hotel_star AS hotelStar,
            h.rating AS rating,
            c.city_code AS cityCode,
            c.name_en_us AS cityNameEnUs,
            c.name_zh_cn AS cityNameZhCn,
            h.address_en_us AS hotelAddressEnUs,
            h.address_zh_cn AS hotelAddressZhCn,
            h.telephone AS telephone,
            ph.last_year_room_night AS lastYearRoomNight,
            ph.tender_avg_price AS tenderAvgPrice,
            ph.hotel_sales_contact_name AS contactName,
            ph.hotel_sales_contact_mobile AS contactMobile,
            ph.hotel_sales_contact_email AS contactEmail,
            ph.currency_code as currencyCode,
            ph.distributor_contact_name as distributorContactName,
            ph.platform_contact_name as platformContactName,
            ph.bid_state AS bidState,
            ph.send_mail_status AS sendMailStatus,
            ph.invite_status AS inviteStatus
        FROM
            t_project_intent_hotel ph
        LEFT JOIN t_hotel h ON ph.hotel_id = h.hotel_id AND h.is_active = 1
        LEFT JOIN t_city c ON h.city_code = c.city_code
        <where>
            ph.project_id = #{req.projectId} AND ph.invite_status = 1
            <if test="req.hotelId != null">
                AND ph.hotel_id = #{req.hotelId}
            </if>
            <if test="req.hotelName != null and req.hotelName != ''">
                AND (h.name_zh_cn LIKE CONCAT('%', #{req.hotelName}, '%') OR h.name_en_us LIKE CONCAT('%', #{req.hotelName}, '%'))
            </if>
            <if test="req.cityCode != null and req.cityCode != ''">
                AND h.city_code = #{req.cityCode}
            </if>
        </where>
        ORDER BY ph.project_intent_hotel_id DESC
    </select>

    <select id="selectByProjectId" resultType="com.fangcang.grfp.core.entity.ProjectIntentHotelEntity">
        select *
        from t_project_intent_hotel
        where project_id = #{projectId,jdbcType=BIGINT}
    </select>

    <select id="queryProjectContractHotelList" resultType="com.fangcang.grfp.core.vo.response.project.ProjectHotelListVO">
        SELECT
            pih.hotel_id as hotelId,
            pih.project_intent_hotel_id as projectIntentHotelId,
            h.city_code as cityCode,
            h.lat_google as latGoogle,
            h.lng_google as lngGoogle,
            h.lat_baidu as latBaidu,
            h.lng_baidu as lngBaidu,
            IF(#{languageId} = 2, h.name_zh_cn, h.name_en_us) as hotelName,
            h.main_pic_url as hotelImageUrl,
            IF(#{languageId} = 2, h.address_zh_cn, h.address_en_us) as hotelAddress,
            h.opening_date as openingDate,
            h.fitment_date as fitmentDate,
            h.room_num as roomNum,
            pih.distributor_contact_name as distributorContactName,
            pih.platform_contact_name as platformContactName,
            h.hotel_star as hotelStar,
            h.rating as ratingScore,
            pih.bid_state as bidState,
            IF(#{languageId} = 2, c.name_zh_cn, c.name_en_us) as cityName,
            IF(#{languageId} = 2, hb.name_zh_cn, hb.name_en_us) as hotelBrandName,
            IF(#{languageId} = 2, hg.name_zh_cn, hg.name_en_us) as hotelGroupName,
            pih.last_year_room_night as lastYearRoomNight,
            pih.tender_avg_price as myPurchaseLastYearAvgPrice,
            pih.bid_weight as weight,
            pih.notify_status as notifyStatus,
            pih.notify_operator as notifyOperator,
            pih.notify_time as notifyTime,
            pih.bid_org_type as bidOrgType,
            bo.org_name as bidOrgName,
            pih.hotel_price_follow_name as hotelPriceFollowName,
            pih.remark as remark
        FROM
            t_project_intent_hotel pih
        JOIN
            t_hotel h ON pih.hotel_id = h.hotel_id
        LEFT JOIN
            t_city c ON h.city_code = c.city_code
        LEFT JOIN
            t_hotel_brand hb ON h.hotel_brand_id = hb.hotel_brand_id
        LEFT JOIN
            t_hotel_group hg ON h.hotel_group_id = hg.hotel_group_id
        LEFT JOIN
            t_org bo ON pih.bid_org_id = bo.org_id
        WHERE
            pih.project_id = #{req.projectId}
            <if test="req.hotelId != null">
                AND pih.hotel_id = #{req.hotelId}
            </if>
            <if test="req.hotelName != null and req.hotelName != ''">
                AND (h.name_zh_cn LIKE CONCAT('%', #{req.hotelName}, '%') OR h.name_en_us LIKE CONCAT('%', #{req.hotelName}, '%'))
            </if>
            <if test="req.cityCode != null and req.cityCode != ''">
                AND h.city_code = #{req.cityCode}
            </if>
            <if test="req.hotelGroupId != null">
                AND h.hotel_group_id = #{req.hotelGroupId}
            </if>
            <if test="req.hotelBrandId != null">
                AND h.hotel_brand_id = #{req.hotelBrandId}
            </if>
            <if test="req.starList != null and req.starList.size() > 0">
                AND h.hotel_star IN
                <foreach collection="req.starList" item="star" open="(" separator="," close=")">
                    #{star}
                </foreach>
            </if>
            <if test="req.bidState != null">
                AND pih.bid_state = #{req.bidState}
            </if>
            <if test="req.notifyStatus != null and req.notifyStatus != ''">
                AND pih.notify_status = #{req.notifyStatus}
            </if>
            <if test="req.bidOrgType != null">
                AND pih.bid_org_type = #{req.bidOrgType}
            </if>
            <if test="req.hotelPriceFollowName != null and req.hotelPriceFollowName != ''">
                AND pih.hotel_price_follow_name LIKE CONCAT('%', #{req.hotelPriceFollowName}, '%')
            </if>
    </select>

    <update id="updateProjectIntentHotel"
            parameterType="com.fangcang.grfp.core.vo.request.project.UpdateProjectIntentHotelRequest">
        update t_project_intent_hotel
        <set>modify_time = now(),
            <if test="operator != null">
                modifier = #{operator},
            </if>
            <if test="operateType == 1">
                hotel_sales_contact_name = #{hotelSalesContactName},
                hotel_sales_contact_mobile =#{hotelSalesContactMobile},
                hotel_sales_contact_email=#{hotelSalesContactEmail},
            </if>
            <if test="operateType == 2">
                last_year_room_night = #{lastYearRoomNight},
                tender_avg_price = #{tenderAvgPrice},
            </if>
            <if test="operateType == 3">
                distributor_contact_uid = #{distributorContactUid},
                distributor_contact_name = #{distributorContactName},
            </if>
            <if test="operateType == 4">
                invite_status = #{inviteStatus},
            </if>
            <if test="operateType == 5">
                send_mail_status = #{sendMailStatus},
            </if>
            <if test="operateType == 9">
                platform_contact_uid = #{platformContactUid},
                platform_contact_name = #{platformContactName},
            </if>
        </set>
        where project_id = #{projectId}
        <if test="hotelIds != null and hotelIds.size() > 0">
            and hotel_id in
            <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
                #{hotelId}
            </foreach>
        </if>
    </update>

    <update id="updateBidContactInfo" parameterType="com.fangcang.grfp.core.entity.ProjectIntentHotelEntity">
        UPDATE T_PROJECT_INTENT_HOTEL SET
        MODIFIER = #{modifier}
        <if test="bidWeight != null ">
            ,bid_weight = #{bidWeight}
        </if>
        <if test="bidState != null ">
            ,BID_STATE = #{bidState}
        </if>
        <if test="hotelBidContactName != null and hotelBidContactName != '' ">
            ,hotel_bid_contact_name = #{hotelBidContactName}
        </if>
        <if test="hotelBidContactMobile != null and hotelBidContactMobile != '' ">
            ,hotel_bid_contact_mobile = #{hotelBidContactMobile}
        </if>
        <if test="hotelBidContactEmail != null and hotelBidContactEmail != '' ">
            ,hotel_bid_contact_email = #{hotelBidContactEmail}
        </if>
        <if test="hotelGroupBidContactName != null and hotelGroupBidContactName != '' ">
            ,hotel_group_bid_contact_name = #{hotelGroupBidContactName}
        </if>
        <if test="hotelGroupBidContactMobile != null and hotelGroupBidContactMobile != '' ">
            ,HOTEL_GROUP_BID_CONTACT_MOBILE = #{hotelGroupBidContactMobile}
        </if>
        <if test="hotelGroupBidContactEmail != null and hotelGroupBidContactEmail != '' ">
            ,HOTEL_GROUP_BID_CONTACT_EMAIL = #{hotelGroupBidContactEmail}
        </if>
        <if test="employeeRight != null and employeeRight != '' ">
            ,EMPLOYEE_RIGHT = #{employeeRight}
        </if>
        <if test="employeeRightFileUrl != null and employeeRightFileUrl != '' ">
            ,EMPLOYEE_RIGHT_FILE_URL = #{employeeRightFileUrl}
        </if>
        <if test="modifier != null and modifier != '' ">
            ,MODIFIER = #{modifier}
        </if>
        <if test="bidOrgId != null ">
            ,BID_ORG_ID = #{bidOrgId}
        </if>
        <if test="bidOrgType != null ">
            ,BID_ORG_TYPE = #{bidOrgType}
        </if>
        <if test="currencyCode != null and currencyCode != '' ">
            ,CURRENCY_CODE = #{currencyCode}
        </if>
        <if test="hotelGroupApproveStatus != null ">
            ,HOTEL_GROUP_APPROVE_STATUS = #{hotelGroupApproveStatus}
        </if>
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
    </update>


    <select id="queryInvitedSingleHotelsSimple"
            resultType="com.fangcang.grfp.core.vo.response.hotelgroup.InvitedSingleHotelResponse"
            parameterType="com.fangcang.grfp.core.vo.request.hotelgroup.QueryInvitedSingleHotelRequest">
        SELECT
        pih.project_id AS projectId,
        p.project_name AS projectName,
        pih.hotel_id AS hotelId,
        h.name_en_us AS hotelNameEnUs,
        h.name_zh_cn AS hotelNameZhCn,
        h.city_code AS cityCode,
        c.name_en_us AS cityNameEnUs,
        c.name_zh_cn AS cityNameZhCn,
        p.tender_type AS tenderType,
        o.org_name AS publishOrgName
        FROM t_project_invite_hotel pih
        JOIN t_hotel h ON pih.hotel_id = h.hotel_id AND h.is_active = 1
        JOIN t_project p ON pih.project_id = p.project_id
        LEFT JOIN t_city c ON h.city_code = c.city_code
        LEFT JOIN t_org o ON p.tender_org_id = o.org_id
        JOIN t_org_related_hotel_brand orb ON orb.hotel_brand_id = h.hotel_brand_id
        WHERE orb.org_id = #{query.orgId}
        AND pih.project_id IN (SELECT project_id FROM t_project_intent_hotel_group WHERE hotel_group_org_id = #{query.orgId} AND is_active = 1)
        <if test="query.cityCode != null and query.cityCode != ''">
            AND h.city_code = #{query.cityCode}
        </if>
        <if test="query.hotelId != null">
            AND h.hotel_id = #{query.hotelId}
        </if>
        <if test="query.projectName != null and query.projectName != ''">
            AND p.project_name LIKE CONCAT('%', #{query.projectName}, '%')
        </if>
        AND pih.HOTEL_ID NOT IN ( SELECT HOTEL_ID FROM T_PROJECT_INTENT_HOTEL  WHERE project_id = p.project_id AND (BID_STATE >= 1 OR hotel_group_approve_status > 0))
        ORDER BY pih.create_time DESC
    </select>

    <!-- 查询酒店集团审核列表（待审核/审核驳回） -->
    <select id="queryHotelGroupApprovalList"
            resultType="com.fangcang.grfp.core.vo.response.hotelgroup.HotelGroupApprovalResponse"
            parameterType="com.fangcang.grfp.core.vo.request.hotelgroup.QueryHotelGroupApprovalRequest">
        SELECT
            pih.project_id as projectId,
            p.project_name as projectName,
            o.org_name as publishOrgName,
            p.tender_type as tenderType,
            p.project_state as projectState,
            pih.bid_state as bidState,
            pih.bid_org_type as bidOrgType,
            p.first_bid_start_time as firstBidStartTime,
            p.first_bid_end_time as firstBidEndTime,
            p.bid_start_time as bidStartTime,
            p.bid_end_time as bidEndTime,
            pih.hotel_sales_contact_name as groupSalesName,
            pih.hotel_id as hotelId,
            pih.project_intent_hotel_id as projectIntentHotelId,
            h.name_en_us as hotelNameEnUs,
            h.name_zh_cn as hotelNameZhCn,
            h.city_code as cityCode,
            c.name_en_us as cityNameEnUs,
            c.name_zh_cn as cityNameZhCn
        FROM t_project_intent_hotel pih
        LEFT JOIN t_project p ON pih.project_id = p.project_id
        LEFT JOIN t_hotel h ON pih.hotel_id = h.hotel_id AND h.is_active = 1
        LEFT JOIN t_city c ON h.city_code = c.city_code
        LEFT JOIN t_org o ON p.tender_org_id = o.org_id
        WHERE
         (pih.BID_ORG_ID = #{query.orgId}
            OR (h.hotel_brand_id IN
            <foreach collection="query.hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach> AND pih.BID_ORG_TYPE = 2)
        )
        <!-- 根据审核状态过滤 -->
        <if test="query.approvalStatus != null">
            AND pih.hotel_group_approve_status = #{query.approvalStatus}
        </if>
        <!-- 根据项目名称过滤 -->
        <if test="query.projectName != null and query.projectName != ''">
            AND p.project_name LIKE CONCAT('%', #{query.projectName}, '%')
        </if>
        <!-- 根据招标机构ID过滤 -->
        <if test="query.publishOrgId != null">
            AND p.tender_org_id = #{query.publishOrgId}
        </if>
        <!-- 根据酒店ID过滤 -->
        <if test="query.hotelId != null">
            AND pih.hotel_id = #{query.hotelId}
        </if>
        <if test="query.bidOrgType != null">
            AND pih.bid_org_type = #{query.bidOrgType}
        </if>
        ORDER BY pih.modify_time DESC, pih.project_intent_hotel_id DESC
    </select>


    <!-- 查询酒店集团审核列表（待审核/审核驳回）数量 -->
    <select id="queryHotelGroupApprovalCount"  resultType="com.fangcang.grfp.core.vo.ApproveStatusCountVO">
        SELECT
            count(hotel_group_approve_status) AS approveStatusCount,
            hotel_group_approve_status AS approveStatus
        FROM t_project_intent_hotel pih
        LEFT JOIN t_project p ON pih.project_id = p.project_id
        LEFT JOIN t_hotel h ON pih.hotel_id = h.hotel_id AND h.is_active = 1
        WHERE
        (pih.BID_ORG_ID = #{orgId}
        OR (h.hotel_brand_id IN
        <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
            #{hotelGroupBrandId}
        </foreach> AND pih.BID_ORG_TYPE = 2)
        )
        AND hotel_group_approve_status IS NOT NULL
        GROUP BY pih.hotel_group_approve_status
    </select>


    <select id="queryHotelApprovalCount"  resultType="com.fangcang.grfp.core.vo.ApproveStatusCountVO">
        SELECT
        count(hotel_group_approve_status) AS approveStatusCount,
        hotel_group_approve_status AS approveStatus
        FROM t_project_intent_hotel pih
        LEFT JOIN t_project p ON pih.project_id = p.project_id
        LEFT JOIN t_hotel h ON pih.hotel_id = h.hotel_id AND h.is_active = 1
        WHERE
            h.hotel_id IN
        <foreach collection="hotelIdList" item="hotelId" open="(" close=")" separator=",">
            #{hotelId}
        </foreach>
        AND hotel_group_approve_status IS NOT NULL
        GROUP BY pih.hotel_group_approve_status
    </select>


    <select id="selectProjectIntentHotelServicePoint" resultType="com.fangcang.grfp.core.entity.ProjectIntentHotelEntity">
        SELECT
        HOTEL_ID as hotelId,
        HOTEL_SERVICE_POINTS as hotelServicePoints
        FROM t_project_intent_hotel
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
        AND BID_STATE = 3
        AND HOTEL_ID IN
        <foreach collection="hotelIdList" open="(" close=")" item="hotelId" separator="," index="index">
            #{hotelId}
        </foreach>
    </select>

    <select id="selectHotelOrgTentList" parameterType="com.fangcang.grfp.core.vo.request.hotel.ProjectIntentHotelRequest"
            resultType="com.fangcang.grfp.core.vo.response.hotel.ProjectHotelTentResponse">
        SELECT
            projectId,
            projectName,
            projectType,
            tenderType,
            tenderOrgId,
            orgName,
            hotelId,
            tenderCount,
            diffMinAmount,
            diffMaxAmount,
            projectState,
            bidStartTime,
            firstBidEndTime,
            secondBidEndTime,
            thirdBidEndTime,
            priceMonitorStartDate,
            priceMonitorEndDate,
            createTime,
            displayOrder
        FROM (
            SELECT
                p.project_id projectId,
                p.project_name projectName,
                p.project_type projectType,
                p.tender_type tenderType,
                p.tender_org_id tenderOrgId,
                tog.ORG_NAME orgName,
                o.HOTEL_ID as hotelId,
                p.TENDER_COUNT tenderCount,
                p.DIFF_MIN_AMOUNT diffMinAmount,
                p.DIFF_MAX_AMOUNT diffMaxAmount,
                p.PROJECT_STATE projectState,
                p.bid_start_time bidStartTime,
                p.FIRST_BID_END_TIME firstBidEndTime,
                p.SECOND_BID_END_TIME secondBidEndTime,
                p.THIRD_BID_END_TIME thirdBidEndTime,
                P.PRICE_MONITOR_START_DATE as priceMonitorStartDate,
                p.PRICE_MONITOR_END_DATE as priceMonitorEndDate,
                p.CREATE_TIME createTime,
                p.DISPLAY_ORDER displayOrder
            FROM T_PROJECT p LEFT JOIN T_ORG tog ON p.TENDER_ORG_ID = tog.ORG_ID,
            (
                SELECT
                t1.ORG_ID,
                t1.ORG_NAME,
                t2.HOTEL_ID
                FROM T_ORG t1
                INNER JOIN T_ORG_RELATED_HOTEL t2 ON t1.ORG_ID = t2.ORG_ID
                WHERE t1.ORG_ID = #{supplyOrgId}
                <if test="hotelId != null and hotelId > 0">
                    AND t2.hotel_id = #{hotelId}
                </if>
            ) o
            where p.create_time >= #{lastYearTime} and p.PROJECT_STATE not in(0,3)
            and p.project_type = 1 AND p.TENDER_TYPE = 1
            <if test="projectName != null and projectName != '' ">
                and p.PROJECT_NAME like CONCAT(CONCAT('%',#{projectName,jdbcType=VARCHAR}),'%')
            </if>
            <if test="projectState != null">
                and p.PROJECT_STATE = #{projectState}
            </if>
        ) t1
        UNION
        (
        SELECT
            p.project_id projectId,
            p.project_name projectName,
            p.project_type projectType,
            p.tender_type tenderType,
            p.tender_org_id tenderOrgId,
            tog.ORG_NAME orgName,
            o.HOTEL_ID hotelId,
            p.TENDER_COUNT tenderCount,
            p.DIFF_MIN_AMOUNT diffMinAmount,
            p.DIFF_MAX_AMOUNT diffMaxAmount,
            p.PROJECT_STATE projectState,
            p.bid_start_time bidStartTime,
            p.FIRST_BID_END_TIME firstBidEndTime,
            p.SECOND_BID_END_TIME secondBidEndTime,
            p.THIRD_BID_END_TIME thirdBidEndTime,
            P.PRICE_MONITOR_START_DATE as priceMonitorStartDate,
            p.PRICE_MONITOR_END_DATE as priceMonitorEndDate,
            p.CREATE_TIME createTime,
            p.DISPLAY_ORDER displayOrder
            FROM T_PROJECT p
            INNER JOIN T_PROJECT_INTENT_HOTEL pih ON p.PROJECT_ID = pih.PROJECT_ID
            INNER JOIN
                (
                    SELECT
                    t1.ORG_ID,
                    t1.ORG_NAME,
                    t2.HOTEL_ID
                    FROM T_ORG t1
                    INNER JOIN T_ORG_RELATED_HOTEL t2 ON t1.ORG_ID = t2.ORG_ID
                    WHERE t1.ORG_ID = #{supplyOrgId}
                ) o ON pih.HOTEL_ID = o.HOTEL_ID
                LEFT JOIN T_ORG tog ON p.TENDER_ORG_ID = tog.ORG_ID
                WHERE p.CREATE_TIME >= #{lastYearTime} and p.PROJECT_STATE not in(0,3)
                AND p.project_type = 1 AND p.TENDER_TYPE = 2
                <if test="bidState != null">
                    AND pih.BID_STATE = #{bidState,jdbcType=INTEGER}
                </if>
                <if test="hotelId != null and hotelId > 0">
                    AND pih.hotel_id = #{hotelId}
                </if>
                and (pih.hotel_org_id = #{supplyOrgId} or pih.hotel_org_id is null)
                <if test="userId != null ">
                    and pih.HOTEL_CONTACT_UID = #{userId}
                </if>
                <if test="projectName != null and projectName != '' ">
                    and p.PROJECT_NAME like CONCAT(CONCAT('%',#{projectName,jdbcType=VARCHAR}),'%')
                </if>
                <if test="projectState != null">
                    and p.PROJECT_STATE = #{projectState}
                </if>
            )
        order by createTime desc
    </select>

    <select id="selectProjectIntentHotelList" parameterType="com.fangcang.grfp.core.vo.request.hotel.ProjectIntentHotelRequest"
            resultType="com.fangcang.grfp.core.entity.ProjectIntentHotelEntity">
        SELECT
            *
        FROM T_PROJECT_INTENT_HOTEL
        WHERE HOTEL_ID IN (
                SELECT HOTEL_ID FROM T_ORG_RELATED_HOTEL WHERE ORG_ID = #{supplyOrgId}
        )
        AND (hotel_org_id = #{supplyOrgId} or hotel_org_id is null)
        <if test="bidState != null  and bidState > 0 ">
            AND BID_STATE = #{bidState}
        </if>
        <if test="bidState !=0 and userId != null ">
            and HOTEL_CONTACT_UID = #{userId}
        </if>
    </select>

    <select id="queryByProjectIntentHotelIds" parameterType="java.util.List" resultType="com.fangcang.grfp.core.entity.ProjectIntentHotelEntity">
        SELECT *
        FROM T_PROJECT_INTENT_HOTEL WHERE project_intent_hotel_id
        IN
        <foreach item="item" collection="list" index="index" close=")" open="(" separator=",">
            #{item}
        </foreach>
        />

    </select>

    <select id="selectHotelTentList" parameterType="com.fangcang.grfp.core.vo.request.hotel.ProjectIntentHotelRequest"
            resultType="com.fangcang.grfp.core.vo.response.hotel.ProjectHotelTentResponse">
        SELECT
            p.project_id projectId,
            p.project_name projectName,
            p.project_type projectType,
            p.tender_type tenderType,
            o.ORG_NAME orgName,
            p.TENDER_COUNT tenderCount,
            p.DIFF_MIN_AMOUNT diffMinAmount,
            p.DIFF_MAX_AMOUNT diffMaxAmount,
            p.PROJECT_STATE projectState,
            p.BID_START_TIME bidStartTime,
            p.BID_END_TIME bidEndTime,
            p.FIRST_BID_END_TIME firstBidEndTime,
            p.SECOND_BID_END_TIME secondBidEndTime,
            p.THIRD_BID_END_TIME thirdBidEndTime,
            P.PRICE_MONITOR_START_DATE as priceMonitorStartDate,
            p.PRICE_MONITOR_END_DATE as priceMonitorEndDate,
            pih.hotel_group_approve_status AS hotelGroupApproveStatus,
            pih.project_intent_hotel_id AS projectIntentHotelId,
            pih.HOTEL_ID AS hotelId,
            h.city_code AS cityCode
        FROM T_PROJECT p
        INNER JOIN T_PROJECT_INTENT_HOTEL pih ON p.PROJECT_ID = pih.PROJECT_ID
        LEFT JOIN T_ORG o ON p.TENDER_ORG_ID = o.ORG_ID
        LEFT JOIN t_hotel h ON pih.HOTEL_ID = h.HOTEL_ID
        where p.CREATE_TIME >= #{query.lastYearTime} and p.PROJECT_STATE not in (0,3)
        and p.project_type = 1 and
        ( p.TENDER_TYPE = 2
        AND pih.HOTEL_ID IN
        <foreach item="item" collection="query.hotelIdList" index="index" close=")" open="(" separator=",">
            #{item}
        </foreach>
        <if test="query.bidState != null and query.bidState == 0">
            AND p.PROJECT_STATE != 2
        </if>
        <if test="query.hotelId != null and query.hotelId > 0">
            and pih.hotel_id = #{query.hotelId}
        </if>
          <if test="query.bidState != null and query.bidState >= 0">
             and pih.BID_STATE = #{query.bidState} AND (pih.HOTEL_GROUP_APPROVE_STATUS IS NULL OR pih.HOTEL_GROUP_APPROVE_STATUS NOT IN (1,3))
          </if>
        <if test="query.hotelGroupApproveStatus != null and query.hotelGroupApproveStatus > 0">
            and pih.hotel_group_approve_status = #{query.hotelGroupApproveStatus}
        </if>
            and (pih.hotel_org_id = #{query.supplyOrgId} or pih.hotel_org_id is null)
        <if test="query.userId != null ">
            and pih.HOTEL_CONTACT_UID = #{query.userId}
        </if>)
        <if test="query.projectName != null and query.projectName != '' ">
            and p.PROJECT_NAME like CONCAT(CONCAT('%',#{query.projectName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="query.projectState != null">
            and p.PROJECT_STATE = #{query.projectState}
        </if>
        <if test="query.orgName != null and query.orgName != '' ">
            and o.ORG_NAME like CONCAT(CONCAT('%',#{query.orgName,jdbcType=VARCHAR}),'%')  )
        </if>
        order by p.CREATE_TIME desc
    </select>

    <select id="selectHotelNoBidCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM T_PROJECT p
        INNER JOIN T_PROJECT_INTENT_HOTEL pih ON p.PROJECT_ID = pih.PROJECT_ID
        LEFT JOIN T_ORG o ON p.TENDER_ORG_ID = o.ORG_ID
        LEFT JOIN t_hotel h ON pih.HOTEL_ID = h.HOTEL_ID
        where p.CREATE_TIME >= #{lastYearTime} and p.PROJECT_STATE not in (0,3)
        and p.project_type = 1 and
        ( p.TENDER_TYPE = 2
        AND pih.HOTEL_ID IN
        <foreach item="item" collection="hotelIdList" index="index" close=")" open="(" separator=",">
            #{item}
        </foreach>
        and pih.BID_STATE =0 AND (pih.HOTEL_GROUP_APPROVE_STATUS IS NULL OR pih.HOTEL_GROUP_APPROVE_STATUS NOT IN (1,3))
        AND  p.PROJECT_STATE != 2)
    </select>

    <select id="queryGroupByHotelBidState" resultType="com.fangcang.grfp.core.vo.response.bidmap.HotelBidStateResponse" parameterType="com.fangcang.grfp.core.vo.request.bidmap.BidHotelStatQueryRequest">
        SELECT
            ph.BID_STATE AS hotelBidState,
            COUNT(ph.BID_STATE) AS count
        FROM t_project_intent_hotel ph
        LEFT JOIN t_hotel ih on ph.hotel_id = ih.hotel_id and ih.is_active = 1
        WHERE ph.project_id = #{projectId}
        AND ph.BID_STATE != 0
        <if test="hotelName != null and hotelName != ''">
            and (ih.name_zh_cn like concat('%',#{hotelName},'%') or ih.name_en_us like concat('%', #{hotelName}, '%'))
        </if>
        <if test="cityCode != null and cityCode != ''">
            and ih.city_code = #{cityCode}
        </if>
        <if test="hotelGroupId != null">
            and ih.HOTEL_GROUP_ID = #{hotelGroupId}
        </if>
        <if test="hotelBrandId != null and hotelBrandId != ''">
            and ih.hotel_brand_id = #{hotelBrandId}
        </if>
        <if test="userIds != null and userIds.siz() > 0">
            and ph.distributor_contact_uid in
            <foreach collection="userIds" open="(" close=")" item="userId" separator="," index="index">
                #{userId}
            </foreach>
        </if>
        GROUP BY ph.BID_STATE
    </select>

    <select id="queryMapHotelListByBidState"
            resultType="com.fangcang.grfp.core.vo.response.bidmap.QueryMapBidHotelListResponse"
            parameterType="com.fangcang.grfp.core.vo.request.bidmap.QueryMapHotelListByBidStateRequest">
        SELECT
            ph.project_intent_hotel_id as projectIntentHotelId,
            ph.project_id as projectId ,
            ph.hotel_id as hotelId,
            ph.currency_code as currencyCode,
            ih.city_code as cityCode,
            ih.country_code as countryCode,
            ih.lng_baidu as lngBaiDu,
            ih.lat_baidu as latBaiDu,
            ih.lng_google as lngGoogle,
            ih.lat_google as latGoogle,
            ih.name_zh_cn as nameZhCn,
            ih.name_en_us as nameEnUs,
            ih.hotel_star as hotelStar,
            ih.rating as rating,
            ih.main_pic_url as mainPicUrl,
            ih.address_en_us as addressEnUs,
            ih.address_zh_cn as addressZhCn,
            ph.bid_weight as bidWeight,
            ph.hotel_service_points as hotelServicePoint,
            ph.last_year_room_night as lastYearRoomNight,
            ph.bid_state as bidState
        FROM t_project_intent_hotel ph
        LEFT JOIN t_hotel ih on ph.hotel_id = ih.hotel_id and ih.is_active = 1
        WHERE ph.project_id = #{query.projectId}
        <if test="query.hotelName != null and query.hotelName != ''">
            and (ih.name_zh_cn like concat('%',#{query.hotelName},'%') or ih.name_en_us like concat('%', #{query.hotelName}, '%'))
        </if>
        <if test="query.cityCode != null and query.cityCode != ''">
            and ih.city_code = #{query.cityCode}
        </if>
        <if test="query.hotelGroupId != null">
            and ih.HOTEL_GROUP_ID = #{query.hotelGroupId}
        </if>
        <if test="query.hotelBrandId != null and query.hotelBrandId != ''">
            and ih.hotel_brand_id = #{query.hotelBrandId}
        </if>
        <if test="query.userIds != null and query.userIds.siz() > 0">
            and ph.distributor_contact_uid in
            <foreach collection="query.userIds" open="(" close=")" item="userId" separator="," index="index">
                #{userId}
            </foreach>
        </if>
        <if test="query.bidState != null">
            and ph.bid_state = #{query.bidState}
        </if>
    </select>

    <select id="queryInviteHotelList" resultType="java.lang.Long">
        SELECT HOTEL_ID
        FROM T_PROJECT_INTENT_HOTEL
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
          AND INVITE_STATUS = 1
    </select>

    <update id="updateToWaitNotify">
        UPDATE T_PROJECT_INTENT_HOTEL
        SET
            NOTIFY_STATUS = 0,
            NOTIFY_OPERATOR = null,
            <if test="modifier != null and modifier != ''">
                MODIFIER = #{modifier,jdbcType=VARCHAR},
            </if>
            NOTIFY_TIME = null
        WHERE project_id = #{projectId,jdbcType=BIGINT}
        AND hotel_id IN
        <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
            #{hotelId}
        </foreach>
    </update>

    <update id="updateBidState" parameterType="com.fangcang.grfp.core.vo.request.bidmap.UpdateHotelBidStateRequest">
        UPDATE T_PROJECT_INTENT_HOTEL
        SET
            bid_state = #{bidState},
            <if test="bidState == 2 and remark != null and remark != ''">
                 remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="bidState == 4 and remark != null and remark != ''">
                 remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="bidState == 7 and rejectNegotiationRemark != null and rejectNegotiationRemark != ''">
                reject_negotiation_remark = #{rejectNegotiationRemark,jdbcType=VARCHAR},
            </if>
            modifier = #{modifier,jdbcType=VARCHAR}
        WHERE project_id = #{projectId}
        AND hotel_id IN
        <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
            #{hotelId}
        </foreach>
    </update>

    <update id="updateHotelSalesContactInfo" parameterType="com.fangcang.grfp.core.vo.request.bidmap.UpdateBidHotelSalesUserRequest">
        UPDATE T_PROJECT_INTENT_HOTEL
        SET
            HOTEL_SALES_CONTACT_NAME = #{hotelSalesContactName},
            HOTEL_SALES_CONTACT_MOBILE = #{hotelSalesContactMobile},
            HOTEL_SALES_CONTACT_EMAIL = #{hotelSalesContactEmail},
            MODIFIER = #{modifier}
        WHERE project_intent_hotel_id IN
        <foreach collection="projectIntentHotelIdList" open="(" close=")" item="projectIntentHotelId" separator="," index="index">
            #{projectIntentHotelId}
        </foreach>
    </update>

    <select id="queryHotelTenderInfo" resultType="com.fangcang.grfp.core.vo.TenderHotelPriceVO">
        select t1.bid_state                      as bidState,
               t2.hotel_id                       as hotelId,
               t2.name_en_us                     as hotelNameEnUs,
               t2.name_zh_cn                     as hotelNameZhCn,
               t2.telephone                      as telephone,
               t2.address_en_us                  as addressEnUs,
               t2.address_zh_cn                  as addressZhCn,
               t2.hotel_star                     as hotelStar,
               t2.lng_google                     as lngGoogle,
               t2.lat_google                     as latGoogle,
               t2.city_code                      AS cityCode,
               t1.hotel_bid_contact_name         as contactName,
               t1.currency_code                  as currencyCode,
               t1.hotel_bid_contact_mobile       as contactMobile,
               t1.hotel_bid_contact_email        as contactEmail,
               t1.hotel_group_bid_contact_name   as hotelGroupContactName,
               t1.hotel_group_bid_contact_mobile as hotelGroupContactMobile,
               t1.hotel_group_bid_contact_email  as hotelGroupContactEmail,
               t1.hotel_sales_contact_name       as hotelSalesContactName,
               t1.hotel_sales_contact_mobile     as hotelSalesContactMobile,
               t1.hotel_sales_contact_email      as hotelSalesContactEmail,
               t1.employee_right                 as employeeRight,
               t1.modify_time                    as modifyTime
        from t_project_intent_hotel t1
                 inner join t_hotel t2 on t1.hotel_id = t2.hotel_id
        <where>
            t1.project_id = #{projectId,jdbcType=BIGINT}
            <if test="bidStates != null and bidStates.size() != 0">
                and t1.bid_state in
                <foreach collection="bidStates" open="(" close=")" item="bidState" separator="," index="index">
                    #{bidState}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryHotelGroupTenderInfo" resultType="com.fangcang.grfp.core.vo.TenderHotelPriceVO">
        select t1.bid_state                  as bidState,
               t2.hotel_id                   as hotelId,
               t2.name_en_us                 as hotelNameEnUs,
               t2.name_zh_cn                 as hotelNameZhCn,
               t2.telephone                  as telephone,
               t2.address_en_us              as addressEnUs,
               t2.address_zh_cn              as addressZhCn,
               t2.city_code                  AS cityCode,
               t1.hotel_sales_contact_name   as hotelSalesContactName,
               t1.hotel_sales_contact_mobile as hotelSalesContactMobile,
               t1.hotel_sales_contact_email  as hotelSalesContactEmail
        from t_project_intent_hotel t1
                 inner join t_hotel t2 on t1.hotel_id = t2.hotel_id
        <where>
            t1.project_id = #{projectId,jdbcType=BIGINT}
            <if test="bidStates != null and bidStates.size() != 0">
                and t1.bid_state in
                <foreach collection="bidStates" open="(" close=")" item="bidState" separator="," index="index">
                    #{bidState}
                </foreach>
            </if>
            and (
            (t1.bid_org_id = #{hotelGroupOrgId} and t1.bid_org_type = 4)
                or t2.hotel_brand_id in
            <foreach collection="hotelGroupBrandIdList" open="(" close=")" item="hotelGroupBrandId" separator=",">
                #{hotelGroupBrandId}
            </foreach>
            and t1.bid_org_type = 2
                )
        </where>
    </select>

    <update id="updateHotelGroupApprove" parameterType="com.fangcang.grfp.core.entity.ProjectIntentHotelEntity">
        UPDATE T_PROJECT_INTENT_HOTEL
        SET
        BID_STATE = #{bidState,jdbcType=INTEGER}
        , HOTEL_GROUP_APPROVE_STATUS = #{hotelGroupApproveStatus,jdbcType=INTEGER}
        , hotel_group_reject_approve_reason = #{hotelGroupRejectApproveReason,jdbcType=VARCHAR}
        <if test="modifier != null and modifier != ''">
            , MODIFIER = #{modifier,jdbcType=VARCHAR}
        </if>
        ,MODIFY_TIME = now()
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>

    <update id="updateContactInfoOnly" parameterType="com.fangcang.grfp.core.entity.ProjectIntentHotelEntity">
        UPDATE
        t_project_intent_hotel
        set
        hotel_sales_contact_name = #{projectIntentHotel.hotelSalesContactName},
        hotel_sales_contact_mobile = #{projectIntentHotel.hotelSalesContactMobile},
        hotel_sales_contact_email = #{projectIntentHotel.hotelSalesContactEmail},
        modifier = #{projectIntentHotel.modifier}
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotel.projectIntentHotelId}
    </update>

    <update id="updateNotify" parameterType="com.fangcang.grfp.core.entity.ProjectIntentHotelEntity">
        UPDATE T_PROJECT_INTENT_HOTEL
        SET
        NOTIFY_STATUS = #{projectIntentHotel.notifyStatus},
        NOTIFY_OPERATOR = #{projectIntentHotel.notifyOperator},
        NOTIFY_TIME = #{projectIntentHotel.modifyTime},
        MODIFY_TIME = now()
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotel.projectIntentHotelId}
    </update>

    <select id="queryHotelBidStatCount"
            resultType="com.fangcang.grfp.core.vo.BidStateCountVO">
        SELECT
        COUNT(ph.BID_STATE) AS bidStateCount,
        ph.BID_STATE AS bidState
        FROM
        t_project p,
        t_org o,
        t_project_intent_hotel ph,
        t_hotel h
        WHERE
        p.tender_org_id = o.org_id
        and p.project_id = ph.project_id
        and h.hotel_id = ph.hotel_id
        and h.is_active=1
        and (ph.hotel_group_approve_status = 0 || ph.hotel_group_approve_status = 2 || ph.hotel_group_approve_status IS NULL)
        and h.hotel_id IN
        <foreach collection="hotelIdList" item="hotelId" open="(" close=")" separator=",">
            #{hotelId}
        </foreach>
        GROUP BY ph.BID_STATE
    </select>

    <select id="queryProjectBidBrandStatInfo" resultType="com.fangcang.grfp.core.vo.ProjectBidBrandStatInfoVO">
        SELECT
            h.country_code AS countryCode,
            count(h.country_code) AS countryBidCount,
            h.city_code AS cityCode,
            count(h.city_code) AS cityBidCount,
            h.hotel_group_id AS hotelGroupId,
            count(h.hotel_group_id) AS hotelGroupBidCount,
            h.hotel_brand_id AS hotelBrandId,
            count(h.hotel_brand_id) AS hotelBrandBidCount
        FROM
            t_project_intent_hotel pih
        LEFT JOIN t_hotel h ON pih.hotel_id = h.hotel_id
        WHERE
            pih.project_id = #{projectId}
        AND pih.bid_state IN (1,2,3,4,6,7)
        GROUP BY
            h.country_code,
            h.city_code,
            h.hotel_group_id,
            h.hotel_brand_id;
    </select>

    <select id="queryProjectInviteCityInfo" resultType="com.fangcang.grfp.core.vo.ProjectBidBrandStatInfoVO">
        SELECT
            ih.country_code AS countryCode,
            count(ih.country_code) AS countryBidCount,
            ih.city_code AS cityCode,
            count(ih.city_code) AS cityBidCount
        FROM T_PROJECT_INVITE_HOTEL ph
        LEFT JOIN t_hotel ih on ph.hotel_id = ih.hotel_id and ih.is_active = 1
        LEFT JOIN t_recommend_hotel rh on ph.hotel_id = rh.hotel_id and rh.state = 1
        LEFT JOIN T_PROJECT_INTENT_HOTEL pih ON ph.project_id = pih.project_id and ph.hotel_id = pih.hotel_id
        WHERE ph.project_id = #{projectId}
        AND ((pih.BID_ORG_ID = #{hotelGroupOrgId} AND hotel_group_approve_status IS NULL)
        OR (ih.hotel_brand_id IN
        <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
            #{hotelGroupBrandId}
        </foreach> AND ((pih.BID_STATE = 0 AND hotel_group_approve_status IS NULL) OR pih.PROJECT_INTENT_HOTEL_ID IS NULL))
        )
        GROUP BY
        ih.country_code,
        ih.city_code
    </select>
</mapper>
