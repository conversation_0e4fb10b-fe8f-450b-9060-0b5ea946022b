<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectHotelTendWeightMapper">
    <insert id="upsert">
        insert into t_project_hotel_tend_weight
        (project_id,
         wht_room_night,
         wht_room_night_state,
         wht_room_night_ex,
         wht_room_night_ex_state,
         wht_city,
         wht_city_state,
         wht_location,
         wht_location_state,
         wht_price_advantage,
         wht_price_advantage_state,
         wht_price_advantage_ex,
         wht_price_advantage_ex_state,
         wht_ota_score,
         wht_ota_score_state,
         wht_co_pay,
         wht_co_pay_state,
         wht_breakfast,
         wht_breakfast_state,
         wht_lra,
         wht_lra_state,
         wht_cancel,
         wht_cancel_state,
         wht_total_weight,
         creator)
        values (#{entity.projectId},
                #{entity.whtRoomNight},
                #{entity.whtRoomNightState},
                #{entity.whtRoomNightEx},
                #{entity.whtRoomNightExState},
                #{entity.whtCity},
                #{entity.whtCityState},
                #{entity.whtLocation},
                #{entity.whtLocationState},
                #{entity.whtPriceAdvantage},
                #{entity.whtPriceAdvantageState},
                #{entity.whtPriceAdvantageEx},
                #{entity.whtPriceAdvantageExState},
                #{entity.whtOtaScore},
                #{entity.whtOtaScoreState},
                #{entity.whtCoPay},
                #{entity.whtCoPayState},
                #{entity.whtBreakfast},
                #{entity.whtBreakfastState},
                #{entity.whtLra},
                #{entity.whtLraState},
                #{entity.whtCancel},
                #{entity.whtCancelState},
                #{entity.whtTotalWeight},
                #{entity.creator})
        ON DUPLICATE KEY UPDATE wht_room_night               = VALUES(wht_room_night),
                                wht_room_night_state         = VALUES(wht_room_night_state),
                                wht_room_night_ex            = VALUES(wht_room_night_ex),
                                wht_room_night_ex_state      = VALUES(wht_room_night_ex_state),
                                wht_city                     = VALUES(wht_city),
                                wht_city_state               = VALUES(wht_city_state),
                                wht_location                 = VALUES(wht_location),
                                wht_location_state           = VALUES(wht_location_state),
                                wht_price_advantage          = VALUES(wht_price_advantage),
                                wht_price_advantage_state    = VALUES(wht_price_advantage_state),
                                wht_price_advantage_ex       = VALUES(wht_price_advantage_ex),
                                wht_price_advantage_ex_state = VALUES(wht_price_advantage_ex_state),
                                wht_ota_score                = VALUES(wht_ota_score),
                                wht_ota_score_state          = VALUES(wht_ota_score_state),
                                wht_co_pay                   = VALUES(wht_co_pay),
                                wht_co_pay_state             = VALUES(wht_co_pay_state),
                                wht_breakfast                = VALUES(wht_breakfast),
                                wht_breakfast_state          = VALUES(wht_breakfast_state),
                                wht_lra                      = VALUES(wht_lra),
                                wht_lra_state                = VALUES(wht_lra_state),
                                wht_cancel                   = VALUES(wht_cancel),
                                wht_cancel_state             = VALUES(wht_cancel_state),
                                wht_total_weight             = VALUES(wht_total_weight),
                                modifier                     = #{entity.modifier}
    </insert>

    <select id="selectByProjectId" resultType="com.fangcang.grfp.core.vo.response.project.ProjectHotelTendWeightVO">
        select * from t_project_hotel_tend_weight where project_id = #{projectId}
    </select>
</mapper>
