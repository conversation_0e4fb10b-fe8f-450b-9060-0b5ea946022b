package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import lombok.Data;

@Data
public class RoomInfo {

    /**
     * 房型id
     */
    private Long roomId;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 房型英文名称
     */
    private String roomEngName;

    /**
     * 房间面积
     */
    private String roomAcreage;

    /**
     * 所在楼层
     */
    private String roomFloor;

    /**
     * 最多入住人数
     */
    private Integer maxPerson;

    /**
     * 有线宽带
     * 0: 没有
     * 1: 全部房间有且收费
     * 2: 全部房间有且免费
     * 3: 部分房间有且收费
     * 4: 部分房间有且免费
     */
    private Integer wiredBroadnet;

    /**
     * 无线宽带
     * 0: 没有
     * 1: 全部房间有且收费
     * 2: 全部房间有且免费
     * 3: 部分房间有且收费
     * 4: 部分房间有且免费
     */
    private Integer wirelessBroadnet;

    /**
     * 窗型信息，见字典【窗型】
     */
    private Integer windowDetail;

    /**
     * 是否可吸烟
     * 1: 是
     * 2: 否
     * 3: 部分
     * -100: 未知
     */
    private Integer isAllowSmoking;

    /**
     * 是否可加床
     * 0: 不可加床
     * 1: 可加床
     * 2: 部分可加床
     */
    private Integer addBedflag;

    /**
     * 是否可加婴儿床
     * 0: 不可加婴儿床
     * 1: 可加婴儿床
     * -1: unknown未设置
     */
    private Integer addCribFlag;

    /**
     * 最多入住成人数
     */
    private Integer maxAdults;

    /**
     * 最多入住儿童数
     */
    private Integer maxChild;

    /**
     * 房型床型信息
     */
    private RoomBedTypeInfo roomBedTypeInfos;

}
