package com.fangcang.grfp.api.controller.textresource.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel("更新文字资源信息")
@Getter
@Setter
public class UpdateTextResourceRequest extends BaseVO {

	// ---------------------------------------------------------------------------------------------------- Private Members
	
	@ApiModelProperty(value = "文字资源编码", required = true)
	@NotBlank
	private String textResourceCode;
	@ApiModelProperty(value = "文字资源类型 1:网页文本,2:消息文本", required = true)
	@NotNull
	private Integer textResourceType;

	@ApiModelProperty(value = "文字资源英文", required = true)
	@NotBlank
	private String valueEnUs;

	@ApiModelProperty(value = "文字资源中文")
	private String valueZhCn;

	

}
