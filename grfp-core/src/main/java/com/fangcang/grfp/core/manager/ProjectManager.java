package com.fangcang.grfp.core.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.util.StringUtils;
import com.fangcang.grfp.core.entity.*;
import com.fangcang.grfp.core.enums.CustomStrategyTypeEnum;
import com.fangcang.grfp.core.enums.HotelBidStateEnum;
import com.fangcang.grfp.core.enums.HotelPriceTypeEnum;
import com.fangcang.grfp.core.enums.RoomLevelEnum;
import com.fangcang.grfp.core.enums.YesOrNoEnum;
import com.fangcang.grfp.core.mapper.*;
import com.fangcang.grfp.core.util.BooleanUtility;
import com.fangcang.grfp.core.util.GenericAppUtility;
import com.fangcang.grfp.core.vo.PriceApplicableRoomVO;
import com.fangcang.grfp.core.vo.ProjectHotelPriceGroupVO;
import com.fangcang.grfp.core.vo.TenderHotelPriceVO;
import com.fangcang.grfp.core.vo.response.city.CityVO;
import com.fangcang.grfp.core.vo.response.hotel.RoomNameInfoVO;
import com.fangcang.grfp.core.vo.response.project.CustomStrategyOptionVO;
import com.fangcang.grfp.core.vo.response.project.ProjectBidCustomTendStrategyVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 项目公共方法下沉 manager
 */
@Component
@Slf4j
public class ProjectManager {

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private ProjectLanyonViewKeysMapper projectLanyonViewKeysMapper;

    @Resource
    private PriceApplicableDayMapper priceApplicableDayMapper;

    @Resource
    private PriceUnapplicableDayMapper priceUnapplicableDayMapper;

    @Resource
    private PriceApplicableRoomMapper priceApplicableRoomMapper;

    @Resource
    private HotelManager hotelManager;

    @Resource
    private ProjectHotelPriceMapper projectHotelPriceMapper;

    @Resource
    private ProjectHotelBidStrategyMapper projectHotelBidStrategyMapper;

    @Resource
    private ProjectHotelPriceGroupMapper projectHotelPriceGroupMapper;

    @Resource
    private ProjectCustomBidStrategyMapper projectCustomBidStrategyMapper;

    @Resource
    private CustomBidStrategyOptionMapper customBidStrategyOptionMapper;

    @Resource
    private ProjectCustomTendStrategyMapper projectCustomTendStrategyMapper;

    @Resource
    private CityMapper cityMapper;

    @Resource
    private ProjectHotelTaxSettingsMapper projectHotelTaxSettingsMapper;

    @Resource
    private ProjectCustomStrategyOptionMapper projectCustomStrategyOptionMapper;

    /**
     * 查询项目报价报价通用逻辑
     */
    public List<TenderHotelPriceVO> queryHotelGroupTenderPriceList(int language, int projectId, List<TenderHotelPriceVO> hotelTenderPriceResponses) {
        if(CollectionUtils.isEmpty(hotelTenderPriceResponses)) {
            return Collections.emptyList();
        }

        // 查询项目信息
        ProjectEntity project = projectMapper.selectById(projectId);

        // 查询可用日期
        List<PriceApplicableDayEntity> allPriceApplicableDayList = priceApplicableDayMapper.selectByProjectId(projectId);
        Map<Long, List<PriceApplicableDayEntity>> hotelPriceApplicableDayMap  = allPriceApplicableDayList.stream().collect(Collectors.groupingBy(PriceApplicableDayEntity::getHotelId));

        // 查询不可用日期
        List<PriceUnapplicableDayEntity> priceUnapplicableDayList = priceUnapplicableDayMapper.selectByProjectId(projectId);
        Map<Long, List<PriceUnapplicableDayEntity>> hotelPriceUnapplicableDayMap = priceUnapplicableDayList.stream().collect(Collectors.groupingBy(PriceUnapplicableDayEntity::getHotelId));

        // 查询房型
        List<PriceApplicableRoomVO> priceApplicableRoomInfoResponseList = priceApplicableRoomMapper.queryPriceApplicableRoomInfoByProjectId(projectId);
        // 填充房型名称
        Set<Long> roomTypeHotelIds = priceApplicableRoomInfoResponseList.stream().map(PriceApplicableRoomVO::getHotelId).collect(Collectors.toSet());
        List<RoomNameInfoVO> roomNameInfoList = hotelManager.queryRoomNameListByHotelIds(language, roomTypeHotelIds);
        Map<Long, String> roomIdRoomNameMap = roomNameInfoList.stream().collect(Collectors.toMap(RoomNameInfoVO::getRoomId, RoomNameInfoVO::getRoomName, (o, n) -> n));
        priceApplicableRoomInfoResponseList.forEach(priceApplicableRoomInfoResponse -> {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(priceApplicableRoomInfoResponse.getCustomRoomTypeName())) {
                priceApplicableRoomInfoResponse.setRoomTypeName(priceApplicableRoomInfoResponse.getCustomRoomTypeName());
            } else if (Objects.nonNull(priceApplicableRoomInfoResponse.getRoomTypeId())) {
                priceApplicableRoomInfoResponse.setRoomTypeName(roomIdRoomNameMap.get(priceApplicableRoomInfoResponse.getRoomTypeId()));
            }
        });
        // 房型按酒店分组
        Map<Long, List<PriceApplicableRoomVO>> hotelPriceApplicableRoomMap = priceApplicableRoomInfoResponseList.stream().collect(Collectors.groupingBy(PriceApplicableRoomVO::getHotelId));

        // 查询所有报价
        List<ProjectHotelPriceEntity> projectHotelPriceList = projectHotelPriceMapper.selectByProjectId(projectId);
        Map<Integer, List<ProjectHotelPriceEntity>> groupProjectHotelPriceMap = projectHotelPriceList.stream().collect(Collectors.groupingBy(ProjectHotelPriceEntity::getHotelPriceGroupId));

        // 查询酒店报价策略信息
        List<ProjectHotelBidStrategyEntity> projectHotelBidStrategies = projectHotelBidStrategyMapper.selectByProjectId(projectId);
        Map<Long, ProjectHotelBidStrategyEntity> projectHotelBidStrategyMap = projectHotelBidStrategies.stream().collect(Collectors.toMap(ProjectHotelBidStrategyEntity::getHotelId, Function.identity()));

        // 查询项目相关价格组
        List<ProjectHotelPriceGroupVO> projectHotelPriceGroupList = projectHotelPriceGroupMapper.selectInfoByProjectId(projectId);
        Map<Long, List<ProjectHotelPriceGroupVO>> projectHotelPriceGroupMap = projectHotelPriceGroupList.stream().collect(Collectors.groupingBy(ProjectHotelPriceGroupVO::getHotelId));

        // 查询酒店报价自定义策略
        List<ProjectCustomBidStrategyEntity> customBidStrategies = projectCustomBidStrategyMapper.selectByProjectId(projectId);
        Map<Long, List<ProjectCustomBidStrategyEntity>> customBidStrategyMap = customBidStrategies.stream().collect(Collectors.groupingBy(ProjectCustomBidStrategyEntity::getHotelId));
        // 查询报价策略选项
        Set<Long> customStrategyIds = customBidStrategies.stream().filter(e -> CustomStrategyTypeEnum.isOptionType(e.getStrategyType()))
            .map(e -> Long.valueOf(e.getCustomTendStrategyId())).collect(Collectors.toSet());
        List<CustomBidStrategyOptionEntity> customBidStrategyOptions = customBidStrategyOptionMapper.selectByProjectIdAndStrategyId(projectId, customStrategyIds);
        Map<Long, List<CustomBidStrategyOptionEntity>> customBidStrategyOptionMap = customBidStrategyOptions.stream().collect(Collectors.groupingBy(CustomBidStrategyOptionEntity::getHotelId));

        // 查询项目自定义策略
        List<ProjectCustomTendStrategyEntity> customTendStrategies = projectCustomTendStrategyMapper.selectByProjectId(projectId);

        // 查询省份国家
        Set<String> cityCodes = hotelTenderPriceResponses.stream().map(TenderHotelPriceVO::getCityCode).collect(Collectors.toSet());
        List<CityVO> cityList = cityMapper.selectCityVOList(cityCodes);
        Map<String, CityVO> cityCodeCityVOMap = cityList.stream().collect(Collectors.toMap(CityVO::getCityCode, Function.identity(), (o, v) -> v));

        // 查询税费设置
        List<ProjectHotelTaxSettingsEntity> projectHotelTaxSettings = projectHotelTaxSettingsMapper.selectByProjectId(projectId);
        Map<Long, ProjectHotelTaxSettingsEntity> hotelIdProjectHotelTaxSettingMap = projectHotelTaxSettings.stream().collect(Collectors.toMap(ProjectHotelTaxSettingsEntity::getHotelId, Function.identity(), (o, v) -> v));

        // 根据价格类型准备数据
        for(TenderHotelPriceVO hotelPriceResponse : hotelTenderPriceResponses) {
            // 过滤未报价，否决，放弃报价
            if (Objects.equals(hotelPriceResponse.getBidState(), HotelBidStateEnum.NO_BID.bidState) ||
                Objects.equals(hotelPriceResponse.getBidState(), HotelBidStateEnum.WITHDRAW_THE_QUOTATION.bidState)) {
                continue;
            }
            // 设置币种
            hotelPriceResponse.setCurrencyCode(hotelPriceResponse.getCurrencyCode());

            long hotelId = hotelPriceResponse.getHotelId();

            // 设置基本信息
            hotelPriceResponse.setHotelName(GenericAppUtility.getName(language, hotelPriceResponse.getHotelId(), hotelPriceResponse.getHotelNameEnUs(), hotelPriceResponse.getHotelNameZhCn()));
            hotelPriceResponse.setAddress(GenericAppUtility.getName(language, hotelPriceResponse.getAddressEnUs(), hotelPriceResponse.getAddressEnUs(), hotelPriceResponse.getAddressZhCn()));
            Optional.ofNullable(cityCodeCityVOMap.get(hotelPriceResponse.getCityCode())).ifPresent(cityVO -> {
                hotelPriceResponse.setCityName(GenericAppUtility.getName(language, cityVO.getCityNameEnUs(), cityVO.getCityNameEnUs(), cityVO.getCityNameZhCn()));
                hotelPriceResponse.setProvinceName(GenericAppUtility.getName(language, cityVO.getProvinceNameEnUs(), cityVO.getProvinceNameEnUs(), cityVO.getProvinceNameZhCn()));
                hotelPriceResponse.setCountryName(GenericAppUtility.getName(language, cityVO.getCountryNameEnUs(), cityVO.getCountryNameEnUs(), cityVO.getCountryNameZhCn()));
            });

            // 设置项目信息
            hotelPriceResponse.setCorpStart(DateUtil.formatDate(project.getPriceMonitorStartDate()));
            hotelPriceResponse.setCorpEnd(DateUtil.formatDate(project.getPriceMonitorEndDate()));

            // 设置房型
            List<PriceApplicableRoomVO> applicableRooms = hotelPriceApplicableRoomMap.get(hotelId);
            if (CollectionUtils.isNotEmpty(applicableRooms)) {
                Map<Integer, List<PriceApplicableRoomVO>> roomLevelRoomListMap = applicableRooms.stream().collect(Collectors.groupingBy(PriceApplicableRoomVO::getRoomLevelNo));
                // 设置五档房型
                IntStream.rangeClosed(1, 5).forEach(roomLevelNo -> {
                    List<PriceApplicableRoomVO> roomList = roomLevelRoomListMap.get(roomLevelNo);
                    if (CollectionUtils.isEmpty(roomList)) {
                        return;
                    }
                    String roomTypeIds = roomList.stream().map(PriceApplicableRoomVO::getRoomTypeId).map(String::valueOf).collect(Collectors.joining(","));
                    String roomTypeNames = roomList.stream().map(PriceApplicableRoomVO::getRoomTypeName).collect(Collectors.joining(","));
                    if (roomLevelNo == RoomLevelEnum.ONE.key) {
                        hotelPriceResponse.setLevel1RoomTypeIds(roomTypeIds);
                        hotelPriceResponse.setLevel1RoomTypeNames(roomTypeNames);
                    } else if (roomLevelNo == RoomLevelEnum.TWO.key) {
                        hotelPriceResponse.setLevel2RoomTypeIds(roomTypeIds);
                        hotelPriceResponse.setLevel2RoomTypeNames(roomTypeNames);
                    } else if (roomLevelNo == RoomLevelEnum.THREE.key) {
                        hotelPriceResponse.setLevel3RoomTypeIds(roomTypeIds);
                        hotelPriceResponse.setLevel3RoomTypeNames(roomTypeNames);
                    } else if (roomLevelNo == RoomLevelEnum.FOUR.key) {
                        hotelPriceResponse.setLevel4RoomTypeIds(roomTypeIds);
                        hotelPriceResponse.setLevel4RoomTypeNames(roomTypeNames);
                    } else if (roomLevelNo == RoomLevelEnum.FIVE.key) {
                        hotelPriceResponse.setLevel5RoomTypeIds(roomTypeIds);
                        hotelPriceResponse.setLevel5RoomTypeNames(roomTypeNames);
                    }
                });
            }

            // 设置报价信息
            List<ProjectHotelPriceGroupVO> hotelPriceGroupList = projectHotelPriceGroupMap.get(hotelId);
            if (CollectionUtils.isNotEmpty(hotelPriceGroupList)) {
                Map<Integer, List<ProjectHotelPriceGroupVO>> roomLevelHotelGroupListMap =
                    hotelPriceGroupList.stream().collect(Collectors.groupingBy(ProjectHotelPriceGroupVO::getRoomLevelNo));
                // 设置五档房型
                IntStream.rangeClosed(1, 5).forEach(roomLevelNo -> {
                    // 获取价格组
                    List<ProjectHotelPriceGroupVO> hotelPriceGroups = roomLevelHotelGroupListMap.get(roomLevelNo);
                    if (CollectionUtils.isEmpty(hotelPriceGroups)) {
                        return;
                    }

                    // 设置价格
                    hotelPriceGroups.forEach(priceGroup -> {

                        // 构建每个价格组的 6 个价格
                        AtomicReference<BigDecimal> singlePrice = new AtomicReference<>();
                        AtomicReference<BigDecimal> doublePrice = new AtomicReference<>();
                        AtomicReference<BigDecimal> season1singlePrice = new AtomicReference<>();
                        AtomicReference<BigDecimal> season1doublePrice = new AtomicReference<>();
                        AtomicReference<BigDecimal> season2singlePrice = new AtomicReference<>();
                        AtomicReference<BigDecimal> season2doublePrice = new AtomicReference<>();
                        List<ProjectHotelPriceEntity> priceList = groupProjectHotelPriceMap.get(priceGroup.getHotelPriceGroupId());
                        if (CollectionUtils.isNotEmpty(priceList)) {
                            priceList.forEach(price -> {
                                if (HotelPriceTypeEnum.BASE_PRICE.key == price.getPriceType()) {
                                    singlePrice.set(price.getOnePersonPrice());
                                    doublePrice.set(price.getTwoPersonPrice());
                                } else if (HotelPriceTypeEnum.SEASON_1_PRICE.key == price.getPriceType()) {
                                    season1singlePrice.set(price.getOnePersonPrice());
                                    season1doublePrice.set(price.getTwoPersonPrice());
                                } else if (HotelPriceTypeEnum.SEASON_2_PRICE.key == price.getPriceType()) {
                                    season2singlePrice.set(price.getOnePersonPrice());
                                    season2doublePrice.set(price.getTwoPersonPrice());
                                }
                            });
                        }

                        Integer isLra = priceGroup.getLra();
                        Integer isIncludeBreakfast = priceGroup.getIsIncludeBreakfast();
                        // lra + 含早
                        if (YesOrNoEnum.YES.getKey().equals(isLra) && YesOrNoEnum.YES.getKey().equals(isIncludeBreakfast)) {
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("lraRt%sSglInb", roomLevelNo), singlePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("lraRt%sDblInb", roomLevelNo), doublePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season1LraRt%sSglInb", roomLevelNo), season1singlePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season1LraRt%sDblInb", roomLevelNo), season1doublePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season2LraRt%sSglInb", roomLevelNo), season2singlePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season2LraRt%sDblInb", roomLevelNo), season2doublePrice);
                        }
                        // lra + 不含早
                        else if (YesOrNoEnum.YES.getKey().equals(isLra) && YesOrNoEnum.NO.getKey().equals(isIncludeBreakfast)) {
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("lraRt%sSglExb", roomLevelNo), singlePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("lraRt%sDblExb", roomLevelNo), doublePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season1LraRt%sSglExb", roomLevelNo), season1singlePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season1LraRt%sDblExb", roomLevelNo), season1doublePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season2LraRt%sSglExb", roomLevelNo), season2singlePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season2LraRt%sDblExb", roomLevelNo), season2doublePrice);
                        }
                        // 非 lra + 含早
                        else if (YesOrNoEnum.NO.getKey().equals(isLra) && YesOrNoEnum.YES.getKey().equals(isIncludeBreakfast)) {
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("nLraRt%sSglInb", roomLevelNo), singlePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("nLraRt%sDblInb", roomLevelNo), doublePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season1nLraRt%sSglInb", roomLevelNo), season1singlePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season1nLraRt%sDblInb", roomLevelNo), season1doublePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season2nLraRt%sSglInb", roomLevelNo), season2singlePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season2nLraRt%sDblInb", roomLevelNo), season2doublePrice);
                        }
                        // 非 lra + 不含早
                        else if (YesOrNoEnum.NO.getKey().equals(isLra) && YesOrNoEnum.NO.getKey().equals(isIncludeBreakfast)) {
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("nLraRt%sSglExb", roomLevelNo), singlePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("nLraRt%sDblExb", roomLevelNo), doublePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season1nLraRt%sSglExb", roomLevelNo), season1singlePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season1nLraRt%sDblExb", roomLevelNo), season1doublePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season2nLraRt%sSglExb", roomLevelNo), season2singlePrice);
                            ReflectUtil.setFieldValue(hotelPriceResponse, String.format("season2nLraRt%sDblExb", roomLevelNo), season2doublePrice);
                        }
                    });

                    // 设置房间数, 任选一个价格组的房型数量都可以
                    // 大床数量, 双床数量, 总房间数量, 逗号隔开
                    ProjectHotelPriceGroupVO anyPriceGroup = hotelPriceGroups.get(0);
                    String roomCount = ObjectUtils.defaultIfNull(anyPriceGroup.getBigBedRoomCount(), "null") +
                        "," + ObjectUtils.defaultIfNull(anyPriceGroup.getDoubleBedRoomCount(), "null")
                        + "," + ObjectUtils.defaultIfNull(anyPriceGroup.getTotalRoomCount(), "null");
                    ReflectUtil.setFieldValue(hotelPriceResponse, "level" + roomLevelNo + "TotalRoomCount", roomCount);
                });
            }

            // 设置适用日期
            List<PriceApplicableDayEntity> priceApplicableDayList = hotelPriceApplicableDayMap.get(hotelId);
            if (CollectionUtils.isNotEmpty(priceApplicableDayList)) {
                Map<Integer, List<PriceApplicableDayEntity>> prictTypeApplicableDayMap = priceApplicableDayList.stream().collect(Collectors.groupingBy(PriceApplicableDayEntity::getPriceType));
                for (HotelPriceTypeEnum priceType : HotelPriceTypeEnum.values()) {
                    List<PriceApplicableDayEntity> priceApplicableDayEntities = prictTypeApplicableDayMap.get(priceType.key);
                    if(CollectionUtils.isEmpty(priceApplicableDayEntities)){
                        continue;
                    }
                    if (priceType == HotelPriceTypeEnum.BASE_PRICE) {
                        PriceApplicableDayEntity basicPriceApplicableDay = priceApplicableDayEntities.get(0);
                        hotelPriceResponse.setCorpStart(DateUtil.format(basicPriceApplicableDay.getStartDate(), "yyyy/MM/dd"));
                        hotelPriceResponse.setCorpEnd(DateUtil.format(basicPriceApplicableDay.getEndDate(), "yyyy/MM/dd"));
                        continue;
                    }
                    String applicableDate = null;
                    if (CollectionUtils.isNotEmpty(priceApplicableDayEntities)) {
                        applicableDate = priceApplicableDayEntities.stream()
                            .map(applicableDay ->
                                DateUtil.format(applicableDay.getStartDate(), "yyyy/MM/dd") + "-" + DateUtil.format(applicableDay.getEndDate(), "yyyy/MM/dd"))
                            .collect(Collectors.joining(","));
                    }
                    if (priceType == HotelPriceTypeEnum.SEASON_1_PRICE) {
                        hotelPriceResponse.setSeason1StartEnd(applicableDate);
                    } else if (priceType == HotelPriceTypeEnum.SEASON_2_PRICE) {
                        hotelPriceResponse.setSeason2StartEnd(applicableDate);
                    }
                }
            }

            // 设置报价策略
            ProjectHotelBidStrategyEntity bidStrategy = projectHotelBidStrategyMap.get(hotelId);
            if (Objects.nonNull(bidStrategy)) {
                // todo 待确认
                hotelPriceResponse.setCancelPolicy("");

                // 是否支持无线网络
                hotelPriceResponse.setWirelessInclude(BooleanUtility.intToYN(bidStrategy.getSupportWifi()));
            }

            // 设置税费
            ProjectHotelTaxSettingsEntity taxSetting = hotelIdProjectHotelTaxSettingMap.get(hotelId);
            if (Objects.nonNull(taxSetting)) {
                // 设置提前入住税
                hotelPriceResponse.setEarlyCheckFee(taxSetting.getEarlyckFeeValue());
                hotelPriceResponse.setEarlyCheckFeeUom(feeTypeStr(taxSetting.getEarlyckFeeType()));
                hotelPriceResponse.setEarlyCheckInclude(BooleanUtility.intToYN(taxSetting.getEarlyckFeeIsInclude()));

                // 设置入住税
                hotelPriceResponse.setLodgtxFee(taxSetting.getLodgtxFeeValue());
                hotelPriceResponse.setLodgtxUom(feeTypeStr(taxSetting.getLodgtxFeeType()));
                hotelPriceResponse.setLodgtxInclude(BooleanUtility.intToYN(taxSetting.getLodgtxFeeIsInclude()));

                // 设置州税
                hotelPriceResponse.setStatetxFee(taxSetting.getStatetxFeeValue());
                hotelPriceResponse.setStatetxUom(feeTypeStr(taxSetting.getStatetxFeeType()));
                hotelPriceResponse.setStatetxInclude(BooleanUtility.intToYN(taxSetting.getStatetxFeeIsInclude()));

                // 设置城市税
                hotelPriceResponse.setCitytxFee(taxSetting.getCitytxFeeValue());
                hotelPriceResponse.setCitytxUom(feeTypeStr(taxSetting.getCitytxFeeType()));
                hotelPriceResponse.setCitytxInclude(BooleanUtility.intToYN(taxSetting.getCitytxFeeIsInclude()));

                // 客房增值税
                hotelPriceResponse.setVatgstrmFee(taxSetting.getVatgstrmFeeValue());
                hotelPriceResponse.setVatgstrmUom(feeTypeStr(taxSetting.getVatgstrmFeeType()));
                hotelPriceResponse.setVatgstrmInclude(BooleanUtility.intToYN(taxSetting.getVatgstrmFeeIsInclude()));

                // 设置餐饮增值税费
                hotelPriceResponse.setVatgstfbFee(taxSetting.getVatgstfbFeeValue());
                hotelPriceResponse.setVatgstfbUom(feeTypeStr(taxSetting.getVatgstfbFeeType()));
                hotelPriceResponse.setVatgstfbInclude(BooleanUtility.intToYN(taxSetting.getVatgstfbFeeIsInclude()));

                // 设置服务费
                hotelPriceResponse.setServiceFee(taxSetting.getServiceFeeValue());
                hotelPriceResponse.setServiceUom(feeTypeStr(taxSetting.getServiceFeeType()));
                hotelPriceResponse.setServiceInclude(BooleanUtility.intToYN(taxSetting.getServiceFeeIsInclude()));

                // 设置占用费
                hotelPriceResponse.setOccFee(taxSetting.getOccFeeValue());
                hotelPriceResponse.setOccUom(feeTypeStr(taxSetting.getOccFeeType()));
                hotelPriceResponse.setOccInclude(BooleanUtility.intToYN(taxSetting.getOccFeeIsInclude()));

                // 设置其他税费
                hotelPriceResponse.setOtherTxFee1(taxSetting.getOthertx1FeeValue());
                hotelPriceResponse.setOtherTxFee1Uom(feeTypeStr(taxSetting.getOthertx1FeeType()));
                hotelPriceResponse.setOtherTxFee1Incl(BooleanUtility.intToYN(taxSetting.getOthertx1FeeIsInclude()));
                hotelPriceResponse.setOtherTxFee1Desc(taxSetting.getOthertx1FeeDesc());
                hotelPriceResponse.setOtherTxFee2(taxSetting.getOthertx2FeeValue());
                hotelPriceResponse.setOtherTxFee2Uom(feeTypeStr(taxSetting.getOthertx2FeeType()));
                hotelPriceResponse.setOtherTxFee2Incl(BooleanUtility.intToYN(taxSetting.getOthertx2FeeIsInclude()));
                hotelPriceResponse.setOtherTxFee2Desc(taxSetting.getOthertx2FeeDesc());
                hotelPriceResponse.setOtherTxFee3(taxSetting.getOthertx3FeeValue());
                hotelPriceResponse.setOtherTxFee3Uom(feeTypeStr(taxSetting.getOthertx3FeeType()));
                hotelPriceResponse.setOtherTxFee3Incl(BooleanUtility.intToYN(taxSetting.getOthertx3FeeIsInclude()));
                hotelPriceResponse.setOtherTxFee3Desc(taxSetting.getOthertx3FeeDesc());

                // todo
                hotelPriceResponse.setParkInclude("");
                hotelPriceResponse.setBreakFee(null);
                hotelPriceResponse.setBreakType("");
            }

            // 不适应日期
            List<PriceUnapplicableDayEntity> hotelPriceUnapplicableDayList = hotelPriceUnapplicableDayMap.get(hotelId);
            if (CollectionUtils.isNotEmpty(hotelPriceUnapplicableDayList)) {
                IntStream.range(1, 11).forEach(index -> {
                    if (index > hotelPriceUnapplicableDayList.size()) {
                        return;
                    }
                    PriceUnapplicableDayEntity unapplicableDay = hotelPriceUnapplicableDayList.get(index - 1);
                    if (Objects.nonNull(unapplicableDay)) {
                        if (index == 1) {
                            hotelPriceResponse.setBd1Start(DateUtil.format(unapplicableDay.getStartDate(), "yyyy/MM/dd"));
                            hotelPriceResponse.setBd1End(DateUtil.format(unapplicableDay.getEndDate(), "yyyy/MM/dd"));
                        } else if (index == 2) {
                            hotelPriceResponse.setBd2Start(DateUtil.format(unapplicableDay.getStartDate(), "yyyy/MM/dd"));
                            hotelPriceResponse.setBd2End(DateUtil.format(unapplicableDay.getEndDate(), "yyyy/MM/dd"));
                        } else if (index == 3) {
                            hotelPriceResponse.setBd3Start(DateUtil.format(unapplicableDay.getStartDate(), "yyyy/MM/dd"));
                            hotelPriceResponse.setBd3End(DateUtil.format(unapplicableDay.getEndDate(), "yyyy/MM/dd"));
                        } else if (index == 4) {
                            hotelPriceResponse.setBd4Start(DateUtil.format(unapplicableDay.getStartDate(), "yyyy/MM/dd"));
                            hotelPriceResponse.setBd4End(DateUtil.format(unapplicableDay.getEndDate(), "yyyy/MM/dd"));
                        } else if (index == 5) {
                            hotelPriceResponse.setBd5Start(DateUtil.format(unapplicableDay.getStartDate(), "yyyy/MM/dd"));
                            hotelPriceResponse.setBd5End(DateUtil.format(unapplicableDay.getEndDate(), "yyyy/MM/dd"));
                        } else if (index == 6) {
                            hotelPriceResponse.setBd6Start(DateUtil.format(unapplicableDay.getStartDate(), "yyyy/MM/dd"));
                            hotelPriceResponse.setBd6End(DateUtil.format(unapplicableDay.getEndDate(), "yyyy/MM/dd"));
                        } else if (index == 7) {
                            hotelPriceResponse.setBd7Start(DateUtil.format(unapplicableDay.getStartDate(), "yyyy/MM/dd"));
                            hotelPriceResponse.setBd7End(DateUtil.format(unapplicableDay.getEndDate(), "yyyy/MM/dd"));
                        } else if (index == 8) {
                            hotelPriceResponse.setBd8Start(DateUtil.format(unapplicableDay.getStartDate(), "yyyy/MM/dd"));
                            hotelPriceResponse.setBd8End(DateUtil.format(unapplicableDay.getEndDate(), "yyyy/MM/dd"));
                        } else if (index == 9) {
                            hotelPriceResponse.setBd9Start(DateUtil.format(unapplicableDay.getStartDate(), "yyyy/MM/dd"));
                            hotelPriceResponse.setBd9End(DateUtil.format(unapplicableDay.getEndDate(), "yyyy/MM/dd"));
                        } else if (index == 10) {
                            hotelPriceResponse.setBd10Start(DateUtil.format(unapplicableDay.getStartDate(), "yyyy/MM/dd"));
                            hotelPriceResponse.setBd10End(DateUtil.format(unapplicableDay.getEndDate(), "yyyy/MM/dd"));
                        }
                    }
                });
            }

            // 设置自定义策略
            List<ProjectCustomBidStrategyEntity> hotelCustomBidStrategies = customBidStrategyMap.get(hotelId);
            if (CollectionUtils.isNotEmpty(hotelCustomBidStrategies)) {
                // 构建报价策略 map
                Map<Integer, ProjectCustomBidStrategyEntity> bidStrategyEntityMap = hotelCustomBidStrategies.stream().collect(Collectors.toMap(ProjectCustomBidStrategyEntity::getCustomTendStrategyId, Function.identity(), (o, n) -> n));

                // 构建自定义策略选项 map
                List<CustomBidStrategyOptionEntity> hotelCustomBidStrategyOptions = Optional.ofNullable(customBidStrategyOptionMap.get(hotelId)).orElse(Collections.emptyList());
                Map<Long, List<CustomBidStrategyOptionEntity>> hotelCustomBidStrategyOptionMap = hotelCustomBidStrategyOptions.stream().collect(Collectors.groupingBy(CustomBidStrategyOptionEntity::getStrategyId));

                if (CollectionUtils.isNotEmpty(customTendStrategies)) {
                    IntStream.rangeClosed(1, 100).forEach(index -> {
                        if (index > customTendStrategies.size()) {
                            return;
                        }
                        ProjectCustomTendStrategyEntity customTendStrategy = customTendStrategies.get(index - 1);
                        if (Objects.isNull(customTendStrategy)) {
                            return;
                        }

                        // 提取导出策略字段值
                        String supportProjectCustomBidStrategy = extractBidStrategyValue(bidStrategyEntityMap, customTendStrategy, hotelCustomBidStrategyOptionMap);

                        // 设置值
                        ReflectUtil.setFieldValue(hotelPriceResponse, "supportCustomBidStrategy" + index, supportProjectCustomBidStrategy);
                    });
                }
            }
        }
        return hotelTenderPriceResponses;
    }

    /**
     * 提取导出策略字段值
     */
    private static String extractBidStrategyValue(Map<Integer, ProjectCustomBidStrategyEntity> bidStrategyEntityMap, ProjectCustomTendStrategyEntity customTendStrategy, Map<Long, List<CustomBidStrategyOptionEntity>> hotelCustomBidStrategyOptionMap) {
        String supportProjectCustomBidStrategy = StringUtils.EMPTY;
        ProjectCustomBidStrategyEntity customBidStrategy = bidStrategyEntityMap.get(customTendStrategy.getId().intValue());

        // 没有配置报价策略, 直接返回空串
        if (Objects.isNull(customBidStrategy)) {
            return supportProjectCustomBidStrategy;
        }
        if (CustomStrategyTypeEnum.YSE_OR_NO.key == customTendStrategy.getStrategyType()) {
            supportProjectCustomBidStrategy = BooleanUtility.intToYN(customBidStrategy.getStrategyType());
        } else if (CustomStrategyTypeEnum.TEXT.key == customTendStrategy.getStrategyType()) {
            supportProjectCustomBidStrategy = customBidStrategy.getSupportStrategyText();
        } else if (CustomStrategyTypeEnum.isOptionType(customTendStrategy.getStrategyType())) {
            List<CustomBidStrategyOptionEntity> optionList = hotelCustomBidStrategyOptionMap.get(customTendStrategy.getId());
            if (CollectionUtils.isNotEmpty(optionList)) {
                supportProjectCustomBidStrategy = optionList.stream().filter(e -> e.getIsSupport() == 1)
                    .map(CustomBidStrategyOptionEntity::getOptionName).collect(Collectors.joining(";"));
            }
        }
        return supportProjectCustomBidStrategy;
    }

    /**
     * 计算方式字符串
     */
    private String feeTypeStr(Integer feeType){
        if (Objects.isNull(feeType)) {
            return "N";
        }
        if (feeType == 1) {
            return "P";
        } else if (feeType == 2) {
            return "F";
        }
        return "";
    }

    /**
     * 构建动态表头
     */
    public Map<String, String> buildCustomStrategyHeader(Integer projectId) {
        Map<String, String> dynamicHeaders = new HashMap<>(50);

        // 查询项目自定义采购策略
        List<ProjectCustomTendStrategyEntity> strategies = projectCustomTendStrategyMapper.selectByProjectId(projectId);
        // 最多 50
        IntStream.range(1, 51).forEach(index -> {
            if (index <= strategies.size()) {
                ProjectCustomTendStrategyEntity strategy = strategies.get(index - 1);
                dynamicHeaders.put("EXPORT_FIELD_CUSTOM_STRATEGY_" + index, strategy.getStrategyName());
            } else {
                dynamicHeaders.put("EXPORT_FIELD_CUSTOM_STRATEGY_" + index, "");
            }
        });
        return dynamicHeaders;
    }

    public void insertLanyonViewKeys(Integer projectId,  // 基础信息
                                     String baseInfo,
                                     String hotelVerify,
                                     String hotelFacilities,
                                     String bidInfo,
                                     String meetingRoomBidInfo,
                                     String longBidInfo,
                                     String hotelService,
                                     String userDefined,
                                     String mtgUserDefined,
                                     String larUnApplicableDayInfo,
                                     String baseServiceFee, String operator){
        ProjectLanyonViewKeysEntity projectLanyonViewKeys = new ProjectLanyonViewKeysEntity();
        projectLanyonViewKeys.setProjectId(projectId);
        projectLanyonViewKeys.setBaseInfo(baseInfo);
        projectLanyonViewKeys.setHotelVerify(hotelVerify);
        projectLanyonViewKeys.setHotelFacilities(hotelFacilities);
        projectLanyonViewKeys.setBidInfo(bidInfo);
        projectLanyonViewKeys.setMeetingRoomBidInfo(meetingRoomBidInfo);
        projectLanyonViewKeys.setLongBidInfo(longBidInfo);
        projectLanyonViewKeys.setHotelService(hotelService);
        projectLanyonViewKeys.setUserDefined(userDefined);
        projectLanyonViewKeys.setMtgUserDefined(mtgUserDefined);
        projectLanyonViewKeys.setLarUnApplicableDayInfo(larUnApplicableDayInfo);
        projectLanyonViewKeys.setBaseServiceFee(baseServiceFee);
        projectLanyonViewKeys.setModifier(operator);
        projectLanyonViewKeys.setCreator(operator);
        projectLanyonViewKeysMapper.insert(projectLanyonViewKeys);

    }

    public void updateLanyonViewKeys(
             Integer lanyonViewKeysId,
             Integer projectId,
             String baseInfo,
             String hotelVerify,
             String hotelFacilities,
             String bidInfo,
             String meetingRoomBidInfo,
             String longBidInfo,
             String hotelService,
             String userDefined,
             String mtgUserDefined,
             String larUnApplicableDayInfo,
             String baseServiceFee, String operator){
        ProjectLanyonViewKeysEntity projectLanyonViewKeys = new ProjectLanyonViewKeysEntity();
        projectLanyonViewKeys.setProjectLanyonViewKeysId(lanyonViewKeysId);
        projectLanyonViewKeys.setProjectId(projectId);
        projectLanyonViewKeys.setBaseInfo(baseInfo);
        projectLanyonViewKeys.setHotelVerify(hotelVerify);
        projectLanyonViewKeys.setHotelFacilities(hotelFacilities);
        projectLanyonViewKeys.setBidInfo(bidInfo);
        projectLanyonViewKeys.setMeetingRoomBidInfo(meetingRoomBidInfo);
        projectLanyonViewKeys.setLongBidInfo(longBidInfo);
        projectLanyonViewKeys.setHotelService(hotelService);
        projectLanyonViewKeys.setUserDefined(userDefined);
        projectLanyonViewKeys.setMtgUserDefined(mtgUserDefined);
        projectLanyonViewKeys.setLarUnApplicableDayInfo(larUnApplicableDayInfo);
        projectLanyonViewKeys.setBaseServiceFee(baseServiceFee);
        projectLanyonViewKeys.setModifier(operator);
        projectLanyonViewKeys.setCreator(operator);
        projectLanyonViewKeysMapper.updateById(projectLanyonViewKeys);

    }

    /**
     * 查询项目自定义采购策略信息
     */
    public List<ProjectBidCustomTendStrategyVO> queryProjectCustomTendStrategyInfo(int projectId) {
        // 查询策略
        List<ProjectBidCustomTendStrategyVO> strategies = projectCustomTendStrategyMapper.selectBidCustomTendStrategyInfoByProjectId(projectId);
        if (CollUtil.isEmpty(strategies)) {
            return strategies;
        }

        // 查询回复选项
        Set<Long> strategyIds = strategies.stream()
            .filter(e -> CustomStrategyTypeEnum.isOptionType(e.getStrategyType()))
            .map(ProjectBidCustomTendStrategyVO::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(strategyIds)) {
            return strategies;
        }
        List<CustomStrategyOptionVO> options = projectCustomStrategyOptionMapper.selectByStrategyIds(strategyIds);
        // 按策略 id 分组
        Map<Long, List<CustomStrategyOptionVO>> optionsMap = options.stream().collect(Collectors.groupingBy(CustomStrategyOptionVO::getStrategyId));
        // 设值
        strategies.forEach(e -> e.setOptions(optionsMap.get(e.getId())));
        return strategies;
    }




}
