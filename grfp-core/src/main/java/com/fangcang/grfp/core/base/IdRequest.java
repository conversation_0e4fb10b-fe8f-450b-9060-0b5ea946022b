package com.fangcang.grfp.core.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel(description = "id 操作请求")
@Getter
@Setter
public class IdRequest<T> extends BaseVO {

    private static final long serialVersionUID = -1186927428425686093L;

    @ApiModelProperty(value = "id", required = true)
    @NotNull
    protected T id;

}
