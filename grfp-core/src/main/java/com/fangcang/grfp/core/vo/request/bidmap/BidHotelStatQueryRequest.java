package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/25 10:52
 */
@ApiModel("酒店报价统计查询")
@Getter
@Setter
public class BidHotelStatQueryRequest extends PageQuery {

    //项目id
    @ApiModelProperty("项目ID")
    private Integer projectId;

    //城市code
    @ApiModelProperty("城市编码")
    private String cityCode;

    //酒店名称
    @ApiModelProperty("酒店名称 关键字")
    private String hotelName;

    // 酒店集团
    @ApiModelProperty("酒店集团ID")
    private Long hotelGroupId;

    //品牌id
    @ApiModelProperty("品牌ID")
    private Long hotelBrandId;

    @ApiModelProperty(hidden = true)
    //投标状态(0：未投标，1：新标，2：议价中，3：已中标，4：已否决，不传查所有)
    private Integer bidState;

    //登录用户id
    @ApiModelProperty(hidden = true)
    private Integer userId;

    // 员工ID
    @ApiModelProperty(hidden = true)
    private List<Integer> userIds;



}
