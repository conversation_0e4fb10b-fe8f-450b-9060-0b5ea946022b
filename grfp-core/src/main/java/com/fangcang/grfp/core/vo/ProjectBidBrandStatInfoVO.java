package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("项目报价品牌统计信息")
@Getter
@Setter
public class ProjectBidBrandStatInfoVO extends BaseVO {

    @ApiModelProperty("国家")
    private String countryCode;

    @ApiModelProperty("国家报价数量")
    private Integer countryBidCount;

    @ApiModelProperty("城市")
    private String cityCode;

    @ApiModelProperty("城市报价数量")
    private Integer cityBidCount;

    @ApiModelProperty("酒店集团")
    private Long hotelGroupId;

    @ApiModelProperty("酒店集团报价数量")
    private Integer hotelGroupBidCount;

    @ApiModelProperty("酒店品牌")
    private Long hotelBrandId;

    @ApiModelProperty("酒店品牌报价数量")
    private Integer hotelBrandBidCount;
}
