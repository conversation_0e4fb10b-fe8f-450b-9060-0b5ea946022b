package com.fangcang.grfp.core.vo.request.project;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(description = "查询项目酒店历史交易数据列表请求")
public class HotelHistoryTradeDataListRequest extends PageQuery {

    @ApiModelProperty(value = "项目 ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "酒店 ID")
    private Long hotelId;

    @ApiModelProperty(value = "品牌 ID")
    private Long hotelBrandId;

    @ApiModelProperty(value = "集团 ID")
    private Long hotelGroupId;

}
