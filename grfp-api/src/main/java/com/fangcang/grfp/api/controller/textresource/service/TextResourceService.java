package com.fangcang.grfp.api.controller.textresource.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.api.controller.textresource.vo.*;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.entity.TextResourceEntity;
import com.fangcang.grfp.core.mapper.TextResourceMapper;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.vo.TextResourceVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TextResourceService {

    @Autowired
    private TextResourceMapper textResourceMapper;

    public TextResourceVO get(GetTextResourceRequest getTextResourceRequest) {
        TextResourceEntity textResourceEntity = textResourceMapper.getByCode(getTextResourceRequest.getTextResourceType(),
                getTextResourceRequest.getTextResourceCode());
        if(textResourceEntity == null){
            return null;
        }
        return new TextResourceVO(textResourceEntity);
    }

    public PageVO<TextResourceVO> listPage(ListTextResourceRequest listTextResourceRequest){
        Page<TextResourceEntity> page = new Page<TextResourceEntity>(listTextResourceRequest.getPageIndex(), listTextResourceRequest.getPageSize());
        if(StringUtils.isNotBlank(listTextResourceRequest.getSearchTextResourceValueLike())) {
            String upperCase = listTextResourceRequest.getSearchTextResourceValueLike().toUpperCase();
            listTextResourceRequest.setSearchTextResourceValueLike(upperCase);
        }
        page = textResourceMapper.queryPageList(page,
                listTextResourceRequest.getSearchTextResourceType(),
                listTextResourceRequest.getSearchTextResourceCodeLike(),
                listTextResourceRequest.getSearchTextResourceValueLike());

        List<TextResourceVO> pageVOList = page.getRecords().stream().map(TextResourceVO::new).collect(Collectors.toList());
        return new PageVO<>((int)page.getTotal(), (int)page.getPages(), pageVOList);
    }

    @Transactional
    public TextResourceVO insertTextResource(UserSession userSession, InsertTextResourceRequest insertTextResourceRequest) {
        // Validate textResource user code
        TextResourceEntity textResource = textResourceMapper.getByCode(insertTextResourceRequest.getTextResourceType(), insertTextResourceRequest.getTextResourceCode());
        if(textResource != null) {
            AppUtility.serviceError(ErrorCode.RECORD_ALREADY_EXISTED);
        }

        // Insert textResource
        textResource = new TextResourceEntity();

        textResource.setTextResourceCode(insertTextResourceRequest.getTextResourceCode());
        textResource.setTextResourceType(insertTextResourceRequest.getTextResourceType());
        textResource.setValueEnUs(insertTextResourceRequest.getValueEnUs());
        textResource.setValueZhCn(insertTextResourceRequest.getValueZhCn());
        textResource.setCreator(userSession.getUsername());
        int insertResult = textResourceMapper.insert(textResource);
        if(insertResult == 0) {
            log.error("textResourceMapper.insert failed");
            AppUtility.serviceError(ErrorCode.INSERT_TEXT_RESOURCE_FAILED);
        }

        // Reurn textResource VO
        return new TextResourceVO(textResource);
    }

    public TextResourceVO updateTextResource(UserSession userSession, UpdateTextResourceRequest updateTextResourceRequest) {
        // Validate textResource user code
        TextResourceEntity textResource = textResourceMapper.getByCode(updateTextResourceRequest.getTextResourceType(), updateTextResourceRequest.getTextResourceCode());
        if(textResource == null) {
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }
        textResource.setValueEnUs(updateTextResourceRequest.getValueEnUs());
        textResource.setValueZhCn(updateTextResourceRequest.getValueZhCn());
        textResource.setModifier(userSession.getUsername());
        textResource.setModifyTime(new Date());
        int updateResult = textResourceMapper.updateById(textResource);
        if(updateResult == 0) {
            log.error("textResourceMapper.updateById failed");
            AppUtility.serviceError(ErrorCode.UPDATE_TEXT_RESOURCE_FAILED);
        }

        // Reurn textResource VO
        return new TextResourceVO(textResource);
    }


    public void delete(UserSession userSession, DeleteTextResourceRequest deleteTextResourceRequest){
        TextResourceEntity textResource = textResourceMapper.getByCode(deleteTextResourceRequest.getTextResourceType(), deleteTextResourceRequest.getTextResourceCode());
        if(textResource == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }

        int deleteResult = textResourceMapper.deleteByCode(textResource.getTextResourceType(), textResource.getTextResourceCode());
        if(deleteResult == 0) {
            log.error("textResourceMapper.deleteById failed");
            AppUtility.serviceError(ErrorCode.DELETE_TEXT_RESOURCE_FAILED);
        }
    }

}
