package com.fangcang.grfp.core.vo.response.hotel;

import java.math.BigDecimal;

public class OtaHotelMinMaxPriceVO {

    // 酒店ID
    private Long hotelId;

    // 最低价格
    private BigDecimal minPrice;

    // 最高价格
    private BigDecimal maxPrice;

    public Long getHotelId() {
        return hotelId;
    }

    public void setHotelId(Long hotelId) {
        this.hotelId = hotelId;
    }

    public BigDecimal getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(BigDecimal minPrice) {
        this.minPrice = minPrice;
    }

    public BigDecimal getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(BigDecimal maxPrice) {
        this.maxPrice = maxPrice;
    }
}
