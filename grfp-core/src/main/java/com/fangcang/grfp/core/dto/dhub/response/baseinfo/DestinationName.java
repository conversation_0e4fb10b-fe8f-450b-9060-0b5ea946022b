package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("目的地名称信息")
public class DestinationName extends BaseVO {

    /**
     * 城市名称
     */
    @ApiModelProperty("城市名称")
    private String cityName;

    /**
     * 城市编码
     */
    @ApiModelProperty("城市编码")
    private String cityCode;

    /**
     * 搜索的目的地
     */
    @ApiModelProperty("搜索的目的地")
    private String name;

    /**
     * 省份编码
     */
    @ApiModelProperty("省份编码")
    private String provinceCode;

    /**
     * 省份名称
     */
    @ApiModelProperty("省份名称")
    private String provinceName;

    /**
     * 国家代码
     */
    @ApiModelProperty("国家代码")
    private String countryCode;

    /**
     * 国家名称
     */
    @ApiModelProperty("国家名称")
    private String countryName;

    /**
     * 酒店ID
     */
    @ApiModelProperty("酒店ID")
    private String hotelId;

    /**
     * 谷歌纬度
     */
    @ApiModelProperty("谷歌纬度")
    private String latGoogle;

    /**
     * 谷歌经度
     */
    @ApiModelProperty("谷歌经度")
    private String lngGoogle;

    /**
     * 语言
     */
    @ApiModelProperty("语言")
    private String language;

}
