package com.fangcang.grfp.core.vo.response.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@ApiModel("项目报价去年统计")
@Getter
@Setter
public class QueryProjectHotelBidStatResponse extends BaseVO {
    // 去年总间数
    @ApiModelProperty("去年总间数")
    private Integer lastYearRoomNightCount;

    // 去年间夜数排名
    @ApiModelProperty("去年间夜数排名")
    private Integer lastYearRoomNightOrder;

    // 去年成交金额
    @ApiModelProperty("去年成交金额")
    private BigDecimal lastYearAmount;

    // 去年成交金额排名
    @ApiModelProperty("去年成交金额排名")
    private Integer lastYearAmountOrder;

    // 去年项目服务分
    @ApiModelProperty("去年项目服务分")
    private BigDecimal lastYearServicePoint;

    // 去年项目服务分排名
    @ApiModelProperty("去年项目服务分排名")
    private Integer lastYearServicePointOrder;

    // OTA排名
    @ApiModelProperty("OTA排名")
    private String rating;

    // OTA排名吗
    @ApiModelProperty("OTA排名码")
    private Integer ratingOrder;

}


