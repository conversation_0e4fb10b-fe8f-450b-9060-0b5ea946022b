package com.fangcang.grfp.core.vo.request.project;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(description = "查询自定义采购策略请求")
public class QueryCustomTendStrategyRequest extends PageQuery {

    private static final long serialVersionUID = -1489114549303418283L;

    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "策略名称")
    private String strategyName;

    @ApiModelProperty(value = "策略类型")
    private Integer strategyType;


}
