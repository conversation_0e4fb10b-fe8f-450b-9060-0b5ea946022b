package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.HotelGroupEntity;
import com.fangcang.grfp.core.enums.YesOrNoEnum;
import com.fangcang.grfp.core.vo.BidStateCountVO;
import com.fangcang.grfp.core.vo.HotelGroupVO;
import com.fangcang.grfp.core.vo.ListHotelGroupDataVO;
import com.fangcang.grfp.core.vo.request.ListHotelGroupDataRequest;
import com.fangcang.grfp.core.vo.response.hotelgroup.QueryBidStatCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 酒店集团 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface HotelGroupMapper extends BaseMapper<HotelGroupEntity> {

    void batchUpsert(@Param("list") Collection<HotelGroupEntity> hotelGroupEntities);

    /**
     * select all active group id
     */
    default Set<Long> selectAllActiveGroupId() {
        LambdaQueryWrapper<HotelGroupEntity> queryWrapper = Wrappers.lambdaQuery(HotelGroupEntity.class)
            .eq(HotelGroupEntity::getIsActive, YesOrNoEnum.YES.getKey())
            .select(HotelGroupEntity::getHotelGroupId);
        return this.selectList(queryWrapper).stream().map(HotelGroupEntity::getHotelGroupId).collect(Collectors.toSet());
    }

    default void updateActiveByGroupIds(Collection<Long> groupIds, Integer active) {
        LambdaUpdateWrapper<HotelGroupEntity> updateWrapper = Wrappers.lambdaUpdate(HotelGroupEntity.class)
            .in(HotelGroupEntity::getHotelGroupId, groupIds)
            .set(HotelGroupEntity::getIsActive, active);
        this.update(null, updateWrapper);
    }

    List<HotelGroupVO> selectHotelGroupNameList(@Param("languageId") Integer languageId,
                                                @Param("hotelGroupName") String hotelGroupName,
                                                @Param("limitCount") Integer limitCount);

    IPage<ListHotelGroupDataVO> listDataPage(IPage<ListHotelGroupDataVO> page, @Param("languageId") Integer languageId,
                                             @Param("query") ListHotelGroupDataRequest query);



}
