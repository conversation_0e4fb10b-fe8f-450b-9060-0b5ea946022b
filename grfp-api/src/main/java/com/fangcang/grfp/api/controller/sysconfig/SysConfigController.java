package com.fangcang.grfp.api.controller.sysconfig;

import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.sysconfig.service.SysConfigService;
import com.fangcang.grfp.api.controller.sysconfig.vo.*;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.constant.UserPermission;
import com.fangcang.grfp.core.usersession.RequiresPermissions;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.vo.ListSysConfigVO;
import com.fangcang.grfp.core.vo.request.ListSysConfigRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Api(tags = "系统配置")
@RestController
@RequestMapping("/Api/SysConfig")
public class SysConfigController extends BaseController {

    @Autowired
    private SysConfigService sysConfigService;

    @ApiOperation("获取系统配置")
    @PostMapping("/GetSysConfig")
    @ResponseBody
    @RequiresPermissions(UserPermission.SYS_CONFIG)
    public Result<SysConfigVO> getSysConfig(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody GetSysConfigRequest getSysConfigRequest) {
        SysConfigVO sysConfigVO = sysConfigService.getSysConfigVO(getSysConfigRequest.getSysConfigCode());
        return Result.ok(sysConfigVO);
    }

    @ApiOperation("系统配置列表")
    @PostMapping("/List")
    @ResponseBody
    @RequiresPermissions(UserPermission.SYS_CONFIG)
    public Result<PageVO<ListSysConfigVO>> list(HttpServletRequest request, HttpServletResponse response,
                                                @Valid @RequestBody ListSysConfigRequest listSysConfigRequest) {
        return Result.ok(sysConfigService.querySysConfigVOPageList(listSysConfigRequest));
    }

    @ApiOperation("新增系统配置")
    @PostMapping("/Add")
    @ResponseBody
    @RequiresPermissions(UserPermission.SYS_CONFIG)
    @UserAuditLog("系统配置-新增")
    public Result<ListSysConfigVO> add(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody AddSysConfigRequest addSysConfigRequest) {
        return Result.ok(sysConfigService.add(UserSession.get(), addSysConfigRequest));
    }

    @ApiOperation("修改系统配置")
    @PostMapping("/Update")
    @ResponseBody
    @RequiresPermissions(UserPermission.SYS_CONFIG)
    @UserAuditLog("系统配置-修改")
    public Result<ListSysConfigVO> update(HttpServletRequest request, HttpServletResponse response,
                                              @Valid @RequestBody UpdateSysConfigRequest updateSysConfigRequest) {
        return Result.ok(sysConfigService.update(UserSession.get(), updateSysConfigRequest));
    }

    @ApiOperation("删除系统配置")
    @PostMapping("/Delete")
    @ResponseBody
    @RequiresPermissions(UserPermission.SYS_CONFIG)
    @UserAuditLog("系统配置-删除")
    public Result<Void> delete(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody DeleteSysConfigRequest deleteSysConfigRequest) {
        sysConfigService.delete(UserSession.get(), deleteSysConfigRequest);
        return Result.ok();
    }


}
