<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HotelHexagonLngLatMapper">

    <select id="selectByHotelId" resultType="com.fangcang.grfp.core.entity.HotelHexagonLngLatEntity">
        SELECT *
        FROM t_hotel_hexagon_lng_lat
        WHERE hotel_id = #{hotelId,jdbcType=BIGINT}
    </select>

    <update id="updateByHotelId">
        UPDATE
            t_hotel_hexagon_lng_lat
        SET hexagon_lng_lat_10 = #{entity.hexagonLngLat10,jdbcType=VARCHAR},
            lng_google         = #{entity.lngGoogle,jdbcType=DOUBLE},
            lat_google         = #{entity.latGoogle,jdbcType=DOUBLE}
        WHERE hotel_id = #{entity.hotelId,jdbcType=BIGINT}
    </update>
</mapper>
