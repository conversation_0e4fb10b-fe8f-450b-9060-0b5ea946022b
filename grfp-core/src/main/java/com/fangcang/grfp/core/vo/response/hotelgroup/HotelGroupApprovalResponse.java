package com.fangcang.grfp.core.vo.response.hotelgroup;

import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel("酒店集团审核列表响应")
@Getter
@Setter
public class HotelGroupApprovalResponse {

    @ApiModelProperty("项目ID")
    private Integer projectId;

    @ApiModelProperty("项目报价ID")
    private Integer projectIntentHotelId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("发起机构名称")
    private String publishOrgName;

    @ApiModelProperty("签约类型")
    private String tenderTypeDesc;

    @ApiModelProperty("项目状态")
    private String projectStatusDesc;

    @ApiModelProperty("报价状态")
    private String bidStatusDesc;

    //评标结果时间 yyyy-MM-dd
    @ApiModelProperty("评标结果时间 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date bidResultTime;


    //招标开始时间 yyyy-MM-dd
    @ApiModelProperty("招标开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date bidStartTime;

    //招标结束时间 yyyy-MM-dd
    @ApiModelProperty("招标结束时间 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date bidEndTime;

    @ApiModelProperty("报价起止时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone= RfpConstant.TIME_ZONE)
    private Date firstBidStartTime;
    @ApiModelProperty("报价截止时间")
    private Date firstBidEndTime;

    @ApiModelProperty("签约公布时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone= RfpConstant.TIME_ZONE)
    private Date contractPublishTime;

    @ApiModelProperty("集团销售姓名")
    private String groupSalesName;

    @ApiModelProperty("酒店ID")
    private Long hotelId;

    @ApiModelProperty("酒店名称")
    private String hotelName;

    @ApiModelProperty("城市")
    private String cityName;

    // 投标价
    @ApiModelProperty("投标价")
    private BigDecimal basePrice;

    @ApiModelProperty("是否包含早餐")
    private Integer isIncludeBreakfast;

    @ApiModelProperty("早餐类型")
    private String breakfastNum;


    @ApiModelProperty("报价来源 报价机构类型 2:酒店，4:酒店集团")
    private Integer bidOrgType;

    // 内部字段，用于数据处理
    @ApiModelProperty(hidden = true)
    private String cityCode;

    @ApiModelProperty(hidden = true)
    private String hotelNameEnUs;

    @ApiModelProperty(hidden = true)
    private String hotelNameZhCn;

    @ApiModelProperty(hidden = true)
    private String cityNameEnUs;

    @ApiModelProperty(hidden = true)
    private String cityNameZhCn;

    @ApiModelProperty(hidden = true)
    private Integer tenderType;

    @ApiModelProperty(hidden = true)
    private Integer projectState;

    @ApiModelProperty(hidden = true)
    private Integer bidState;


}