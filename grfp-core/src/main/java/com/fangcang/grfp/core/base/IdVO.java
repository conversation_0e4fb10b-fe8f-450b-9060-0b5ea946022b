package com.fangcang.grfp.core.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@ApiModel(description = "id VO")
public class IdVO<T> extends BaseVO {

    private static final long serialVersionUID = 2410313264189586546L;

    @ApiModelProperty(value = "ID")
    private T id;


}
