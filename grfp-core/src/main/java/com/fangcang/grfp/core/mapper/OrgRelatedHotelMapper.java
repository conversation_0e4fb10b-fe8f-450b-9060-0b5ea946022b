package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fangcang.grfp.core.entity.OrgRelatedHotelBrandEntity;
import com.fangcang.grfp.core.entity.OrgRelatedHotelEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.vo.HotelRelatedOrgContactInfoVO;
import com.fangcang.grfp.core.vo.OrgRelatedHotelBrandVO;
import com.fangcang.grfp.core.vo.OrgRelatedHotelVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 机构关联酒店 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
public interface OrgRelatedHotelMapper extends BaseMapper<OrgRelatedHotelEntity> {

    default int deleteByOrgId(Integer orgId){
        return delete(new LambdaQueryWrapper<OrgRelatedHotelEntity>().eq(OrgRelatedHotelEntity::getOrgId, orgId));
    }
    default int selectCountByHotelId(Long hotelId) {
        LambdaQueryWrapper<OrgRelatedHotelEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrgRelatedHotelEntity::getHotelId, hotelId);
        return selectCount(wrapper);
    }

    default OrgRelatedHotelEntity selectOne(Integer hotelOrgId, Long hotelId) {
        LambdaQueryWrapper<OrgRelatedHotelEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrgRelatedHotelEntity::getOrgId, hotelOrgId);
        wrapper.eq(OrgRelatedHotelEntity::getHotelId, hotelId);
        return this.selectOne(wrapper);
    }

    default int delete(Integer hotelOrgId, Long hotelId) {
        LambdaQueryWrapper<OrgRelatedHotelEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrgRelatedHotelEntity::getOrgId, hotelOrgId);
        wrapper.eq(OrgRelatedHotelEntity::getHotelId, hotelId);
        return this.delete(wrapper);
    }


    IPage<OrgRelatedHotelVO> queryPageList(IPage<?> page,
                                           @Param("languageId") Integer languageId,
                                           @Param("orgId") Integer orgId);


    List<OrgRelatedHotelEntity> queryOrgRelatedHotelList(Integer orgId);

    /**
     * 根据酒店ID查询
     */
    default List<OrgRelatedHotelEntity> selectByHotelIds(Collection<Long> hotelIds) {
        LambdaQueryWrapper<OrgRelatedHotelEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrgRelatedHotelEntity::getHotelId, hotelIds);
        return selectList(queryWrapper);
    }
}
