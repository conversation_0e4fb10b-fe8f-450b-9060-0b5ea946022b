package com.fangcang.grfp.core.manager;

import com.fangcang.grfp.core.constant.RedisConstant;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.entity.HotelHexagonLngLatEntity;
import com.fangcang.grfp.core.mapper.HotelHexagonLngLatMapper;
import com.fangcang.grfp.core.util.HexagonStatUtil;
import com.fangcang.grfp.core.util.JsonUtil;
import com.fangcang.grfp.core.vo.response.HexagonsStat;
import com.fangcang.grfp.core.vo.response.HotelResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 查询酒店报价热力图
 */
@Service
@Slf4j
public class HotelHexagonLngLatManager {

    @Resource
    private HotelHexagonLngLatMapper hotelHexagonLngLatMapper;

    @Resource
    private RedissonClient redissonClient;

    // 查询如果不存在就新增一条
    public HotelHexagonLngLatEntity getByHotelIfNullThenInit(HotelResponse hotelResponse) {
        // 定义返回值
        HotelHexagonLngLatEntity hotelHexagonLngLat = null;

        // 获取锁
        String lockKey = String.format(RedisConstant.GENERATE_HOTEL_HEXAGON_LOCK_KEY, hotelResponse.getHotelId());
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(300, TimeUnit.SECONDS)) {
                hotelHexagonLngLat = hotelHexagonLngLatMapper.selectByHotelId(hotelResponse.getHotelId());
                hotelHexagonLngLat = insertOrUpdate(hotelResponse, hotelHexagonLngLat);
            } else {
                log.info("获取生产酒店六边形锁失败, 酒店 ID : {} ", hotelResponse.getHotelId());
                return null;
            }
        } catch (Throwable e) {
            log.error("生成酒店六边形失败,酒店 ID : {} ", hotelResponse.getHotelId(), e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return hotelHexagonLngLat;
    }

    public void generateHotelIfNullThenInit(HotelResponse hotelResponse) {
        // 获取锁
        String lockKey = String.format(RedisConstant.GENERATE_HOTEL_HEXAGON_LOCK_KEY, hotelResponse.getHotelId());
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(300, TimeUnit.SECONDS)) {
                HotelHexagonLngLatEntity hotelHexagonLngLat = hotelHexagonLngLatMapper.selectByHotelId(hotelResponse.getHotelId());
                insertOrUpdate(hotelResponse, hotelHexagonLngLat);
            } else {
                log.error("生成酒店六边形失败,酒店 ID : {} ", hotelResponse.getHotelId());
            }
        } catch (Throwable e) {
            log.error("生成酒店六边形失败,酒店 ID : {} ", hotelResponse.getHotelId(), e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    /**
     * 新增或者修改六边形
     */
    private HotelHexagonLngLatEntity insertOrUpdate(HotelResponse hotelResponse, HotelHexagonLngLatEntity hotelHexagonLngLat){
        if (hotelHexagonLngLat == null) {
            if (hotelResponse.getLngGoogle() != null && hotelResponse.getLatGoogle() != null) {
                hotelHexagonLngLat = new HotelHexagonLngLatEntity();
                hotelHexagonLngLat.setHotelId(hotelResponse.getHotelId());
                hotelHexagonLngLat.setLatGoogle(hotelResponse.getLatGoogle());
                hotelHexagonLngLat.setLngGoogle(hotelResponse.getLngGoogle());
                hotelHexagonLngLat.setCreator(RfpConstant.CREATOR);
                List<HexagonsStat> uniqueHexagonList = new ArrayList<>();
                Set<String> uniqueHexagonStringSet = new HashSet<>();
                HexagonStatUtil.generateHexagons(new BigDecimal[]{hotelResponse.getLngGoogle(), hotelResponse.getLatGoogle()}, 6, uniqueHexagonStringSet, uniqueHexagonList);
                hotelHexagonLngLat.setHexagonLngLat10(JsonUtil.objectToJson(uniqueHexagonList));
                HotelHexagonLngLatEntity dbHotelHexagonLngLat = hotelHexagonLngLatMapper.selectByHotelId(hotelResponse.getHotelId());
                if (dbHotelHexagonLngLat == null) {
                    hotelHexagonLngLatMapper.insert(hotelHexagonLngLat);
                }
            }
            // 根据酒店经纬度判断是否需要更新
        } else if (!Objects.equals(hotelHexagonLngLat.getLatGoogle(), hotelResponse.getLatGoogle())
            || !Objects.equals(hotelHexagonLngLat.getLngGoogle(), hotelResponse.getLngGoogle())) {
            hotelHexagonLngLat = new HotelHexagonLngLatEntity();
            hotelHexagonLngLat.setHotelId(hotelResponse.getHotelId());
            hotelHexagonLngLat.setLatGoogle(hotelResponse.getLatGoogle());
            hotelHexagonLngLat.setLngGoogle(hotelResponse.getLngGoogle());
            List<HexagonsStat> hexagonsStatList = generateHexagons(hotelHexagonLngLat.getLngGoogle(), hotelHexagonLngLat.getLatGoogle());
            hotelHexagonLngLat.setHexagonLngLat10(JsonUtil.objectToJson(hexagonsStatList));
            hotelHexagonLngLatMapper.updateByHotelId(hotelHexagonLngLat);
        }
        return hotelHexagonLngLat;
    }


    private List<HexagonsStat> generateHexagons(BigDecimal lngBaidu, BigDecimal latBaidu) {
        List<HexagonsStat> uniqueHexagonList = new ArrayList<>();
        Set<String> uniqueHexagonStringSet = new HashSet<>();
        HexagonStatUtil.generateHexagons(new BigDecimal[]{lngBaidu, latBaidu}, 6, uniqueHexagonStringSet, uniqueHexagonList);
        return uniqueHexagonList;
    }
}
