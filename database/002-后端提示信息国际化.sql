-- 错误代码国际化资源

INSERT INTO t_text_resource (text_resource_code, text_resource_type, value_en_us, value_zh_cn, creator, create_time) VALUES
-- 参数校验相关
('VALIDATE_REQUEST_PARAMETER_CANNOT_BE_EMPTY', 2, 'Parameter cannot be empty', '参数不能为空字符串', 'system', NOW()),
('VALIDATE_REQUEST_PARAMETER_CANNOT_BE_NULL', 2, 'Parameter cannot be null', '参数不能为空值', 'system', NOW()),
('VALIDATE_REQUEST_PARAMETER_LENGTH_MUST_BE_BETWEEN_X_AND_Y', 2, 'Parameter length must be between {0} and {1}', '参数长度必须大于{0}小于{1}', 'system', NOW()),
('VALIDATE_REQUEST_PARAMETER_LENGTH_MUST_BE_X', 2, 'Parameter length must be {0}', '参数长度必须为{0}', 'system', NOW()),
('VALIDATE_REQUEST_ENUM_PARAMETER_ERROR', 2, 'Invalid enum parameter', '参数枚举类型错误', 'system', NOW()),
('VALIDATE_REQUEST_PARAMETER_MUST_BE_GREATER_THAN_OR_EQUAL_TO_X', 2, 'Parameter must be greater than or equal to {0}', '参数必须大于等于{0}', 'system', NOW()),
('VALIDATE_REQUEST_PARAMETER_MUST_BE_LESS_THAN_OR_EQUAL_TO_X', 2, 'Parameter must be less than or equal to {0}', '参数必须小于等于{0}', 'system', NOW()),
('VALIDATE_REQUEST_PARAMETER_MUST_MATCH_PATTERN', 2, 'Parameter must match the required pattern', '参数必须匹配正则', 'system', NOW()),
('VALIDATE_REQUEST_PARAMETER_RANGE_MUST_BE_BETWEEN_X_AND_Y', 2, 'Parameter value must be between {0} and {1}', '参数值范围必须在{0}和{1}之间', 'system', NOW()),
('VALIDATE_REQUEST_PARAMETER_MUST_BE_BETWEEN_X', 2, 'Parameter value must be {0}', '参数值必须为{0}', 'system', NOW()),

-- 系统错误相关
('SYSTEM_ERROR', 2, 'System error', '系统错误', 'system', NOW()),
('REQUEST_PARAMETER_ERROR', 2, 'Request parameter error', '请求参数错误', 'system', NOW()),
('SIZE_IS_TOO_LARGE', 2, 'File size exceeds limit', '文件大小超出限制', 'system', NOW()),

-- 用户会话相关
('INVALIDATE_USER_SESSION_PLEASE_LOGIN', 2, 'Session expired, please login again', '登录信息已失效，请重新登录', 'system', NOW()),
('USER_SESSION_IS_NULL', 2, 'Session is null', 'Session会话为空', 'system', NOW()),
('NO_PERMISSION_ACCESS', 2, 'No permission to access', '没有权限访问', 'system', NOW()),

-- 公共操作相关
('RECORD_ALREADY_EXISTED', 2, 'Record already exists', '记录已存在', 'system', NOW()),
('ADD_FAILED', 2, 'Add failed', '添加失败', 'system', NOW()),
('DELETE_FAILED', 2, 'Delete failed', '删除失败', 'system', NOW()),
('UPDATE_FAILED', 2, 'Update failed', '更新失败', 'system', NOW()),
('DATA_NOT_FOUND', 2, 'Data not found', '数据未找到', 'system', NOW()),
('OPERATE_TOO_FREQUENCY', 2, 'Operation too frequent, please try again later', '您操作的频率过快，请稍后再操作', 'system', NOW()),
('ONLY_ADMIN_HAS_PERMISSION_UPDATE', 2, 'Only admin has permission to update', '只有管理员有权限修改', 'system', NOW()),
('ONLY_ADMIN_HAS_PERMISSION_ADD', 2, 'Only admin has permission to add', '只有管理员有权限添加', 'system', NOW()),
('IMPORT_RECORD_NOT_EXIST', 2, 'Import record does not exist', '导入记录不存在', 'system', NOW()),

-- 验证码相关
('EMAIL_IS_REGISTERED', 2, 'Email is already registered', '邮箱已经注册', 'system', NOW()),
('EMAIL_NOT_REGISTERED', 2, 'Email is not registered', '电邮未注册', 'system', NOW()),
('AUDITING_USER', 2, 'User under review', '用户审核中', 'system', NOW()),
('NOT_SUPPORT_BUSSINESS_TYPE', 2, 'Business type not supported', '不支持的业务类型', 'system', NOW()),
('SEND_SMS_CODE_EXCEPTION', 2, 'SMS code sending exception', '发送验证码异常', 'system', NOW()),
('SEND_SMS_CODE_ERROR', 2, 'Failed to send SMS code', '发送验证码失败', 'system', NOW()),
('INVALIDATE_VERIFY_CODE_PLEASE_RESEND', 2, 'Verification code expired, please resend', '验证码失效，请重新发送', 'system', NOW()),
('VERIFY_CODE_ERROR', 2, 'Verification code is incorrect', '验证码不正确', 'system', NOW()),

-- 登录相关
('LOGIN_PASSWORD_CANNOT_BE_EMPTY', 2, 'Login password cannot be empty', '登录密码不能为空', 'system', NOW()),
('VERIFY_CODE_CANNOT_BE_EMPTY', 2, 'Verification code cannot be empty', '验证码不能为空', 'system', NOW()),
('LOGIN_ACCOUNT_FORMAT_ERROR', 2, 'Login account format error', '登录账号格式不正确', 'system', NOW()),
('NOT_EXISTS_USER', 2, 'User does not exist', '用户不存在', 'system', NOW()),
('LOGIN_PASSWORD_ERROR', 2, 'Login password error', '登录密码错误', 'system', NOW()),
('LOGIN_PASSWORD_FAILED', 2, 'Password login failed', '密码登录失败', 'system', NOW()),
('INVALID_USER', 2, 'Invalid user', '无效用户', 'system', NOW()),

-- 找回密码
('NOT_EXISTS_EMAIL', 2, 'Email does not exist', '电邮不存在', 'system', NOW()),

-- 机构相关
('NOT_EXIST_ORG', 2, 'Organization does not exist', '机构不存在', 'system', NOW()),
('INVALID_ORG', 2, 'Invalid organization', '无效机构', 'system', NOW()),
('ORG_EXIST_USER', 2, 'Organization has existing users', '机构存在用户', 'system', NOW()),

-- 文字资源
('INSERT_TEXT_RESOURCE_FAILED', 2, 'Failed to insert text resource', '新增文字资源失败', 'system', NOW()),
('UPDATE_TEXT_RESOURCE_FAILED', 2, 'Failed to update text resource', '修改文字资源失败', 'system', NOW()),
('DELETE_TEXT_RESOURCE_FAILED', 2, 'Failed to delete text resource', '删除文字资源失败', 'system', NOW()),

-- 机构管理
('PLEASE_SELECT_HOTEL_GROUP_BRAND', 2, 'Please select at least one brand permission', '至少选择一个品牌权限', 'system', NOW()),
('CONTACT_EMAIL_INVALIDATED', 2, 'Organization contact email is invalid', '机构联系电邮不正确', 'system', NOW()),
('FINANCIAL_CONTACT_NAME_CANNOT_BE_EMPTY', 2, 'Organization financial contact name cannot be empty', '机构财务联系人不能为空', 'system', NOW()),
('FINANCIAL_CONTACT_MOBILE_CANNOT_BE_EMPTY', 2, 'Organization financial contact mobile cannot be empty', '机构财务联系人电话不能为空', 'system', NOW()),
('FINANCIAL_CONTACT_EMAIL_INVALIDATED', 2, 'Organization financial contact email is invalid', '机构财务联系人邮箱不正确', 'system', NOW()),
('EXIST_THE_SAME_ORG_NAME', 2, 'Organization with same name already exists', '存在相同名称的机构', 'system', NOW()),
('ADD_ORG_FAILED_DUE_TO_NULL_HOTEL_ID', 2, 'Failed to add organization due to null hotel ID', '酒店id为空新增机构失败', 'system', NOW()),
('HOTEL_EXIST_RELATED_ORG', 2, 'Hotel already has related organization', '酒店已经存在关联机构', 'system', NOW()),
('CANNOT_VIEW_OTHER_ORG_INFO', 2, 'Cannot view other organization information', '不能查看其他机构信息', 'system', NOW()),
('CANNOT_UPDATE_PLATFORM_ORG', 2, 'Platform organization cannot be updated', '平台机构不能编辑', 'system', NOW()),
('ORG_MUST_BE_HOTEL_GROUP', 2, 'Organization must be hotel group', '机构必须为酒店集团', 'system', NOW()),
('ORG_MUST_BE_HOTEL', 2, 'Organization must be hotel', '机构必须为酒店', 'system', NOW()),
('HOTEL_INFO_IS_NULL', 2, 'Hotel information is null', '酒店信息为空', 'system', NOW()),
('HOTEL_ID_IS_NOT_NUMBER', 2, 'Hotel ID is not a number', '酒店ID不为数字', 'system', NOW()),
('HOTEL_ID_HAS_RELATED_ORG', 2, 'Hotel ID already has related organization', '酒店ID已经关联机构', 'system', NOW()),
('ORG_CONTACT_NAME_CANNOT_BE_EMPTY', 2, 'Organization contact name cannot be empty', '机构联系人不能为空', 'system', NOW()),
('CONTACT_MOBILE_CANNOT_BE_EMPTY', 2, 'Contact mobile cannot be empty', '联系电话不能为空', 'system', NOW()),
('HOTEL_ID_NOT_EXIST', 2, 'Hotel ID does not exist', '酒店ID不存在', 'system', NOW()),
('ADD_ORG_FAILED', 2, 'Failed to add organization', '新增机构失败', 'system', NOW()),
('HOTEL_NOT_EXIST', 2, 'Hotel does not exist', '酒店不存在', 'system', NOW()),
('HOTEL_ID_INVALID', 2, 'Hotel ID is invalid', '酒店ID无效', 'system', NOW()),
('HOTEL_NOT_BELONG_HOTEL_GROUP', 2, 'Hotel does not belong to hotel group', '酒店不属于酒店集团', 'system', NOW()),
('HOTEL_NOT_BELONG_HOTEL_GROUP_BRAND', 2, 'Hotel does not belong to hotel group brand', '酒店不属于酒店集团品牌', 'system', NOW()),

-- 上传相关
('UPLOAD_FILE_CANNOT_BE_NULL', 2, 'Upload file cannot be null', '上传文件不能为空', 'system', NOW()),
('UPLOAD_FILE_FILED', 2, 'Upload file failed', '上传文件失败', 'system', NOW()),
('UPLOAD_FILE_SIZE_LIMIT', 2, 'Upload file size limit exceeded', '上传文件大小限制', 'system', NOW()),
('UPLOAD_FILE_BUSSINESS_TYPE_ERROR', 2, 'Upload business type cannot be empty', '上传业务类型不能为空', 'system', NOW()),

-- 用户相关
('EXIST_THE_SAME_USER_EMAIL', 2, 'User with same email already exists', '存在相同用户邮箱', 'system', NOW()),
('CANNOT_UPDATE_OTHER_ORG_USER', 2, 'Cannot update users from other organizations', '不能操作其他机构用户', 'system', NOW()),
('USER_EMAIL_EXIST', 2, 'User email already exists', '用户电邮已经存在', 'system', NOW()),

-- 项目相关
('PROJECT_NOT_EXIST', 2, 'Project does not exist', '项目不存在', 'system', NOW()),
('THRID_BID_END_TIME_CANNOT_ARTER_BID_END_TIME', 2, 'Third bid end time cannot be after bid end time', '第三轮结束时间不能晚于报价结束时间', 'system', NOW()),
('THRID_BID_END_TIME_CANNOT_BEFORE_BID_START_TIME', 2, 'Third bid end time cannot be before bid start time', '第三轮结束时间不能早于报价开始时间', 'system', NOW()),
('SECOND_BID_END_TIME_RANG_UNPASS_VALIDATE', 2, 'Second bid end time range validation failed', '第二轮结束时间范围校验不通过', 'system', NOW()),
('FIRST_BID_END_TIME_RANG_UNPASS_VALIDATE', 2, 'First bid end time range validation failed', '第一轮结束时间范围检查不通过', 'system', NOW()),
('FIRST_BID_START_END_TIME_UNPASS_VALIDATE', 2, 'First bid start/end time validation failed', '第一轮报价时间输入错误', 'system', NOW()),
('SECOND_BID_START_AND_FIRST_END_TIME_UNPASS_VALIDATE', 2, 'Second bid start and first end time validation failed', '第二轮开始时间和第一轮结束时间校验不通过', 'system', NOW()),
('SECOND_BID_START_AND_END_TIME_UNPASS_VALIDATE', 2, 'Second bid start and end time validation failed', '第二轮开始和结束时间校验不通过', 'system', NOW()),
('SECOND_BID_END_TIME_AND_THIRD_BID_START_TIME_UNPASS_VALIDATE', 2, 'Second bid end time and third bid start time validation failed', '第二轮结束时间和第三轮开始时间校验不通过', 'system', NOW()),
('THIRD_BID_START_END_TIME_UNPASS_VALIDATE', 2, 'Third bid start/end time validation failed', '第三轮开始结束时间校验不通过', 'system', NOW()),
('ORG_CANNOT_ADD_PROJECT', 2, 'Organization cannot add project', '机构不能添加项目', 'system', NOW()),
('CANNOT_UPDATE_OTHER_ORG_PROJECT', 2, 'Cannot update projects from other organizations', '机构不能修改其他机构项目', 'system', NOW()),
('UPDATE_PROJECT_STATE_FAILED', 2, 'Failed to update project state', '修改项目状态失败', 'system', NOW()),
('INSERT_HOTEL_FAILED', 2, 'Failed to insert hotel', '新增酒店失败', 'system', NOW()),
('PROJECT_CUSTOM_TEND_STRATEGY_COUNT_EXCEED_LIMIT', 2, 'Project custom tender strategy count exceeds limit', '项目自定义策略数量超出限制', 'system', NOW()),
('PROJECT_CUSTOM_TEND_STRATEGY_NAME_EXIST', 2, 'Project custom tender strategy name already exists', '项目自定义策略名称已存在', 'system', NOW()),
('PROJECT_TEND_STRATEGY_LIMIT_PRICE_IS_NULL', 2, 'Project tender strategy limit price is null', '报价范围为空', 'system', NOW()),
('PROJECT_TEND_STRATEGY_PRICE_INVALID', 2, 'Project tender strategy price is invalid', '报价范围错误', 'system', NOW()),
('PROJECT_TEND_MAX_NOT_APPLICABLE_DAY_IS_NULL', 2, 'Project tender max non-applicable days is null', '酒店报价中产品不适用日期数总和不能超过天数必填', 'system', NOW()),
('PROJECT_TEND_MAX_NOT_APPLICABLE_DAY_IS_INVALID', 2, 'Project tender max non-applicable days is invalid', '酒店报价中产品不适用日期数总和不能超过天数无效', 'system', NOW()),
('PROJECT_TEND_MAX_ROOM_TYPE_COUNT_IS_NULL', 2, 'Project tender max room type count is null', '酒店报价最多不超过房档个数必填', 'system', NOW()),
('PROJECT_TEND_MAX_ROOM_TYPE_COUNT_IS_INVALID', 2, 'Project tender max room type count is invalid', '酒店报价最多不超过房档个数无效', 'system', NOW()),
('PROJECT_TEND_MAX_SEASON_DAY_IS_NULL', 2, 'Project tender max season days is null', '最大Season日期数不能为空', 'system', NOW()),
('PROJECT_TEND_MAX_SEASON_DAY_IS_INVALID', 2, 'Project tender max season days is invalid', '最大Season日期数无效', 'system', NOW()),
('HOTEL_HAS_BID_CANNOT_DELETE', 2, 'Hotel with bid cannot be deleted', '酒店已提供报价，无法删除', 'system', NOW()),
('GENERATE_PROJECT_HISTORY_STAT_NOT_FINISH', 2, 'Generate project history statistics not finished', '项目上一次统计未完成', 'system', NOW()),
('GENERATE_PROJECT_RECOMMEND_LEVEL_NOT_FINISH', 2, 'Generate project recommend level not finished', '项目上一次生成推荐等级未完成', 'system', NOW()),
('GENERATE_PROJECT_HISTORY_STAT_ERROR', 2, 'Failed to generate project history statistics', '生成项目POI统计失败', 'system', NOW()),
('GENERATE_PROJECT_RECOMMEND_LEVEL_ERROR', 2, 'Failed to generate project recommend level', '更新推荐酒店列表错误', 'system', NOW()),
('PROJECT__INTENT_HOTEL_SALES_CONTACT_EMAIL', 2, 'Hotel sales contact email cannot be empty', '酒店销售联系人邮箱不能为空', 'system', NOW()),
('PROJECT__INTENT_HOTEL_DISTRIBUTOR_CONTACT_NAME', 2, 'Hotel distributor contact name cannot be empty', '企业(分销商)跟进人姓名不能为空', 'system', NOW()),
('PROJECT__INTENT_HOTEL_DISTRIBUTOR_CONTACT_ID', 2, 'Hotel distributor contact ID cannot be empty', '企业(分销商)跟进人id不能为空', 'system', NOW()),
('PROJECT__INTENT_HOTEL_PLATFORM_CONTACT_NAME', 2, 'Hotel platform contact name cannot be empty', '平台跟进人姓名不能为空', 'system', NOW()),
('PROJECT__INTENT_HOTEL_PLATFORM_CONTACT_UID', 2, 'Hotel platform contact UID cannot be empty', '平台跟进人id不能为空', 'system', NOW()),

-- POI相关
('POI_ORG_ID_ILLEGAL', 2, 'POI organization ID is illegal', '机构ID不合法', 'system', NOW()),
('POI_NOT_EXIST', 2, 'POI does not exist', 'POI不存在', 'system', NOW()),
('POI_NO_PERMISSION', 2, 'No permission to operate this POI', '没有权限操作该POI', 'system', NOW()),
('POI_NAME_VALID', 2, 'POI name cannot be empty', 'POI名称不能为空', 'system', NOW()),
('POI_ADDRESS_VALID', 2, 'POI address cannot be empty', 'POI地址不能为空', 'system', NOW()),
('POI_LONGITUDE_INVALID', 2, 'POI longitude cannot be empty', 'POI经度不能为空', 'system', NOW()),
('POI_LATITUDE_INVALID', 2, 'POI latitude cannot be empty', 'POI纬度不能为空', 'system', NOW()),
('POI_CITY_NOT_EXIST', 2, 'POI city does not exist', 'POI城市不存在', 'system', NOW()),

-- 酒店相关
('ROOM_NIGHT_COUNT_INVALID', 2, 'Room night count is invalid', '间夜数数字无效', 'system', NOW()),
('AMOUNT_INVALID', 2, 'Amount is invalid', '金额无效', 'system', NOW()),

-- 国家省市
('CITY_NOT_EXIST', 2, 'City does not exist', '城市不存在', 'system', NOW()),

-- 导入相关
('IMPORT_EXCEL_TEMPLATE_NOT_MATCH', 2, 'Import Excel template does not match', '导入模板不匹配', 'system', NOW()),

-- 币种相关
('CURRENCY_CODE_EXIST', 2, 'Currency code already exists', '币种已存在', 'system', NOW()),
('CURRENCY_CODE_NOT_EXIST', 2, 'Currency code does not exist', '币种不存在', 'system', NOW()),

-- 酒店集团相关
('CANNOT_FOUND_RELATED_HOTEL_GROUP_BY_ORG', 2, 'Cannot find related hotel group by organization', '没有找到关联酒店集团', 'system', NOW()),
('CANNOT_FOUND_RELATED_HOTEL_BRAND_BY_ORG', 2, 'Cannot find related hotel brand by organization', '没有找到关联酒店集团品牌', 'system', NOW()),
('HOTEL_INTENT_GROUP_NOT_EXIST', 2, 'Hotel intent group does not exist', '酒店意向集团不存在', 'system', NOW()),

-- 酒店报价相关
('HOTEL_ALREADY_BID_CANNOT_REPEAT_BID', 2, 'Hotel already bid, cannot repeat bid', '酒店已经报价，不可重复报价', 'system', NOW()),
('CANNOT_BID_DUE_TO_ALREADY_SUBMIT_BID', 2, 'Cannot bid due to already submitted bid', '酒店已经提交报价，不能继续报价', 'system', NOW()),
('ADD_OR_UPDATE_HOTEL_PRICE_LEVEL_FAILED', 2, 'Failed to add or update hotel price level', '新增或者修改价格房档失败', 'system', NOW()),
('ADD_OR_UPDATE_HOTEL_PRICE_GROUP_FAILED', 2, 'Failed to add or update hotel price group', '新增或者修改价格组失败', 'system', NOW()),
('ADD_OR_UPDATE_PRICE_APPLICABLE_ROOM_FAILED', 2, 'Failed to add or update price applicable room', '新增或者修改房档房型失败', 'system', NOW()),
('ADD_OR_UPDATE_HOTEL_PRICE_FAILED', 2, 'Failed to add or update hotel price', '新增或者修改价格失败', 'system', NOW()),
('ROOM_TYPE_EXIST_IN_OTHER_PRICE_LEVEL', 2, 'Room type exists in other price level', '房型存在其他房档', 'system', NOW()),
('ADD_HOTEL_PRICE_LEVEL_FAILED', 2, 'Failed to add hotel price level', '新增价格房档失败', 'system', NOW()),
('ADD_HOTEL_PRICE_GROUP_FAILED', 2, 'Failed to add hotel price group', '新增价格组失败', 'system', NOW()),
('ADD_PRICE_APPLICABLE_ROOM_FAILED', 2, 'Failed to add price applicable room', '新增房档房型失败', 'system', NOW()),
('ADD_HOTEL_PRICE_FAILED', 2, 'Failed to add hotel price', '新增价格失败', 'system', NOW()),
('CANNOT_FOUND_PROJECT_INVITED_HOTEL_GROUP', 2, 'Cannot find project invited hotel group', '不能找到项目邀请酒店集团', 'system', NOW()),
('CANNOT_FOUND_PROJECT_INTENT_HOTEL', 2, 'Cannot find project intent hotel', '不能找到酒店意向报价', 'system', NOW()),

-- 酒店报价提交相关
('CANNOT_SUBMIT_BID_DUE_TO_BID_WINNING', 2, 'Cannot submit bid due to bid winning status', '已中签状态不允许重新提交报价', 'system', NOW()),
('CANNOT_SUBMIT_BID_DUE_TO_BID_REJECTED', 2, 'Cannot submit bid due to bid rejected status', '已否决状态不允许重新提交报价', 'system', NOW()),
('CANNOT_SUBMIT_BID_DUE_TO_REJECT_NEGOTIATION', 2, 'Cannot submit bid due to reject negotiation status', '保持原价状态不允许重新提交报价', 'system', NOW()),
('CANNOT_SUBMIT_BID_DUE_TO_HOTEL_GROUP_APPROVE_SATUS_WAITING', 2, 'Cannot submit bid due to hotel group approval waiting status', '待审核报价不允许重新提交报价', 'system', NOW()),
('EXIST_PROJECT_HOTEL_BID_STRATEGY', 2, 'Project hotel bid strategy already exists', '已经存在报价策略主键', 'system', NOW()),

-- 通知相关
('NOTIFY_BIDDER_CONTAINS_NO_BID_HOTEL', 2, 'Notification failed because the list contains hotels that have not submitted bids', '列表中包含未报价酒店，通知失败', 'system', NOW());

-- 导出报价
insert into t_text_resource(text_resource_code, text_resource_type, value_en_us, value_zh_cn)
values ('EXPORT_FIELD_GROUP_HOTEL_CODE', 1, 'Group Hotel Code', '酒店集团编码'),
       ('EXPORT_FIELD_GROUP_CODE', 1, 'Group Code', 'AMADEUS 连锁编码'),
       ('EXPORT_FIELD_HOTEL_CODE', 1, 'Hotel Code', '酒店编码'),
       ('EXPORT_FIELD_HOTEL_NAME', 1, 'Hotel Name', '酒店名称'),
       ('EXPORT_FIELD_PHONE', 1, 'Phone', '电话'),
       ('EXPORT_FIELD_ADDRESS', 1, 'Address', '地址'),
       ('EXPORT_FIELD_CITY', 1, 'City', '城市'),
       ('EXPORT_FIELD_PROVINCE', 1, 'Province', '省份'),
       ('EXPORT_FIELD_COUNTRY', 1, 'Country', '国家'),
       ('EXPORT_FIELD_FC_HOTEL_CODE', 1, 'FC Hotel Code', '房仓酒店编码'),
       ('EXPORT_FIELD_FC_HOTEL_NAME', 1, 'FC Hotel Name', '房仓酒店名称'),
       ('EXPORT_FIELD_FC_PHONE', 1, 'FC Phone', '房仓电话'),
       ('EXPORT_FIELD_FC_ADDRESS', 1, 'FC Address', '房仓地址'),
       ('EXPORT_FIELD_FC_CITY', 1, 'FC City', '房仓城市'),
       ('EXPORT_FIELD_FC_PROVINCE', 1, 'FC Province', '房仓省份'),
       ('EXPORT_FIELD_FC_COUNTRY', 1, 'FC Country', '房仓国家'),
       ('EXPORT_FIELD_SALES_NAME', 1, 'Sales Name', '销售人姓名'),
       ('EXPORT_FIELD_SALES_EMAIL', 1, 'Sales Email', '销售邮箱'),
       ('EXPORT_FIELD_SALES_MOBILE', 1, 'Sales Mobile', '销售电话'),
       ('EXPORT_FIELD_CURRENCY_CODE', 1, 'Currency Code', '币种'),
       ('EXPORT_FIELD_ROOM_TYPE_1_DEFINE', 1, 'Room Type 1 Define', '房型1定义'),
       ('EXPORT_FIELD_ROOM_TYPE_1_CODE', 1, 'Room Type 1 Code', '房型1编码'),
       ('EXPORT_FIELD_ROOM_TYPE_1_NAME', 1, 'Room Type 1 Name', '房型1名称'),
       ('EXPORT_FIELD_ROOM_TYPE_1_NUMBER', 1, 'Room Type 1 Number', '房型1数量'),
       ('EXPORT_FIELD_ROOM_TYPE_2_DEFINE', 1, 'Room Type 2 Define', '房型2定义'),
       ('EXPORT_FIELD_ROOM_TYPE_2_CODE', 1, 'Room Type 2 Code', '房型2编码'),
       ('EXPORT_FIELD_ROOM_TYPE_2_NAME', 1, 'Room Type 2 Name', '房型2名称'),
       ('EXPORT_FIELD_ROOM_TYPE_2_NUMBER', 1, 'Room Type 2 Number', '房型2数量'),
       ('EXPORT_FIELD_ROOM_TYPE_3_DEFINE', 1, 'Room Type 3 Define', '房型3定义'),
       ('EXPORT_FIELD_ROOM_TYPE_3_CODE', 1, 'Room Type 3 Code', '房型3编码'),
       ('EXPORT_FIELD_ROOM_TYPE_3_NAME', 1, 'Room Type 3 Name', '房型3名称'),
       ('EXPORT_FIELD_ROOM_TYPE_3_NUMBER', 1, 'Room Type 3 Number', '房型3数量'),
       ('EXPORT_FIELD_ROOM_TYPE_4_DEFINE', 1, 'Room Type 4 Define', '房型4定义'),
       ('EXPORT_FIELD_ROOM_TYPE_4_CODE', 1, 'Room Type 4 Code', '房型4编码'),
       ('EXPORT_FIELD_ROOM_TYPE_4_NAME', 1, 'Room Type 4 Name', '房型4名称'),
       ('EXPORT_FIELD_ROOM_TYPE_4_NUMBER', 1, 'Room Type 4 Number', '房型4数量'),
       ('EXPORT_FIELD_ROOM_TYPE_5_DEFINE', 1, 'Room Type 5 Define', '房型5定义'),
       ('EXPORT_FIELD_ROOM_TYPE_5_CODE', 1, 'Room Type 5 Code', '房型5编码'),
       ('EXPORT_FIELD_ROOM_TYPE_5_NAME', 1, 'Room Type 5 Name', '房型5名称'),
       ('EXPORT_FIELD_ROOM_TYPE_5_NUMBER', 1, 'Room Type 5 Number', '房型5数量'),
       ('EXPORT_FIELD_CORP_START', 1, 'Corp Start', '协议价开始时间'),
       ('EXPORT_FIELD_CORP_END', 1, 'Corp End', '协议价结束时间'),
       ('EXPORT_FIELD_RT_1_SGL', 1, 'Room Type 1 Single Price', '房型1单人价格'),
       ('EXPORT_FIELD_RT_1_DBL', 1, 'Room Type 1 Double Price', '房型1双人价格'),
       ('EXPORT_FIELD_RT_2_SGL', 1, 'Room Type 2 Single Price', '房型2单人价格'),
       ('EXPORT_FIELD_RT_2_DBL', 1, 'Room Type 2 Double Price', '房型2双人价格'),
       ('EXPORT_FIELD_RT_3_SGL', 1, 'Room Type 3 Single Price', '房型3单人价格'),
       ('EXPORT_FIELD_RT_3_DBL', 1, 'Room Type 3 Double Price', '房型3双人价格'),
       ('EXPORT_FIELD_RT_4_SGL', 1, 'Room Type 4 Single Price', '房型4单人价格'),
       ('EXPORT_FIELD_RT_4_DBL', 1, 'Room Type 4 Double Price', '房型4双人价格'),
       ('EXPORT_FIELD_RT_5_SGL', 1, 'Room Type 5 Single Price', '房型5单人价格'),
       ('EXPORT_FIELD_RT_5_DBL', 1, 'Room Type 5 Double Price', '房型5双人价格'),
       ('EXPORT_FIELD_SEASON1_START_END', 1, 'Season 1 Start-End', '淡旺季1开始时间-结束时间'),
       ('EXPORT_FIELD_SEASON1_RT_1_SGL', 1, 'Season 1 Room Type 1 Single Price', '淡旺季1房型1单人价格'),
       ('EXPORT_FIELD_SEASON1_RT_1_DBL', 1, 'Season 1 Room Type 1 Double Price', '淡旺季1房型1双人价格'),
       ('EXPORT_FIELD_SEASON1_RT_2_SGL', 1, 'Season 1 Room Type 2 Single Price', '淡旺季1房型2单人价格'),
       ('EXPORT_FIELD_SEASON1_RT_2_DBL', 1, 'Season 1 Room Type 2 Double Price', '淡旺季1房型2双人价格'),
       ('EXPORT_FIELD_SEASON1_RT_3_SGL', 1, 'Season 1 Room Type 3 Single Price', '淡旺季1房型3单人价格'),
       ('EXPORT_FIELD_SEASON1_RT_3_DBL', 1, 'Season 1 Room Type 3 Double Price', '淡旺季1房型3双人价格'),
       ('EXPORT_FIELD_SEASON1_RT_4_SGL', 1, 'Season 1 Room Type 4 Single Price', '淡旺季1房型4单人价格'),
       ('EXPORT_FIELD_SEASON1_RT_4_DBL', 1, 'Season 1 Room Type 4 Double Price', '淡旺季1房型4双人价格'),
       ('EXPORT_FIELD_SEASON1_RT_5_SGL', 1, 'Season 1 Room Type 5 Single Price', '淡旺季1房型5单人价格'),
       ('EXPORT_FIELD_SEASON1_RT_5_DBL', 1, 'Season 1 Room Type 5 Double Price', '淡旺季1房型5双人价格'),
       ('EXPORT_FIELD_SEASON2_START_END', 1, 'Season 2 Start-End', '淡旺季2开始时间-结束时间'),
       ('EXPORT_FIELD_SEASON2_RT_1_SGL', 1, 'Season 2 Room Type 1 Single Price', '淡旺季2房型1单人价格'),
       ('EXPORT_FIELD_SEASON2_RT_1_DBL', 1, 'Season 2 Room Type 1 Double Price', '淡旺季2房型1双人价格'),
       ('EXPORT_FIELD_SEASON2_RT_2_SGL', 1, 'Season 2 Room Type 2 Single Price', '淡旺季2房型2单人价格'),
       ('EXPORT_FIELD_SEASON2_RT_2_DBL', 1, 'Season 2 Room Type 2 Double Price', '淡旺季2房型2双人价格'),
       ('EXPORT_FIELD_SEASON2_RT_3_SGL', 1, 'Season 2 Room Type 3 Single Price', '淡旺季2房型3单人价格'),
       ('EXPORT_FIELD_SEASON2_RT_3_DBL', 1, 'Season 2 Room Type 3 Double Price', '淡旺季2房型3双人价格'),
       ('EXPORT_FIELD_SEASON2_RT_4_SGL', 1, 'Season 2 Room Type 4 Single Price', '淡旺季2房型4单人价格'),
       ('EXPORT_FIELD_SEASON2_RT_4_DBL', 1, 'Season 2 Room Type 4 Double Price', '淡旺季2房型4双人价格'),
       ('EXPORT_FIELD_SEASON2_RT_5_SGL', 1, 'Season 2 Room Type 5 Single Price', '淡旺季2房型5单人价格'),
       ('EXPORT_FIELD_SEASON2_RT_5_DBL', 1, 'Season 2 Room Type 5 Double Price', '淡旺季2房型5双人价格'),
       ('EXPORT_FIELD_RATE_TYPE', 1, 'Rate Type', '价格类型'),
       ('EXPORT_FIELD_CANCEL_POLICY', 1, 'Cancel Policy', '取消政策'),
       ('EXPORT_FIELD_EARLY_CHECK_FEE', 1, 'Early Check Fee', '提前入住费用'),
       ('EXPORT_FIELD_EARLY_CHECK_FEE_UOM', 1, 'Early Check Fee UOM', '提前入住费用单位'),
       ('EXPORT_FIELD_EARLY_CHECK_INCLUDE', 1, 'Early Check Include', '提前入住费用是否包含'),
       ('EXPORT_FIELD_LODGTX_FEE', 1, 'Lodging Tax Fee', '入住税费用'),
       ('EXPORT_FIELD_LODGTX_UOM', 1, 'Lodging Tax UOM', '入住税收费方式'),
       ('EXPORT_FIELD_LODGTX_INCLUDE', 1, 'Lodging Tax Include', '是否包含入住税'),
       ('EXPORT_FIELD_STATETX_FEE', 1, 'State Tax Fee', '州税费用'),
       ('EXPORT_FIELD_STATETX_UOM', 1, 'State Tax UOM', '州税收费方式'),
       ('EXPORT_FIELD_STATETX_INCLUDE', 1, 'State Tax Include', '是否包含州税'),
       ('EXPORT_FIELD_CITYTX_FEE', 1, 'City Tax Fee', '市税费用'),
       ('EXPORT_FIELD_CITYTX_UOM', 1, 'City Tax UOM', '市税收费方式'),
       ('EXPORT_FIELD_CITYTX_INCLUDE', 1, 'City Tax Include', '是否包含市税'),
       ('EXPORT_FIELD_VATGSTRM_FEE', 1, 'VAT/GST Room Fee', '客房增值税费用'),
       ('EXPORT_FIELD_VATGSTRM_UOM', 1, 'VAT/GST Room UOM', '客房增值税收费方式'),
       ('EXPORT_FIELD_VATGSTRM_INCLUDE', 1, 'VAT/GST Room Include', '是否含客房增值税'),
       ('EXPORT_FIELD_VATGSTFB_FEE', 1, 'VAT/GST F&B Fee', '餐饮增值税费用'),
       ('EXPORT_FIELD_VATGSTFB_UOM', 1, 'VAT/GST F&B UOM', '餐饮增值税收费方式'),
       ('EXPORT_FIELD_VATGSTFB_INCLUDE', 1, 'VAT/GST F&B Include', '是否含餐饮增值税'),
       ('EXPORT_FIELD_SERVICE_FEE', 1, 'Service Fee', '服务费'),
       ('EXPORT_FIELD_SERVICE_UOM', 1, 'Service UOM', '服务费单位'),
       ('EXPORT_FIELD_SERVICE_INCLUDE', 1, 'Service Include', '服务费是否包含'),
       ('EXPORT_FIELD_OCC_FEE', 1, 'Occupancy Fee', '占用费'),
       ('EXPORT_FIELD_OCC_UOM', 1, 'Occupancy UOM', '占用费收费方式'),
       ('EXPORT_FIELD_OCC_INCLUDE', 1, 'Occupancy Include', '是否包含占用费'),
       ('EXPORT_FIELD_OTHER_TX_FEE_1', 1, 'Other Tax Fee 1', '其他税费1'),
       ('EXPORT_FIELD_OTHER_TX_FEE_1_UOM', 1, 'Other Tax Fee 1 UOM', '其他税费1收费方式'),
       ('EXPORT_FIELD_OTHER_TX_FEE_1_DESC', 1, 'Other Tax Fee 1 Description', '其他税费1描述'),
       ('EXPORT_FIELD_OTHER_TX_FEE_1_INCL', 1, 'Other Tax Fee 1 Include', '其他税费1是否包含'),
       ('EXPORT_FIELD_OTHER_TX_FEE_2', 1, 'Other Tax Fee 2', '其他税费2'),
       ('EXPORT_FIELD_OTHER_TX_FEE_2_UOM', 1, 'Other Tax Fee 2 UOM', '其他税费2收费方式'),
       ('EXPORT_FIELD_OTHER_TX_FEE_2_DESC', 1, 'Other Tax Fee 2 Description', '其他税费2描述'),
       ('EXPORT_FIELD_OTHER_TX_FEE_2_INCL', 1, 'Other Tax Fee 2 Include', '其他税费2是否包含'),
       ('EXPORT_FIELD_OTHER_TX_FEE_3', 1, 'Other Tax Fee 3', '其他税费3'),
       ('EXPORT_FIELD_OTHER_TX_FEE_3_UOM', 1, 'Other Tax Fee 3 UOM', '其他税费3收费方式'),
       ('EXPORT_FIELD_OTHER_TX_FEE_3_DESC', 1, 'Other Tax Fee 3 Description', '其他税费3描述'),
       ('EXPORT_FIELD_OTHER_TX_FEE_3_INCL', 1, 'Other Tax Fee 3 Include', '其他税费3是否包含'),
       ('EXPORT_FIELD_PARK_INCLUDE', 1, 'Parking Include', '是否包含停车费'),
       ('EXPORT_FIELD_BREAK_INCLUDE', 1, 'Breakfast Include', '是否包含早餐'),
       ('EXPORT_FIELD_BREAK_FEE', 1, 'Breakfast Fee', '早餐费用'),
       ('EXPORT_FIELD_BREAK_TYPE', 1, 'Breakfast Type', '早餐类型'),
       ('EXPORT_FIELD_FITON_CENT', 1, 'Fitness Center', '健身中心'),
       ('EXPORT_FIELD_WIRELESS_INCLUDE', 1, 'Wireless Include', '是否包含无线网络'),
       ('EXPORT_FIELD_RATE_SUBS', 1, 'Rate Substitute', '费率替代'),
       ('EXPORT_FIELD_RATE_PERSO', 1, 'Rate Personal', '费率个人'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_1', 1, 'Custom Strategy 1', '自定义策略1'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_2', 1, 'Custom Strategy 2', '自定义策略2'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_3', 1, 'Custom Strategy 3', '自定义策略3'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_4', 1, 'Custom Strategy 4', '自定义策略4'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_5', 1, 'Custom Strategy 5', '自定义策略5'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_6', 1, 'Custom Strategy 6', '自定义策略6'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_7', 1, 'Custom Strategy 7', '自定义策略7'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_8', 1, 'Custom Strategy 8', '自定义策略8'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_9', 1, 'Custom Strategy 9', '自定义策略9'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_10', 1, 'Custom Strategy 10', '自定义策略10'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_11', 1, 'Custom Strategy 11', '自定义策略11'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_12', 1, 'Custom Strategy 12', '自定义策略12'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_13', 1, 'Custom Strategy 13', '自定义策略13'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_14', 1, 'Custom Strategy 14', '自定义策略14'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_15', 1, 'Custom Strategy 15', '自定义策略15'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_16', 1, 'Custom Strategy 16', '自定义策略16'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_17', 1, 'Custom Strategy 17', '自定义策略17'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_18', 1, 'Custom Strategy 18', '自定义策略18'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_19', 1, 'Custom Strategy 19', '自定义策略19'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_20', 1, 'Custom Strategy 20', '自定义策略20'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_21', 1, 'Custom Strategy 21', '自定义策略21'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_22', 1, 'Custom Strategy 22', '自定义策略22'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_23', 1, 'Custom Strategy 23', '自定义策略23'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_24', 1, 'Custom Strategy 24', '自定义策略24'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_25', 1, 'Custom Strategy 25', '自定义策略25'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_26', 1, 'Custom Strategy 26', '自定义策略26'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_27', 1, 'Custom Strategy 27', '自定义策略27'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_28', 1, 'Custom Strategy 28', '自定义策略28'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_29', 1, 'Custom Strategy 29', '自定义策略29'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_30', 1, 'Custom Strategy 30', '自定义策略30'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_31', 1, 'Custom Strategy 31', '自定义策略31'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_32', 1, 'Custom Strategy 32', '自定义策略32'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_33', 1, 'Custom Strategy 33', '自定义策略33'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_34', 1, 'Custom Strategy 34', '自定义策略34'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_35', 1, 'Custom Strategy 35', '自定义策略35'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_36', 1, 'Custom Strategy 36', '自定义策略36'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_37', 1, 'Custom Strategy 37', '自定义策略37'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_38', 1, 'Custom Strategy 38', '自定义策略38'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_39', 1, 'Custom Strategy 39', '自定义策略39'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_40', 1, 'Custom Strategy 40', '自定义策略40'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_41', 1, 'Custom Strategy 41', '自定义策略41'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_42', 1, 'Custom Strategy 42', '自定义策略42'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_43', 1, 'Custom Strategy 43', '自定义策略43'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_44', 1, 'Custom Strategy 44', '自定义策略44'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_45', 1, 'Custom Strategy 45', '自定义策略45'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_46', 1, 'Custom Strategy 46', '自定义策略46'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_47', 1, 'Custom Strategy 47', '自定义策略47'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_48', 1, 'Custom Strategy 48', '自定义策略48'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_49', 1, 'Custom Strategy 49', '自定义策略49'),
       ('EXPORT_FIELD_CUSTOM_STRATEGY_50', 1, 'Custom Strategy 50', '自定义策略50'),
       ('EXPORT_FIELD_BD1_START', 1, 'BD1 Start', '价格不适用时段1开始'),
       ('EXPORT_FIELD_BD1_END', 1, 'BD1 End', '价格不适用时段1结束'),
       ('EXPORT_FIELD_BD1_NAME', 1, 'BD1 Name', '价格不适用时段1名称'),
       ('EXPORT_FIELD_BD2_START', 1, 'BD2 Start', '价格不适用时段2开始'),
       ('EXPORT_FIELD_BD2_END', 1, 'BD2 End', '价格不适用时段2结束'),
       ('EXPORT_FIELD_BD2_NAME', 1, 'BD2 Name', '价格不适用时段2名称'),
       ('EXPORT_FIELD_BD3_START', 1, 'BD3 Start', '价格不适用时段3开始'),
       ('EXPORT_FIELD_BD3_END', 1, 'BD3 End', '价格不适用时段3结束'),
       ('EXPORT_FIELD_BD3_NAME', 1, 'BD3 Name', '价格不适用时段3名称'),
       ('EXPORT_FIELD_BD4_START', 1, 'BD4 Start', '价格不适用时段4开始'),
       ('EXPORT_FIELD_BD4_END', 1, 'BD4 End', '价格不适用时段4结束'),
       ('EXPORT_FIELD_BD4_NAME', 1, 'BD4 Name', '价格不适用时段4名称'),
       ('EXPORT_FIELD_BD5_START', 1, 'BD5 Start', '价格不适用时段5开始'),
       ('EXPORT_FIELD_BD5_END', 1, 'BD5 End', '价格不适用时段5结束'),
       ('EXPORT_FIELD_BD5_NAME', 1, 'BD5 Name', '价格不适用时段5名称'),
       ('EXPORT_FIELD_BD6_START', 1, 'BD6 Start', '价格不适用时段6开始'),
       ('EXPORT_FIELD_BD6_END', 1, 'BD6 End', '价格不适用时段6结束'),
       ('EXPORT_FIELD_BD6_NAME', 1, 'BD6 Name', '价格不适用时段6名称'),
       ('EXPORT_FIELD_BD7_START', 1, 'BD7 Start', '价格不适用时段7开始'),
       ('EXPORT_FIELD_BD7_END', 1, 'BD7 End', '价格不适用时段7结束'),
       ('EXPORT_FIELD_BD7_NAME', 1, 'BD7 Name', '价格不适用时段7名称'),
       ('EXPORT_FIELD_BD8_START', 1, 'BD8 Start', '价格不适用时段8开始'),
       ('EXPORT_FIELD_BD8_END', 1, 'BD8 End', '价格不适用时段8结束'),
       ('EXPORT_FIELD_BD8_NAME', 1, 'BD8 Name', '价格不适用时段8名称'),
       ('EXPORT_FIELD_BD9_START', 1, 'BD9 Start', '价格不适用时段9开始'),
       ('EXPORT_FIELD_BD9_END', 1, 'BD9 End', '价格不适用时段9结束'),
       ('EXPORT_FIELD_BD9_NAME', 1, 'BD9 Name', '价格不适用时段9名称'),
       ('EXPORT_FIELD_BD10_START', 1, 'BD10 Start', '价格不适用时段10开始'),
       ('EXPORT_FIELD_BD10_END', 1, 'BD10 End', '价格不适用时段10结束'),
       ('EXPORT_FIELD_BD10_NAME', 1, 'BD10 Name', '价格不适用时段10名称'),
       ('EXPORT_FIELD_LAUN_DRY', 1, 'Laundry Dry', '干洗服务'),
       ('EXPORT_FIELD_LAUNDRY_SITE', 1, 'Laundry Site', '洗衣房'),
       ('EXPORT_FIELD_MINI_FRIG', 1, 'Mini Fridge', 'Mini 吧');

-- 导入报价相关错误码国际化
insert into t_text_resource(text_resource_code, text_resource_type, value_en_us, value_zh_cn)
values ('CANNOT_UPDATE_BID_DUE_TO_BID_STATE_NOT_UNDER_NEGOTIATION', 2, 'Cannot update bid due to current bid state not under negotiation', '当前报价状态下不允许修改报价'),
       ('CANNOT_UPDATE_BID_DUE_TO_BID_ORG_NOT_HOTEL_GROUP', 2, 'Cannot update bid due to bid organization not being hotel group', '当前报价所属机构不是酒店集团, 不允许修改报价'),
       ('HOTEL_NAME_CANNOT_BE_EMPTY', 2, 'Hotel name cannot be empty', '酒店名称不能为空'),
       ('CURRENCY_CODE_CANNOT_BE_EMPTY', 2, 'Currency code cannot be empty', '币种不能为空'),
       ('ROOM_TYPE_1_DEFINE_CANNOT_BE_EMPTY', 2, 'Room type 1 definition cannot be empty', '第一档房型不可为空'),
       ('CONTRACT_START_DATE_CANNOT_BE_EMPTY', 2, 'Contract start date cannot be empty', '合同开始时间不可为空'),
       ('CONTRACT_END_DATE_CANNOT_BE_EMPTY', 2, 'Contract end date cannot be empty', '合同结束时间不可为空'),
       ('CONTRACT_START_DATE_CANNOT_AFTER_END_DATE', 2, 'Contract start date cannot be after end date', '合同开始时间不能大于结束时间'),
       ('CONTRACT_DATE_FORMAT_INVALID', 2, 'Contract date format is invalid', '合同开始结束时间格式错误'),
       ('ROOM_TYPE_1_SGL_PRICE_CANNOT_BE_EMPTY', 2, 'Room type 1 single price cannot be empty', '房档1单人报价不可为空'),
       ('ROOM_TYPE_1_DBL_PRICE_CANNOT_BE_EMPTY', 2, 'Room type 1 double price cannot be empty', '房档1双人报价不可为空'),
       ('RATE_TYPE_INVALID', 2, 'Rate type is invalid, must be LRA or NLRA', '是否LRA,必录项，值必须是LRA,NLRA'),
       ('CANCEL_POLICY_CANNOT_BE_EMPTY', 2, 'Cancel policy cannot be empty', '取消条款不能为空'),
       ('IS_INCLUDE_BREAKFAST_INVALID', 2, 'Include breakfast field is invalid, must be Y or N', '是否含早必须录入,值只能是Y N'),
       ('SEASON_START_END_DATE_FORMAT_INVALID', 2, 'Season {0} start-end date format is invalid', '淡旺季{0}开始时间-结束时间格式错误'),
       ('SEASON_START_DATE_NOT_IN_CONTRACT_DATE_RANGE', 2, 'Season {0} start date is not in contract date range', '淡旺季{0}开始时间没在协议日期内'),
       ('SEASON_END_DATE_NOT_IN_CONTRACT_DATE_RANGE', 2, 'Season {0} end date is not in contract date range', '淡旺季{0}结束时间没在协议日期内'),
       ('SEASON_1_AND_2_DATE_CROSS', 2, 'Season 1 and 2 dates overlap', '淡旺季日期存在交叉'),
       ('CUSTOM_STRATEGY_INVALID', 2, 'Custom strategy {0} is invalid', '自定义策略{0}无效'),
       ('BD_START_END_DATE_FORMAT_INVALID', 2, 'Blackout date {0} start or end date format is invalid', '价格不适用时段{0}开始或结束时间格式错误'),
       ('BD_START_DATE_NOT_IN_CONTRACT_DATE_RANGE', 2, 'Blackout date {0} start date cannot exceed contract start date', '价格不适用时段{0}开始日期不能超过协议开始日期'),
       ('BD_END_DATE_NOT_IN_CONTRACT_DATE_RANGE', 2, 'Blackout date {0} end date cannot exceed contract end date', '价格不适用时段{0}结束日期不能超过协议结束日期'),
       ('BD_DATE_CROSS', 2, 'Blackout date {0} and blackout date {1} overlap', '价格不适用时段{0}和价格不适用时段{1}日期存在交叉'),
       ('VATGSTRM_INCLUDE_INVALID', 2, 'VAT/GST room include field is invalid', '是否含客房增值税字段不合法'),
       ('VATGSTRM_UOM_INVALID', 2, 'VAT/GST room unit of measure field is invalid', '客房增值税收费方式字段不合法'),
       ('VATGSTFB_INCLUDE_INVALID', 2, 'VAT/GST F&B include field is invalid', '是否含餐饮增值税字段不合法'),
       ('VATGSTFB_UOM_INVALID', 2, 'VAT/GST F&B unit of measure field is invalid', '餐饮增值税收费方式字段不合法'),
       ('SERVICE_INCLUDE_INVALID', 2, 'Service fee include field is invalid', '是否含服务费字段不合法'),
       ('SERVICE_UOM_INVALID', 2, 'Service fee unit of measure field is invalid', '服务费收费方式字段不合法'),
       ('OCC_INCLUDE_INVALID', 2, 'Occupancy fee include field is invalid', '是否含占用费字段不合法'),
       ('OCC_UOM_INVALID', 2, 'Occupancy fee unit of measure field is invalid', '占用费收费方式字段不合法'),
       ('OTHER_TX_FEE_1_INCLUDE_INVALID', 2, 'Other tax fee 1 include field is invalid', '是否含其他税费1字段不合法'),
       ('OTHER_TX_FEE_1_UOM_INVALID', 2, 'Other tax fee 1 unit of measure field is invalid', '其他税费1收费方式字段不合法'),
       ('OTHER_TX_FEE_2_INCLUDE_INVALID', 2, 'Other tax fee 2 include field is invalid', '是否含其他税费2字段不合法'),
       ('OTHER_TX_FEE_2_UOM_INVALID', 2, 'Other tax fee 2 unit of measure field is invalid', '其他税费2收费方式字段不合法'),
       ('OTHER_TX_FEE_3_INCLUDE_INVALID', 2, 'Other tax fee 3 include field is invalid', '是否含其他税费3字段不合法'),
       ('OTHER_TX_FEE_3_UOM_INVALID', 2, 'Other tax fee 3 unit of measure field is invalid', '其他税费3收费方式字段不合法'),
       ('ROOM_TYPE_ID_REPEAT', 2, 'Room type {0} has duplicate room type ID', '房档{0}存在重复的房型 ID'),
       ('ROOM_TYPE_NOT_EXIST', 2, 'Room type {0} contains room types not available in hotel', '房档{0}存在酒店不包含的房型');