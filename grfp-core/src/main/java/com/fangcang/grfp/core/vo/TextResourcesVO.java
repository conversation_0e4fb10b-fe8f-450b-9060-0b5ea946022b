package com.fangcang.grfp.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;

@ApiModel("文字资源对象")
public class TextResourcesVO {

	// ---------------------------------------------------------------------------------------------------- Private Member
	
	@ApiModelProperty(value = "文字资源集合")
	protected Map<String, String> textResources;
	@ApiModelProperty(value = "最新更新时间")
	protected String lastUpdatedTime;
	
	// ---------------------------------------------------------------------------------------------------- Public Method

	public Map<String, String> getTextResources() {
		return textResources;
	}

	public String getLastUpdatedTime() {
		return lastUpdatedTime;
	}

	public void setLastUpdatedTime(String lastUpdatedTime) {
		this.lastUpdatedTime = lastUpdatedTime;
	}

	public void setTextResources(Map<String, String> textResources) {
		this.textResources = textResources;
	}


	@Override
	public String toString() {
		return "TextResourcesVO [textResources=" + textResources.size() + ", lastUpdatedTime=" + lastUpdatedTime + "]";
	}
	
}
