package com.fangcang.grfp.core.dto.dhub.request.product;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HotelLowestPriceRequest {

    /**
     * 酒店id（最多100个，最少1个）
     */
    private List<Long> hotelIds;

    /**
     * 入住日期（格式:yyyy-MM-dd）
     */
    private String checkInDate;

    /**
     * 离店日期（格式:yyyy-MM-dd）（入离日期不传默认是30天，当前时间开始）
     */
    private String checkOutDate;

    /**
     * 入住类型：1-全日房（默认），2-钟点房
     */
    private Integer checkInType;

}
