<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HotelDataMapper">
    <insert id="batchUpsert">
        insert into t_hotel_data(hotel_id, data_en_us, data_zh_cn)
        values
        <foreach collection="hotelDataEntities" item="item" separator=",">
            (#{item.hotelId}, #{item.dataEnUs}, #{item.dataZhCn})
        </foreach>
        on duplicate key update data_en_us = values(data_en_us),
                                data_zh_cn = values(data_zh_cn)
    </insert>

    <select id="selectByHotelId" resultType="com.fangcang.grfp.core.vo.response.hotel.HotelDataVO">
        select
        <choose>
            <when test="language != null and language == 1">
                t1.data_en_us as hotelData,
            </when>
            <otherwise>
                t1.data_zh_cn as hotelData,
            </otherwise>
        </choose>
        t2.data as imageData
        from t_hotel_data t1
                 left join t_hotel_image t2 on t1.hotel_id = t2.hotel_id
        <where>
            t1.hotel_id = #{hotelId}
        </where>
    </select>
</mapper>
