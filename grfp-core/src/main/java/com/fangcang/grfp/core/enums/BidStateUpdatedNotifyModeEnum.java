package com.fangcang.grfp.core.enums;

/**
  报价状态变化通知方式 签约状态通知模式(0：手工通知，1：自动通知)
 */
public enum BidStateUpdatedNotifyModeEnum {

    MANUAL_NOTIFY(0, "手工通知"), AUTO_NOTIFY(1, "自动通知");

    public Integer key;
    public String value;

    BidStateUpdatedNotifyModeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static Integer getKeyByValue(String value) {
        Integer key = 0;
        for (BidStateUpdatedNotifyModeEnum tenderTypeEnum : BidStateUpdatedNotifyModeEnum.values()) {
            if (tenderTypeEnum.value.equals(value)) {
                key = tenderTypeEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (BidStateUpdatedNotifyModeEnum tenderTypeEnum : BidStateUpdatedNotifyModeEnum.values()) {
            if (tenderTypeEnum.key.equals(key)) {
                value = tenderTypeEnum.value;
                break;
            }
        }
        return value;
    }

    public static BidStateUpdatedNotifyModeEnum getEnumByKey(Integer key) {
        BidStateUpdatedNotifyModeEnum tenderTypeEnum = null;
        for (BidStateUpdatedNotifyModeEnum tenderType : BidStateUpdatedNotifyModeEnum.values()) {
            if (tenderTypeEnum.key.equals(key)) {
                tenderTypeEnum = tenderType;
                break;
            }
        }
        return tenderTypeEnum;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
