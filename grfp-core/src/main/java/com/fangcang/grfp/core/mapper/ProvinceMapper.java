package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fangcang.grfp.core.entity.CountryEntity;
import com.fangcang.grfp.core.entity.ProvinceEntity;
import com.fangcang.grfp.core.vo.CountryNameVO;
import com.fangcang.grfp.core.vo.ProvinceNameVO;
import com.fangcang.grfp.core.vo.request.ListCountryDataRequest;
import com.fangcang.grfp.core.vo.request.ListProvinceDataRequest;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 州/省/邦 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface ProvinceMapper extends BaseMapper<ProvinceEntity> {

    void batchUpsert(@Param("list") Collection<ProvinceEntity> provinceEntities);

    /**
     * 分页查询数据
     */
    IPage<ProvinceEntity> listDataPage(IPage<ProvinceEntity> page, @Param("query") ListProvinceDataRequest query);


    /**
     * 根据名称查询
     */
    List<ProvinceNameVO> selectProvinceNameList(@Param("languageId") Integer languageId,
                                               @Param("countryCode") String countryCode,
                                                @Param("provinceName") String provinceName,
                                               @Param("limitCount") Integer limitCount);

}
