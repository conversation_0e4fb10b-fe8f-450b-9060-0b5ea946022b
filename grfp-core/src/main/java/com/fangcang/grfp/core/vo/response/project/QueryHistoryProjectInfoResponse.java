package com.fangcang.grfp.core.vo.response.project;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/20 19:40
 */
@ApiModel("项目历史数据")
@Getter
@Setter
public class QueryHistoryProjectInfoResponse extends BaseVO {

    // 项目id
    @ApiModelProperty("项目id")
    private Integer projectId;

    // 酒店ID
    @ApiModelProperty("酒店ID")
    private Long hotelId;

    //酒店名称
    @ApiModelProperty("酒店名称")
    private String hotelName;

    //酒店名称
    @ApiModelProperty("酒店名称(英文)")
    private String hotelNameEnUs;

    //酒店名称
    @ApiModelProperty("酒店名称(中文)")
    private String hotelNameZhCn;


    //城市编码
    @ApiModelProperty("城市编码")
    private String cityCode;

    //城市名称
    @ApiModelProperty("城市名称")
    private String cityName;

    // 总间夜数
    @ApiModelProperty("总间夜数")
    private Integer roomNightCount;

    // 成交金额
    @ApiModelProperty("成交金额")
    private BigDecimal totalAmount;

    // 品牌id
    @ApiModelProperty("品牌id")
    private Integer hotelBrandId;

    //品牌
    @ApiModelProperty("品牌")
    private String hotelBrandName;

    // 酒店集团id
    @ApiModelProperty("酒店集团id")
    private Integer hotelGroupId;

    //酒店集团
    @ApiModelProperty("酒店集团")
    private String hotelGroupName;

    // 百度经度
    @ApiModelProperty("百度经度")
    private BigDecimal lngBaidu;

    // 百度维度
    @ApiModelProperty("百度维度")
    private BigDecimal latBaidu;

    // Google经度
    @ApiModelProperty("Google经度")
    private BigDecimal lngGoogle;

    // Google维度
    @ApiModelProperty("Google维度")
    private BigDecimal latGoogle;

    // 城市排名
    @ApiModelProperty("城市排名")
    private int cityOrder;

    // 节省金额
    @ApiModelProperty("节省金额")
    private BigDecimal savedAmount;

    // 节省率
    @ApiModelProperty("节省率")
    private BigDecimal savedAmountRate;

    // 城市范围所属POI ID
    @ApiModelProperty("城市范围所属POI ID")
    private Long poiId;

    // 酒店距离POI距离
    @ApiModelProperty("酒店距离POI距离")
    private BigDecimal poiDistance;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private Date createTime;

    //创建人
    @ApiModelProperty("创建人")
    private String creator;

    // 推荐等级
    @ApiModelProperty("推荐等级")
    private Integer recommendLevel;

    // 签约要求月均采购间夜数
    @ApiModelProperty("签约要求月均采购间夜数")
    private String requiredRoomNight;

    // 酒店星级
    @ApiModelProperty("酒店星级")
    private String hotelStar;

    // OTA评分
    @ApiModelProperty("OTA评分")
    private String rating;

    // 开业日期
    @ApiModelProperty("开业日期")
    private Date praciceDate;

    // 总违规次数
    @ApiModelProperty("总违规次数")
    private Integer totalViolationsCount;

    // OTA最低价格 OTA_MIN_PRICE
    @ApiModelProperty("OTA最低价格")
    private BigDecimal otaMinPrice;

    // OTA最低价格 OTA_MAX_PRICE
    @ApiModelProperty("OTA最高价格")
    private BigDecimal otaMaxPrice;

    // OTA最低最高价格生成日期 MIN_MAX_OTA_PRICE_DATE
    @ApiModelProperty("OTA最低最高价格生成日期")
    private Date minMaxOtaPriceDate;

    // 服务分
    @ApiModelProperty("服务分")
    private BigDecimal servicePoint;

    // 商旅价格
    @ApiModelProperty("商旅价格")
    private BigDecimal lowestPrice;

    // 商旅价格生成日期
    @ApiModelProperty("商旅价格生成日期")
    private Date lowestPriceDate;

    // 差旅价格信息
    @ApiModelProperty("差旅价格信息")
    private String lowestPriceItemInfo;

    // 签约混合价格
    @ApiModelProperty("签约混合价格")
    private BigDecimal adjustLowestPrice;

    // 是否上传
    @ApiModelProperty("是否上传")
    private Integer isUploaded;

    // 周边3公里同价位总间夜数
    @ApiModelProperty("周边3公里同价位总间夜数")
    private Long priceLevelRoomNight;

    // 签约参考价
    @ApiModelProperty("签约参考价")
    private BigDecimal referencePrice;


}
