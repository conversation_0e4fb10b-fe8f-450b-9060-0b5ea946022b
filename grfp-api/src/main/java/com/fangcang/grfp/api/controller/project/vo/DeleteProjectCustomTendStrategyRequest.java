package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(description = "删除自定义采购策略请求")
public class DeleteProjectCustomTendStrategyRequest extends BaseVO {

    private static final long serialVersionUID = 6744349654753967854L;

    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "策略ID", required = true)
    @NotNull
    private Integer strategyId;
}
