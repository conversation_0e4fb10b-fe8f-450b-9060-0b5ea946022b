package com.fangcang.grfp.core.vo.request;

import com.fangcang.grfp.core.base.PageQuery;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * 历史项目分页请求
 * @date 2024/8/18 19:04
 */
@Getter
@Setter
public class QueryHistoryProjectInfoRequest extends PageQuery {

    /**项目ID **/
    private Integer projectId;

    //城市编码
    private String cityCode;

    //酒店id
    private Long hotelId;

    //酒店品牌ID
    private Long hotelBrandId;

    //酒店集团id
    private Long hotelGroupId;

    // 酒店id集合
    private List<Long> hotelIdList;

}
