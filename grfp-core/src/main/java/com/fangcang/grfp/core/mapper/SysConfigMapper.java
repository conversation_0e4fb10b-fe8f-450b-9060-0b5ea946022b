package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fangcang.grfp.core.entity.SysConfigEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.vo.ListSysConfigVO;
import com.fangcang.grfp.core.vo.request.ListSysConfigRequest;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 系统配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface SysConfigMapper extends BaseMapper<SysConfigEntity> {


    default  SysConfigEntity getByCode(String sysConfigCode){
        return this.selectOne(new LambdaQueryWrapper<SysConfigEntity>()
                .eq(SysConfigEntity::getSysConfigCode, sysConfigCode));
    }

    IPage<ListSysConfigVO> querySysConfigVOPageList(IPage<ListSysConfigVO> page, @Param("query") ListSysConfigRequest query);

}
