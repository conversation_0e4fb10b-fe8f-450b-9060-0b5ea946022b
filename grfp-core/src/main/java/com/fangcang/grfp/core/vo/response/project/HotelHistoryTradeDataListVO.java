package com.fangcang.grfp.core.vo.response.project;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel(description = "查询项目酒店历史交易数据列表响应")
public class HotelHistoryTradeDataListVO extends BaseVO {

    @ApiModelProperty(value = "项目 ID")
    private Integer projectId;

    @ApiModelProperty(value = "酒店 ID")
    private Long hotelId;

    @ApiModelProperty(value = "酒店名称")
    private String hotelName;

    @ApiModelProperty(value = "酒店星级")
    private String hotelStar;

    @ApiModelProperty(value = "编码")
    private String cityCode;

    @ApiModelProperty(value = "城市排名")
    private Integer cityOrder;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "成交间夜数")
    private Integer roomNightCount;

    @ApiModelProperty(value = "成交金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "品牌 ID")
    private Long hotelBrandId;

    @ApiModelProperty(value = "品牌名称")
    private String hotelBrandName;

    @ApiModelProperty(value = "集团 ID")
    private Long hotelGroupId;

    @ApiModelProperty(value = "集团名称")
    private String hotelGroupName;

    @ApiModelProperty(value = "推荐等级: 1-SSS 2-SS 3-S 4-A 5-B 6-C")
    private Integer recommendLevel;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "节省金额")
    private BigDecimal savedAmount;

    @ApiModelProperty(value = "POI ID")
    private Long poiId;

    @ApiModelProperty(value = "酒店距离 POI 距离")
    private BigDecimal poiDistance;

    @ApiModelProperty(value = "去年总违规数")
    private Integer totalViolationsCount;

    @ApiModelProperty(value = "签约混合价")
    private BigDecimal adjustLowestPrice;

    @ApiModelProperty(value = "OTA最低价格")
    private BigDecimal otaMinPrice;

    @ApiModelProperty(value = "OTA最高价格")
    private BigDecimal otaMaxPrice;

    @ApiModelProperty(value = "OTA最低最高价格生成日期")
    private Date minMaxOtaPriceDate;

    @ApiModelProperty(value = "商旅价格")
    private BigDecimal lowestPrice;

    @ApiModelProperty(value = "商旅价格生成日期")
    private Date lowestPriceDate;

    @ApiModelProperty(value = "去年服务分")
    private BigDecimal servicePoint;

    @ApiModelProperty(value = "Google经度")
    private BigDecimal lngGoogle;

    @ApiModelProperty(value = "Google纬度")
    private BigDecimal latGoogle;

    @ApiModelProperty(value = "酒店评分")
    private String rating;

    @ApiModelProperty(value = "开业日期")
    private Date openingDate;

    @ApiModelProperty(value = "节省率")
    private BigDecimal savedAmountRate;

}
