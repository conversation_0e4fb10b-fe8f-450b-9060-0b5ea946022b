package com.fangcang.grfp.core.auditlog;

import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.entity.SysAuditLogEntity;
import com.fangcang.grfp.core.mapper.SysAuditLogMapper;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.HttpServletRequestUtility;
import com.fangcang.grfp.core.util.JsonUtil;
import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 审计日志 aop
 */
@Slf4j
@Aspect
public class UserAuditLogAspect {

    @Autowired
    private SysAuditLogMapper sysAuditLogMapper;

    @Resource
    private Executor auditLogExecutor;

    @Around("@annotation(userAuditLog)")
    public Object aroundAdvice(ProceedingJoinPoint pjp, UserAuditLog userAuditLog) throws Throwable {
        long startTime = System.currentTimeMillis();
        SysAuditLogEntity sysAuditLog = new SysAuditLogEntity();
        try {
            // 执行方法前取一次 user, 避免执行登出后, 在方法后取不到
            this.setUserId(sysAuditLog);
            sysAuditLog.setRequestParams(this.trim(filterHttpObject(pjp.getArgs())));
            sysAuditLog.setRequestTime(new Date());

            Object returnObject = pjp.proceed();

            sysAuditLog.setResponse(this.trim(returnObject));
            sysAuditLog.setResponseCode(this.getResultCode(returnObject));

            return returnObject;
        } catch (Exception e) {
            sysAuditLog.setResponse(this.trim(e.getMessage()));
            throw e;
        } finally {
            try {
                sysAuditLog.setResponseTime(new Date());
                // 再尝试取 user, 避免执行登录时, 在方法前取不到
                this.setUserId(sysAuditLog);
                sysAuditLog.setFunctionName(userAuditLog.value());

                ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                if (requestAttributes != null) {
                    HttpServletRequest request = requestAttributes.getRequest();
                    sysAuditLog.setClientIp(HttpServletRequestUtility.extractRequestIpAddress(request));
                    sysAuditLog.setUserAgent(StringUtils.abbreviate(request.getHeader("User-Agent"), 200));
                    sysAuditLog.setRequestUri(StringUtils.abbreviate(request.getRequestURI(), 128));
                }

                // 异步保存
                CompletableFuture.runAsync(() -> sysAuditLogMapper.insert(sysAuditLog), auditLogExecutor);
            } catch (Exception e) {
                log.error("audit log error", e);
            }
            log.info("audit log: {}", System.currentTimeMillis() - startTime);
        }
    }

    @VisibleForTesting
    void setUserId(SysAuditLogEntity sysAuditLog) {
        UserSession session = UserSession.get();
        if (session != null) {
            sysAuditLog.setUserId(session.getUserId());
        }
    }

    @VisibleForTesting
    String getResultCode(Object returnObject) {
        if (returnObject instanceof Result) {
            return ((Result<?>) returnObject).getCode();
        }
        return "200";
    }

    @VisibleForTesting
    List<Object> filterHttpObject(Object[] objects) {
        if (objects == null) {
            return new ArrayList<>();
        }
        return Arrays.stream(objects).filter(o -> !(o instanceof HttpServletRequest
            || o instanceof HttpServletResponse
            || o instanceof MultipartFile
            || o instanceof HttpSession
        )).collect(Collectors.toList());
    }

    @VisibleForTesting
    String trim(Object object) {
        if (object == null) {
            return "";
        }
        return StringUtils.abbreviate(JsonUtil.objectToJson(object), 500);
    }

}
