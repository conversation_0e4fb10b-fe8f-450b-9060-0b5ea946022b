package com.fangcang.grfp.core.util;

import com.fangcang.grfp.core.vo.response.HexagonsStat;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class HexagonStatUtil {

    // 六边形的边长
    private static final double SIZE = 0.0045;

    // 递归深度
    private static final int DEPTH = 8;



    public static void main(String[] args) {
        // 初始百度经纬度
        double initialBdLng = 114.03557611;
        double initialBdLat = 22.6152751111111;


        List<HexagonsStat> uniqueHexagonsList = new ArrayList<>();
        Set<String> uniqueHexagonsStringSet = new HashSet<>();
        long l = System.currentTimeMillis();

        generateHexagons(new double[]{initialBdLng, initialBdLat}, DEPTH, uniqueHexagonsStringSet, uniqueHexagonsList);
        System.out.println((System.currentTimeMillis() - l));

        // 输出唯一六边形的坐标
        System.out.println("最终生成的唯一六边形 BD-09 坐标:");
        //  System.out.println(uniqueHexagons);
        System.out.println(uniqueHexagonsList.size());
       /* for (String hexagon : uniqueHexagons) {
            System.out.println("六边形顶点坐标: " + hexagon);
        }*/
    }

    /**
     * 兼容 BigDecimal 类型
     */
    public static void generateHexagons(BigDecimal[] center, int depth, Set<String> uniqueHexagonsSet, List<HexagonsStat> uniqueHexagonsList) {
        // 转为 double 计算
        double[] centerDouble = new double[]{center[0].doubleValue(), center[1].doubleValue()};
        generateHexagons(centerDouble, depth, uniqueHexagonsSet, uniqueHexagonsList);
    }

    // 生成六边形
    public static void generateHexagons(double[] center, int depth, Set<String> uniqueHexagonsSet, List<HexagonsStat> uniqueHexagonsList) {
        if (depth == 0) {
            List<double[]> hexagonCoords = calculateHexagon(center);  // 获取六边形坐标
            HexagonsStat hexagonsStat = hexagonToHexagonsStat(hexagonCoords);
            if(!uniqueHexagonsSet.contains(hexagonsStat.toString())){
                uniqueHexagonsSet.add(hexagonsStat.toString());
                uniqueHexagonsList.add(hexagonsStat);  // 将当前六边形的字符串形式添加到集合中
            }
            return;
        }

        List<double[]> hexagonCoords = calculateHexagon(center);
        List<double[]> newCenters = getNewHexagonCenters(hexagonCoords);

        for (double[] newCenter : newCenters) {
            generateHexagons(newCenter, depth - 1, uniqueHexagonsSet, uniqueHexagonsList);
        }
    }

    // 计算六边形的顶点并返回其坐标
    public static List<double[]> calculateHexagon(double[] center) {
        List<double[]> hexagon = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            double angle = Math.PI / 3 * i;
            double x = center[0] + SIZE * Math.cos(angle); // 经度
            double y = center[1] + SIZE * Math.sin(angle); // 纬度
            hexagon.add(new double[]{x, y});
        }
        return hexagon;
    }

    // 将六边形坐标转换数组
    public static HexagonsStat hexagonToHexagonsStat(List<double[]> hexagonCoords) {
        HexagonsStat hexagonsStat = new HexagonsStat();
        List<String[]> hexagonLanLatList = new ArrayList<>();
        for (int i = 0; i < hexagonCoords.size(); i++) {
            double[] coord = hexagonCoords.get(i);
            String[] langlat = new String[]{String.format("%.6f", coord[0]), String.format("%.6f", coord[1])};
            hexagonLanLatList.add(langlat);
        }

        hexagonsStat.setLanlatList(hexagonLanLatList);
        return hexagonsStat;
    }

    // 根据六边形的顶点返回新六边形的中心坐标
    public static List<double[]> getNewHexagonCenters(List<double[]> hexagonCoords) {
        List<double[]> newCenters = new ArrayList<>();

        for (int i = 0; i < 6; i++) {
            double[] startPoint = hexagonCoords.get(i);
            double[] endPoint = hexagonCoords.get((i + 1) % 6);

            // 中点
            double midX = (startPoint[0] + endPoint[0]) / 2;
            double midY = (startPoint[1] + endPoint[1]) / 2;

            // 计算偏移量
            double offsetX = (endPoint[0] - startPoint[0]) /
                Math.sqrt(Math.pow(endPoint[0] - startPoint[0], 2) + Math.pow(endPoint[1] - startPoint[1], 2)) * SIZE;
            double offsetY = (endPoint[1] - startPoint[1]) /
                Math.sqrt(Math.pow(endPoint[0] - startPoint[0], 2) + Math.pow(endPoint[1] - startPoint[1], 2)) * SIZE;

            double[] newCenter = {midX + offsetY, midY - offsetX};
            newCenters.add(newCenter);
        }
        return newCenters;
    }

}
