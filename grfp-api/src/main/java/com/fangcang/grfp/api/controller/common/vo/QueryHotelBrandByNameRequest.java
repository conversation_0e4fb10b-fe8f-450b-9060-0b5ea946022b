package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel("根据名称查找酒店品牌查询")
@Getter
@Setter
public class QueryHotelBrandByNameRequest extends BaseVO {

    @ApiModelProperty(value = "酒店集团ID")
    @NotNull
    private Long hotelGroupId;

    @ApiModelProperty(value = "酒店品牌名称")
    private String name;

}
