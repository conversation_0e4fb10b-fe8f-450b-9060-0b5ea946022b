package com.fangcang.grfp.core.vo.request.hotelgroup;

import com.fangcang.grfp.core.base.PageQuery;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.List;

@Getter
@Setter
@ApiModel("酒店集团签约项目列表请求")
public class QueryProjectOverviewRequest extends PageQuery {

    //项目名称查询
    @ApiModelProperty("项目名称查询")
    private String searchProjectName;

    //机构名称查询
    @ApiModelProperty("机构名称查询")
    private String searchOrgName;

    //项目状态(1：签约中，2：签约结束，3：已废除) ，0查询所有
    @ApiModelProperty("项目状态(1：签约中，2：签约结束，3：已废除) ，0查询所有")
    private Integer projectState;

    //酒店id
    @ApiModelProperty("酒店id")
    private Long hotelId;


    //-1: 报价总览, 0未报价， 1已报价 2议价中 3已中签 4已否决, -2 表示待审核
    @ApiModelProperty("-1:报价总览, 0未报价， 1已报价 2议价中 3已中签 4已否决, -2 表示待审核")
    private Integer quoteStatus;

    // 品牌ID
    @ApiModelProperty("品牌ID")
    private Long hotelBrandId;

    // 报价来源 2：酒店，4：酒店集团
    @ApiModelProperty("报价来源 2：酒店，4：酒店集团")
    private Integer bidOrgType;

    // 酒店集团审核状态 1 待审核 3 审核驳回
    @ApiModelProperty("酒店集团审核状态 1 待审核 3 审核驳回")
    private Integer hotelGroupApproveStatus;

    // 酒店集团品牌id集合
    private Collection<Long> hotelGroupBrandIdList;

    //酒店集团员工用户id
    @JsonIgnore
    private Integer employeeUserId;

    //酒店集团机构id
    @JsonIgnore
    private Integer orgId;

}
