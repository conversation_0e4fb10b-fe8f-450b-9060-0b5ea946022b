ALTER TABLE `grfp`.`t_project_hotel_price_group`
    ADD COLUMN `is_include_breakfast` int(1) NOT NULL COMMENT '是否包含早餐' AFTER `lra`;

### 导入报价任务
create table `grfp`.`t_import_bid_task`
(
    `id`                    bigint       not null auto_increment comment '主键',
    `hotel_id`              bigint       null comment '酒店id',
    `project_id`            int          not null comment '项目id',
    `batch_id`              varchar(64)  not null comment '批次 id',
    `prop_code`             varchar(128) null comment '导入集团内部酒店编码',
    `amadeus_chain_code`    varchar(128) null comment '导入AMADEUS集团编码',
    `amadeus_hotel_code`    varchar(128) null comment '导入AMADEUS酒店编码',
    `prop_name`             varchar(128) null comment '导入酒店名称',
    `prop_phone`            varchar(128) null comment '导入酒店电话',
    `prop_add`              varchar(128) null comment '导入酒店地址',
    `is_validate_error`     int          not null comment '是否校验错误',
    `validate_error_detail` mediumtext   null comment '校验错误详情',
    `generate_bid_status`   int          not null comment '生成报价状态: 0:待生成，1:已生成',
    `data_detail`           mediumtext   not null comment '导入数据详情',
    `file_key`              varchar(64)  not null comment 'oss 文件 key',
    `file_name`             varchar(128) null comment '文件名称',
    `creator`               varchar(100) not null comment '创建人',
    `create_time`           datetime     not null default current_timestamp comment '创建时间',
    `modifier`              varchar(100) null comment '修改人',
    `modify_time`           datetime     null on update current_timestamp comment '修改时间',
    primary key (id)
) comment '导入报价任务表';

create index idx_project_id_hotel_id on `grfp`.`t_import_bid_task` (project_id, hotel_id);

insert into t_text_resource(text_resource_code, text_resource_type, value_en_us, value_zh_cn)
values
    ('GENERATED_BID_STATUS_0', 1, 'Not generated', '待生成'),
    ('GENERATED_BID_STATUS_1', 1, 'Generated', '已生成');

insert into t_text_resource(text_resource_code, text_resource_type, value_en_us, value_zh_cn)
values
    ('IMPORT_BID_TASK_VALIDATE_NO_PASSED', 2, 'Import bid task validate no passed', '报价任务校验不通过'),
    ('IMPORT_BID_TASK_CANNOT_BE_UPDATE', 2, 'Import bid task can not be update', '报价任务已生成报价，不允许修改'),
    ('IMPORT_BID_TASK_NOT_EXIST', 2, 'Import bid task not exist', '导入报价任务不存在'),
    ('IMPORT_DATA_EXCEED_LIMIT', 2, 'Import data exceed limit', '导入数据超过限制'),
    ('IMPORT_DATA_CANNOT_BE_EMPTY', 2, 'Import data can not be empty', '导入数据不能为空');
;

insert into t_user_permission(org_type, role_code, permission, creator)
    value (1, 'ADMIN', 'BID_TASK_C_U_D', 'chutao');

### 自定义采购策略优化
alter table `grfp`.t_project_custom_tend_strategy
    modify strategy_type int not null comment '策略类型: 1-是或否 2-文本 3-回复选项';

create index idx_project_id_display_order on `grfp`.t_project_custom_tend_strategy (project_id, display_order);

create table `grfp`.`t_project_custom_strategy_option`
(
    id            bigint auto_increment comment '主键',
    strategy_id   bigint                             not null comment '策略ID',
    option_name   varchar(1024)                      not null comment '选项',
    display_order int                                not null comment '排序(小到大)',
    weight_score  decimal(10, 2)                     null comment '权重分值',
    creator       varchar(100)                       not null comment '创建人',
    create_time   datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    modifier      varchar(100)                       null comment '修改人',
    modify_time   datetime                           null on update current_timestamp comment '修改时间',
    primary key (id)
) comment '项目自定义策略选项表';

create index idx_strategy_id_display_order on `grfp`.`t_project_custom_strategy_option` (strategy_id, display_order);

create table `grfp`.`t_custom_bid_strategy_option`
(
    id                      bigint auto_increment comment '主键',
    option_id               bigint comment '策略选项 id',
    strategy_id             bigint                             not null comment '策略ID',
    project_intent_hotel_id int                                not null comment '项目意向酒店id',
    hotel_id                bigint                             not null comment '酒店id',
    project_id              int                                not null comment '项目id',
    option_name             varchar(1024)                      not null comment '选项',
    is_support              int                                null comment '是否支持',
    creator                 varchar(100)                       not null comment '创建人',
    create_time             datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    modifier                varchar(100)                       null comment '修改人',
    modify_time             datetime                           null on update current_timestamp comment '修改时间',
    primary key (id)
) comment '自定义报价策略选项表';

create unique index udx_project_id_hotel_id_strategy_id_option_id on t_custom_bid_strategy_option (project_id, hotel_id, strategy_id, option_id);

create table `grfp`.`t_h_group_default_cus_strategy_option`
(
    id                            bigint auto_increment comment '主键',
    option_id                     bigint comment '策略选项 id',
    strategy_id                   bigint                             not null comment '策略ID',
    project_intent_hotel_group_id int                                not null comment '项目意向酒店集团ID',
    project_id                    int                                not null comment '项目id',
    option_name                   varchar(1024)                      not null comment '选项',
    is_support                    int                                null comment '是否支持',
    creator                       varchar(100)                       not null comment '创建人',
    create_time                   datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    modifier                      varchar(100)                       null comment '修改人',
    modify_time                   datetime                           null on update current_timestamp comment '修改时间',
    primary key (id)
) comment '酒店集团默认报价自定义策略选项';

create unique index udx_project_intent_hotel_group_id_strategy_id_option_id on t_h_group_default_cus_strategy_option (project_intent_hotel_group_id, strategy_id, option_id);


ALTER TABLE `grfp`.`t_lanyon_import_column`
    ADD COLUMN `data_type_code` varchar(255) NULL AFTER `data_type`;
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'BASIC_INFO'  WHERE data_type = '基础信息';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'BASIC_BID_INFO'  WHERE data_type = '基础协议价报价信息';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'SEASON1'  WHERE data_type = '季节1';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'SEASON2'  WHERE data_type = '季节2';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'SEASON3'  WHERE data_type = '季节3';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'SEASON4'  WHERE data_type = '季节4';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'SEASON5'  WHERE data_type = '季节5';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'BASIC_TAX_AND_AGREE_INFO'  WHERE data_type = '基础协议税费及协议相关信息';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'USERDEFINED'  WHERE data_type = '基础协议定制问题';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'HOTEL_FACILITIES'  WHERE data_type = '酒店设施';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'LASTROOMAVAIL_BD'  WHERE data_type = 'LRA价格不适用日期报价信息';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'BD1_INFO'  WHERE data_type = '价格不适用时段1';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'BD2_INFO'  WHERE data_type = '价格不适用时段2';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'BD3_INFO'  WHERE data_type = '价格不适用时段3';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'BD4_INFO'  WHERE data_type = '价格不适用时段4';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'BD5_INFO'  WHERE data_type = '价格不适用时段5';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'BD6_INFO'  WHERE data_type = '价格不适用时段6';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'BD7_INFO'  WHERE data_type = '价格不适用时段7';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'BD8_INFO'  WHERE data_type = '价格不适用时段8';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'BD9_INFO'  WHERE data_type = '价格不适用时段9';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'BD10_INFO'  WHERE data_type = '价格不适用时段10';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'HOTEL_SERVICE_INFO'  WHERE data_type = '酒店服务信息';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'LOS_ES'  WHERE data_type = '长住房报价信息';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'ADDTL_CHARGE'  WHERE data_type = '长住房额外费用';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'MGS_USERDEFINE'  WHERE data_type = '会议室定制问题';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'GROUP_MGS_INFO'  WHERE data_type = '团房会议室报价信息';
UPDATE  `t_lanyon_import_column` SET DATA_TYPE_CODE = 'CSR_CERTIFIED'  WHERE data_type = '酒店认证信息';






###