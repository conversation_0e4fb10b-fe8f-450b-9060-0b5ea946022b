package com.fangcang.grfp.core.vo.response.hotel;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class HotelDataVO extends BaseVO {

    @ApiModelProperty(value = "酒店 ID")
    private Long hotelId;

    @ApiModelProperty(value = "酒店数据")
    private String hotelData;

    @ApiModelProperty(value = "图片数据")
    private String imageData;
}
