package com.fangcang.grfp.api.controller.account;

import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.account.service.AccountService;
import com.fangcang.grfp.api.controller.account.vo.*;
import com.fangcang.grfp.api.controller.org.vo.OrgInfoVO;
import com.fangcang.grfp.api.controller.user.vo.ResetUserPasswordRequest;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.constant.UserPermission;
import com.fangcang.grfp.core.usersession.Anon;
import com.fangcang.grfp.core.usersession.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;


@Api(tags="账户")
@RestController
@RequestMapping("/Api/Account")
public class AccountController extends BaseController {

    @Autowired
    private AccountService accountService;

    /**
     * 根据邮箱找回密码或修改密码
     */
    @Anon
    @ApiOperation("邮箱找回密码")
    @RequestMapping(value = "/FindPswdByEmail",method = RequestMethod.POST)
    @UserAuditLog("账户-邮箱找回密码")
    public Result<Void> findPswdByEmail(@Valid @RequestBody FindPswdByEmailRequest findPswdByEmailRequest) {
        accountService.findPswdByEmail(findPswdByEmailRequest);
        return Result.ok();
    }

    @ApiOperation("用户信息")
    @RequestMapping(value = "/AccountInfo",method = RequestMethod.POST)
    public Result<AccountInfoVO> accountInfo(HttpServletRequest request) {
        return Result.ok(accountService.accountInfo(request));
    }

    @ApiOperation("更新用户信息")
    @UserAuditLog("账户-更新账户信息")
    @RequestMapping(value = "/UpdateAccountInfo",method = RequestMethod.POST)
    public Result<Void> updateAccountInfo(HttpServletRequest request, @Valid @RequestBody UpdateAccountInfoRequest updateAccountInfoRequest) {
        accountService.updateAccountInfo(request, updateAccountInfoRequest);
        return Result.ok();
    }

    @ApiOperation("用户机构信息")
    @RequestMapping(value = "/AccountOrgInfo",method = RequestMethod.POST)
    public Result<OrgInfoVO> accountOrgInfo(HttpServletRequest request) {
        return Result.ok(accountService.accountOrgInfo(request));
    }

    @ApiOperation("编辑账户机构信息")
    @PostMapping("/UpdateAccountOrgInfo")
    @ResponseBody
    @UserAuditLog("账户-编辑账户机构信息")
    public Result<Void> updateAccountOrgInfo(HttpServletRequest request, @Valid @RequestBody UpdateAccountOrgRequest updateAccountOrgRequest) {
        accountService.updateAccountOrgInfo(request, updateAccountOrgRequest);
        return Result.ok();
    }

    @ApiOperation("重置密码")
    @PostMapping("/ResetPasword")
    @ResponseBody
    @UserAuditLog("账户-重置密码")
    public Result<Void> resetPassword(HttpServletRequest request) {
        accountService.resetPassword(request);
        return Result.ok();
    }

    @ApiOperation("修改用户自己密码")
    @PostMapping("/UpdateSelfPassword")
    @ResponseBody
    @UserAuditLog("账户-修改用户自己密码")
    public Result<Void> updateSelfPassword(HttpServletRequest request, @Valid @RequestBody UpdateSelfPasswordRequest updateSelfPasswordRequest) {
        accountService.updateSelfPassword(request, updateSelfPasswordRequest);
        return Result.ok();
    }
}
