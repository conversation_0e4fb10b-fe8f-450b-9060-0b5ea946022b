package com.fangcang.grfp.core.cached;

import com.fangcang.grfp.core.entity.HotelEntity;
import com.fangcang.grfp.core.vo.HotelOrgRelatedInfoVO;
import com.fangcang.grfp.core.vo.response.hotel.RoomNameInfoVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface CachedHotelService {

    Map<Long, String> getNameMap(int languageId);

    void clearNameMap(int languageId);

    HotelEntity getById(long id);

    HotelOrgRelatedInfoVO queryOrgRelatedInfoVO(Integer hotelOrgId);

    List<RoomNameInfoVO> queryRoomNameListByHotelIds(int language, Collection<Long> hotelIds);

}
