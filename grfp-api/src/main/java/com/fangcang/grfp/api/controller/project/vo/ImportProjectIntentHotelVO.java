package com.fangcang.grfp.api.controller.project.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fangcang.grfp.core.base.ImportVO;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 */
@Data
public class ImportProjectIntentHotelVO extends ImportVO {
    //房仓酒店id	历史采购间夜	历史采购单价	酒店销售姓名	酒店销售手机号	酒店销售邮箱	企业跟进人电邮	运营平台报价跟进人	运营平台上线跟进人	运营平台履约跟进人
    //FC HotelId	Historical Procurement Room Nights	Historical Procurement Unit Price	Hotel Sales Contact Name	Hotel Sales Contact Mobile	Hotel Sales Contact Email	Distributor Contact Email	Platform Quotation Follower	Platform Online Follower	 Platform Performance Follower

    @ExcelProperty(value = "FC HotelId", index = 0)
    private Long hotelId;

    /**
     * 近一年采购间夜数
     */
    @ExcelProperty(value = "Historical Procurement Room Nights", index = 1)
    private Long lastYearRoomNight;

    /**
     * 采购均价
     */
    @ExcelProperty(value = "Historical Procurement Unit Price", index = 2)
    private BigDecimal tenderAvgPrice;

    /**
     * 酒店销售联系人姓名
     */
    @ExcelProperty(value = "Hotel Sales Contact Name", index = 3)
    private String hotelSalesContactName;

    /**
     * 酒店销售联系人电话
     */
    @ExcelProperty(value = "Hotel Sales Contact Mobile", index = 4)
    private String hotelSalesContactMobile;

    /**
     * 酒店销售联系人邮箱
     */
    @ExcelProperty(value = "Hotel Sales Contact Email", index = 5)
    private String hotelSalesContactEmail;

    /**
     * 企业跟进人电邮
     */
    @ExcelProperty(value = "Distributor Contact Email", index = 6)
    private String distributorContactEmail;

    // 平台报价监控人
    @ExcelProperty(value = "Platform Quotation Follower", index = 7)
    private String hotelPriceFollowName;

    // 平台线上跟进人姓名
    @ExcelProperty(value = "Platform Online Follower", index = 8)
    private String onlineFollowName;

    // 平台履约跟进人姓名
    @ExcelProperty(value = "Platform Performance Follower", index = 9)
    private String monitorFollowName;
    /**
     * 企业(分销商)跟进人姓名(招投标项目时指派的)
     */
    private String distributorContactName;

    /**
     * 企业(分销商)跟进人id(招投标项目时指派的)
     */

    private Long distributorContactUid;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 项目id
     */
    private Long projectId;

}
