package com.fangcang.grfp.api.controller.currencyexchangerate.vo;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.entity.CurrencyExchangeRateLogEntity;
import com.fangcang.grfp.core.util.KvpLogUtility;
import com.fangcang.grfp.core.vo.KvpLogBeforeAfterValuePairVO;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("币种汇率日志")
@Getter
@Setter
public class CurrencyExchangeRateLogVO extends BaseVO {
	// ---------------------------------------------------------------------------------------------------- Private Member Variables

	@ApiModelProperty("币种汇率日志ID")
	protected Integer currencyExchangeRateLogId;
	
	@ApiModelProperty("币种编码")
	protected String currencyCode;
	
	@ApiModelProperty("beforeKvJson")
    private String beforeKvJson;
	
	@ApiModelProperty("afterKvJson")
    private String afterKvJson;
	
	@ApiModelProperty("新增用户编码")
	protected String creator;

	@ApiModelProperty("新增时间")
	@JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	@ApiModelProperty("relatedBeforeAfterValuePairList")
	protected List<KvpLogBeforeAfterValuePairVO> relatedBeforeAfterValuePairList;
	
	// ---------------------------------------------------------------------------------------------------- Constructor
	
	public CurrencyExchangeRateLogVO() {
		super();
	}
	
	public CurrencyExchangeRateLogVO(CurrencyExchangeRateLogEntity currencyLog) {
		super();
		BeanUtils.copyProperties(currencyLog, this);
		this.relatedBeforeAfterValuePairList = KvpLogUtility.constructKvpLogBeforeAfterValuePairVOList(currencyLog.getBeforeKvJson(), currencyLog.getAfterKvJson());
	}



}
