package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("修改项目状态请求")
@Getter
@Setter
public class UpdateProjectStateRequest extends BaseVO {

    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;

    @ApiModelProperty("项目状态 项目状态(0：未启动，1：招标中(已启动)，2：招标完成，3：已废标)")
    @NotNull
    private Integer projectState;
}
