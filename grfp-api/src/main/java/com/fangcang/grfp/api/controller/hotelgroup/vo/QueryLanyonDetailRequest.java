package com.fangcang.grfp.api.controller.hotelgroup.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("查询Lanyon导入详情")
@Getter
@Setter
public class QueryLanyonDetailRequest {

    @ApiModelProperty("项目酒店意向ID")
    private Integer projectIntentHotelId;

    /**
     * 数据类型， 1:Lanyon导入数据，2:Lanyon导入数据（如果存在两条显示有早）
     */
    @ApiModelProperty("数据类型")
    private Integer dataType;



}
