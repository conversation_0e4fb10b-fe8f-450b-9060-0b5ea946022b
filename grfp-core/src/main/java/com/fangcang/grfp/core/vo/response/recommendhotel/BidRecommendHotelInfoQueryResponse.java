package com.fangcang.grfp.core.vo.response.recommendhotel;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@ApiModel("报价酒店推荐VO")
@Getter
@Setter
public class BidRecommendHotelInfoQueryResponse implements java.io.Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Integer projectId;

    /**
     * 酒店ID
     */
    @ApiModelProperty("酒店ID")
    private Long hotelId;

    //城市编码
    @ApiModelProperty("城市编码")
    private String cityCode;


    //酒店名称
    @ApiModelProperty("酒店名称")
    private String hotelName;

    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String nameEnUs;

    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String nameZhCn;

    //星级
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String hotelStar;

    // 酒店星级名称
    @ApiModelProperty("酒店星级名称")
    private String hotelStarName;

    //评分
    @ApiModelProperty("评分")
    private String rating;

    //百度经度
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private BigDecimal lngBaidu;

    //百度纬度
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private BigDecimal latBaidu;

    //百度经度
    @ApiModelProperty("经度Google")
    private BigDecimal lngGoogle;

    //百度纬度
    @ApiModelProperty("维度Google")
    private BigDecimal latGoogle;

    // 推荐等级 1:SSS,2:SS,3:S,4:A,5:B,6:C
    @ApiModelProperty("推荐等级 1:SSS,2:SS,3:S,4:A,5:B,6:C")
    private String recommendLevel;

    // 签约间夜数
    @ApiModelProperty("签约间夜数")
    private String requiredRoomNight;

    // 是否可以预订酒店最后一间未售出的客房(1:是，0：否
    @ApiModelProperty("是否可以预订酒店最后一间未售出的客房(1:是，0：否")
    private Integer lra;

    // 早餐
    @ApiModelProperty("早餐")
    private Integer breakfastNum;

    // 签约参考价
    @ApiModelProperty("签约参考价")
    private BigDecimal referencePrice;

    // 是否邀约 1:邀约，0没有邀约
    @ApiModelProperty("是否邀约 1:邀约，0没有邀约")
    private Integer isInvited;

    // 是否报价
    @ApiModelProperty("是否报价")
    private Integer isBid;

    @ApiModelProperty("报价状态 0:未报价，1:新标，2：议价中，3：议价中签，4：已否定，5，放弃报价，6：修订报价，7：拒绝报价")
    private Integer bidState;

    @ApiModelProperty("项目名称")
    private String projectName;

}
