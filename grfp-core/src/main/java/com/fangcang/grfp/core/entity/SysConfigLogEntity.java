package com.fangcang.grfp.core.entity;

import com.fangcang.grfp.core.base.BaseVO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 系统配置修改日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sys_config_log")
public class SysConfigLogEntity extends BaseVO {


    /**
     * 系统配置修改日志ID
     */
    @TableId(value = "sys_config_log_id", type = IdType.AUTO)
    private Integer sysConfigLogId;

    /**
     * 操作编号
     */
    @TableField("operate_code")
    private String operateCode;

    /**
     * 系统配置编号
     */
    @TableField("sys_config_code")
    private String sysConfigCode;

    /**
     * 修改前值
     */
    @TableField("before_kv_json")
    private String beforeKvJson;

    /**
     * 修改后值
     */
    @TableField("after_kv_json")
    private String afterKvJson;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;


}
