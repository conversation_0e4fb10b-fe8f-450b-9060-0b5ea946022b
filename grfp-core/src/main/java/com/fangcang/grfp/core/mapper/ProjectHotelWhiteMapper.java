package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.core.entity.ProjectHotelWhiteEntity;
import com.fangcang.grfp.core.vo.request.project.QueryProjectHotelWhiteRequest;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelWhiteVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 项目酒店白名单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface ProjectHotelWhiteMapper extends BaseMapper<ProjectHotelWhiteEntity> {

    /**
     * 插入或更新
     */
    void upsert(@Param("entity") ProjectHotelWhiteEntity entity);

    /**
     * 根据条件查询项目酒店白名单详情
     */
    Page<ProjectHotelWhiteVO> selectByCondition(Page<ProjectHotelWhiteVO> page, @Param("req") QueryProjectHotelWhiteRequest req);

    /**
     * 查询项目酒店白名ID
     *
     */
    List<Long> queryProjectWhiteHotelIdList(QueryProjectHotelWhiteRequest queryProjectHotelWhiteRequest);


}
