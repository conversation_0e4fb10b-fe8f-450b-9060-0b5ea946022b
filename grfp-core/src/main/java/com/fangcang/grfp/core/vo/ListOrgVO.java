package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("机构列表查询返回")
@Getter
@Setter
public class ListOrgVO extends BaseVO {

    @ApiModelProperty("机构ID")
    private Integer orgId;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("机构类型")
    private Integer orgType;

    @ApiModelProperty("机构类型名称")
    private String orgTypeName;

    @ApiModelProperty("联系人")
    private String contactName;

    @ApiModelProperty("联系人电话区号")
    private String contactMobileAreaCode;

    @ApiModelProperty("联系人电话")
    private String contactMobile;

    @ApiModelProperty("联系人电邮")
    private String contactEmail;

    @ApiModelProperty("Logo Url地址")
    private String logoUrl;

    @ApiModelProperty("状态(1：有效，0：无效)")
    private Integer state;


}
