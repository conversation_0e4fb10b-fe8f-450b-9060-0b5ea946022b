package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.ProjectRecommendStatLogEntity;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 项目历史数据推荐统计日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface ProjectRecommendStatLogMapper extends BaseMapper<ProjectRecommendStatLogEntity> {

    void finishRecord(ProjectRecommendStatLogEntity projectRecommendStatLog);

    ProjectRecommendStatLogEntity getProjectRecommendStatLog(@Param("projectId") Integer projectId, @Param("statReferenceNo") String statReferenceNo,
                                                             @Param("statName") String statName);
}
