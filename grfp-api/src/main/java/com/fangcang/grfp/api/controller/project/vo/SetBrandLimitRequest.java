package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel("设置品牌报价限制请求")
public class SetBrandLimitRequest extends BaseVO {

    @NotNull
    @ApiModelProperty(value = "项目ID", required = true)
    private Integer projectId;

    @NotNull
    @ApiModelProperty(value = "酒店集团机构ID", required = true)
    private Integer hotelGroupOrgId;

    @NotNull
    @ApiModelProperty(value = "是否品牌限制 1-是 0-否", required = true)
    private Integer isBrandLimit;
}