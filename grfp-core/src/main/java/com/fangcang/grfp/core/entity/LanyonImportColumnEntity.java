package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * Lanyon导入列
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_lanyon_import_column")
public class LanyonImportColumnEntity extends BaseVO {


    /**
     * 序列号
     */
    @TableId(value = "column_index", type = IdType.ASSIGN_ID)
    private Integer columnIndex;

    /**
     * 数据类型
     */
    @TableField("data_type")
    private String dataType;

    /**
     * 列编码
     */
    @TableField("column_code")
    private String columnCode;

    /**
     * 列名称
     */
    @TableField("`column_name`")
    private String columnName;

    /**
     * 排序
     */
    @TableField("display_order")
    private Integer displayOrder;

    /**
     * 分类
     */
    @TableField("data_category")
    private String dataCategory;


}
