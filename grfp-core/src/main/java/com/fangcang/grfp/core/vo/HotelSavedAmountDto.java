package com.fangcang.grfp.core.vo;

import java.math.BigDecimal;

public class HotelSavedAmountDto {

    // 酒店ID
    private Long hotelId;

    // 分销商
    private String distributorCode;

    // 节省金额
    private BigDecimal savedAmount;

    // OTA价格
    private BigDecimal totalOtaPrice;

    private Long orgId;

    public Long getHotelId() {
        return hotelId;
    }

    public void setHotelId(Long hotelId) {
        this.hotelId = hotelId;
    }

    public String getDistributorCode() {
        return distributorCode;
    }

    public void setDistributorCode(String distributorCode) {
        this.distributorCode = distributorCode;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public BigDecimal getSavedAmount() {
        return savedAmount;
    }

    public void setSavedAmount(BigDecimal savedAmount) {
        this.savedAmount = savedAmount;
    }

    public BigDecimal getTotalOtaPrice() {
        return totalOtaPrice;
    }

    public void setTotalOtaPrice(BigDecimal totalOtaPrice) {
        this.totalOtaPrice = totalOtaPrice;
    }
}
