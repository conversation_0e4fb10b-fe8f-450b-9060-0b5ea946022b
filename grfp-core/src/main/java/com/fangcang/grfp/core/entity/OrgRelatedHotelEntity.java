package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 机构关联酒店
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_org_related_hotel")
public class OrgRelatedHotelEntity extends BaseVO {


    @TableId(value = "org_related_hotel_id", type = IdType.AUTO)
    private Integer orgRelatedHotelId;

    /**
     * 机构ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;


}
