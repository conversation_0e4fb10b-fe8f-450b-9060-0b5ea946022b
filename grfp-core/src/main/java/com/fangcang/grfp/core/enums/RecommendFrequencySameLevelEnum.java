 package com.fangcang.grfp.core.enums;

 /**
  * 高频预订同档推荐原因
  */
 public enum RecommendFrequencySameLevelEnum {

     OTA_49(1, "高产周边酒店，同档超高评分"),
     OPEN_IN_2_YEAR(2, "高产周边酒店，同档新开业酒店"),
     LAST_YEAR_ROOM_NIGHT_600(3, "高产周边酒店，同档商旅常订酒店"), // 商旅近12个月产量超过600间夜推荐邀约（不限客户，看预订监控该酒店所有分销商总和间夜数）。

     OTA_45_PRICE_LOWER_THAN_10_PERCENT(4, "高产周边酒店，同档高性价比酒店推荐"), // OTA评分4.5分以上，签约混淆后价格比当前高产酒店去年成交均价低于10%，且酒店+机构纬度（一个酒店不同机构签约价格可能不同，看签约最好的节省率）的最高节省率高于10%

        // OTA评分4.5分以上，签约混淆后价格比当前高产酒店去年成交均价低，且酒店+机构纬度（一个酒店不同机构签约价格可能不同，看签约最好的节省率）的最高节省率高于10%
     OTA_45_LOWER_PRICE(5,"高产周边酒店，同价位优质酒店推荐");

     public int key;

     public String value;
     RecommendFrequencySameLevelEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (RecommendFrequencySameLevelEnum recommendFrequencyEnum : RecommendFrequencySameLevelEnum.values()) {
            if (recommendFrequencyEnum.key == key) {
                value = recommendFrequencyEnum.value;
                break;
            }
        }
        return value;
    }
}
