package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("修改酒店报价状态请求")
@Getter
@Setter
public class UpdateHotelBidStateRequest extends BaseVO {

    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;

    @ApiModelProperty("报价状态 2：议价中，3：议价中签，4：已否定，5，放弃报价，6：修订报价，7：拒绝报价")
    @NotNull
    private Integer bidState;

    @ApiModelProperty("酒店ID集合")
    @NotNull
    private List<Long> hotelIds;

    @ApiModelProperty("继续议价备注")
    private String remark;

    @ApiModelProperty("拒绝议价备注备注")
    private String rejectNegotiationRemark;

    @ApiModelProperty("上传备注")
    private String uploadRemark;

    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String modifier;

}
