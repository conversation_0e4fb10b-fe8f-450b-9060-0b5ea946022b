package com.fangcang.grfp.core.enums;

import com.fangcang.grfp.core.util.GenericAppUtility;

/**
 * <AUTHOR>
 * @date 2022/11/1 15:14
 */
public enum ProjectStateEnum {

    //项目状态(0：未启动，1：招标中(已启动)，2：招标完成，3：已废标)
    NOT_STARTED(0, "未启动", "Not Started"),
    STARTED(1, "签约中(已启动)", "Signing"),
    BIDDING_COMPLETED(2, "签约完成", "Signed"),
    BID_ABANDONED(3, "已废标", "Abandoned Bid");

    public int key;

    public String value;
    public String valueEn;

    ProjectStateEnum(int key, String value, String valueEn) {
        this.key = key;
        this.value = value;
        this.valueEn = valueEn;
    }

    public static String geTextByKey(Integer key, int languageId) {
        String value = null;
        for(ProjectStateEnum projectStateEnum : ProjectStateEnum.values()) {
            if(projectStateEnum.key == key) {
                value =  GenericAppUtility.getText(languageId, "PROJECT_STATE_" + projectStateEnum.key);
                break;
            }
        }
        return value;
    }

}
