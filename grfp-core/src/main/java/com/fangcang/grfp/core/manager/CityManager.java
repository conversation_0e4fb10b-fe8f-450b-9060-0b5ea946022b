package com.fangcang.grfp.core.manager;

import com.fangcang.grfp.core.mapper.CityMapper;
import com.fangcang.grfp.core.vo.response.city.CityVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CityManager {

    @Resource
    private CityMapper cityMapper;

    public Map<String, CityVO> queryCityInfo(Collection<String> cityCodes) {
        List<CityVO> cityList = cityMapper.selectCityVOList(cityCodes);
        return cityList.stream().collect(Collectors.toMap(CityVO::getCityCode, Function.identity(), (o, v) -> v));
    }

}
