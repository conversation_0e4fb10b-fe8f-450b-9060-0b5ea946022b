package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.BidApplicableDayVO;
import com.fangcang.grfp.core.vo.BidUnApplicableDayVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("修改报价日期请求")
@Getter
@Setter
public class UpdateBidDateSettingRequest extends BaseVO {

    @ApiModelProperty("项目酒店ID")
    @NotNull
    private Integer projectIntentHotelId;

    @ApiModelProperty("可用日期列表")
    @NotNull
    private List<BidApplicableDayVO> bidApplicableDayList;

    @ApiModelProperty("不可用日期列表")
    @NotNull
    private List<BidUnApplicableDayVO> bidUnApplicableDayList;

}
