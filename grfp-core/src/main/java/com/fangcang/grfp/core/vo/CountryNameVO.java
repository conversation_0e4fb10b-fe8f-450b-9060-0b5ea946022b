package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("国家名称信息")
@Getter
@Setter
public class CountryNameVO extends BaseVO {

    @ApiModelProperty(value = "国家编号")
    private String countryCode;

    @ApiModelProperty(value = "国家名称")
    private String name;

    @ApiModelProperty(value = "国家名称简称")
    private String shortName;

    @ApiModelProperty(value = "英文名称")
    @JsonIgnore
    private String nameEnUs;
    @ApiModelProperty(value = "中文名称")
    @JsonIgnore
    private String nameZhCn;
    @ApiModelProperty(value = "英文名称简称")
    @JsonIgnore
    private String shortNameEnUs;
    @ApiModelProperty(value = "中文名称简称")
    @JsonIgnore
    private String shortNameZhCn;


}
