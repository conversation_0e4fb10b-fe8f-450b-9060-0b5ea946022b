package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("获取项目请求")
@Getter
@Setter
public class GetProjectInfoRequest extends BaseVO {

    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;
}
