package com.fangcang.grfp.core.filter;

import cn.hutool.core.util.IdUtil;
import org.slf4j.MDC;

import javax.servlet.*;
import java.io.IOException;

/**
 * 日志跟踪 ID 过滤器.
 * <p>
 * 在 sfl4j mdc 增加 traceId, 可串起整个请求的日志
 */
public class TraceIdFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            // logback 日志配置可用 %X{traceId}| 取得 traceId
            MDC.put("traceId", IdUtil.objectId());
            chain.doFilter(request, response);
        } finally {
            MDC.remove("traceId");
        }
    }

}
