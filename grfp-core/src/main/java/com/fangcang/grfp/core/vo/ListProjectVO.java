package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.refs.RefFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel("项目列表查询返回")
@Getter
@Setter
public class ListProjectVO extends BaseVO {

    @ApiModelProperty("项目ID")
    private Integer projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目状态 项目状态(0：未启动，1：招标中(已启动)，2：招标完成，3：已废标)  文字资源编号：PROJECT_STATE_xx")
    private Integer projectState;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("项目类型 项目类型(1：酒店)，跟合同模板业务类型保持一致 文字资源编号：PROJECT_TYPE_xx")
    private Integer projectType;

    @ApiModelProperty("预计签约酒店数")
    private Integer tenderCount;

    @ApiModelProperty("报名开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date bidStartTime;

    @ApiModelProperty("报名结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date bidEndTime;

    @ApiModelProperty("报价开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date firstBidStartTime;

    @ApiModelProperty("报价结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date firstBidEndTime;

    @ApiModelProperty("公布报价结果日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date bidResultTime;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("创建时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("报价酒店数量")
    private Integer bidHotelCount;

    @ApiModelProperty("邀请酒店数量")
    private Integer inviteHotelCount;

}
