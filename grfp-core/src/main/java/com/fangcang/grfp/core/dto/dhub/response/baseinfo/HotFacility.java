package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import lombok.Data;

@Data
public class HotFacility {

    /**
     * 游泳池：0否 1是
     */
    private Integer swimmingPool;

    /**
     * 健身中心：0否 1是
     */
    private Integer fitnessCenter;

    /**
     * SPA及健康中心：0否 1是
     */
    private Integer spaAndWellnessCenter;

    /**
     * 餐厅：0否 1是
     */
    private Integer restaurant;

    /**
     * 24小时前台：0否 1是
     */
    private Integer hourFrontDesk_24;

    /**
     * 酒吧：0否 1是
     */
    private Integer bar;

    /**
     * 免费早餐：0否 1是
     */
    private Integer freeBreakfast;

    /**
     * 停车场：0否 1是
     */
    private Integer parkingLot;

    /**
     * 免费WiFi：0否 1是
     */
    private Integer freeWiFi;

    /**
     * 宠物友好 ：0否 1是
     */
    private Integer petFriendly;

    /**
     * 充电车位：0否 1是
     */
    private Integer chargingStation;

    /**
     * 货币兑换：0否 1是
     */
    private Integer currencyExchange;

    /**
     * 会议室：0否 1是
     */
    private Integer meetingRoom;

    /**
     * 洗衣设施：0否 1是
     */
    private Integer laundryFacilities;

    /**
     * 接送服务：0否 1是
     */
    private Integer shuttleService;

    /**
     * 行李寄存：0否 1是
     */
    private Integer luggageStorage;

}
