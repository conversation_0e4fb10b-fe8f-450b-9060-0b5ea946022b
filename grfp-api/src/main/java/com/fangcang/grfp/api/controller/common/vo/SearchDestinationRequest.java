package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@ApiModel("目的地查询")
@Getter
@Setter
public class SearchDestinationRequest extends BaseVO {

    @ApiModelProperty("目的地关键字")
    @NotBlank
    private String keyWord;;

}
