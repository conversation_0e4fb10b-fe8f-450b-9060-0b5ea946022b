package com.fangcang.grfp.core.vo.response.project;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@ApiModel(description = "项目 POI 信息")
@Getter
@Setter
public class ProjectPoiVO extends BaseVO {

    private static final long serialVersionUID = -348256162235833478L;

    @ApiModelProperty(value = "项目 ID")
    private Integer projectId;

    @ApiModelProperty(value = "POI ID")
    private Long poiId;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "城市名称", hidden = true)
    @JsonIgnore
    private String cityNameZhCn;

    @ApiModelProperty(value = "城市名称", hidden = true)
    @JsonIgnore
    private String cityNameEnUs;

    @ApiModelProperty(value = "POI 名称")
    private String poiName;

    @ApiModelProperty(value = "POI 地址")
    private String poiAddress;

    @ApiModelProperty(value = "谷歌 POI 经度")
    private BigDecimal lngGoogle;

    @ApiModelProperty(value = "谷歌 POI 纬度")
    private BigDecimal latGoogle;

    @ApiModelProperty(value = "百度 POI 经度")
    private BigDecimal lngBaidu;

    @ApiModelProperty(value = "百度 POI 纬度")
    private BigDecimal latBaidu;

}
