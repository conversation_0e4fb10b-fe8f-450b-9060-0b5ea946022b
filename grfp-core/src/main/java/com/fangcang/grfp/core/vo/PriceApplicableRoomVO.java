package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class PriceApplicableRoomVO extends BaseVO {

    /**
     * 价格可用房型ID
     */
    private Integer priceApplicableRoomId;

    /**
     * 项目意向酒店ID
     */
    private Integer projectIntentHotelId;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 酒店ID
     */
    private Long hotelId;

    /**
     * 房档号
     */
    private Integer hotelPriceLevelId;

    /**
     * 房型ID
     */
    private Long roomTypeId;

    /**
     * 排序
     */
    private Integer displayOrder;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 房档等级号码
     */
    private Integer roomLevelNo;

    /**
     * 房型名称
     */
    private String roomTypeName;

    /**
     * 自定义房型名称
     */
    private String customRoomTypeName;

}
