package com.fangcang.grfp.api.controller.org.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("新增关联酒店品牌请求")
@Getter
@Setter
public class AddRelatedHotelBrandRequest extends BaseVO {

    @ApiModelProperty("酒店集团机构Id")
    @NotNull
    private Integer hotelGroupOrgId;

    @ApiModelProperty("酒店集团Id")
    @NotNull
    private Long hotelGroupId;

    @ApiModelProperty("酒店品牌Id集合")
    private List<Long> hotelBrandIdList;

    @ApiModelProperty("酒店品牌Id")
    private Long hotelBrandId;
}
