package com.fangcang.grfp.core.vo.request.project;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("分页查询酒店集团下意向酒店列表")
public class QueryHotelGroupInviteHotelRequest extends PageQuery {

    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "酒店集团ID")
    private Long hotelGroupId;

    @ApiModelProperty(value = "酒店品牌ID")
    private Long hotelBrandId;

}
