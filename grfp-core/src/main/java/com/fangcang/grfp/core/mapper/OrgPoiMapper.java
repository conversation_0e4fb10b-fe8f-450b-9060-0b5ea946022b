package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.core.entity.OrgPoiEntity;
import com.fangcang.grfp.core.vo.request.poi.ListPoiRequest;
import com.fangcang.grfp.core.vo.response.poi.ListPoiVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 机构 POI Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public interface OrgPoiMapper extends BaseMapper<OrgPoiEntity> {

    /**
     * 插入或更新
     */
    void upsert(@Param("orgPoiEntity") OrgPoiEntity orgPoiEntity);

    /**
     * 根据机构 ID 和 谷歌 POI 查询
     */
    default OrgPoiEntity selectByOrgIdAndGooglePoiId(Integer orgId, String mapPoiId) {
        LambdaQueryWrapper<OrgPoiEntity> queryWrapper = Wrappers.lambdaQuery(OrgPoiEntity.class)
            .eq(OrgPoiEntity::getOrgId, orgId)
            .eq(OrgPoiEntity::getMapPoiId, mapPoiId);
        return this.selectOne(queryWrapper);
    }

    /**
     * 批量插入
     */
    void batchInsert(@Param("list") List<OrgPoiEntity> list);

    /**
     * 根据条件查询 POI
     */
    Page<ListPoiVO> selectByCondition(IPage<?> page, @Param("request") ListPoiRequest request);
}
