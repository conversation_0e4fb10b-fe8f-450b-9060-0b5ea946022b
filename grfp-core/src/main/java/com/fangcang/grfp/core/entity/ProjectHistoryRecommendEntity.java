package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 项目历史数据推荐
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_history_recommend")
public class ProjectHistoryRecommendEntity extends BaseVO {


    /**
     * 项目ID
     */
    @TableId(value = "project_id", type = IdType.ASSIGN_ID)
    private Integer projectId;

    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 是否高频预订 1:是 0:不是
     */
    @TableField("is_frequency")
    private Integer isFrequency;

    /**
     * 是否高频预订推荐 1:是 0:不是
     */
    @TableField("is_frequency_recommend")
    private Integer isFrequencyRecommend;

    /**
     * 是否高频预订推荐 1:高产酒店 2:重点高产酒店 3:超高产酒店
     */
    @TableField("frequency_recommends")
    private String frequencyRecommends;

    /**
     * 是否高频预订同档 1:是 0:不是
     */
    @TableField("is_same_level_freq")
    private Integer isSameLevelFreq;

    /**
     * 是否高频预订同档推荐 1:是 0:不是
     */
    @TableField("is_same_level_freq_recommend")
    private Integer isSameLevelFreqRecommend;

    /**
     * 附近高产酒店ID
     */
    @TableField("same_level_freq_hotel_id")
    private Long sameLevelFreqHotelId;

    /**
     * 附近高产酒店距离
     */
    @TableField("same_level_freq_hotel_distance")
    private BigDecimal sameLevelFreqHotelDistance;

    /**
     * 附近高产酒店信息
     */
    @TableField("same_level_freq_hotel_info")
    private String sameLevelFreqHotelInfo;

    /**
     * 高频预订同档推荐 1:OTA评分4.9分及以上 2:开业时间在近2年 3:商旅常订酒店 4:成交均价低于10% 5:成交均价低
     */
    @TableField("same_level_freq_recommends")
    private String sameLevelFreqRecommends;

    /**
     * 是否POI周边酒店 1:是 0:不是
     */
    @TableField("is_poi_near_hotel")
    private Integer isPoiNearHotel;

    /**
     * 是否POI周边酒店推荐 1:是 0:不是
     */
    @TableField("is_poi_near_hotel_recommend")
    private Integer isPoiNearHotelRecommend;

    /**
     * POI周边酒店推荐 1:高产酒店，建议邀约 2:同档员工最常订酒店 3:超高评分酒店 4:新开业酒店 5:商旅常订酒店
     */
    @TableField("poi_near_hotel_recommends")
    private String poiNearHotelRecommends;

    /**
     * POI ID
     */
    @TableField("poi_near_hotel_poi_id")
    private Long poiNearHotelPoiId;

    /**
     * 是否非POI热点区域 1:是 0:不是
     */
    @TableField("is_no_poi_hot_area")
    private Integer isNoPoiHotArea;

    /**
     * 是否非POI热点区域推荐 1:是 0:不是
     */
    @TableField("is_no_poi_hot_area_recommend")
    private Integer isNoPoiHotAreaRecommend;

    /**
     * 非POI热点区域推荐 1:高产酒店 2:同档员工最常订酒店 3:超高评分酒店 4:新开业酒店 5:商旅常订酒店
     */
    @TableField("no_poi_hot_area_recommends")
    private String noPoiHotAreaRecommends;

    /**
     * 非POI热点区域ID
     */
    @TableField("no_poi_hotel_id")
    private Long noPoiHotelId;

    /**
     * 非POI热点信息 (酒店id,酒店名称，酒店距离，区域总间夜,周边采购价格统计分布,星级统计分布)
     */
    @TableField("no_poi_hotel_info")
    private String noPoiHotelInfo;

    /**
     * 是否散布聚量 1:是 0:不是
     */
    @TableField("is_area_gather")
    private Integer isAreaGather;

    /**
     * 是否散布聚量推荐
     */
    @TableField("is_area_gather_recommend")
    private Integer isAreaGatherRecommend;

    /**
     * 聚量酒店ID
     */
    @TableField("area_gather_hotel_id")
    private Long areaGatherHotelId;

    /**
     * 聚量酒店信息 (酒店ID,酒店名称,距离，同档总间夜数，同档最高价格，最低价格)
     */
    @TableField("area_gather_hotel_info")
    private String areaGatherHotelInfo;

    /**
     * 散布聚量推荐 1:推荐去年员工预订最多酒店 2:推荐去年高性价比酒店 3:城市去年员工预订最多酒店 4:城市去年高性价比酒店
     */
    @TableField("area_gather_recommends")
    private String areaGatherRecommends;

    /**
     * 是否优质酒店 1:是 0:不是
     */
    @TableField("is_high_quality")
    private Integer isHighQuality;

    /**
     * 是否优质酒店推荐:节省明星酒店推荐 (优质商旅酒店推荐等级XX)
     */
    @TableField("is_high_quality_recommend")
    private Integer isHighQualityRecommend;

    /**
     * 是否节省明星酒店 1:是 0:不是
     */
    @TableField("is_saved_hotel")
    private Integer isSavedHotel;

    /**
     * 是否节省明星酒店推荐 1:是 0:不是 (节省明星酒店推荐)
     */
    @TableField("is_saved_hotel_recommend")
    private Integer isSavedHotelRecommend;

    /**
     * 周边3KM同价位酒店间夜
     */
    @TableField("price_level_room_night")
    private Long priceLevelRoomNight;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    private String city;

}
