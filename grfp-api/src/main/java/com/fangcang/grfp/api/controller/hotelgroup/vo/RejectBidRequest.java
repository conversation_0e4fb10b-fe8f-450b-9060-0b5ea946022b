package com.fangcang.grfp.api.controller.hotelgroup.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 *  审核通过报价
 */
@ApiModel("酒店集团-审核拒绝报价请求")
@Getter
@Setter
public class RejectBidRequest extends BaseVO {

    // 酒店报价ID
    @ApiModelProperty("酒店报价ID")
    private Integer projectIntentHotelId;

    /**
     * 拒绝原因
     */
    @ApiModelProperty("拒绝原因")
    private String remark;


}
