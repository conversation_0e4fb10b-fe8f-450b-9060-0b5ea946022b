package com.fangcang.grfp.api.controller.org.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel("修改机构状态请求")
@Getter
@Setter
public class UpdateOrgStateRequest extends BaseVO {

    @ApiModelProperty(value = "机构ID")
    @NotNull
    private Integer orgId;

    @ApiModelProperty(value = "状态(1：有效，0：无效)")
    @NotNull
    private Integer state;

}
