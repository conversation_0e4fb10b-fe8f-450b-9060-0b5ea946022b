package com.fangcang.grfp.api.controller.org.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel("更新用户")
@Getter
@Setter
public class UpdateUserRequest extends BaseVO {

    @ApiModelProperty(value = "用户ID")
    @NotNull
    private Long userId;

    @ApiModelProperty(value = "用户姓名")
    @NotBlank
    private String userName;

    @ApiModelProperty(value = "用户电邮")
    @NotBlank
    @Email
    private String email;

    @ApiModelProperty(value = "手机号码区号")
    private String mobileAreaCode;

    @ApiModelProperty(value = "手机号码区号")
    @NotBlank
    private String mobile;

    @ApiModelProperty(value = "角色编码类型 ADMIN：管理员，EMPLOYEE：员工，HEAD_ORGANIZATION：机构负责人，CHANNEL_ADMIN:渠道合作管理员")
    @NotBlank
    private String rolCode;


}
