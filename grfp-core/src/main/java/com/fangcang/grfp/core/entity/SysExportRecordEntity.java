package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 导出记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sys_export_record")
public class SysExportRecordEntity extends BaseVO {


    /**
     * 系统导出记录ID
     */
    @TableId(value = "sys_export_record_id", type = IdType.AUTO)
    private Long sysExportRecordId;

    /**
     * 导出记录名称
     */
    @TableField("export_name")
    private String exportName;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 导出路径, 只存 key (方便换桶)
     */
    @TableField("export_path")
    private String exportPath;

    /**
     * 状态(fail:失败 processing:处理中 success:成功)
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


}
