package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@ApiModel("根据名称查询酒店请求")
@Getter
@Setter
public class QueryHotelByNameRequest extends BaseVO {

    @ApiModelProperty("城市编号")
    @NotBlank
    private String cityCode;

    @ApiModelProperty("酒店名称")
    @NotBlank
    private String name;
}
