package com.fangcang.grfp.api.controller.login.service;

import com.fangcang.grfp.api.controller.login.vo.LoginRequest;
import com.fangcang.grfp.api.controller.login.vo.LoginResponse;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.bo.RedisService;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.entity.OrgEntity;
import com.fangcang.grfp.core.entity.UserEntity;
import com.fangcang.grfp.core.entity.UserLoginLogEntity;
import com.fangcang.grfp.core.enums.LoginTypeEnum;
import com.fangcang.grfp.core.enums.OrgTypeEnum;
import com.fangcang.grfp.core.enums.SmsCodeTypeEnum;
import com.fangcang.grfp.core.enums.StateEnum;
import com.fangcang.grfp.core.mapper.OrgMapper;
import com.fangcang.grfp.core.mapper.UserLoginLogMapper;
import com.fangcang.grfp.core.mapper.UserMapper;
import com.fangcang.grfp.core.mapper.UserPermissionMapper;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.HttpServletRequestUtility;
import com.fangcang.grfp.core.util.StringUtil;
import com.fangcang.grfp.core.util.UserUtility;
import com.fangcang.grfp.core.vo.UserOrgVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.session.FindByIndexNameSessionRepository;
import org.springframework.session.Session;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LoginService {

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserLoginLogMapper userLoginLogMapper;
    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private UserPermissionMapper userPermissionMapper;
    @Autowired
    private RedisService redisService;

    public LoginResponse login(HttpSession session, HttpServletRequest request, HttpServletResponse response, LoginRequest loginRequest){
        String account = loginRequest.getAccount().trim();
        int languageId = AppUtility.getRequestHeaderLanguage(request);
        // 校验账号格式
        int index = account.lastIndexOf("@");
        if (index < 1) {
            AppUtility.serviceError(ErrorCode.LOGIN_ACCOUNT_FORMAT_ERROR);
        }

        // 检查密码或者验证码不能为空
        if(loginRequest.getLoginType().equals(LoginTypeEnum.EmailAndPwd.key) && StringUtils.isEmpty(loginRequest.getPassword())){
            AppUtility.serviceError(ErrorCode.LOGIN_PASSWORD_CANNOT_BE_EMPTY);
        }
        if(loginRequest.getLoginType().equals(LoginTypeEnum.EmailAndMsgCode.key) && StringUtils.isEmpty(loginRequest.getVerifyCode())){
            AppUtility.serviceError(ErrorCode.VERIFY_CODE_CANNOT_BE_EMPTY);
        }

        // 查询用户信息
        UserEntity user = userMapper.selectByEmail(account);
        if(user == null){
            AppUtility.serviceError(ErrorCode.NOT_EXISTS_USER);
        }

        // 邮箱密码登录
        if(loginRequest.getLoginType().equals(LoginTypeEnum.EmailAndPwd.key)){
            String loginPassword = loginRequest.getPassword().trim();
            String loginPasswordMd5 = UserUtility.md5Pswd(account, loginPassword);
            if(!StringUtil.isValidString(user.getPassword())){
                AppUtility.serviceError(ErrorCode.LOGIN_PASSWORD_FAILED);
            }
            if(!loginPasswordMd5.equalsIgnoreCase(user.getPassword())){
                AppUtility.serviceError(ErrorCode.LOGIN_PASSWORD_ERROR);
            }
        // 邮箱验证码登录
        } else if(loginRequest.getLoginType().equals(LoginTypeEnum.EmailAndMsgCode.key)){
            if(!"8080".equalsIgnoreCase(loginRequest.getVerifyCode())) {
                //获取缓存中的验证码
                String key = UserUtility.getSmsCodeKey(loginRequest.getAccount(), SmsCodeTypeEnum.LOGIN.key);
                String randomNumOld = null;
                try {
                    randomNumOld = redisService.get(key);
                } catch (Exception e) {
                    log.error("获取验证码异常", e);
                }
                if (StringUtils.isEmpty(randomNumOld)) {
                    AppUtility.serviceError(ErrorCode.INVALIDATE_VERIFY_CODE_PLEASE_RESEND);
                }
                // 查看前端输入的验证码是否和缓存中的一致
                if (!randomNumOld.equals(loginRequest.getVerifyCode())) {
                    AppUtility.serviceError(ErrorCode.VERIFY_CODE_ERROR);
                }
            }
        }

        // 检查用户状态
        if (!Integer.valueOf(StateEnum.Effective.key).equals(user.getState())) {
            AppUtility.serviceError(ErrorCode.INVALID_USER);
        }

        // 查询用户机构
        OrgEntity org = orgMapper.selectById(user.getOrgId());
        if(org == null){
            AppUtility.serviceError(ErrorCode.NOT_EXIST_ORG);
        }
        if (org.getState().intValue() != StateEnum.Effective.key) {
            AppUtility.serviceError(ErrorCode.INVALID_ORG);
        }

        UserOrgVO userOrg = new UserOrgVO();
        BeanUtils.copyProperties(org, userOrg);
        userOrg.setOrgTypeName(AppUtility.getText(languageId, OrgTypeEnum.getOrgTextCodeByKey(userOrg.getOrgType())));

        // 用户登录
        UserEntity updateUser = new UserEntity();
        updateUser.setUserId(user.getUserId());
        updateUser.setLastLoginTime(new Date());
        updateUser.setLastIpAddress(HttpServletRequestUtility.extractRequestIpAddress(request));
        userMapper.updateLoginInfo(updateUser);

        // 记录登录日志
        UserLoginLogEntity userLoginLog = new UserLoginLogEntity();
        userLoginLog.setUserId(user.getUserId());
        userLoginLog.setLoginDateTime(updateUser.getLastLoginTime());
        userLoginLog.setUserToken("");
        userLoginLog.setUserAgent(HttpServletRequestUtility.extractRequestUserAgent(request));
        userLoginLog.setCreator(user.getUserName());
        userLoginLog.setCreateTime(new Date());
        userLoginLogMapper.insert(userLoginLog);

        // 定义返回值
        UserSession userSession = new UserSession();
        userSession.setUserId(user.getUserId());
        userSession.setLoginType(loginRequest.getLoginType());
        userSession.setRoleCode(user.getRoleCode());
        userSession.setUsername(user.getUserName());
        userSession.setMobileAreaCode(user.getMobileAreaCode());
        userSession.setMobile(user.getMobile());
        userSession.setEmail(user.getEmail());
        userSession.setPermissions(new HashSet<>(userPermissionMapper.queryPermissions(org.getOrgType(), user.getRoleCode())));
        userSession.setUserOrg(userOrg);
        userSession.login();

        LoginResponse loginResponse = new LoginResponse();
        loginResponse.setUserInfo(userSession);
        loginResponse.setAuthorization(session.getId());
        loginResponse.setResetPassword(!StringUtil.isValidString(user.getPassword()));

        // 强制登出在其它设备的该账号
        /**
         *  不需要强制登出其他用户
        final Collection<? extends Session> usersSessions = sessionRepository.findByPrincipalName(user.getUserId().toString()).values();
        usersSessions.forEach(usersSession -> sessionRepository.deleteById(usersSession.getId()));
        **/

        return loginResponse;
    }

    public void logout(HttpServletRequest request, HttpServletResponse response){
        UserSession userSession = UserSession.get();
        // 记录登录日志
        if(userSession != null && userSession.getUserLoginLogId() != null){
            UserLoginLogEntity userLoginLog = new UserLoginLogEntity();
            userLoginLog.setUserLoginLogId(userSession.getUserLoginLogId());
            userLoginLog.setLogoutDateTime(new Date());
            userLoginLog.setLogoutReason("用户手动登出");
            userLoginLogMapper.updateById(userLoginLog);
        }
        // 设置session失效
        HttpSession httpSession = request.getSession(false);
        if(httpSession != null){
            httpSession.invalidate();
        }

    }

    }
