package com.fangcang.grfp.core.logging;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 请求和响应日志
 */
@Configuration(proxyBeanMethods = false)
public class RequestResponseBodyLoggingAutoConfiguration {

    /**
     * RequestBody 的请求日志
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(value = "web.logging.body.enabled", matchIfMissing = true)
    public RequestResponseBodyLoggingAdvice requestBodyLoggingAdvice() {
        return new RequestResponseBodyLoggingAdvice();
    }

}
