package com.fangcang.grfp.core.vo.request.project;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 */

@ApiModel(description = "查询项目酒店白名单详情请求")
@Data
public class UpdateProjectIntentHotelRequest {

    /**
     * 项目id
     */
    @NotNull(message = "projectId NotNull")
    @ApiModelProperty(value = "项目id", required = true)
    private Long projectId;

    /**
     * 酒店ID
     */
    @NotNull(message = "hotelIds NotNull")
    @ApiModelProperty(value = "酒店hotelIds", required = true)
    private List<Long> hotelIds;

    /**
     * 酒店销售联系人姓名
     */
    private String hotelSalesContactName;

    /**
     * 酒店销售联系人电话
     */
    private String hotelSalesContactMobile;

    /**
     * 酒店销售联系人邮箱
     */
    private String hotelSalesContactEmail;

    /**
     * 企业(分销商)跟进人id(招投标项目时指派的)
     */
    private Long distributorContactUid;

    /**
     * 企业(分销商)跟进人姓名(招投标项目时指派的)
     */
    private String distributorContactName;

    /**
     * 平台跟进人跟进人
     */
    private String platformContactName;
    /**
     * 平台跟进人跟进人
     */
    private String  platformContactUid;

    /**
     * 近一年采购间夜数
     */
    private Long lastYearRoomNight;

    /**
     * 采购均价
     */
    private BigDecimal tenderAvgPrice;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作类型
     * 1：修改酒店销售信息
     * 2：近1年我的采购
     * 3：指派跟进人
     * 4:修改酒店邀请状态
     * 5:修改酒店邮件发送状态
     * 6:修改企业签约主体
     * 7:修改公付服务主体
     * 8:修改标书状态 todo
     */
    @ApiModelProperty(value = "操作类型\n" +
            "      1：修改酒店销售信息\n" +
            "      2：近1年我的采购\n" +
            "      3：指派企业跟进人\n" +
            "      4:修改酒店邀请状态\n" +
            "      5:修改酒店邮件发送状态\n" +
            "      9:指派平台跟进人\n" +
            "      ", required = true)
    @NotNull(message = "operateType NotNull")
    private Integer operateType;

    //邀约状态(0：未邀请，1：已邀请)
    private Integer inviteStatus;

    //邀约邮件发送状态(0：未发送邀约邮件，1：已发送邀约邮件)
    private Integer sendMailStatus;

    //公付签约主体ID
    private Long coPayerSubjectId;

    //企业签约主体id
    private Long distributorSubjectId;

    //标书状态(2：议价中，3：已中标，4：已否决)
    private Integer bidState;

    //企业开户银行
    private String distributorBank;

    //企业开户名
    private String distributorAccountName;

    //企业银行账户
    private String distributorAccountNumber;

    //酒店签约主体id
    private Long hotelSubjectId;

    //酒店签约机构id
    private Long hotelOrgId;

    //酒店开户银行
    private String hotelBank;

    //酒店开户名
    private String hotelAccountName;

    //酒店银行账户
    private String hotelAccountNumber;

    //酒店银行账号类型
    private Integer hotelBankAccountType;

    //公付签约主体开户银行(企业添加意向酒店或酒店提交报价的时候取公付机构默认签约主体账户信息，修改报价时不改)
    private String coPayerBank;

    //提供公付服务签约主体机构id(当项目需要第三方公付时，默认取提供公付服务机构ID)
    private Long coPayerOrgId;

    //公付签约主体开户名(企业添加意向酒店或酒店提交报价的时候取公付机构默认签约主体账户信息，修改报价时不改)
    private String coPayerAccountName;

    //公付签约主体银行账户(企业添加意向酒店或酒店提交报价的时候取公付机构默认签约主体账户信息，修改报价时不改)
    private String coPayerAccountNumber;

    // 备注，继续议价内容
    private String remark;

    // 拒绝议价备注
    private String rejectNegotiationRemark;

    // 修改人
    private String modifier;

    // 导入签约状态备注
    private String uploadRemark;


}
