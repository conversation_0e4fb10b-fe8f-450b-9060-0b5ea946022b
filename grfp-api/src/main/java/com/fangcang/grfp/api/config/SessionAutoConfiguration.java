package com.fangcang.grfp.api.config;

import com.fangcang.grfp.api.interceptor.PreAuthorizeInterceptor;
import com.fangcang.grfp.api.interceptor.UserAuthInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.session.web.http.HeaderHttpSessionIdResolver;
import org.springframework.session.web.http.HttpSessionIdResolver;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 分布式会话配置
 *
 */
@Configuration
@ConditionalOnProperty(name = "spring.session.store-type", havingValue = "redis")
public class SessionAutoConfiguration implements WebMvcConfigurer {


    @Value("${web.session.header.name:authorization}")
    private String headerName;

    /**
     * session id 默认存放在 cookie, 改为放在 http authorization header
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(value = "web.session.header.enabled", matchIfMissing = true)
    public HttpSessionIdResolver httpSessionIdResolver() {
        return new HeaderHttpSessionIdResolver(headerName);
    }

    /**
     * 默认 jdk 序列化, 改为 jackson
     */
    @Bean
    public RedisSerializer<Object> springSessionDefaultRedisSerializer() {
        return new GenericJackson2JsonRedisSerializer();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(userAuthInterceptor()).addPathPatterns("/Api/**");
        registry.addInterceptor(preAuthorizeInterceptor()).addPathPatterns("/Api/**");
    }

    @Bean
    public HandlerInterceptor userAuthInterceptor() {
        return new UserAuthInterceptor();
    }

    @Bean
    public HandlerInterceptor preAuthorizeInterceptor() {
        return new PreAuthorizeInterceptor();
    }


}
