package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
  地图酒店报价信息
 */
@ApiModel("保持原价请求")
@Getter
@Setter
public class RejectNegotiationRequest extends BaseVO {

    @ApiModelProperty("项目意向酒店ID")
    private Integer projectIntentHotelId;
    @ApiModelProperty("项目ID")
    private Integer projectId;
    @ApiModelProperty("酒店ID")
    private Long hotelId;

    /**拒绝议价备注*/
    @ApiModelProperty("拒绝议价备注")
    private String rejectNegotiationRemark;
}
