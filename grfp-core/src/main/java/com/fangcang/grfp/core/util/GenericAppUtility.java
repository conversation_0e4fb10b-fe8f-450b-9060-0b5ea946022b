package com.fangcang.grfp.core.util;


import com.fangcang.grfp.core.base.AppLogicException;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.cached.*;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.entity.HotelEntity;
import com.fangcang.grfp.core.entity.ProjectIntentHotelEntity;
import com.fangcang.grfp.core.enums.HotelWhitTypeEnum;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.vo.HotelGroupUserRelatedInfoVO;
import com.fangcang.grfp.core.vo.HotelOrgRelatedInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.List;

public class GenericAppUtility {

	public static Logger logger = LoggerFactory.getLogger(GenericAppUtility.class);


	// ---------------------------------------------------------------------------------------------------- Private Static Member

	public static final String HTTP_HEADERS_LANGUAGE = "language";
	
	public static final String HTTP_HEADERS_AUTHORIZATION = "Authorization";

	/**
	 * 目标币种请求头 name
	 */
	public static final String TARGET_CURRENCY_HEADER = "X-Target-Currency";
	
	private final static String ALPHA_NUMERIC = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
	
	public static CachedTextResourceService cachedTextResourceService;
	
	public static CachedSysConfigService cachedSysConfigService;

	private static CachedHotelBrandService cachedHotelBrandService;
	private static CachedHotelGroupService cachedHotelGroupService;
	private static CachedHotelService cachedHotelService;
	private static CachedCityService cachedCityService;
	private static CachedProjectService cachedProjectService;



	// ---------------------------------------------------------------------------------------------------- Public Static Method
	
	public static void setCachedTextResourceService(CachedTextResourceService cachedTextResourceService) {
		GenericAppUtility.cachedTextResourceService = cachedTextResourceService;
	}
	
	public static void setCachedSysConfigService(CachedSysConfigService cachedSysConfigService) {
		GenericAppUtility.cachedSysConfigService = cachedSysConfigService;
	}
	public static void setCachedHotelBrandService(CachedHotelBrandService cachedHotelBrandService) {
		GenericAppUtility.cachedHotelBrandService = cachedHotelBrandService;
	}
	public static void setCachedHotelGroupService(CachedHotelGroupService cachedHotelGroupService) {
		GenericAppUtility.cachedHotelGroupService = cachedHotelGroupService;
	}
	public static void setCachedHotelService(CachedHotelService cachedHotelService) {
		GenericAppUtility.cachedHotelService = cachedHotelService;
	}
	public static void setCachedCityService(CachedCityService cachedCityService) {
		GenericAppUtility.cachedCityService = cachedCityService;
	}
	public static void setCachedProjectService(CachedProjectService cachedProjectService) {
		GenericAppUtility.cachedProjectService = cachedProjectService;
	}

	// ----------------------------------------------------------------------------------------------------
	
	public static int getRequestHeaderLanguage(HttpServletRequest request) {
		int language = IntegerUtility.parseNullOrEmptyString(request.getHeader(HTTP_HEADERS_LANGUAGE));
		if(language < 0) {
			language = 1;
		}
		return language;
	}
	
    public static String getRequestHeaderToken(HttpServletRequest request) {
        return StringUtility.parseNullAndNotEmpty(request.getHeader(HTTP_HEADERS_AUTHORIZATION));
    }
    
    public static void serviceError(String errorMessage) throws AppLogicException {
    	HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
        throw new AppLogicException(Result.SYSTEM_ERROR_CODE, cachedTextResourceService.getMsgValue(getRequestHeaderLanguage(httpServletRequest), errorMessage));
    }

	public static void serviceError(String errorMessage, Object... params) throws AppLogicException {
		HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
		throw new AppLogicException(Result.SYSTEM_ERROR_CODE, MessageFormat.format(cachedTextResourceService.getMsgValue(getRequestHeaderLanguage(httpServletRequest), errorMessage), params));
	}
    
    public static void serviceError(String errorCode, String errorMessage) throws AppLogicException {
        throw new AppLogicException(errorCode, errorMessage);
    }
    
	public static void serviceError(int languageId, String errorMessage) throws AppLogicException {
		throw new AppLogicException(Result.SYSTEM_ERROR_CODE, cachedTextResourceService.getMsgValue(languageId, errorMessage));
	}
	
	public static void serviceError(HttpServletRequest request, String errorMessage) throws AppLogicException {
		throw new AppLogicException(Result.SYSTEM_ERROR_CODE, cachedTextResourceService.getMsgValue(getRequestHeaderLanguage(request), errorMessage));
	}
	
	public static void serviceError(HttpServletRequest request, String errorCode, String errorMessage) throws AppLogicException {
		throw new AppLogicException(errorCode, cachedTextResourceService.getMsgValue(getRequestHeaderLanguage(request), errorMessage));
	}
	
	// ----------------------------------------------------------------------------------------------------
	
	public static String getSysInfo(String sysConfigCode) {
		return cachedSysConfigService.getValue(sysConfigCode);
	}
	
	public static String getText(int languageId, String textResourceCode) {
		if(!StringUtil.isValidString(textResourceCode)){
			return "";
		}
		return cachedTextResourceService.getWebValue(languageId, textResourceCode);
	}

	// -----------------------------------------------------------------------------------------------------

	/**
	 * 获取城市名称
	 */
	public static String getCityName(int languageId, String cityCode){
		String name = cachedCityService.getNameMap(languageId).get(cityCode);
		if(!StringUtil.isValidString(name)){
			name = cachedCityService.getNameMap(LanguageEnum.EN_US.key).get(cityCode);
		}
		if(StringUtil.isEmpty(name)){
			name = String.valueOf(cityCode);
		}
		return name;
	}
	/**
	 * 获取酒店品牌名称
	 */
	public static String getHotelBrandName(int languageId, Long hotelBrandId){
		String name = cachedHotelBrandService.getNameMap(languageId).get(hotelBrandId);
		if(!StringUtil.isValidString(name)){
			name = cachedHotelBrandService.getNameMap(LanguageEnum.EN_US.key).get(hotelBrandId);
		}
		if(StringUtil.isEmpty(name)){
			name = String.valueOf(hotelBrandId);
		}
		return name;
	}

	/**
	 * 获取酒店集团名称
	 */
	public static String getHotelGroupName(int languageId, Long hotelGroupId){
		String name = cachedHotelGroupService.getNameMap(languageId).get(hotelGroupId);
		if(!StringUtil.isValidString(name)){
			name = cachedHotelGroupService.getNameMap(LanguageEnum.EN_US.key).get(hotelGroupId);
		}
		if(StringUtil.isEmpty(name)){
			name = String.valueOf(hotelGroupId);
		}
		return name;
	}

	/**
	 * 获取酒店名称
	 */
	public static String getHotelName(int languageId, Long hotelId) {
		if (hotelId == null) {
			return null;
		}
		String name = cachedHotelService.getNameMap(languageId).get(hotelId);
		if (!StringUtil.isValidString(name)) {
			name = cachedHotelService.getNameMap(LanguageEnum.EN_US.key).get(hotelId);
		}
		if (!StringUtil.isValidString(name)) {
			HotelEntity hotel = cachedHotelService.getById(hotelId);
			if (hotel != null) {
				return HotelUtility.getHotelName(languageId, hotel);
			}
		}
		return name;
	}

	/**
	 * 获取酒店集团用户相关数据
	 */
	public static HotelGroupUserRelatedInfoVO getHotelGroupUserRelatedInfoVO(UserSession userSession){
		return cachedHotelGroupService.getHotelGroupUserRelatedInfoVO(userSession.getUserOrg().getOrgId(), UserUtility.getEmployUserId(userSession));
	}

	/**
	 * 获取酒店用户相关数据
	 */
	public static HotelOrgRelatedInfoVO getHotelOrgRelatedInfoVO(UserSession userSession){
		return cachedHotelService.queryOrgRelatedInfoVO(userSession.getUserOrg().getOrgId());
	}

	/**
	 * Lanyon 导入报价不需要校验
	 * @param projectIntentHotel
	 * @return
	 */
	public static boolean isNeedValidateBid(ProjectIntentHotelEntity projectIntentHotel){
		if(projectIntentHotel.getIsUpload() != null && projectIntentHotel.getIsUpload() == RfpConstant.constant_1){
			return false;
		}
		List<Long> whiteHotelIdList = cachedProjectService.queryProjectWhiteHotelIdList(projectIntentHotel.getProjectId(), HotelWhitTypeEnum.BID_NO_VALIDATE.key);
		return !whiteHotelIdList.contains(projectIntentHotel.getHotelId());

	}

	// -----------------------------------------------------------------------------------------
	
	public static String validatePassword(String password) {
		String result = "";
		if (password.length() < 8) {
			return "PASSWORD_TOO_SHORT";
		} else if (password.length() > 12) {
			return "PASSWORD_TOO_LONG";
		} else if (password.matches("[0-9]+")) {
	    	return "PASSWORD_NO_CAPITAL_ALPHABET";
		} else if (password.matches("[0-9a-z]+")) {
			return "PASSWORD_NO_LARGE_CAPITAL_ALPHABET";
		} else if (password.matches("[0-9A-Z]+")) {
			return "PASSWORD_NO_SMALL_CAPITAL_ALPHABET";
		} else if (password.matches("[a-zA-Z]+")) {
			return "PASSWORD_NO_DIGIT";
		} else if (password.matches("^[0-9A-Za-z\\!\\@\\#\\$\\%\\^\\&\\*\\(\\)\\_\\+\\.\\,\\;\\:]{8,}$") == false) {
			return "PASSWORD_HAS_SPECIAL_CHARACTER";
		} else {
			return result;
		}
	}

	public static Long parseLong(String longStr){
		Long result = null;
		try {
			result = Long.parseLong(longStr);
		} catch (Exception ex){
			logger.error(ExceptionUtility.getDetailedExceptionString(ex));
		}
		return result;
	}

	public static String getName(Integer language, Long id, String nameEnUs, String nameZhCn) {
		String result = null;
		if(language.equals(LanguageEnum.EN_US.key)){
			result = nameEnUs;
		}
		if(language.equals(LanguageEnum.ZH_CN.key)){
			result = nameZhCn;
		}

		// 如果文本为空 访问对应id
		if(!StringUtil.isValidString(result) && StringUtil.isValidString(nameEnUs)){
			result = nameEnUs;
		}
		if(!StringUtil.isValidString(result) && StringUtil.isValidString(nameZhCn)){
			result = nameZhCn;
		}
		return result;
	}

	public static String getName(Integer language, String code, String nameEnUs, String nameZhCn) {
		String result = null;
		if(language.equals(LanguageEnum.EN_US.key)){
			result = nameEnUs;
		}
		if(language.equals(LanguageEnum.ZH_CN.key)){
			result = nameZhCn;
		}

		// 如果文本为空 访问对应id
		if(!StringUtil.isValidString(result) && StringUtil.isValidString(nameEnUs)){
			result = nameEnUs;
		}
		if(!StringUtil.isValidString(result) && StringUtil.isValidString(nameZhCn)){
			result = nameZhCn;
		}
		return result;
	}

	/**
	 * 获取请求头中的默认币种
	 */
	public static String getRequestHeaderCurrency() {
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
		return request.getHeader(TARGET_CURRENCY_HEADER);
	}
}
