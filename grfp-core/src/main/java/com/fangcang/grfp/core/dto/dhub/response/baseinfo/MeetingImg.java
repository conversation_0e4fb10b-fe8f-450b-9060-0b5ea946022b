package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import lombok.Data;

@Data
public class MeetingImg {

    /**
     * 图片id
     */
    private Long imageId;

    /**
     * 图片类型
     */
    private Integer imageType;

    /**
     * 是否为主图（1-是，0-否）
     */
    private Integer isMain;

    /**
     * 酒店id
     */
    private Integer hotelId;

    /**
     * 房型id
     */
    private Integer roomId;

    /**
     * 原图地址
     */
    private String orgImageUrl;

    /**
     * 大尺寸图片地址
     */
    private String bigSizeUrl;

    /**
     * 中尺寸图片地址
     */
    private String midSizeUrl;

    /**
     * 小尺寸图片地址
     */
    private String smallSizeUrl;

    /**
     * 图片 FTP 地址
     */
    private String ftpUrl;

    /**
     * 图片标题
     */
    private String title;

}
