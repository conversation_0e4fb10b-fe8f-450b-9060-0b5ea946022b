package com.fangcang.grfp.core.vo.request.hotel;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 项目邀请酒店报价查询
 */
@ApiModel("项目邀请酒店报价查询")
@Getter
@Setter
public class ProjectIntentHotelRequest extends PageQuery {

    // 项目意向酒店ID
    @ApiModelProperty("项目意向酒店ID")
    private Integer projectIntentHotelId;

    // 项目ID
    @ApiModelProperty("项目ID")
    private Integer projectId;

    //招标项目
    @ApiModelProperty("招标项目")
    private String projectName;

    // 酒店ID
    @ApiModelProperty("酒店ID")
    private Long hotelId;


    // 酒店ID
    @ApiModelProperty(hidden = true)
    private List<Long> hotelIdList;

    // 机构名称
    @ApiModelProperty("机构名称")
    private String orgName;

    /**
     * 角色编码类型 EMPLOYEE-员工 ADMIN-管理员
     */
    @ApiModelProperty("角色编码类型")
    private String roleCodeType;

    //用户ID
    @ApiModelProperty("用户ID")
    private Integer userId;

    //招标状态
    @ApiModelProperty("招标状态")
    private Integer projectState;

    //投标状态
    @ApiModelProperty("投标状态")
    private Integer bidState;

    // 去年时间
    @ApiModelProperty("去年时间")
    private Date lastYearTime;

    @ApiModelProperty("创建人")
    private String creator;

    //酒店机构id或酒店集团机构id
    @ApiModelProperty("酒店机构id或酒店集团机构id")
    private Integer supplyOrgId;

    // 酒店机构类型
    @ApiModelProperty("酒店机构类型")
    private Integer hotelOrgId;

    // 机构类型
    @ApiModelProperty("机构类型")
    private Integer orgType;

    // 循环过滤酒店ID
    @ApiModelProperty("循环过滤酒店ID")
    private Long loopFilterHotelId;

    // 房档号
    @ApiModelProperty("房档号")
    private String roomLevelNo;

    // 房价组ID
    @ApiModelProperty("房价组ID")
    private Integer hotelPriceGroupId;

    // 酒店集团审核状态 1 待审核 3 审核驳回
    @ApiModelProperty("酒店集团审核状态 1 待审核 3 审核驳回")
    private Integer hotelGroupApproveStatus;


}
