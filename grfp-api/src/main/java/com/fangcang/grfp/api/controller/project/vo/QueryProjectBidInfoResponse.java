package com.fangcang.grfp.api.controller.project.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.entity.ProjectIntentHotelGroupEntity;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelTendStrategyVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("项目报价信息")
@Getter
@Setter
public class QueryProjectBidInfoResponse extends BaseVO {

    @ApiModelProperty("项目ID")
    private Integer projectId;

    /**
     * 意向酒店集团ID
     */
    @ApiModelProperty("意向酒店集团ID")
    private Integer projectIntentHotelGroupId;

    /**
     * 酒店集团机构ID
     */
    @ApiModelProperty("酒店集团机构ID")
    private Integer hotelGroupOrgId;

    /**
     * 酒店集团指派销售跟进人id(招投标项目时指派的)
     */
    @ApiModelProperty("酒店集团指派销售跟进人id(招投标项目时指派的)")
    private Integer hotelGroupContactUid;

    /**
     * 酒店集团指派销售跟进人姓名(招投标项目时指派的)
     */
    @ApiModelProperty("酒店集团指派销售跟进人姓名(招投标项目时指派的)")
    private Integer hotelGroupContactName;

    /**
     * 酒店集团销售联系人姓名
     */
    @ApiModelProperty("酒店集团销售联系人姓名")
    private String hotelGroupBidContactName;

    /**
     * 酒店集团销售联系人电话
     */
    @ApiModelProperty("酒店集团销售联系人电话")
    private String hotelGroupBidContactMobile;

    /**
     * 酒店集团销售联系人邮箱
     */
    @ApiModelProperty("酒店集团销售联系人邮箱")
    private String hotelGroupBidContactEmail;


    @ApiModelProperty("酒店服务承诺")
    private ProjectHotelTendStrategyVO projectHotelTendStrategy;



}
