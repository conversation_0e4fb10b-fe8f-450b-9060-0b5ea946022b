package com.fangcang.grfp.api.mail;

import com.fangcang.grfp.api.BaseTest;
import com.fangcang.grfp.core.cached.CachedProjectService;
import com.fangcang.grfp.core.manager.BidMapManager;
import com.fangcang.grfp.core.manager.MailManager;
import com.fangcang.grfp.core.vo.AttachmentFileVO;
import com.fangcang.grfp.core.vo.request.MailSendRequest;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamSource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.thymeleaf.TemplateEngine;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.ByteArrayInputStream;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.List;


public class MaiManagerTest extends BaseTest {

    @Autowired
    private MailManager mailManager;
    @Value("${spring.mail.username}")
    private String from;
    @Resource
    private JavaMailSender javaMailSender;
    @Resource
    private TemplateEngine templateEngine;
    @Autowired
    private CachedProjectService cachedProjectService;
    @Resource
    private BidMapManager bidMapManager;

    private static final String[] to = new String[]{"<EMAIL>","<EMAIL>"};
    private static final String[] cc = new String[]{"<EMAIL>"};
    private static final String subject = "这里是邮件的主题";
    private static final String text = "<!DOCTYPE html>\n" +
            "<html lang=\"en\">\n" +
            "<head>\n" +
            "  <meta charset=\"UTF-8\">\n" +
            "  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
            "  <title>报价监控通知</title>\n" +
            " <style> \n" +
            "     .nb-row1{ \n" +
            "           width: 900px; border: 1px solid #ccc; border-bottom: none; font-size: 14px;margin-top: 20px;\n" +
            "      } \n" +
            "   .nb-row1 :last-child{ \n" +
            "       border-right: none !important;\n" +
            "   }\n" +
            "   .nb-row>div:last-child{ \n" +
            "   border-right: none !important; \n" +
            "    } \n" +
            "   .day15item{ \n" +
            "       padding: 10px;border-top: 1px solid #ccc; \n" +
            "       display: flex; \n" +
            "       justify-content:space-between;\n" +
            "       align-items: center;\n" +
            "   }\n" +
            "   .icon{ \n" +
            "   width: 16px;    \n" +
            "   height: 16px;    \n" +
            "   border-radius: 50%; \n" +
            "   color: white;\n" +
            "   text-align: center;\n" +
            "   line-height: 16px; \n" +
            "   font-size: 13px;\n" +
            "   }\n" +
            "</style>\n" +
            "</head>\n" +
            "<body style=\"margin:0; padding:0; background-color:rgb(232, 232, 237); font-family: Arial, sans-serif;\">\n" +
            "  <!-- 外层容器 -->\n" +
            "  <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "    <tr>\n" +
            "      <td align=\"center\" style=\"padding: 10px;\">\n" +
            "        <!-- 主内容区域 -->\n" +
            "        <table width=\"1040\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#FFFFFF\" style=\"border-radius:5px; margin:20px auto;\">\n" +
            "          \n" +
            "          <!-- 顶部标题栏 -->\n" +
            "          <tr>\n" +
            "            <td style=\"padding:0 30px; height:64px; border-radius:5px 5px 0 0; background-color: rgba(13, 82, 156, 1);\">\n" +
            "              <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                <tr>\n" +
            "                  <td style=\"font-weight:900; font-style:normal; font-size:24px; letter-spacing:1px; color:#FFFFFF;\">\n" +
            "                    协议通 | <span style=\"font-size:20px;\">报价监控通知</span>\n" +
            "                  </td>\n" +
            "                  <td align=\"right\">\n" +
            "                    <a href=\"https://www.fangcang.com/RFP/login.html#/login\"\n" +
            "                      style=\"width:140px; text-decoration:none; height:40px; line-height:40px; text-align:center; color:#fff; background-color:rgb(251, 126, 51); border-radius:3px; cursor:pointer; border:0px; display:inline-block;\">\n" +
            "                      登录协议通\n" +
            "                    </a>\n" +
            "                  </td>\n" +
            "                </tr>\n" +
            "              </table>\n" +
            "            </td>\n" +
            "          </tr>         <!-- 通知内容区域 -->\n" +
            "          <tr>\n" +
            "            <td style=\"padding:20px 30px; height:94px; font-size:13px; box-sizing:border-box; background-color: rgba(235, 244, 255, 1)\">\n" +
            "              <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                <tr>\n" +
            "                  <td style=\"color:#444444;\">\n" +
            "                    <div style=\"color:#0D529C;\">尊敬的深圳小胖熊酒店：</div>\n" +
            "                    <div style=\"margin-top:5px;\">\n" +
            "                      您在<span style=\"color:#FF4200;\">测试2025监控</span>中因<span style=\"color:#FF4200;\">近15天协议关房C端有房可售天数超过5天；；</span>违反了项目要求扣减服务分<span style=\"color:#FF4200;\">3</span>分，剩余酒店服务分: <span style=\"color:#FF4200;\">89</span>; 为了不影响后续协议续约与企业合作，请尽快处理！\n" +
            "                    </div>\n" +
            "                  </td>\n" +
            "                </tr>\n" +
            "              </table>\n" +
            "            </td>\n" +
            "          </tr>        <!-- 监控产品区域 -->\n" +
            "          <tr>\n" +
            "            <td style=\"padding:30px 40px; border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
            "              <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                <tr>\n" +
            "                  <td style=\"font-size:18px; padding-bottom:15px;\">监控产品</td>\n" +
            "                </tr>\n" +
            "                <tr>\n" +
            "                  <td>\n" +
            "                    <table width=\"800\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\" bordercolor=\"#ccc\" style=\"font-size:14px; margin-top:20px;\">\n" +
            "                      <tr bgcolor=\"#ebf5f6\" style=\"color:#666;\">\n" +
            "                        <td width=\"25%\" style=\"padding:10px; border-right:1px solid #ccc;\">协议监控房型</td>\n" +
            "                        <td width=\"10%\" style=\"padding:10px; border-right:1px solid #ccc;\">适用日期</td>\n" +
            "                        <td width=\"10%\" style=\"padding:10px; border-right:1px solid #ccc;\">早餐</td>\n" +
            "                        <td width=\"20%\" style=\"padding:10px; border-right:1px solid #ccc;\">LRA承诺情况</td>\n" +
            "                        <td width=\"35%\" style=\"padding:10px;\">签约价格(￥)</td>\n" +
            "                      </tr>\n" +
            "                      <tr style=\"color:#666;\">\n" +
            "                        <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            "                        <td style=\"padding:10px; border-right:1px solid #ccc;\">--</td>\n" +
            "                        <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            "                        <td style=\"padding:10px; border-right:1px solid #ccc;\">承诺LRA</td>\n" +
            "                        <td style=\"padding:10px; color:#FF4200;\">具体每日签约价格，请查看近15天监控报价明细</td>\n" +
            "                      </tr>\n" +
            "                    </table>\n" +
            "                  </td>\n" +
            "                </tr>\n" +
            "              </table>\n" +
            "            </td>\n" +
            "         </tr>        <!-- 近15天监控报价明细 -->\n" +
            "          <tr>\n" +
            "            <td style=\"padding:30px 40px; border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
            "              <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                <tr>\n" +
            "                  <td style=\"font-size:18px; padding-bottom:15px;\">近15天监控报价明细</td>\n" +
            "                </tr>\n" +
            "                <tr>\n" +
            "                  <td style=\"font-size:14px;\">\n" +
            "                    <span style=\"color:red;\">无报价:&nbsp;</span><span>指协议房满房或未查询到协议报价.</span>\n" +
            "                    <span style=\"color:red; margin-left:40px;\">原价:&nbsp;</span><span>指最新查询协议价格与签约协议价格一致.</span>\n" +
            "                    <span style=\"color:red; margin-left:40px;\">不适用:&nbsp;</span><span>协议签约中的不适用日期,不监控.</span>\n" +
            "                  </td>\n" +
            "                </tr>\n" +
            "                <tr>\n" +
            "                  <td style=\"padding:20px 0 5px;\">\n" +
            "                    <table cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                      <tr>\n" +
            "                        <td>\n" +
            "                          <table cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                            <tr>\n" +
            "                              <td>\n" +
            "                                <div style=\"width:16px; height:16px; border-radius:50%; background-color:#417505; color:white; text-align:center; line-height:16px; font-size:13px;\">?</div>\n" +
            "                              </td>\n" +
            "                              <td style=\"font-size:12px; padding-left:6px;\">表示当前日历未违规</td>\n" +
            "                            </tr>\n" +
            "                          </table>\n" +
            "                        </td>\n" +
            "                        <td style=\"padding-left:20px;\">\n" +
            "                          <table cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                            <tr>\n" +
            "                              <td>\n" +
            "                                <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                              </td>\n" +
            "                              <td style=\"font-size:12px; padding-left:6px;\">表示当前日历违规</td>\n" +
            "                            </tr>\n" +
            "                          </table>\n" +
            "                        </td>\n" +
            "                      </tr>\n" +
            "                    </table>\n" +
            "                  </td>\n" +
            "                </tr>\n" +
            "                <tr>\n" +
            "                  <td style=\"padding-top:21px;\">\n" +
            "                    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                      <tr>\n" +
            "                        <!-- 日期格子 -->\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-17</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-18</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-19</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-20</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-21</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-22</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-23</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                      </tr>\n" +
            "                      <tr>\n" +
            "                        <!-- 日期格子 -->\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-24</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-25</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-26</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-27</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-28</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-29</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-06-30</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                      </tr>\n" +
            "                      <tr>\n" +
            "                        <!-- 日期格子 -->\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                    <tr>\n" +
            "                              <td style=\"background:rgb(245, 245, 245); color:#666; padding:10px; border-bottom:1px solid #ccc;\">2025-07-01</td>\n" +
            "                            </tr>\n" +
            "                            <tr>\n" +
            "                              <td style=\"padding:10px; border-top:1px solid #ccc;\">\n" +
            "                                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                                  <tr>\n" +
            "                                    <td style=\"color: red;\">110<br/>无报价</td>\n" +
            "<td align=\"right\">\n" +
            "                                      <div style=\"width:16px; height:16px; border-radius:50%; background-color:#ff6000; color:white; text-align:center; line-height:16px; font-size:13px;\">!</div>\n" +
            "                                    </td>\n" +
            "                                  </tr>\n" +
            "                                </table>\n" +
            "                              </td>\n" +
            "                            </tr>\n" +
            "           </table>\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "                        </td>\n" +
            "                        <td width=\"6.66%\" style=\"border:1px solid #ccc; vertical-align:top;\">\n" +
            "                        </td>\n" +
            "                      </tr>\n" +
            "                    </table>\n" +
            "                  </td>\n" +
            "                </tr>\n" +
            "              </table>\n" +
            "            </td>\n" +
            "          </tr>         \n" +
            "          <!-- 复核记录 -->\n" +
            "          <tr>\n" +
            "            <td style=\"padding:30px 40px; border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
            "              <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                <tr>\n" +
            "                  <td style=\"font-size:18px; padding-bottom:15px;\">复核记录</td>\n" +
            "                </tr>\n" +
            "                <tr>\n" +
            "                  <td>\n" +
            "                    <table width=\"900\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\" bordercolor=\"#ccc\" style=\"font-size:14px; margin-top:20px; border-bottom:none;\">\n" +
            "                      <tr bgcolor=\"#ebf5f6\" style=\"color:#666;\">\n" +
            "                        <td width=\"13%\" style=\"padding:10px; border-right:1px solid #ccc;\">违规日期</td>\n" +
            "                        <td width=\"18%\" style=\"padding:10px; border-right:1px solid #ccc;\">违规原因</td>\n" +
            "                        <td width=\"12%\" style=\"padding:10px; border-right:1px solid #ccc;\">监控房型复核报价情况</td>\n" +
            "                        <td width=\"27%\" style=\"padding:10px; border-right:1px solid #ccc;\">同档房型报价记录</td>\n" +
            "                        <td width=\"10%\" style=\"padding:10px; border-right:1px solid #ccc;\">复核结果</td>\n" +
            "                        <td width=\"20%\" style=\"padding:10px;\">复核时间</td>\n" +
            "                      </tr>\n" +
            "                    </table>\n" +
            "                  </td>\n" +
            "                </tr>\n" +
            "              </table>\n" +
            "            </td>\n" +
            "          </tr>         <!-- 协议房型无报价C端查询日志 -->\n" +
            "          <tr>\n" +
            "            <td style=\"padding:30px 40px; border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
            "              <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n" +
            "                <tr>\n" +
            "                  <td style=\"font-size:18px; padding-bottom:15px;\">协议房型无报价C端查询日志</td>\n" +
            "                </tr>\n" +
            "                <tr>\n" +
            "                  <td>\n" +
            "                    <table width=\"900\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\" bordercolor=\"#ccc\" style=\"font-size:14px; margin-top:20px; border-bottom:none;\">\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/20</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:10:50</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/30</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:10:51</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/07/01</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:10:52</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/22</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:10:52</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/21</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:10:53</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/24</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:10:54</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/23</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:10:55</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/26</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:10:56</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/25</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:10:56</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/17</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:10:57</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/28</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:10:58</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/27</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:10:59</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/19</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:11:00</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/18</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:11:00</td>\n" +
            " </tr>\n" +
            " <tr style=\"color:#666; border-bottom:1px solid #ccc;\">\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">深圳小胖熊酒店</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">监控房型</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\">双早</td>\n" +
            " <td style=\"padding:10px; border-right:1px solid #ccc;\"2025/06/29</td>\n" +
            "  <td style=\"padding:10px; border-right:1px solid #ccc; color:#ff6000;\">100</td>\n" +
            " <td style=\"padding:10px;\">2025/06/17 18:11:01</td>\n" +
            " </tr>\n" +
            "                    </table>\n" +
            "                  </td>\n" +
            "                </tr>\n" +
            "              </table>\n" +
            "            </td>\n" +
            "          </tr> </table>\n" +
            "      </td>\n" +
            "    </tr>\n" +
            "  </table>\n" +
            "</body>\n" +
            "</html>";


    @Test
   // @Disabled("测试时需配置, 并注释此注解")
    void testSendTemplateMail() throws MessagingException, MalformedURLException, URISyntaxException {
        // 构建邮件发送对象
        MailSendRequest request = new MailSendRequest();
        // 收件人,必填
        request.setTo(to);
        // 抄送人,非必填
        request.setCc(cc);
        // 邮件主题,必填
        request.setSubject(subject);
        // 邮件正文,非必填
        request.setContent(text);
        // 用户称呼,非必填
        request.setUsername(null);
        // 邮件附件,非必填

        MimeMessage mimeMailMessage;
        mimeMailMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMailMessage, true);
        mimeMessageHelper.setFrom(from);
        mimeMessageHelper.setTo(request.getTo());
        mimeMessageHelper.setCc(request.getCc());
        mimeMessageHelper.setSubject(request.getSubject());

        List<AttachmentFileVO> attachmentFileVOS = cachedProjectService.queryProjectEmailAttachmentList(113);
        for(AttachmentFileVO attachmentFileVO : attachmentFileVOS) {
            InputStreamSource inputStreamSource = () -> new ByteArrayInputStream(attachmentFileVO.getData());
            mimeMessageHelper.addAttachment(attachmentFileVO.getFileName(), inputStreamSource);
        }

        mimeMessageHelper.setText(text, true);

        javaMailSender.send(mimeMailMessage);
    }

    @Test
    @Disabled("测试时需配置, 并注释此注解")
    void testSendSmsCodeMail() throws MessagingException {
        mailManager.sendSmsCodeMail("<EMAIL>", "123456");
    }

    @Test
    void testSendNotifyMail() throws MessagingException {
        bidMapManager.notifyBidderConsumer(197, "");
    }

    /**
     * # spring mail
     * spring.mail.default-encoding=utf-8
     * spring.mail.host=smtp.office365.com
     * spring.mail.port=587
     * spring.mail.username=<EMAIL>
     * spring.mail.password=cvsndbngxjhghcrg
     * spring.mail.protocol=smtp
     * spring.mail.properties.mail.smtp.auth=true
     * spring.mail.properties.mail.smtp.starttls.enable=true
     * spring.mail.properties.mail.smtp.starttls.required=true
     */
}
