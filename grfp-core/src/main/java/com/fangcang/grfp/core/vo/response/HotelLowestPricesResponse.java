package com.fangcang.grfp.core.vo.response;


import com.fangcang.grfp.core.dto.dhub.response.product.HotelPriceItem;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/25 9:43
 */
public class HotelLowestPricesResponse {

    //参考价
    private BigDecimal referencePrice;

    // 起价
    private BigDecimal lowestPrice;

    /** 酒店id */
    private Long hotelId;

    /** 每日起价信息 */
    private List<HotelPriceItem> priceItems;

    public BigDecimal getReferencePrice() {
        return referencePrice;
    }

    public void setReferencePrice(BigDecimal referencePrice) {
        this.referencePrice = referencePrice;
    }

    public Long getHotelId() {
        return hotelId;
    }

    public BigDecimal getLowestPrice() {
        return lowestPrice;
    }

    public void setLowestPrice(BigDecimal lowestPrice) {
        this.lowestPrice = lowestPrice;
    }

    public void setHotelId(Long hotelId) {
        this.hotelId = hotelId;
    }

    public List<HotelPriceItem> getPriceItems() {
        return priceItems;
    }

    public void setPriceItems(List<HotelPriceItem> priceItems) {
        this.priceItems = priceItems;
    }
}
