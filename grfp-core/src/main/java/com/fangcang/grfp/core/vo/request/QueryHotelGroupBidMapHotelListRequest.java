package com.fangcang.grfp.core.vo.request;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;

@ApiModel("酒店集团地图报价酒店查询列表")
@Getter
@Setter
public class QueryHotelGroupBidMapHotelListRequest extends PageQuery {

    //项目id
    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;

    //酒店id
    @ApiModelProperty("酒店id")
    private Long hotelId;

    //城市code
    @ApiModelProperty("城市code")
    private String cityCode;

    @ApiModelProperty("酒店名称")
    private String hotelName;

    //酒店集团关联品牌
    @ApiModelProperty(hidden = true)
    private Collection<Long> hotelGroupBrandIdList;

    // 酒店集团机构ID
    @ApiModelProperty(hidden = true)
    private Integer orgId;




}
