package com.fangcang.grfp.core.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * json 简单操作的工具类
 * <AUTHOR>
 *
 */
public class JsonUtil {

	private static Gson gson = null;
	static {
		if (gson == null) {
			gson = new Gson();
		}
	}

	private JsonUtil() {
	}

	/**
	 * 
	 * @Title: objectToJson
	 * @Description: 将对象转换成json格式
	 * @param @param ts
	 * @param @return
	 * @return String
	 * @throws
	 */
	public static String objectToJson(Object ts) {
		String jsonStr = null;
		if (gson != null) {
			jsonStr = gson.toJson(ts);
		}
		return jsonStr;
	}

	/**
	 * 
	 * @Title: objectToJsonDateSerializer
	 * @Description: 将对象转换成json格式(并自定义日期格式)
	 * @param @param ts
	 * @param @param dateformat
	 * @param @return
	 * @return String
	 * @throws
	 */
	public static String objectToJsonDateSerializer(Object ts, final String dateformat) {
		String jsonStr = null;
		gson = new GsonBuilder().registerTypeHierarchyAdapter(Date.class, new JsonSerializer<Date>() {
			public JsonElement serialize(Date src, Type typeOfSrc, JsonSerializationContext context) {
				SimpleDateFormat format = new SimpleDateFormat(dateformat);
				return new JsonPrimitive(format.format(src));
			}
		}).setDateFormat(dateformat).create();
		if (gson != null) {
			jsonStr = gson.toJson(ts);
		}
		return jsonStr;
	}

	/**
	 * 
	 * @Title: jsonToList
	 * @Description: 将json格式转换成list对象
	 * @param @param jsonStr
	 * @param @return
	 * @return List<?>
	 * @throws
	 */
	public static List<?> jsonToList(String jsonStr) {
		List<?> objList = null;
		if (gson != null) {
			Type type = new com.google.gson.reflect.TypeToken<List<?>>() {
			}.getType();
			objList = gson.fromJson(jsonStr, type);
		}
		return objList;
	}

	/**
	 * 
	 * @Title: jsonToList
	 * @Description: 将Json转为对应的List
	 * @param @param jsonStr
	 * @param @param type
	 * @param @return
	 * @return List<T>
	 * @throws
	 */
	public static <T> List<T> jsonToList(String jsonStr, Class<T> type) {
		List<T> lst = new ArrayList<T>();

		JsonArray array = new JsonParser().parse(jsonStr).getAsJsonArray();
		for (final JsonElement elem : array) {
			lst.add(new Gson().fromJson(elem, type));
		}

		return lst;
	}

	/**
	 * 
	 * @Title: json2Collection
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param jsonStr
	 * @param @param type
	 * @param @return
	 * @return List<T>
	 * @throws
	 */
	public static <T> List<T> json2Collection(String jsonStr, Type type) {
		return new Gson().fromJson(jsonStr, type);
	}

	/**
	 * 将json格式转换成map对象
	 * 
	 * @param jsonStr
	 * @return
	 */
	public static Map<?, ?> jsonToMap(String jsonStr) {
		Map<?, ?> objMap = null;
		if (gson != null) {
			Type type = new com.google.gson.reflect.TypeToken<Map<?, ?>>() {
			}.getType();
			objMap = gson.fromJson(jsonStr, type);
		}
		return objMap;
	}

	/**
	 * 将json转换成bean对象
	 * 
	 * @param jsonStr
	 * @return
	 */
	public static <T> T jsonToBean(String jsonStr, Class<T> cl) {
		return gson.fromJson(jsonStr, cl);
	}

	/**
	 * 将json转换成bean对象
	 * 
	 * @param jsonStr
	 * @param cl
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T jsonToBeanDateSerializer(String jsonStr, Class<T> cl, final String pattern) {
		Object obj = null;
		gson = new GsonBuilder().registerTypeAdapter(Date.class, new JsonDeserializer<Date>() {
			public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
				SimpleDateFormat format = new SimpleDateFormat(pattern);
				String dateStr = json.getAsString();
				try {
					return format.parse(dateStr);
				} catch (ParseException e) {
					e.printStackTrace();
				}
				return null;
			}
		}).setDateFormat(pattern).create();
		if (gson != null) {
			obj = gson.fromJson(jsonStr, cl);
		}
		return (T) obj;
	}

	/**
	 * 根据
	 * 
	 * @param jsonStr
	 * @param key
	 * @return
	 */
	public static Object getJsonValue(String jsonStr, String key) {
		Object rulsObj = null;
		Map<?, ?> rulsMap = jsonToMap(jsonStr);
		if (rulsMap != null && rulsMap.size() > 0) {
			rulsObj = rulsMap.get(key);
		}
		return rulsObj;
	}

	/**
	 * 将JAVA对象转换成JSON字符串
	 * 
	 * @param obj
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	@SuppressWarnings("rawtypes")
	public static String simpleObjectToJsonStr(Object obj, List<Class> claList) throws IllegalArgumentException, IllegalAccessException {
		if (obj == null) {
			return "null";
		}
		String jsonStr = "{";
		Class<?> cla = obj.getClass();
		Field fields[] = cla.getDeclaredFields();
		for (Field field : fields) {
			field.setAccessible(true);
			if (field.getType() == long.class) {
				jsonStr += "\"" + field.getName() + "\":" + field.getLong(obj) + ",";
			} else if (field.getType() == double.class) {
				jsonStr += "\"" + field.getName() + "\":" + field.getDouble(obj) + ",";
			} else if (field.getType() == float.class) {
				jsonStr += "\"" + field.getName() + "\":" + field.getFloat(obj) + ",";
			} else if (field.getType() == int.class) {
				jsonStr += "\"" + field.getName() + "\":" + field.getInt(obj) + ",";
			} else if (field.getType() == boolean.class) {
				jsonStr += "\"" + field.getName() + "\":" + field.getBoolean(obj) + ",";
			} else if (field.getType() == Integer.class || field.getType() == Boolean.class || field.getType() == Double.class || field.getType() == Float.class || field.getType() == Long.class) {
				jsonStr += "\"" + field.getName() + "\":" + field.get(obj) + ",";
			} else if (field.getType() == String.class) {
				jsonStr += "\"" + field.getName() + "\":\"" + field.get(obj) + "\",";
			} else if (field.getType() == List.class) {
				String value = simpleListToJsonStr((List<?>) field.get(obj), claList);
				jsonStr += "\"" + field.getName() + "\":" + value + ",";
			} else {
				if (claList != null && claList.size() != 0 && claList.contains(field.getType())) {
					String value = simpleObjectToJsonStr(field.get(obj), claList);
					jsonStr += "\"" + field.getName() + "\":" + value + ",";
				} else {
					jsonStr += "\"" + field.getName() + "\":null,";
				}
			}
		}
		jsonStr = jsonStr.substring(0, jsonStr.length() - 1);
		jsonStr += "}";
		return jsonStr;
	}

	/**
	 * 将JAVA的LIST转换成JSON字符串
	 * 
	 * @param list
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	@SuppressWarnings("rawtypes")
	public static String simpleListToJsonStr(List<?> list, List<Class> claList) throws IllegalArgumentException, IllegalAccessException {
		if (list == null || list.size() == 0) {
			return "[]";
		}
		String jsonStr = "[";
		for (Object object : list) {
			jsonStr += simpleObjectToJsonStr(object, claList) + ",";
		}
		jsonStr = jsonStr.substring(0, jsonStr.length() - 1);
		jsonStr += "]";
		return jsonStr;
	}

	/**
	 * 将JAVA的MAP转换成JSON字符串， 只转换第一层数据
	 * 
	 * @param map
	 * @return
	 */
	public static String simpleMapToJsonStr(Map<?, ?> map) {
		if (map == null || map.isEmpty()) {
			return "null";
		}
		String jsonStr = "{";
		Set<?> keySet = map.keySet();
		for (Object key : keySet) {
			jsonStr += "\"" + key + "\":\"" + map.get(key) + "\",";
		}
		jsonStr = jsonStr.substring(0, jsonStr.length() - 1);
		jsonStr += "}";
		return jsonStr;
	}

	/**
	 * 反序列化简单泛型对象（单个泛型参数）
	 * @param json JSON 字符串
	 * @param mainClass 目标容器类（如 BaseApiResponse.class）
	 * @param genericType 泛型参数类型（如 String.class 或 User.class）
	 */
	public static <T> T fromJson(String json, Class<?> mainClass, Class<?> genericType) {
		Type type = TypeToken.getParameterized(mainClass, genericType).getType();
		return gson.fromJson(json, type);
	}

	/**
	 * 反序列化复杂泛型对象（支持任意数量泛型参数）
	 * @param json JSON 字符串
	 * @param mainClass 目标容器类
	 * @param genericTypes 泛型参数类型数组
	 */
	public static <T> T fromJson(String json, Class<?> mainClass, Type... genericTypes) {
		Type type = TypeToken.getParameterized(mainClass, genericTypes).getType();
		return gson.fromJson(json, type);
	}

	/**
	 * 通过 TypeToken 处理多层嵌套泛型
	 * @param json JSON 字符串
	 * @param typeToken 目标类型的 TypeToken
	 */
	public static <T> T fromJson(String json, TypeToken<T> typeToken) {
		return gson.fromJson(json, typeToken.getType());
	}

}