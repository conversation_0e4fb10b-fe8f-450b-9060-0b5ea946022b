package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.PriceApplicableDayEntity;
import com.fangcang.grfp.core.vo.BidApplicableDayVO;

import java.util.List;

/**
 * <p>
 * 价格可用日期 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface PriceApplicableDayMapper extends BaseMapper<PriceApplicableDayEntity> {

    default List<PriceApplicableDayEntity> selectByProjectIntentHotelId(int projectIntentHotelId){
        LambdaQueryWrapper<PriceApplicableDayEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PriceApplicableDayEntity::getProjectIntentHotelId, projectIntentHotelId);
        return selectList(lambdaQueryWrapper);
    }

    List<BidApplicableDayVO> selectVOListByProjectIntentHotelId(int projectIntentHotelId);

    default int deleteByProjectAndHotelId(int projectId, long hotelId){
        LambdaQueryWrapper<PriceApplicableDayEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PriceApplicableDayEntity::getProjectId, projectId);
        lambdaQueryWrapper.eq(PriceApplicableDayEntity::getHotelId, hotelId);
        return delete(lambdaQueryWrapper);
    }

    default int deleteByProjectIntentHotelAndPriceType(int projectIntentHotelId, int priceType){
        LambdaQueryWrapper<PriceApplicableDayEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PriceApplicableDayEntity::getProjectIntentHotelId, projectIntentHotelId);
        lambdaQueryWrapper.eq(PriceApplicableDayEntity::getPriceType, priceType);
        return delete(lambdaQueryWrapper);
    }

    int insertBatch(List<PriceApplicableDayEntity> applicableDayList);

    /**
     * 根据项目 ID 查询
     */
    default List<PriceApplicableDayEntity> selectByProjectId(Integer projectId) {
        LambdaQueryWrapper<PriceApplicableDayEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PriceApplicableDayEntity::getProjectId, projectId);
        return selectList(queryWrapper);
    }
}
