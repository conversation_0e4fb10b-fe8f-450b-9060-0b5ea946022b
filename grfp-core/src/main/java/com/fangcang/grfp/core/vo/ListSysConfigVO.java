package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel("系统配置")
@Getter
@Setter
public class ListSysConfigVO extends BaseVO {

    @ApiModelProperty("系统配置ID")
    private Long sysConfigId;

    @ApiModelProperty("系统配置编号")
    private String sysConfigCode;

    @ApiModelProperty("系统配置值")
    private String sysConfigValue;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("创建时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("修改人")
    private String modifier;

    @ApiModelProperty("修改时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

}
