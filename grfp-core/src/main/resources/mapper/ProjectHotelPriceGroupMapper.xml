<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectHotelPriceGroupMapper">
    <select id="selectInfoByProjectId" resultType="com.fangcang.grfp.core.vo.ProjectHotelPriceGroupVO">
        select t1.hotel_price_group_id,
               t1.project_intent_hotel_id,
               t1.project_id,
               t1.hotel_id,
               t1.hotel_price_level_id,
               t1.applicable_weeks,
               t1.lra,
               t1.is_include_breakfast,
               t1.remark,
               t1.is_locked,
               t1.creator,
               t1.create_time,
               t1.modifier,
               t1.modify_time,
               t2.room_level_no,
               t2.total_room_count,
               t2.big_bed_room_count,
               t2.double_bed_room_count
        from t_project_hotel_price_group t1
                 inner join t_project_hotel_price_level t2 on t1.hotel_price_level_id = t2.hotel_price_level_id
        where t1.project_id = #{projectId}
    </select>

    <select id="queryHotelGroupBrandIds" resultType="java.lang.Long">
        SELECT
            hotel_brand_id
        FROM t_org_related_hotel_brand
        WHERE org_id IN
        <foreach collection="orgIds" item="orgId" close=")" open="(" separator=",">
            #{orgId}
        </foreach>
    </select>

    <insert id="batchInsert" keyProperty="hotel_price_group_id" useGeneratedKeys="true">
        insert into t_project_hotel_price_group(project_intent_hotel_id, project_id, hotel_id, hotel_price_level_id, applicableWeeks, lra, remark, creator)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectIntentHotelId}, #{item.projectId}, #{item.hotelId}, #{item.hotelPriceLevelId}, #{item.applicableWeeks}, #{item.lra}, #{item.remark}, #{item.creator})
        </foreach>
    </insert>
</mapper>
