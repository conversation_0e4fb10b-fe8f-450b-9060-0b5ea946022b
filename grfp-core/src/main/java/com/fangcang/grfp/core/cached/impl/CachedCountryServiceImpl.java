package com.fangcang.grfp.core.cached.impl;

import com.fangcang.grfp.core.cached.CachedCountryService;
import com.fangcang.grfp.core.entity.CountryEntity;
import com.fangcang.grfp.core.mapper.CityMapper;
import com.fangcang.grfp.core.mapper.CountryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CachedCountryServiceImpl implements CachedCountryService {

    @Autowired
    private CountryMapper countryMapper;

    @Override
    @Cacheable(value="cachedCountryService.getByCountryCode", key = "#countryCode ?: 'UNKNOWN'", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public CountryEntity getByCountryCode(String countryCode) {
        return countryMapper.getByCountryCode(countryCode);
    }
}
