package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 酒店六边形坐标
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_hotel_hexagon_lng_lat")
public class HotelHexagonLngLatEntity extends BaseVO {


    /**
     * 酒店ID
     */
    @TableId(value = "hotel_id", type = IdType.ASSIGN_ID)
    private Long hotelId;

    /**
     * 谷歌经度
     */
    @TableField("lng_google")
    private BigDecimal lngGoogle;

    /**
     * 谷歌纬度
     */
    @TableField("lat_google")
    private BigDecimal latGoogle;

    /**
     * 酒店10公里范围六边形坐标
     */
    @TableField("hexagon_lng_lat_10")
    private String hexagonLngLat10;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
