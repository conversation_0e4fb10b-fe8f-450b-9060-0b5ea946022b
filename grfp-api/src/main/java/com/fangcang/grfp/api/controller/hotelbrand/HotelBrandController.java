package com.fangcang.grfp.api.controller.hotelbrand;

import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.hotelbrand.service.HotelBrandService;
import com.fangcang.grfp.api.controller.hotelbrand.vo.HotelBrandRequest;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.vo.HotelBrandVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@Api(tags="酒店品牌")
@RestController
@RequestMapping("/Api/HotelBrand")
public class HotelBrandController extends BaseController {

    @Autowired
    private HotelBrandService hotelBrandService;




}
