package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("查询Lanyon导入数据请求")
@Getter
@Setter
public class QueryLanyonImportDataRequest extends BaseVO {

    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;

    @ApiModelProperty("酒店ID")
    @NotNull
    private Long hotelId;

    @ApiModelProperty("数据类型， 1:Lanyon导入数据，2:Lanyon导入数据（如果存在两条显示有早）")
    @NotNull
    private Integer dataType;
}
