package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("查询酒店列表响应信息")
public class FindHotelListResponse extends BaseVO {

    /**
     * 酒店列表
     */
    @ApiModelProperty("酒店列表")
    private List<FindHotelInfo> list;

    @ApiModelProperty("总数")
    private Integer totalCount;

    @ApiModelProperty("总页数")
    private Integer totalPage;

    @ApiModelProperty("当前页")
    private Integer currentPage;

    @ApiModelProperty("每页数")
    private Integer pageSize;


}
