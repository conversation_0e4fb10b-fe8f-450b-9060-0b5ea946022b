package com.fangcang.grfp.api.controller.project.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.fangcang.grfp.core.base.Response;
import com.fangcang.grfp.core.config.ThreadPoolAutoConfiguration;
import com.fangcang.grfp.core.constant.RedisConstant;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.entity.ProjectEntity;
import com.fangcang.grfp.core.entity.ProjectHistoryRecommendEntity;
import com.fangcang.grfp.core.entity.ProjectIntentHotelEntity;
import com.fangcang.grfp.core.entity.ProjectPoiEntity;
import com.fangcang.grfp.core.entity.ProjectRecommendStatLogEntity;
import com.fangcang.grfp.core.enums.HotelStarGroupEnum;
import com.fangcang.grfp.core.enums.RecommendFrequencyEnum;
import com.fangcang.grfp.core.enums.RecommendFrequencySameLevelEnum;
import com.fangcang.grfp.core.enums.RecommendHotelNameEnum;
import com.fangcang.grfp.core.enums.RecommendLevelEnum;
import com.fangcang.grfp.core.enums.RecommendPoiNearHotelEnum;
import com.fangcang.grfp.core.enums.ResultEnum;
import com.fangcang.grfp.core.enums.ReturnResultEnum;
import com.fangcang.grfp.core.manager.HotelManager;
import com.fangcang.grfp.core.mapper.DisHotelDailyOrderMapper;
import com.fangcang.grfp.core.mapper.HotelMapper;
import com.fangcang.grfp.core.mapper.HotelViolationsMonitorMapper;
import com.fangcang.grfp.core.mapper.OtaHotelDailyMinPriceMapper;
import com.fangcang.grfp.core.mapper.ProjectHistoryRecommendMapper;
import com.fangcang.grfp.core.mapper.ProjectHotelHistoryDataMapper;
import com.fangcang.grfp.core.mapper.ProjectIntentHotelMapper;
import com.fangcang.grfp.core.mapper.ProjectPoiMapper;
import com.fangcang.grfp.core.mapper.ProjectRecommendStatLogMapper;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.ExceptionUtility;
import com.fangcang.grfp.core.util.JsonUtil;
import com.fangcang.grfp.core.util.LocationUtil;
import com.fangcang.grfp.core.util.StopWatch;
import com.fangcang.grfp.core.vo.GeneratePoiStatVO;
import com.fangcang.grfp.core.vo.HotelRoomNightCountDto;
import com.fangcang.grfp.core.vo.HotelSavedAmountDto;
import com.fangcang.grfp.core.vo.QueryRoomNightsDto;
import com.fangcang.grfp.core.vo.RecommendFrequencySameLevelInfo;
import com.fangcang.grfp.core.vo.RecommendNearPoiStatInfo;
import com.fangcang.grfp.core.vo.request.hotel.DisHotelDailyOrderRequest;
import com.fangcang.grfp.core.vo.response.HotelLowestPricesResponse;
import com.fangcang.grfp.core.vo.response.HotelResponse;
import com.fangcang.grfp.core.vo.response.hotel.DisHotelDailyOrderResponse;
import com.fangcang.grfp.core.vo.response.hotel.HotelViolationCountStatDto;
import com.fangcang.grfp.core.vo.response.hotel.OtaHotelMinMaxPriceVO;
import com.fangcang.grfp.core.vo.response.project.QueryHistoryProjectInfoResponse;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 项目酒店历史数据推荐服务实现类
 */
@Service
public class ProjectHotelHistoryDataRecommendService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectHotelHistoryDataRecommendService.class);

    public static final String UPDATE_LOWEST_PRICE = "更新酒店商旅价格";


    @Autowired
    private ProjectHotelHistoryDataMapper projectHotelHistoryDataMapper;
    @Autowired
    private HotelViolationsMonitorMapper hotelViolationsMonitorMapper;
    @Autowired
    private ProjectIntentHotelMapper projectIntentHotelMapper;
    @Autowired
    private OtaHotelDailyMinPriceMapper otaHotelDailyMinPriceMapper;
    @Autowired
    private ProjectHistoryRecommendMapper projectHistoryRecommendMapper;
    @Autowired
    private DisHotelDailyOrderMapper disHotelDailyOrderMapper;
    @Autowired
    private ProjectPoiMapper projectPoiMapper;
    @Autowired
    private ProjectRecommendStatLogMapper projectRecommendStatLogMapper;
    @Autowired
    private HotelMapper hotelMapper;
    @Resource
    private HotelManager hotelManager;
    @Resource
    private RedissonClient redissonClient;

    public static Long recordLogHotelId = 0L;

    private ProjectRecommendStatLogEntity initProjectRecommendStatLog(UserSession userDTO, String statName, String statReferenceNo, Integer projectId) {
        ProjectRecommendStatLogEntity projectRecommendStatLog = new ProjectRecommendStatLogEntity();
        projectRecommendStatLog.setProjectId(projectId);
        projectRecommendStatLog.setStatReferenceNo(statReferenceNo);
        projectRecommendStatLog.setStatName(statName);
        projectRecommendStatLog.setBeginTime(new Date());
        projectRecommendStatLog.setIsFinished(RfpConstant.constant_0);
        projectRecommendStatLog.setCreator(userDTO.getUsername());
        return projectRecommendStatLog;
    }

    private void recordFinishedLog(ProjectRecommendStatLogEntity projectRecommendStatLog, Response response){
        // 记录完成统计日志
        projectRecommendStatLog.setIsFinished(RfpConstant.constant_1);
        projectRecommendStatLog.setEndTime(new Date());
        projectRecommendStatLog.setResult(response.getResult());
        projectRecommendStatLog.setResultMsg(response.getMsg());
        projectRecommendStatLogMapper.finishRecord(projectRecommendStatLog);
    }

    @Async(ThreadPoolAutoConfiguration.EXECUTOR_NAME)
    public Future<Response> generateRecommendLevel(UserSession userDTO, String statReferenceNo, Integer projectId, CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("生成项目酒店推荐等级：" +projectId);

        // 记录统计日志
        String statName = "生成项目酒店推荐等级";
        ProjectRecommendStatLogEntity projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, projectId);
        projectRecommendStatLogMapper.insert(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            // 查询有推荐的酒店历史数据
            List<QueryHistoryProjectInfoResponse> recommendHotelHistoryDataList = projectHotelHistoryDataMapper.queryAllRecommendHistoryProjectHotelList(projectId, null);
            // 初始设置为推荐等级为空
            recommendHotelHistoryDataList.forEach(item -> {
                item.setRecommendLevel(null);
            });
            List<QueryHistoryProjectInfoResponse> hotelHistoryDataList = projectHotelHistoryDataMapper.queryHistoryProjectHotelList(projectId,  null,null, null, null);
            Map<String, List<QueryHistoryProjectInfoResponse>> cityHotelHistoryDataMap = hotelHistoryDataList.stream().collect(Collectors.groupingBy(QueryHistoryProjectInfoResponse::getCityCode));
            Map<Long, Long> theSameLevelHotelAreaRoomCountMap = new HashMap<>();
            for (QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : recommendHotelHistoryDataList) {
                if (queryHistoryProjectInfoResponse.getRoomNightCount() == null) {
                    continue;
                }
                // 过滤间夜数或者总价为0的情况
                if (queryHistoryProjectInfoResponse.getRoomNightCount() == 0 || queryHistoryProjectInfoResponse.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                // 签约要求间夜数
                int requireRoomNight = 10; // 默认为10的间夜数计算
                if(StringUtils.isNotEmpty(queryHistoryProjectInfoResponse.getRequiredRoomNight())){
                    requireRoomNight = Integer.parseInt(queryHistoryProjectInfoResponse.getRequiredRoomNight());
                }
                // 均价
                BigDecimal baseAvgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()), 2, RoundingMode.HALF_UP);
                BigDecimal hotelRecommendPrice = queryHistoryProjectInfoResponse.getReferencePrice();
                if(hotelRecommendPrice == null){
                    hotelRecommendPrice = queryHistoryProjectInfoResponse.getAdjustLowestPrice();
                }

                // 计算月成交间夜数
                int roomNightCountPerMonth = BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()).divide(new BigDecimal("12"), 0, RoundingMode.HALF_UP).intValue();

                // 记录日志
                if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), recordLogHotelId)){
                    logger.info("hotelID: {}, roomNightCountPerMonth {} hotelRecommendPrice: {}, {}", recordLogHotelId, roomNightCountPerMonth, hotelRecommendPrice, requireRoomNight);
                }

                // SSS级别 酒店推荐列表酒店 历史成交月均成交高于500
                if (roomNightCountPerMonth >= 500) {
                    queryHistoryProjectInfoResponse.setRecommendLevel(RecommendLevelEnum.SSS.key);
                    //SS：酒店推荐列表酒店 历史成交月均高于200，低于499
                } else if (roomNightCountPerMonth >= 200) {
                    queryHistoryProjectInfoResponse.setRecommendLevel(RecommendLevelEnum.SS.key);
                    //S：酒店推荐列表酒店 历史成交月均高于100，低于199
                } else if (roomNightCountPerMonth >= 100) {
                    queryHistoryProjectInfoResponse.setRecommendLevel(RecommendLevelEnum.S.key);
                } else if (roomNightCountPerMonth >= requireRoomNight && // 历史间夜和均价大于签约要求 （月均间夜数，签约价格）
                        hotelRecommendPrice != null && baseAvgPrice.compareTo(hotelRecommendPrice) >= 0
                ) {
                    //  A：酒店推荐列表酒店 历史成交月均高于酒店签约要求，低于99
                    queryHistoryProjectInfoResponse.setRecommendLevel(RecommendLevelEnum.A.key);
                } else {
                    // B：酒店推荐列表酒店 历史成交没有高于酒店签约要求，但同城3公里范围内同价位预订酒店间夜超过酒店签约要求
                    // 过滤同城
                    if(hotelRecommendPrice != null && hotelRecommendPrice.compareTo(BigDecimal.ZERO) > 0) {
                        List<QueryHistoryProjectInfoResponse> cityQueryHistoryProjectInfoResponseList = cityHotelHistoryDataMap.get(queryHistoryProjectInfoResponse.getCityCode());
                        long totalRoomNightCount = 0;
                        for (QueryHistoryProjectInfoResponse distanceHistoryProjectInfoResponse : cityQueryHistoryProjectInfoResponseList) {
                            // 过滤3公里内同价位酒店
                            if (distanceHistoryProjectInfoResponse.getRoomNightCount() == 0 || distanceHistoryProjectInfoResponse.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
                                continue;
                            }
                            // 计算是否在3公里范围
                            BigDecimal distance = LocationUtil.getKmDistance(distanceHistoryProjectInfoResponse.getLatBaidu(), distanceHistoryProjectInfoResponse.getLngBaidu(),
                                queryHistoryProjectInfoResponse.getLatBaidu(), queryHistoryProjectInfoResponse.getLngBaidu());
                            if (NumberUtil.isGreater(distance, new BigDecimal("3"))) {
                                continue;
                            }
                            // 计算是否同价位置 比如A酒店推荐合作报价为380，企业在这个酒店之前没有预订过，但是这个酒店周边3公里范围内，同价位区间有超过酒店签约要求的预订间数，这时为B
                            // 比如一个酒店没有录入酒店签约要求，但是独立起价我们查到近30天最低价为328，那么签约混淆价格为348，默认签约要求为10间夜/月，这个标准进行酒店推荐计算
                            boolean isTheSameLevelPrice = isTheSameLevelPrice(hotelRecommendPrice, distanceHistoryProjectInfoResponse);
                            if (isTheSameLevelPrice) {
                                totalRoomNightCount = totalRoomNightCount + distanceHistoryProjectInfoResponse.getRoomNightCount();
                                // 周边是否满足签约要求 , 满足设置B
                                if(distanceHistoryProjectInfoResponse.getRoomNightCount() >= requireRoomNight * 12){
                                    queryHistoryProjectInfoResponse.setRecommendLevel(RecommendLevelEnum.B.key);
                                }
                            }
                        }
                        theSameLevelHotelAreaRoomCountMap.put(queryHistoryProjectInfoResponse.getHotelId(), totalRoomNightCount);
                        queryHistoryProjectInfoResponse.setPriceLevelRoomNight(totalRoomNightCount);
                    }
                }

                // C：酒店推荐列表酒店 历史成交没有高于酒店签约要求，3公里历史成交同价位酒店没有高于酒店签约要求，城市同价位酒店满足酒店签约要求，推荐值为C。
                if(queryHistoryProjectInfoResponse.getRecommendLevel() == null && hotelRecommendPrice != null && hotelRecommendPrice.compareTo(BigDecimal.ZERO) > 0) {
                    List<QueryHistoryProjectInfoResponse> cityQueryHistoryProjectInfoResponseList = cityHotelHistoryDataMap.get(queryHistoryProjectInfoResponse.getCityCode());
                    for (QueryHistoryProjectInfoResponse cityHistoryProjectInfoResponse : cityQueryHistoryProjectInfoResponseList) {
                        // 计算月成交间夜数
                        int compareRoomNightCountPerMonth = BigDecimal.valueOf(cityHistoryProjectInfoResponse.getRoomNightCount()).divide(new BigDecimal("12"), 0, RoundingMode.HALF_UP).intValue();
                        if (compareRoomNightCountPerMonth < requireRoomNight) {
                            continue;
                        }
                        // 计算是否同价位置
                        int avgPrice = cityHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(cityHistoryProjectInfoResponse.getRoomNightCount()), 2, RoundingMode.HALF_UP).intValue();
                        // 记录日志
                        if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), recordLogHotelId)){
                            logger.info("avgPrice {} cityHistoryProjectInfoResponse hotelId: {}", avgPrice, cityHistoryProjectInfoResponse.getHotelId());
                        }
                        if (hotelRecommendPrice.subtract(new BigDecimal(avgPrice)).abs().intValue() <= 50) {
                            queryHistoryProjectInfoResponse.setRecommendLevel(RecommendLevelEnum.C.key);
                            break;
                        }
                    }
                }

                // 计算同价位周边3公里间夜数 （C不需要统计， B已经计算）
                if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), recordLogHotelId)){
                    logger.info("同价位周边酒店数量比较酒店价格 {}, {}, hotelRecommendPrice {}", recordLogHotelId, queryHistoryProjectInfoResponse.getAdjustLowestPrice(), hotelRecommendPrice);
                }
                if(queryHistoryProjectInfoResponse.getRecommendLevel() != null && hotelRecommendPrice != null && hotelRecommendPrice.compareTo(BigDecimal.ZERO) > 0 &&
                        queryHistoryProjectInfoResponse.getRecommendLevel() < RecommendLevelEnum.B.key) {
                    List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoResponseList = cityHotelHistoryDataMap.get(queryHistoryProjectInfoResponse.getCityCode());
                    long totalTheSameLevelPriceRoomNightCount = 0;
                    for (QueryHistoryProjectInfoResponse distanceHistoryProjectInfoResponse : queryHistoryProjectInfoResponseList) {
                        BigDecimal distance = LocationUtil.getKmDistance(distanceHistoryProjectInfoResponse.getLatBaidu(), distanceHistoryProjectInfoResponse.getLngBaidu(),
                            queryHistoryProjectInfoResponse.getLatBaidu(), queryHistoryProjectInfoResponse.getLngBaidu());
                        if (NumberUtil.isGreater(distance, new BigDecimal("3"))) {
                            continue;
                        }
                        // 计算是否同价位置
                        if (distanceHistoryProjectInfoResponse.getRoomNightCount() == 0) {
                            continue;
                        }
                        // 均价
                        boolean isTheSameLevelPrice = isTheSameLevelPrice(hotelRecommendPrice, distanceHistoryProjectInfoResponse);
                        if (isTheSameLevelPrice) {
                            totalTheSameLevelPriceRoomNightCount = totalTheSameLevelPriceRoomNightCount + distanceHistoryProjectInfoResponse.getRoomNightCount();
                        }
                    }
                    if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), recordLogHotelId)){
                        logger.info("同价位周边酒店数量 {}, {}", recordLogHotelId, totalTheSameLevelPriceRoomNightCount);
                    }
                    theSameLevelHotelAreaRoomCountMap.put(queryHistoryProjectInfoResponse.getHotelId(), totalTheSameLevelPriceRoomNightCount);
                    queryHistoryProjectInfoResponse.setPriceLevelRoomNight(totalTheSameLevelPriceRoomNightCount);
                }
            }

            // 更新历史数据推荐等级
            recommendHotelHistoryDataList = recommendHotelHistoryDataList.stream().filter(o -> o.getRecommendLevel() != null).collect(Collectors.toList());
            // 清空旧推荐等级数据
            projectHotelHistoryDataMapper.clearHistoryProjectHotelRecommendLevel(projectId);
            for (QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : recommendHotelHistoryDataList) {
                // 更新推荐等级和同价位3公里数据
                projectHotelHistoryDataMapper.updateRecommendLevelAndPriceLevelRoomNight(projectId, queryHistoryProjectInfoResponse.getHotelId(),
                        queryHistoryProjectInfoResponse.getRecommendLevel(), queryHistoryProjectInfoResponse.getPriceLevelRoomNight());
            }

            // 优质商旅酒店推荐
            // 记录统计日志
            long beginTime = System.currentTimeMillis();
            statName = "优质商旅酒店推荐";
            ProjectRecommendStatLogEntity projectHighQualityRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, projectId);
            projectRecommendStatLogMapper.insert(projectHighQualityRecommendStatLog);
            List<ProjectHistoryRecommendEntity> highQualityRecommendList = new ArrayList<>();
            try {
                projectHistoryRecommendMapper.resetHighQuality(projectId);
                if (!recommendHotelHistoryDataList.isEmpty()) {
                    recommendHotelHistoryDataList.forEach(item -> {
                        if (item.getRecommendLevel() == null) {
                            return;
                        }
                        if (item.getRecommendLevel() >= RecommendLevelEnum.C.key) {
                            return;
                        }
                        ProjectHistoryRecommendEntity projectHistoryRecommend = new ProjectHistoryRecommendEntity();
                        projectHistoryRecommend.setProjectId(projectId);
                        projectHistoryRecommend.setHotelId(item.getHotelId());
                        projectHistoryRecommend.setIsHighQuality(RfpConstant.constant_1);
                        projectHistoryRecommend.setCreator(userDTO.getUsername());
                        projectHistoryRecommend.setModifier(userDTO.getUsername());
                        projectHistoryRecommend.setIsHighQualityRecommend(RfpConstant.constant_1);
                        // 记录日志
                        if(Objects.equals(item.getHotelId(), recordLogHotelId)){
                            logger.info("优质商旅酒店推荐 hotelId: {}", item.getHotelId());
                        }
                        // 设置同价位3公里范围所有间夜数
                        if (theSameLevelHotelAreaRoomCountMap.containsKey(item.getHotelId())) {
                            projectHistoryRecommend.setPriceLevelRoomNight(theSameLevelHotelAreaRoomCountMap.get(item.getHotelId()));
                        }
                        int insertCount = projectHistoryRecommendMapper.insertOrUpdate(projectHistoryRecommend);
                        if (insertCount == 1) {
                            highQualityRecommendList.add(projectHistoryRecommend);
                            try {
                                redissonClient.getSet(RedisConstant.AI_REVIEW_HOTEL_IDS).add(String.valueOf(item.getHotelId()));
                            } catch (Exception e) {
                                logger.error("新增AI Review 异常 ", e);
                            }
                        }
                    });
                }
                response.setResult(ResultEnum.SUCCESS.key);
                response.setMsg("优质商旅酒店推荐成功");
                logger.info("优质商旅酒店推荐，总耗时=" + (System.currentTimeMillis() - beginTime)  + "，projectId=" + projectId);
            } catch (Exception ex){
                logger.error(ExceptionUtility.getDetailedExceptionString(ex));
                logger.error("优质商旅酒店推荐异常 projectId:" + projectId, ex);
                response.setResult(ResultEnum.FAILURE.key);
                response.setMsg("生成优质商旅酒店推荐异常");
            } finally {
                // 记录完成统计日志
                recordFinishedLog(projectHighQualityRecommendStatLog, response);
            }

            // 节省明星酒店推荐
            beginTime = System.currentTimeMillis();
            statName = "节省明星酒店推荐";
            ProjectRecommendStatLogEntity savedRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, projectId);
            projectRecommendStatLogMapper.insert(savedRecommendStatLog);
            try {
                projectHistoryRecommendMapper.resetSavedHotel(projectId);
                String startDate = DateUtil.formatDate(DateUtil.offset(new Date(), DateField.MONTH, -3));
                String endDate = DateUtil.formatDate(new Date());
                Lists.partition(highQualityRecommendList, 100).forEach(subHighQualityRecommendList -> {
                    List<Long> hotelIdList = subHighQualityRecommendList.stream().map(ProjectHistoryRecommendEntity::getHotelId).collect(Collectors.toList());
                    DisHotelDailyOrderRequest disHotelDailyOrderRequest = new DisHotelDailyOrderRequest();
                    disHotelDailyOrderRequest.setHotelIdList(hotelIdList);
                    disHotelDailyOrderRequest.setStartTime(startDate);
                    disHotelDailyOrderRequest.setEndTime(endDate);
                    List<HotelSavedAmountDto> hotelSavedAmountDtoList = disHotelDailyOrderMapper.selectHotelSavedAmountGroupByOrg(disHotelDailyOrderRequest);
                    Map<Long, List<HotelSavedAmountDto>> hotelSavedAmountDtoMap = hotelSavedAmountDtoList.stream().collect(Collectors.groupingBy(HotelSavedAmountDto::getHotelId));
                    for(ProjectHistoryRecommendEntity projectHistoryRecommend : subHighQualityRecommendList){
                        List<HotelSavedAmountDto> savedAmountHotelSavedAmountDtoList = hotelSavedAmountDtoMap.get(projectHistoryRecommend.getHotelId());
                        // 记录日志
                        if(Objects.equals(projectHistoryRecommend.getHotelId(), recordLogHotelId)){
                            logger.info("节省明星推荐 hotelId: {}, {}", projectHistoryRecommend.getHotelId(), JsonUtil.objectToJson(savedAmountHotelSavedAmountDtoList));
                        }
                        if(CollUtil.isEmpty(savedAmountHotelSavedAmountDtoList)){
                            continue;
                        }
                        // 取最大节省率
                        savedAmountHotelSavedAmountDtoList = savedAmountHotelSavedAmountDtoList.stream().filter(o -> o.getTotalOtaPrice() != null && o.getTotalOtaPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                        BigDecimal maxSavedAmount = BigDecimal.ZERO;
                        for(HotelSavedAmountDto item : savedAmountHotelSavedAmountDtoList) {
                            BigDecimal savedAmountRate = item.getSavedAmount().divide(item.getTotalOtaPrice(), 3, RoundingMode.HALF_UP);
                            if (savedAmountRate.compareTo(maxSavedAmount) > 0) {
                                maxSavedAmount = savedAmountRate;
                            }
                        }

                        if(maxSavedAmount.compareTo(BigDecimal.valueOf(0.2)) >=0){
                            logger.info("HotelId: {}, SavedAmountRate: {}", savedAmountHotelSavedAmountDtoList.get(0).getHotelId(), maxSavedAmount);
                            projectHistoryRecommend.setIsHighQuality(null);
                            projectHistoryRecommend.setIsHighQualityRecommend(null);
                            projectHistoryRecommend.setIsSavedHotel(RfpConstant.constant_1);
                            projectHistoryRecommend.setIsSavedHotelRecommend(RfpConstant.constant_1);
                            int insertCount = projectHistoryRecommendMapper.insertOrUpdate(projectHistoryRecommend);
                            if (insertCount == 1) {
                                try {
                                    redissonClient.getSet(RedisConstant.AI_REVIEW_HOTEL_IDS).add(String.valueOf(projectHistoryRecommend.getHotelId()));
                                } catch (Exception e) {
                                    logger.error("新增AI Review 异常 ", e);
                                }
                            }
                        }
                    }
                });
                response.setResult(ResultEnum.SUCCESS.key);
                response.setMsg("节省明星酒店推荐成功");
                logger.info("节省明星酒店推荐，总耗时=" +(System.currentTimeMillis() - beginTime) + "，projectId=" + projectId);
            } catch (Exception ex){
                logger.error(ExceptionUtility.getDetailedExceptionString(ex));
                logger.error("节省明星酒店推荐异常 projectId:" + projectId, ex);
                response.setResult(ResultEnum.FAILURE.key);
                response.setMsg("节省明星酒店推荐异常");
            } finally {
                // 记录完成统计日志
                recordFinishedLog(savedRecommendStatLog, response);
            }

            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("生成项目酒店推荐等级成功");
            logger.info("生成项目酒店推荐等级，总耗时=" + watch.getStepSplitTime() + "，projectId=" + projectId);
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            logger.error("生成项目酒店推荐等级异常 projectId:" + projectId, ex);
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("生成项目酒店推荐等级异常");
        } finally {
            countDownLatch.countDown();

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");

        }

        return new AsyncResult<>(response);
    }

    @Async(ThreadPoolAutoConfiguration.EXECUTOR_NAME)
    public Future<Response> updateLastYearViolationsCount(UserSession userDTO, String statReferenceNo, ProjectEntity project, List<Long> hotelIdList, CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("更新酒店历史违规次数：" + project.getProjectId());

        // 记录统计日志
        String statName = "更新酒店历史违规次数";
        ProjectRecommendStatLogEntity projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, project.getProjectId());
        projectRecommendStatLogMapper.insert(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            // 重置违规次数
            projectHotelHistoryDataMapper.resetHistoryProjectHotelViolationsCount(project.getProjectId());

            // 更新酒店违规次数
            Lists.partition(hotelIdList, 1000).forEach(hotelIdListPartition -> {
                List<Long> projectIntentHotelIdList = projectIntentHotelMapper.selectProjectIntentHotelServicePoint(project.getRelatedProjectId(), hotelIdListPartition).stream().map(ProjectIntentHotelEntity::getHotelId).collect(Collectors.toList());
                if(CollUtil.isEmpty(projectIntentHotelIdList)){
                    return;
                }
                // 默认更新有报价的酒店违规次数为0
                projectHotelHistoryDataMapper.updateHistoryProjectHotelViolationsCount(project.getProjectId(), 0, projectIntentHotelIdList);

                // 查询违规次数
                List<HotelViolationCountStatDto> subHotelViolationCountList = hotelViolationsMonitorMapper.queryHotelViolationCountStat(project.getRelatedProjectId(), hotelIdListPartition);
                if(CollUtil.isEmpty(subHotelViolationCountList)){
                    return;
                }
                // 更新酒店违规次数
                Map<Integer, List<HotelViolationCountStatDto>> hotelViolationCountMap = subHotelViolationCountList.stream().collect(Collectors.groupingBy(HotelViolationCountStatDto::getViolationCount));
                for (Integer violationCount : hotelViolationCountMap.keySet()) {
                    List<HotelViolationCountStatDto> hotelViolationCountStatDtoList = hotelViolationCountMap.get(violationCount);
                    List<Long> hotelViolationCountStatDtoListHotelIdList = hotelViolationCountStatDtoList.stream().map(HotelViolationCountStatDto::getHotelId).collect(Collectors.toList());
                    projectHotelHistoryDataMapper.updateHistoryProjectHotelViolationsCount(project.getProjectId(), violationCount, hotelViolationCountStatDtoListHotelIdList);
                }
            });
            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("更新酒店历史违规次数成功");
            logger.info("更新酒店历史违规次数，总耗时=" + watch.getStepSplitTime() + "，projectId=" + project.getProjectId());
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            logger.error("更新酒店历史违规次数异常", ex);
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("更新酒店历史违规次数异常");
        } finally {
            countDownLatch.countDown();
            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }

        return new AsyncResult<>(response);
    }

    @Async("rfpCommonExecutor")
    public Future<Response> updateLastYearServicePoint(UserSession userDTO, String statReferenceNo, ProjectEntity project, List<Long> hotelIdList, CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("更新酒店历史服务分：" +project.getProjectId());

        // 记录统计日志
        String statName = "更新酒店历史服务分";
        ProjectRecommendStatLogEntity projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, project.getProjectId());
        projectRecommendStatLogMapper.insert(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            // 重置服务分
            projectHotelHistoryDataMapper.resetHistoryProjectHotelServicePoint(project.getProjectId());

            // 更新酒店去年服务分
            Lists.partition(hotelIdList, 1000).forEach(hotelIdListPartition -> {
                List<ProjectIntentHotelEntity> subProjectIntentHotelList = projectIntentHotelMapper.selectProjectIntentHotelServicePoint(project.getRelatedProjectId(),
                    hotelIdListPartition);
                if(CollUtil.isEmpty(subProjectIntentHotelList)){
                    return;
                }
                Map<BigDecimal, List<ProjectIntentHotelEntity>> subProjectIntentHotelListMap = subProjectIntentHotelList.stream().collect(Collectors.groupingBy(ProjectIntentHotelEntity::getHotelServicePoints));
                for (BigDecimal servicePoint : subProjectIntentHotelListMap.keySet()) {
                    List<ProjectIntentHotelEntity> projectIntentHotelList = subProjectIntentHotelListMap.get(servicePoint);
                    List<Long> projectIntentHotelIdList = projectIntentHotelList.stream().map(ProjectIntentHotelEntity::getHotelId).collect(Collectors.toList());
                    projectHotelHistoryDataMapper.updateHistoryProjectHotelServicePoint(project.getProjectId(), servicePoint, projectIntentHotelIdList);
                }
            });
            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("更新酒店历史服务分成功");
            logger.info("更新酒店历史服务分成功，总耗时=" + watch.getStepSplitTime() + "，projectId=" + project.getProjectId());
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            logger.error("更新酒店历史服务分异常", ex);
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("更新酒店历史服务分异常");
        } finally {
            countDownLatch.countDown();

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }

        return new AsyncResult<>(response);
    }

    @Async("rfpCommonExecutor")
    public Future<Response> updateLastYearOtaPrice(UserSession userDTO, String statReferenceNo, ProjectEntity project, List<QueryHistoryProjectInfoResponse> historyProjectInfoList,
                                                   CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("更新酒店去年OTA最低和最高价格：" +project.getProjectId());

        // 记录统计日志
        String statName = "更新酒店去年OTA最低和最高价格";
        ProjectRecommendStatLogEntity projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, project.getProjectId());
        projectRecommendStatLogMapper.insert(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            // 更新酒店违规次数
            Date now = new Date();
            String todayStr = DateUtil.formatDate(now);
            String lastYearDateStr = DateUtil.formatDate(DateUtil.offsetDay(now, -365));
            List<Long> hotelIdList = historyProjectInfoList.stream().filter(o -> o.getMinMaxOtaPriceDate() == null).map(QueryHistoryProjectInfoResponse::getHotelId).collect(Collectors.toList());
            Lists.partition(hotelIdList, 1000).forEach(hotelIdListPartition -> {
                List<OtaHotelMinMaxPriceVO> otaHotelMinMaxPriceVOList = otaHotelDailyMinPriceMapper.queryOtaHotelMinMaxPriceVOList(hotelIdListPartition, lastYearDateStr, todayStr);
                if(CollUtil.isEmpty(otaHotelMinMaxPriceVOList)){
                    return;
                }
                otaHotelMinMaxPriceVOList.forEach(item -> {
                    projectHotelHistoryDataMapper.updateHistoryProjectHotelMinMaxOtaPrice(project.getProjectId(), item.getHotelId(), item.getMinPrice(), item.getMaxPrice(), now);
                });
            });
            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("更新酒店去年OTA最低和最高价格成功");
            logger.info("更新酒店去年OTA最低和最高价格成功，总耗时=" + watch.getStepSplitTime() + "，projectId=" + project.getProjectId());
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            logger.error("更新酒店去年OTA最低和最高价格异常", ex);
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("更新酒店去年OTA最低和最高价格异常");
        } finally {
            countDownLatch.countDown();

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }

        return new AsyncResult<>(response);
    }

    @Async(ThreadPoolAutoConfiguration.EXECUTOR_NAME)
    public Future<Response> updateLowestPrice(UserSession userDTO, String statReferenceNo, ProjectEntity project, CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("更新酒店商旅价格：" +project.getProjectId());

        // 记录统计日志
        ProjectRecommendStatLogEntity projectRecommendStatLog = initProjectRecommendStatLog(userDTO, UPDATE_LOWEST_PRICE, statReferenceNo, project.getProjectId());
        projectRecommendStatLogMapper.insert(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            // 过滤已经存在酒店差旅价格酒店
            List<Long> needUpdateLowPriceHotelIdList = projectHotelHistoryDataMapper.queryNeedUpdateLowestPriceHotelIds(project.getProjectId());
            Date now = new Date();
            // 更新酒店差旅价格
            Lists.partition(needUpdateLowPriceHotelIdList, 100).forEach(hotelIdListPartition -> {
                Response hotelLowestPriceResponse = hotelManager.queryHotelLowestPrice(hotelIdListPartition, null, null);
                List<HotelLowestPricesResponse> hotelLowestPricesResponses = null;
                if(Objects.equals(hotelLowestPriceResponse.getResult(), ReturnResultEnum.SUCCESS.errorNo)){
                    hotelLowestPricesResponses = (List<HotelLowestPricesResponse>)hotelLowestPriceResponse.getData();
                }
                if(CollUtil.isEmpty(hotelLowestPricesResponses)) {
                    logger.info("hotelLowestPricesResponses is null {}", hotelIdListPartition);
                    return;
                }
                Map<Long, HotelLowestPricesResponse> hotelLowestPricesResponsesMap = hotelLowestPricesResponses.stream().collect(Collectors.toMap(HotelLowestPricesResponse::getHotelId, item -> item));
                hotelLowestPricesResponsesMap.forEach((hotelId, hotelLowestPricesResponse) -> {
                    if(hotelLowestPricesResponse.getLowestPrice() != null) {
                        BigDecimal adjustLowestPrice = generateAdjustLowestPrice(hotelLowestPricesResponse.getLowestPrice());
                        String priceItemJson = JsonUtil.objectToJson(hotelLowestPricesResponse.getPriceItems());
                        // 避免长度过大异常
                        if(priceItemJson.length() > 3500){
                            logger.error("projectHotelHistoryDataDao.updateHistoryProjectHotelLowestPrice too large price item {}", priceItemJson);
                            priceItemJson = "[]";
                        }
                        int updateResult = projectHotelHistoryDataMapper.updateHistoryProjectHotelLowestPrice(project.getProjectId(),
                                hotelLowestPricesResponse.getHotelId(),
                                hotelLowestPricesResponse.getLowestPrice(), adjustLowestPrice, now,
                                priceItemJson);
                        if(updateResult == 0){
                            logger.error("projectHotelHistoryDataDao.updateHistoryProjectHotelLowestPrice update failed {}",hotelLowestPricesResponse.getHotelId());
                        }
                    }
                });

                // 减少调用频率
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            });

            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("更新酒店商旅价格成功");
            logger.info("更新酒店商旅价格成功，总耗时=" + watch.getStepSplitTime() + "，projectId=" + project.getProjectId());
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            logger.error("更新酒店商旅价格异常", ex);
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("更新酒店商旅价格异常");
        } finally {
            if(countDownLatch != null) {
                countDownLatch.countDown();
            }

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }
        return new AsyncResult<>(response);
    }

    /**
     * 异步生成高频预订和同档预定酒店邀约推荐
     */
    @Async(ThreadPoolAutoConfiguration.EXECUTOR_NAME)
    public Future<Response> generateFrequencyRecommend(String statReferenceNo, UserSession userDTO, ProjectEntity project, List<QueryHistoryProjectInfoResponse> historyProjectInfoList,
                                                       CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("生成高频预订邀约推荐：" +project.getProjectId());

        // 定义返回值
        Response response = new Response();

        // 查看更新签约混合价是否完成 （查询等待10分钟是否完成）
        boolean isFinishedSucceeded = isStatTaskFinished(project.getProjectId(), statReferenceNo, UPDATE_LOWEST_PRICE, 120);
        if(!isFinishedSucceeded){
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("更新酒店商旅价格失败，不能生成高频预订邀约推荐");
            new AsyncResult<>(response);
        }

        // 记录统计日志
        String statName = "生成高频预订邀约推荐";
        ProjectRecommendStatLogEntity projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, project.getProjectId());
        projectRecommendStatLogMapper.insert(projectRecommendStatLog);
        Map<Long, QueryHistoryProjectInfoResponse> historyProjectInfoMap = historyProjectInfoList.stream().collect(Collectors.toMap(QueryHistoryProjectInfoResponse::getHotelId, item -> item));

        StopWatch watch = new StopWatch();
        watch.start();
        try {
            List<QueryHistoryProjectInfoResponse> frequencyHistoryProjectInfoList = historyProjectInfoList.stream().filter(o -> o.getRoomNightCount() != null && o.getRoomNightCount() >= 30).collect(Collectors.toList());
            List<ProjectHistoryRecommendEntity> frequencyHistoryRecommendList = new ArrayList<>();
            List<Long> frequencyHotelIdList = new ArrayList<>();
            Lists.partition(frequencyHistoryProjectInfoList, 1000).forEach(frequencyHistoryProjectInfoListListPartition -> {
                for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : frequencyHistoryProjectInfoListListPartition){
                    ProjectHistoryRecommendEntity projectHistoryRecommend = new ProjectHistoryRecommendEntity();
                    projectHistoryRecommend.setProjectId(project.getProjectId());
                    projectHistoryRecommend.setHotelId(queryHistoryProjectInfoResponse.getHotelId());
                    projectHistoryRecommend.setIsFrequency(RfpConstant.constant_1);
                    projectHistoryRecommend.setIsFrequencyRecommend(RfpConstant.constant_0);
                    projectHistoryRecommend.setCreator(userDTO.getUsername());
                    projectHistoryRecommend.setModifier(userDTO.getUsername());
                    List<Integer> recommendReasonList = new ArrayList<>();
                    if(queryHistoryProjectInfoResponse.getRoomNightCount() >= 600){
                        recommendReasonList.add(RecommendFrequencyEnum.ROOM_NIGHT_600.key);
                    } else if(queryHistoryProjectInfoResponse.getRoomNightCount() >= 200){
                        recommendReasonList.add(RecommendFrequencyEnum.ROOM_NIGHT_200_599.key);
                    } else if(queryHistoryProjectInfoResponse.getRoomNightCount() >= 100){
                        recommendReasonList.add(RecommendFrequencyEnum.ROOM_NIGHT_100_199.key);
                    }
                    if(!recommendReasonList.isEmpty()){
                        projectHistoryRecommend.setIsFrequencyRecommend(RfpConstant.constant_1);
                        projectHistoryRecommend.setFrequencyRecommends(JsonUtil.objectToJson(recommendReasonList));
                        frequencyHotelIdList.add(queryHistoryProjectInfoResponse.getHotelId());
                    }
                    frequencyHistoryRecommendList.add(projectHistoryRecommend);

                }
            });

            // 更新高频推荐
            projectHistoryRecommendMapper.resetRecommendFrequency(project.getProjectId());
            if(!frequencyHistoryRecommendList.isEmpty()){
                frequencyHistoryRecommendList.forEach(item -> {
                    int insertCount = projectHistoryRecommendMapper.insertOrUpdate(item);
                    if(insertCount == 1 && item.getIsFrequencyRecommend() == RfpConstant.constant_1) {
                        try {
                            redissonClient.getSet(RedisConstant.AI_REVIEW_HOTEL_IDS).add(String.valueOf(item.getHotelId()));
                        } catch (Exception e) {
                            logger.error("新增AI Review 异常 ", e);
                        }
                    }
                });
            }


        // 更新高频同档推荐
            if(CollUtil.isNotEmpty(frequencyHistoryRecommendList)){
                logger.info("生成高频预订同档推荐");
                // 过滤高频酒店
                Map<Long, ProjectHistoryRecommendEntity> theSameLevelRecommendMap = new HashMap<>();
                Map<String, List<ProjectHistoryRecommendEntity>> cityFrequencyHistoryRecommendMap =
                        frequencyHistoryRecommendList.stream().filter(o -> o.getIsFrequencyRecommend() == RfpConstant.constant_1
                                && !JsonUtil.jsonToList(o.getFrequencyRecommends(), Integer.class).contains(RecommendFrequencyEnum.ROOM_NIGHT_100_199.key)
                        ).collect(Collectors.groupingBy(ProjectHistoryRecommendEntity::getCity));
                // 按照城市分组查找同档周边酒店
                String lastYearStr = DateUtil.formatDate(DateUtil.offset(new Date(), DateField.YEAR, -1));
                String todayStr = DateUtil.formatDate(new Date());
                Date lastTwoYearDate = DateUtil.offset(new Date(), DateField.YEAR, -2);
                BigDecimal rating49 = new BigDecimal("4.9");
                BigDecimal rating45 = new BigDecimal("4.5");
                for(String city : cityFrequencyHistoryRecommendMap.keySet()){
                    List<HotelResponse> hotelResponses = hotelMapper.selectHotelInfoByCityAndRating(city, "4.2");
                    for(ProjectHistoryRecommendEntity projectHistoryRecommend : cityFrequencyHistoryRecommendMap.get(city)){
                        List<Long> newHistoryProjectHotelIdList = new ArrayList<>();
                        Map<Long, BigDecimal> historyProjectHotelDistanceMap = new HashMap<>();
                        Map<Long, HotelResponse> frequencyTheSameLevelHotelMap = new HashMap<>();
                        QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse = historyProjectInfoMap.get(projectHistoryRecommend.getHotelId());

                        // 过滤相同星级的酒店
                        List<HotelResponse> theSameLevelHotelResponses = hotelResponses.stream().filter(o ->
                                StringUtils.isNotEmpty(o.getHotelStar()) && !frequencyHotelIdList.contains(o.getHotelId()) && o.getHotelStar().equals(queryHistoryProjectInfoResponse.getHotelStar())
                                ).collect(Collectors.toList());
                        if(CollUtil.isEmpty(theSameLevelHotelResponses)){
                            continue;
                        }

                        BigDecimal lastYearAvgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(new BigDecimal(queryHistoryProjectInfoResponse.getRoomNightCount()), 2, RoundingMode.HALF_UP);

                        // 检查酒店是否在2公里范围
                        for(HotelResponse hotelResponse : theSameLevelHotelResponses){
                            // 是否为2公里距离
                            if(hotelResponse.getLatBaidu() == null || hotelResponse.getLngBaidu() == null || queryHistoryProjectInfoResponse.getLatBaidu() == null || queryHistoryProjectInfoResponse.getLngBaidu() == null){
                                continue;
                            }
                            BigDecimal distance = LocationUtil.getKmDistance(hotelResponse.getLatBaidu(), hotelResponse.getLngBaidu(), queryHistoryProjectInfoResponse.getLatBaidu(),
                                queryHistoryProjectInfoResponse.getLngBaidu());
                            if(distance == null){
                                continue;
                            }
                            if(NumberUtil.equals(distance, BigDecimal.ZERO) || NumberUtil.isGreater(distance, new BigDecimal("2"))){
                                continue;
                            }

                            BigDecimal hotelDistance = distance.setScale(2, RoundingMode.HALF_UP);
                            // 已经推荐过酒店获取上次推荐信息, 如果上次已经被推荐，并且距离小于当前距离，不用处理
                            ProjectHistoryRecommendEntity lastProjectHistoryRecommend = theSameLevelRecommendMap.get(hotelResponse.getHotelId());
                            if(lastProjectHistoryRecommend != null && lastProjectHistoryRecommend.getIsSameLevelFreqRecommend() == RfpConstant.constant_1 &&
                                    lastProjectHistoryRecommend.getSameLevelFreqHotelDistance().compareTo(hotelDistance) < 0){
                                logger.info("已经推荐过 {}", JsonUtil.objectToJson(lastProjectHistoryRecommend));
                                continue;
                            }

                            historyProjectHotelDistanceMap.put(hotelResponse.getHotelId(), hotelDistance);
                            frequencyTheSameLevelHotelMap.put(hotelResponse.getHotelId(), hotelResponse);

                            // 一个高产酒店周边同档（同星级分组）的酒店，OTA评分4.9分及以上推荐邀约
                            QueryHistoryProjectInfoResponse recommendQueryHistoryProjectInfoResponse = historyProjectInfoMap.get(hotelResponse.getHotelId());
                            if(recommendQueryHistoryProjectInfoResponse == null){
                                recommendQueryHistoryProjectInfoResponse = new QueryHistoryProjectInfoResponse();
                                recommendQueryHistoryProjectInfoResponse.setProjectId(project.getProjectId());
                                recommendQueryHistoryProjectInfoResponse.setHotelId(hotelResponse.getHotelId());
                                recommendQueryHistoryProjectInfoResponse.setRoomNightCount(0);
                                recommendQueryHistoryProjectInfoResponse.setTotalAmount(BigDecimal.ZERO);
                                recommendQueryHistoryProjectInfoResponse.setCityCode(hotelResponse.getCity());
                                recommendQueryHistoryProjectInfoResponse.setCityOrder(0);
                                recommendQueryHistoryProjectInfoResponse.setSavedAmount(BigDecimal.ZERO);
                                recommendQueryHistoryProjectInfoResponse.setSavedAmountRate(BigDecimal.ZERO);
                                recommendQueryHistoryProjectInfoResponse.setCreator(userDTO.getUsername());
                                recommendQueryHistoryProjectInfoResponse.setIsUploaded(RfpConstant.constant_0);
                                recommendQueryHistoryProjectInfoResponse.setLatBaidu(hotelResponse.getLatBaidu());
                                recommendQueryHistoryProjectInfoResponse.setLngBaidu(hotelResponse.getLngBaidu());
                                newHistoryProjectHotelIdList.add(hotelResponse.getHotelId());
                                historyProjectInfoMap.put(hotelResponse.getHotelId(), recommendQueryHistoryProjectInfoResponse);
                            }
                        }

                        // 查询最近一年历史数据
                        if(CollUtil.isNotEmpty(newHistoryProjectHotelIdList)) {
                            DisHotelDailyOrderRequest disHotelDailyOrderRequest = new DisHotelDailyOrderRequest();
                            disHotelDailyOrderRequest.setHotelIdList(newHistoryProjectHotelIdList);
                            disHotelDailyOrderRequest.setStartTime(lastYearStr);
                            disHotelDailyOrderRequest.setEndTime(todayStr);
                            List<DisHotelDailyOrderResponse> disHotelDailyOrderResponseList = disHotelDailyOrderMapper.selectHotelSavedAmountStatList(disHotelDailyOrderRequest);
                            for(DisHotelDailyOrderResponse disHotelDailyOrderResponse : disHotelDailyOrderResponseList){
                                QueryHistoryProjectInfoResponse hotelHistoryProjectInfoResponse = historyProjectInfoMap.get(disHotelDailyOrderResponse.getHotelId());
                                hotelHistoryProjectInfoResponse.setRoomNightCount(disHotelDailyOrderResponse.getRoomNightCount());
                                hotelHistoryProjectInfoResponse.setTotalAmount(disHotelDailyOrderResponse.getOrderAmount());
                                hotelHistoryProjectInfoResponse.setSavedAmount(disHotelDailyOrderResponse.getSavedAmount());
                                hotelHistoryProjectInfoResponse.setSavedAmountRate(disHotelDailyOrderResponse.getSavedAmountRate());
                            }

                            // 计算混淆价格
                            Response hotelLowestPriceResponse = hotelManager.queryHotelLowestPrice(newHistoryProjectHotelIdList, null, null);
                            List<HotelLowestPricesResponse> hotelLowestPricesResponses = null;
                            if(Objects.equals(hotelLowestPriceResponse.getResult(), ReturnResultEnum.SUCCESS.errorNo)){
                                hotelLowestPricesResponses = (List<HotelLowestPricesResponse>)hotelLowestPriceResponse.getData();
                            }
                            if(CollUtil.isNotEmpty(hotelLowestPricesResponses)) {
                                Map<Long, HotelLowestPricesResponse> hotelLowestPricesResponsesMap = hotelLowestPricesResponses.stream().collect(Collectors.toMap(HotelLowestPricesResponse::getHotelId, item -> item));
                                hotelLowestPricesResponsesMap.forEach((hotelId, hotelLowestPricesResponse) -> {
                                    if(hotelLowestPricesResponse.getLowestPrice() != null) {
                                        BigDecimal adjustLowestPrice = generateAdjustLowestPrice(hotelLowestPricesResponse.getLowestPrice());
                                        QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse1 = historyProjectInfoMap.get(hotelId);
                                        queryHistoryProjectInfoResponse1.setAdjustLowestPrice(adjustLowestPrice);
                                        queryHistoryProjectInfoResponse1.setLowestPriceDate(new Date());
                                        queryHistoryProjectInfoResponse1.setLowestPrice(hotelLowestPricesResponse.getLowestPrice());
                                        queryHistoryProjectInfoResponse1.setLowestPriceItemInfo(JsonUtil.objectToJson(hotelLowestPricesResponse.getPriceItems()));
                                    }
                                });
                            }

                            // 新增历史推荐数据
                            List<QueryHistoryProjectInfoResponse> addQueryHistoryProjectInfoResponseList = new ArrayList<>();
                            for(Long hotelId : newHistoryProjectHotelIdList){
                                QueryHistoryProjectInfoResponse newQueryHistoryProjectInfoResponse = historyProjectInfoMap.get(hotelId);
                                logger.info("Add newQueryHistoryProjectInfoResponse {}", newQueryHistoryProjectInfoResponse.getHotelId());
                                newQueryHistoryProjectInfoResponse.setRoomNightCount(0);
                                newQueryHistoryProjectInfoResponse.setTotalAmount(BigDecimal.ZERO);
                                addQueryHistoryProjectInfoResponseList.add(newQueryHistoryProjectInfoResponse);

                            }
                            if(CollUtil.isNotEmpty(addQueryHistoryProjectInfoResponseList)) {
                                projectHotelHistoryDataMapper.batchMergeHistoryResponse(addQueryHistoryProjectInfoResponseList);
                            }
                        }
                        Set<Long> queryLast12MonthHotelIdSet = new HashSet<>();
                        for(Long hotelId : frequencyTheSameLevelHotelMap.keySet()){
                            HotelResponse hotelResponse = frequencyTheSameLevelHotelMap.get(hotelId);
                            QueryHistoryProjectInfoResponse theSameLevelHotelQueryHistoryProjectInfoResponse = historyProjectInfoMap.get(hotelId);
                            ProjectHistoryRecommendEntity theSameHotelStarHotelRecommend = new ProjectHistoryRecommendEntity();
                            theSameHotelStarHotelRecommend.setProjectId(project.getProjectId());
                            theSameHotelStarHotelRecommend.setHotelId(hotelId);
                            theSameHotelStarHotelRecommend.setIsSameLevelFreq(RfpConstant.constant_1);
                            theSameHotelStarHotelRecommend.setIsSameLevelFreqRecommend(RfpConstant.constant_0);
                            theSameHotelStarHotelRecommend.setSameLevelFreqHotelId(projectHistoryRecommend.getHotelId());
                            theSameHotelStarHotelRecommend.setSameLevelFreqHotelDistance(historyProjectHotelDistanceMap.get(hotelId));

                            QueryHistoryProjectInfoResponse recommendQueryHistoryProjectInfoResponse = historyProjectInfoMap.get(projectHistoryRecommend.getHotelId());
                            RecommendFrequencySameLevelInfo recommendFrequencySameLevelInfo = new RecommendFrequencySameLevelInfo();
                            recommendFrequencySameLevelInfo.setHotelName(recommendQueryHistoryProjectInfoResponse.getHotelName());
                            recommendFrequencySameLevelInfo.setDistance(historyProjectHotelDistanceMap.get(hotelId));
                            recommendFrequencySameLevelInfo.setRoomNightCount(recommendQueryHistoryProjectInfoResponse.getRoomNightCount());
                            recommendFrequencySameLevelInfo.setAvgPrice(BigDecimal.ZERO);
                            if(recommendQueryHistoryProjectInfoResponse.getRoomNightCount() > 0) {
                                recommendFrequencySameLevelInfo.setAvgPrice(recommendQueryHistoryProjectInfoResponse.getTotalAmount().divide(new BigDecimal(recommendQueryHistoryProjectInfoResponse.getRoomNightCount()), 0, RoundingMode.HALF_UP));
                            }
                            theSameHotelStarHotelRecommend.setSameLevelFreqHotelInfo(JsonUtil.objectToJson(recommendFrequencySameLevelInfo));
                            theSameHotelStarHotelRecommend.setCreator(userDTO.getUsername());
                            theSameHotelStarHotelRecommend.setModifier(userDTO.getUsername());

                            List<Integer> recommendReasonList = new ArrayList<>();
                            BigDecimal theSameHotelRating = new BigDecimal(hotelResponse.getRatting());
                            // 查询酒店节省率
                            BigDecimal maxSaveAmountRate = queryMaxSavedAmountRate(hotelId, lastYearStr, todayStr);


                            if(StringUtils.isNotEmpty(hotelResponse.getHotelStar()) && hotelResponse.getHotelStar().equals(queryHistoryProjectInfoResponse.getHotelStar())) {
                                // 一个高产酒店周边同档（同星级分组）的酒店，OTA评分4.9分及以上推荐邀约
                                if (theSameHotelRating.compareTo(rating49) >= 0) {
                                    recommendReasonList.add(RecommendFrequencySameLevelEnum.OTA_49.key);
                                }
                                //一个高产酒店周边同档（同星级分组）的酒店，开业时间在近2年的推荐邀约 （今年2025年，2023年开业的推荐邀约）
                                if (hotelResponse.getPraciceDate() != null && hotelResponse.getPraciceDate().after(lastTwoYearDate)) {
                                    recommendReasonList.add(RecommendFrequencySameLevelEnum.OPEN_IN_2_YEAR.key);
                                }

                                // OTA评分4.5分以上，签约混淆后价格比当前高产酒店去年成交均价低于10%，且酒店+机构纬度（一个酒店不同机构签约价格可能不同，看签约最好的节省率）的最高节省率高于10%
                                if (theSameHotelRating.compareTo(rating45) >= 0 &&
                                        theSameLevelHotelQueryHistoryProjectInfoResponse.getAdjustLowestPrice() != null &&  theSameLevelHotelQueryHistoryProjectInfoResponse.getAdjustLowestPrice().compareTo(lastYearAvgPrice.multiply(new BigDecimal("0.9"))) <= 0 &&
                                        maxSaveAmountRate.compareTo(new BigDecimal("0.1")) >= 0
                                ) {
                                    logger.info("高频同档OTA_45_PRICE_LOWER_THAN_10_PERCENT {}, {}, {}, {}, {}", theSameLevelHotelQueryHistoryProjectInfoResponse.getHotelId(), theSameLevelHotelQueryHistoryProjectInfoResponse.getAdjustLowestPrice(), projectHistoryRecommend.getHotelId(), lastYearAvgPrice, maxSaveAmountRate);
                                    recommendReasonList.add(RecommendFrequencySameLevelEnum.OTA_45_PRICE_LOWER_THAN_10_PERCENT.key);
                                }
                                // 一个高产酒店周边同档（同星级分组）的酒店，商旅近12个月产量超过600间夜推荐邀约（不限客户，看预订监控该酒店所有分销商总和间夜数）。
                                queryLast12MonthHotelIdSet.add(hotelResponse.getHotelId());
                            } else if(StringUtils.isNotEmpty(hotelResponse.getHotelStar()) && hotelResponse.getHotelStar().compareTo(queryHistoryProjectInfoResponse.getHotelStar()) > 0) {
                                // ，一个高产酒店周边比当前高产酒店档次更高 OTA评分4.5分以上，签约混淆后价格比当前高产酒店去年成交均价低，且酒店+机构纬度（一个酒店不同机构签约价格可能不同，看签约最好的节省率）的最高节省率高于10%
                                //   （若这个酒店之前不在总名单中时，同时加入总名单）（例如，高产酒店为高档型，去年成交均价460，计算周边更高当次酒店独立起价时，有一个酒店为豪华型，签约最低价428，混淆后458，比当前高产酒店星级分组更高，价格更优，因此推荐签约）
                                if (theSameHotelRating.compareTo(rating45) >= 0 &&
                                        theSameLevelHotelQueryHistoryProjectInfoResponse.getAdjustLowestPrice() != null && theSameLevelHotelQueryHistoryProjectInfoResponse.getAdjustLowestPrice().compareTo(lastYearAvgPrice) <= 0 &&
                                        maxSaveAmountRate.compareTo(new BigDecimal("0.1")) >= 0
                                ) {
                                    logger.info("OTA_45_LOWER_PRICE {}, {}, {}, {}, {}", theSameLevelHotelQueryHistoryProjectInfoResponse.getHotelId(), theSameLevelHotelQueryHistoryProjectInfoResponse.getAdjustLowestPrice(), projectHistoryRecommend.getHotelId(), lastYearAvgPrice, maxSaveAmountRate);
                                    recommendReasonList.add(RecommendFrequencySameLevelEnum.OTA_45_LOWER_PRICE.key);
                                }
                            }

                            // 设置推荐
                            if(CollUtil.isNotEmpty(recommendReasonList)){
                                theSameHotelStarHotelRecommend.setIsSameLevelFreqRecommend(RfpConstant.constant_1);
                                theSameHotelStarHotelRecommend.setSameLevelFreqRecommends(JsonUtil.objectToJson(recommendReasonList));
                            }
                            theSameLevelRecommendMap.put(theSameHotelStarHotelRecommend.getHotelId(), theSameHotelStarHotelRecommend);
                        }
                        if(CollUtil.isNotEmpty(queryLast12MonthHotelIdSet)){
                            addRecommendForLast12MonthRoomNight(new ArrayList<>(queryLast12MonthHotelIdSet), lastYearStr, todayStr, theSameLevelRecommendMap);
                        }
                    }
                }


                // 新增或者更新统计
                projectHistoryRecommendMapper.resetRecommendFrequencySameLevelInfo(project.getProjectId());
                if(!theSameLevelRecommendMap.isEmpty()){
                    for(ProjectHistoryRecommendEntity projectHistoryRecommend : theSameLevelRecommendMap.values()){
                        int insertCount = projectHistoryRecommendMapper.insertOrUpdate(projectHistoryRecommend);
                        if(insertCount == 1 && projectHistoryRecommend.getIsSameLevelFreqRecommend()== RfpConstant.constant_1) {
                            try {
                                redissonClient.getSet(RedisConstant.AI_REVIEW_HOTEL_IDS).add(projectHistoryRecommend.getHotelId());
                            } catch (Exception e) {
                                logger.error("新增AI Review 异常 ", e);
                            }
                        }
                    }
                }
                logger.info("更新高频同档推荐统计数量: {}", theSameLevelRecommendMap.size());
            } else {
                logger.info("生成高频预订同档推荐 没有满足要求的酒店");
            }

            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("生成高频预订邀约推荐成功");
            logger.info("生成高频预订邀约推荐成功，总耗时=" + watch.getStepSplitTime() + "，projectId=" + project.getProjectId());
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            logger.error("生成高频预订邀约推荐异常", ex);
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("生成高频预订邀约推荐异常");
        } finally {
            if(countDownLatch != null) {
                countDownLatch.countDown();
            }

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }

        return new AsyncResult<>(response);
    }

    public Future<Response> generatePoiNearHotelRecommend(String statReferenceNo, UserSession userDTO, ProjectEntity project,
                                                          List<QueryHistoryProjectInfoResponse> historyProjectInfoList,
                                                          CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("POI周边优质酒店邀约推荐：" +project.getProjectId());

        String statName = RecommendHotelNameEnum.POI_NEAR_HOTEL_RECOMMEND.value;
        ProjectRecommendStatLogEntity projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, project.getProjectId());
        projectRecommendStatLogMapper.insert(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            List<QueryHistoryProjectInfoResponse> poiNearHotelList = historyProjectInfoList.stream().filter(o ->
                            o.getPoiId() != null && o.getPoiId() > 0 && o.getPoiDistance().doubleValue() <=3).collect(Collectors.toList());
            if(CollUtil.isEmpty(poiNearHotelList)){
                response.setResult(ResultEnum.SUCCESS.key);
                response.setMsg(ResultEnum.SUCCESS.value);
                logger.info("POI周边优质酒店邀约推荐为空：" +project.getProjectId());
                return new AsyncResult<>(response);
            }

            BigDecimal ota49 = new BigDecimal("4.9");
            Date lastTwoYearDate = DateUtil.offset(new Date(), DateField.YEAR, -2);
            String lastTwoYearDateStr = DateUtil.formatDate(lastTwoYearDate);
            String todayDateStr = DateUtil.formatDate(new Date());
            List<ProjectHistoryRecommendEntity> projectHistoryRecommendList = new ArrayList<>();
            Map<Long, List<QueryHistoryProjectInfoResponse>> poiNearHotelMap = poiNearHotelList.stream().collect(Collectors.groupingBy(QueryHistoryProjectInfoResponse::getPoiId));
            for(Long poiId : poiNearHotelMap.keySet()){
                List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoResponseList = poiNearHotelMap.get(poiId);
                GeneratePoiStatVO generatePoiStatVO = generatePoiStatVO(queryHistoryProjectInfoResponseList);
                // 统计POI推荐
                Set<Long> poiNearHotelIds = new HashSet<>();
                for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : queryHistoryProjectInfoResponseList){
                    boolean isPoiNearHotel = false;
                    boolean isMaxRoomNightHotel = false;
                    if(StringUtils.isEmpty(queryHistoryProjectInfoResponse.getHotelStar())){
                        continue;
                    }
                    if(queryHistoryProjectInfoResponse.getHotelStar().equals("19") && generatePoiStatVO.getFiveStarHotelRoomNightCount() >= 100){
                        isPoiNearHotel = true;
                        isMaxRoomNightHotel = generatePoiStatVO.getFiveStarHotelIds().contains(queryHistoryProjectInfoResponse.getHotelId());
                    } else if(queryHistoryProjectInfoResponse.getHotelStar().equals("29") && generatePoiStatVO.getQuisaFiveStarHotelRoomNightCount() >= 100){
                        isPoiNearHotel = true;
                        isMaxRoomNightHotel = generatePoiStatVO.getQuisaFiveStarHotelIds().contains(queryHistoryProjectInfoResponse.getHotelId());
                    } else if ((queryHistoryProjectInfoResponse.getHotelStar().equals("39") || queryHistoryProjectInfoResponse.getHotelStar().equals("49")) && generatePoiStatVO.getFourAndQuisaFourStarHotelRoomNightCount() >= 100){
                        isPoiNearHotel = true;
                        isMaxRoomNightHotel = generatePoiStatVO.getFourAndQuisaFourStarHotelIds().contains(queryHistoryProjectInfoResponse.getHotelId());
                    } else if ((queryHistoryProjectInfoResponse.getHotelStar().equals("59") || queryHistoryProjectInfoResponse.getHotelStar().equals("64")) && generatePoiStatVO.getThreeAndQuisaThreeStarHotelRoomNightCount() >= 100){
                        isPoiNearHotel = true;
                        isMaxRoomNightHotel = generatePoiStatVO.getThreeAndQuisaThreeStarHotelIds().contains(queryHistoryProjectInfoResponse.getHotelId());
                    } else if (queryHistoryProjectInfoResponse.getHotelStar().equals("66") && generatePoiStatVO.getQuisaTwoStarHotelRoomNightCount() >= 100){
                        isPoiNearHotel = true;
                        isMaxRoomNightHotel = generatePoiStatVO.getQuisaTwoStarHotelIds().contains(queryHistoryProjectInfoResponse.getHotelId());
                    } else if ((queryHistoryProjectInfoResponse.getHotelStar().equals("69") || queryHistoryProjectInfoResponse.getHotelStar().equals("79")) && generatePoiStatVO.getTwoAndDownStarHotelRoomNightCount() >= 100){
                        isPoiNearHotel = true;
                        isMaxRoomNightHotel = generatePoiStatVO.getTwoAndDownStarHotelIds().contains(queryHistoryProjectInfoResponse.getHotelId());
                    }

                    if(isPoiNearHotel){
                        ProjectHistoryRecommendEntity poiNearHotelRecommend = new ProjectHistoryRecommendEntity();
                        poiNearHotelRecommend.setProjectId(project.getProjectId());
                        poiNearHotelRecommend.setHotelId(queryHistoryProjectInfoResponse.getHotelId());
                        poiNearHotelRecommend.setIsPoiNearHotel(RfpConstant.constant_1);
                        poiNearHotelRecommend.setPoiNearHotelPoiId(poiId);
                        poiNearHotelRecommend.setIsPoiNearHotelRecommend(RfpConstant.constant_0);
                        poiNearHotelRecommend.setCreator(userDTO.getUsername());
                        poiNearHotelRecommend.setModifier(userDTO.getUsername());
                        List<Integer> recommendReasonList = new ArrayList<>();
                        // POI周边进入总名单酒店，如果酒店去年预订间夜超过100间夜，进入推荐邀约
                        if(queryHistoryProjectInfoResponse.getRoomNightCount() >= 100){
                            recommendReasonList.add(RecommendPoiNearHotelEnum.ROOM_NIGHT_MORE_THEN_100.key);
                        }
                        // POI周边进入总名单酒店，如果酒店在当前星级预订间夜量第一，进入推荐邀约  （比如，豪华型酒店比较分散，第一只有85间夜，那么因为这个酒店在豪华型星级排名第一，所以推荐邀约）
                        if(isMaxRoomNightHotel){
                            recommendReasonList.add(RecommendPoiNearHotelEnum.SAME_LEVEL_ROOM_NIGHT_FIRST.key);
                        }
                        // POI周边进入总名单酒店，如果酒店评分4.9分及以上，进入推荐邀约
                        if(StringUtils.isNotEmpty(queryHistoryProjectInfoResponse.getRating()) && new BigDecimal(queryHistoryProjectInfoResponse.getRating()).compareTo(ota49) >= 0){
                            recommendReasonList.add(RecommendPoiNearHotelEnum.OTA_49.key);
                        }
                        //POI周边进入总名单酒店，如果酒店为近2年开业，进入推荐邀约
                        if(queryHistoryProjectInfoResponse.getPraciceDate() != null && queryHistoryProjectInfoResponse.getPraciceDate().after(lastTwoYearDate)){
                            recommendReasonList.add(RecommendPoiNearHotelEnum.OPEN_IN_2_YEAR.key);
                        }
                        poiNearHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                        // 设置POI推荐
                        if(CollUtil.isNotEmpty(recommendReasonList)){
                            poiNearHotelRecommend.setPoiNearHotelRecommends(JsonUtil.objectToJson(recommendReasonList));
                            poiNearHotelRecommend.setIsPoiNearHotelRecommend(RfpConstant.constant_1);
                        }
                        projectHistoryRecommendList.add(poiNearHotelRecommend);
                    }
                }

                // POI周边进入总名单酒店，如果酒店商旅近12个月预订总间夜超过600间夜，进入推荐邀约
                if(CollUtil.isNotEmpty(poiNearHotelIds) && CollUtil.isNotEmpty(projectHistoryRecommendList)){
                    Map<Long, ProjectHistoryRecommendEntity> projectHistoryRecommendMap =
                        projectHistoryRecommendList.stream().collect(Collectors.toMap(ProjectHistoryRecommendEntity::getHotelId, Function.identity()));
                    addPoiNearRecommendForLast12MonthRoomNight(new ArrayList<>(poiNearHotelIds), lastTwoYearDateStr, todayDateStr, projectHistoryRecommendMap);
                    projectHistoryRecommendList = new ArrayList<>(projectHistoryRecommendMap.values());
                }

                // 更新POI3公里 统计
                ProjectPoiEntity projectPoi = new ProjectPoiEntity();
                projectPoi.setPoiId(poiId);
                projectPoi.setProjectId(project.getProjectId());
                projectPoi.setModifier(userDTO.getUsername());
                projectPoi.setPoiHotelStat3km(JsonUtil.objectToJson(generatePoiStatVO.getRecommendNearPoiStat()));
                projectPoiMapper.updateProjectPoi3KmStatInfo(projectPoi);
            }

            // 新增或者更新统计
            projectHistoryRecommendMapper.resetRecommendPoiNearHotelInfo(project.getProjectId());
            if(!projectHistoryRecommendList.isEmpty()){
                for(ProjectHistoryRecommendEntity projectHistoryRecommend : projectHistoryRecommendList){
                   int insertResult = projectHistoryRecommendMapper.insertOrUpdate(projectHistoryRecommend);
                   if(insertResult == RfpConstant.constant_1 && projectHistoryRecommend.getIsPoiNearHotelRecommend() == RfpConstant.constant_1) {
                       try {
                           redissonClient.getSet(RedisConstant.AI_REVIEW_HOTEL_IDS).add(String.valueOf(projectHistoryRecommend.getHotelId()));
                       } catch (Exception e) {
                           logger.error("新增AI Review 异常 ", e);
                       }
                   }
                }
            }

            // 设置成功
            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("POI周边优质酒店邀约推荐成功");
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("POI周边优质酒店邀约推荐异常");
        } finally {
            if(countDownLatch != null) {
                countDownLatch.countDown();
            }

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }

        return new AsyncResult<>(response);
    }

    private GeneratePoiStatVO generatePoiStatVO(List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoResponseList){
        // 500以上均价酒店
        int up500HotelCount = 0;
        int _400_500HotelCount = 0;
        int _300_400HotelCount = 0;
        int _200_300HotelCount = 0;
        int down200HotelCount = 0;
        // 五星级 19
        int fiveStarHotelCount = 0;
        // 豪华型 (29)
        int quisaFiveStarHotelCount = 0;
        //  四星级/高档型百分比 (39,49)
        int fourAndQuisaFourStarHotelCount = 0;
        //  三星/舒适型百分比 (59,64)
        int threeAndQuisaThreeStarHotelCount = 0;
        //  经济型、(69)
        int quisaTwoStarHotelCount = 0;
        //  二星及以下/公寓、(66,79)
        int twoAndDownStarHotelCount = 0;
        // 五星级 19
        int fiveStarHotelRoomNightCount = 0;
        // 豪华型 (29)
        int quisaFiveStarHotelRoomNightCount = 0;
        //  四星级/高档型百分比 (39,49)
        int fourAndQuisaFourStarHotelRoomNightCount = 0;
        //  三星/舒适型百分比 (59,64)
        int threeAndQuisaThreeStarHotelRoomNightCount = 0;
        //  经济型、(69)
        int quisaTwoStarHotelRoomNightCount = 0;
        //  二星及以下/公寓、(66,79)
        int twoAndDownStarHotelRoomNightCount = 0;
        // 五星级 19
        List<Long> fiveStarHotelIds = new ArrayList<>();
        // 豪华型 (29)
        List<Long> quisaFiveStarHotelIds = new ArrayList<>();
        //  四星级/高档型百分比 (39,49)
        List<Long> fourAndQuisaFourStarHotelIds = new ArrayList<>();
        //  三星/舒适型百分比 (59,64)
        List<Long> threeAndQuisaThreeStarHotelIds = new ArrayList<>();
        //  经济型、(69)
        List<Long> quisaTwoStarHotelIds = new ArrayList<>();
        //  二星及以下/公寓、(66,79)
        List<Long> twoAndDownStarHotelIds = new ArrayList<>();
        // 五星级 19
        int fiveStarHotelMaxRoomNightCount= 0;
        // 豪华型 (29)
        int quisaFiveStarHotelMaxRoomNightCount= 0;
        //  四星级/高档型百分比 (39,49)
        int fourAndQuisaFourStarHotelMaxRoomNightCount= 0;
        //  三星/舒适型百分比 (59,64)
        int threeAndQuisaThreeStarHotelMaxRoomNightCount= 0;
        //  经济型、(69)
        int quisaTwoStarHotelMaxRoomNightCount= 0;
        //  二星及以下/公寓、(66,79)
        int twoAndDownStarHotelMaxRoomNightCount= 0;

        BigDecimal price500 = new BigDecimal(500);
        BigDecimal price400 = new BigDecimal(400);
        BigDecimal price300 = new BigDecimal(300);
        BigDecimal price200 = new BigDecimal(200);
        BigDecimal bigDecimal100 = new BigDecimal("100");
        for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : queryHistoryProjectInfoResponseList){
            if(queryHistoryProjectInfoResponse.getRoomNightCount() == 0){
                continue;
            }
            BigDecimal avgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(new BigDecimal(queryHistoryProjectInfoResponse.getRoomNightCount()), 0, RoundingMode.HALF_UP);
            if(avgPrice.compareTo(BigDecimal.ZERO) == 0){
                continue;
            }
            // 价格统计
            if(avgPrice.compareTo(price500) >= 0){
                up500HotelCount++;
            } else if(avgPrice.compareTo(price400) >= 0){
                _400_500HotelCount++;
            } else if(avgPrice.compareTo(price300) >= 0){
                _300_400HotelCount++;
            } else if(avgPrice.compareTo(price200) >= 0){
                _200_300HotelCount++;
            } else {
                down200HotelCount++;
            }
            // 星级统计
            if(StringUtils.isEmpty(queryHistoryProjectInfoResponse.getHotelStar())){
                continue;
            }
            if(queryHistoryProjectInfoResponse.getHotelStar().equals("19")){
                fiveStarHotelCount++;
                fiveStarHotelRoomNightCount = fiveStarHotelRoomNightCount + queryHistoryProjectInfoResponse.getRoomNightCount();
                if(queryHistoryProjectInfoResponse.getRoomNightCount() > fiveStarHotelMaxRoomNightCount){
                    fiveStarHotelMaxRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                    fiveStarHotelIds.clear();
                    fiveStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                } else if(queryHistoryProjectInfoResponse.getRoomNightCount() == fiveStarHotelMaxRoomNightCount){
                    fiveStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                }
            } else if(queryHistoryProjectInfoResponse.getHotelStar().equals("29")){
                quisaFiveStarHotelCount++;
                quisaFiveStarHotelRoomNightCount = quisaFiveStarHotelRoomNightCount + + queryHistoryProjectInfoResponse.getRoomNightCount();
                if(queryHistoryProjectInfoResponse.getRoomNightCount() > quisaFiveStarHotelMaxRoomNightCount){
                    quisaFiveStarHotelMaxRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                    quisaFiveStarHotelIds.clear();
                    quisaFiveStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                } else if(queryHistoryProjectInfoResponse.getRoomNightCount() == quisaFiveStarHotelMaxRoomNightCount){
                    quisaFiveStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                }
            } else if (queryHistoryProjectInfoResponse.getHotelStar().equals("39") || queryHistoryProjectInfoResponse.getHotelStar().equals("49")){
                fourAndQuisaFourStarHotelCount++;
                fourAndQuisaFourStarHotelRoomNightCount = fourAndQuisaFourStarHotelRoomNightCount + queryHistoryProjectInfoResponse.getRoomNightCount();
                if(queryHistoryProjectInfoResponse.getRoomNightCount() > fourAndQuisaFourStarHotelMaxRoomNightCount){
                    fourAndQuisaFourStarHotelMaxRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                    fourAndQuisaFourStarHotelIds.clear();
                    fourAndQuisaFourStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                } else if(queryHistoryProjectInfoResponse.getRoomNightCount() == fourAndQuisaFourStarHotelMaxRoomNightCount){
                    fourAndQuisaFourStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                }
            } else if (queryHistoryProjectInfoResponse.getHotelStar().equals("59") || queryHistoryProjectInfoResponse.getHotelStar().equals("64")){
                threeAndQuisaThreeStarHotelCount++;
                threeAndQuisaThreeStarHotelRoomNightCount = threeAndQuisaThreeStarHotelRoomNightCount + queryHistoryProjectInfoResponse.getRoomNightCount();
                if(queryHistoryProjectInfoResponse.getRoomNightCount() > threeAndQuisaThreeStarHotelMaxRoomNightCount){
                    threeAndQuisaThreeStarHotelMaxRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                    threeAndQuisaThreeStarHotelIds.clear();
                    threeAndQuisaThreeStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                } else if(queryHistoryProjectInfoResponse.getRoomNightCount() == threeAndQuisaThreeStarHotelMaxRoomNightCount){
                    threeAndQuisaThreeStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                }
            } else if (queryHistoryProjectInfoResponse.getHotelStar().equals("66")){
                quisaTwoStarHotelCount++;
                quisaTwoStarHotelRoomNightCount = quisaTwoStarHotelRoomNightCount + queryHistoryProjectInfoResponse.getRoomNightCount();
                if(queryHistoryProjectInfoResponse.getRoomNightCount() > quisaTwoStarHotelMaxRoomNightCount){
                    quisaTwoStarHotelMaxRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                    quisaTwoStarHotelIds.clear();
                    quisaTwoStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                } else if(queryHistoryProjectInfoResponse.getRoomNightCount() == quisaTwoStarHotelMaxRoomNightCount){
                    quisaTwoStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                }
            } else if (queryHistoryProjectInfoResponse.getHotelStar().equals("69") || queryHistoryProjectInfoResponse.getHotelStar().equals("79")){
                twoAndDownStarHotelCount++;
                twoAndDownStarHotelRoomNightCount = twoAndDownStarHotelRoomNightCount + queryHistoryProjectInfoResponse.getRoomNightCount();
                if(queryHistoryProjectInfoResponse.getRoomNightCount() > twoAndDownStarHotelMaxRoomNightCount){
                    twoAndDownStarHotelMaxRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                    twoAndDownStarHotelIds.clear();
                    twoAndDownStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                } else if(queryHistoryProjectInfoResponse.getRoomNightCount() == twoAndDownStarHotelMaxRoomNightCount){
                    twoAndDownStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                }
            }
        }

        // 计算POI统计
        RecommendNearPoiStatInfo recommendNearPoiStat = new RecommendNearPoiStatInfo();
        int totalHotelCount = up500HotelCount + _400_500HotelCount + _300_400HotelCount + _200_300HotelCount + down200HotelCount;
        BigDecimal totalHotelCountDecimal = new BigDecimal(totalHotelCount);
        int totalStarHotelCount = fiveStarHotelCount + quisaFiveStarHotelCount + fourAndQuisaFourStarHotelCount +
                threeAndQuisaThreeStarHotelCount + quisaTwoStarHotelCount + twoAndDownStarHotelCount;
        BigDecimal totalStarHotelCountDecimal = new BigDecimal(totalStarHotelCount);
        // 计算均价统计百分比
        if(totalHotelCount > 0){
            if(up500HotelCount > 0){
                recommendNearPoiStat.setUp500Percentage(new BigDecimal(up500HotelCount).divide(totalHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(_400_500HotelCount > 0){
                recommendNearPoiStat.set_400_500Percentage(new BigDecimal(_400_500HotelCount).divide(totalHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(_300_400HotelCount > 0){
                recommendNearPoiStat.set_300_400Percentage(new BigDecimal(_300_400HotelCount).divide(totalHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(_200_300HotelCount > 0){
                recommendNearPoiStat.set_200_300Percentage(new BigDecimal(_200_300HotelCount).divide(totalHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(down200HotelCount > 0){
                recommendNearPoiStat.setDown200Percentage(new BigDecimal(down200HotelCount).divide(totalHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            BigDecimal totalPercentage = recommendNearPoiStat.getUp500Percentage().add(recommendNearPoiStat.get_400_500Percentage())
                    .add(recommendNearPoiStat.get_300_400Percentage()).add(recommendNearPoiStat.get_200_300Percentage())
                    .add(recommendNearPoiStat.getDown200Percentage());
            BigDecimal adjustPercentage = bigDecimal100.subtract(totalPercentage);
            if(adjustPercentage.compareTo(BigDecimal.ZERO) != 0) {
                if (up500HotelCount > 0) {
                    recommendNearPoiStat.setUp500Percentage(recommendNearPoiStat.getUp500Percentage().add(adjustPercentage));
                } else if (_400_500HotelCount > 0) {
                    recommendNearPoiStat.set_400_500Percentage(recommendNearPoiStat.get_400_500Percentage().add(adjustPercentage));
                } else if (_300_400HotelCount > 0) {
                    recommendNearPoiStat.set_300_400Percentage(recommendNearPoiStat.get_300_400Percentage().add(adjustPercentage));
                } else if (_200_300HotelCount > 0) {
                    recommendNearPoiStat.set_200_300Percentage(recommendNearPoiStat.get_200_300Percentage().add(adjustPercentage));
                } else if (down200HotelCount > 0) {
                    recommendNearPoiStat.setDown200Percentage(recommendNearPoiStat.getDown200Percentage().add(adjustPercentage));
                }
            }
        }
        // 计算星级分布百分比
        if(totalStarHotelCount > 0){
            if(fiveStarHotelCount > 0){
                recommendNearPoiStat.setFiveStarPercentage(new BigDecimal(fiveStarHotelCount).divide(totalStarHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(quisaFiveStarHotelCount > 0){
                recommendNearPoiStat.setQuisaFiveStarPercentage(new BigDecimal(quisaFiveStarHotelCount).divide(totalStarHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(fourAndQuisaFourStarHotelCount > 0){
                recommendNearPoiStat.setFourAndQuisaFourStarPercentage(new BigDecimal(fourAndQuisaFourStarHotelCount).divide(totalStarHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(threeAndQuisaThreeStarHotelCount > 0){
                recommendNearPoiStat.setThreeAndQuisaThreeStarPercentage(new BigDecimal(threeAndQuisaThreeStarHotelCount).divide(totalStarHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(quisaTwoStarHotelCount > 0){
                recommendNearPoiStat.setQuisaTwoStarPercentage(new BigDecimal(quisaTwoStarHotelCount).divide(totalStarHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(twoAndDownStarHotelCount > 0){
                recommendNearPoiStat.setTwoAndDownStarPercentage(new BigDecimal(twoAndDownStarHotelCount).divide(totalStarHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            BigDecimal totalPercentage = recommendNearPoiStat.getFiveStarPercentage().add(recommendNearPoiStat.getQuisaFiveStarPercentage())
                    .add(recommendNearPoiStat.getFourAndQuisaFourStarPercentage()).add(recommendNearPoiStat.getThreeAndQuisaThreeStarPercentage())
                    .add(recommendNearPoiStat.getQuisaTwoStarPercentage()).add(recommendNearPoiStat.getTwoAndDownStarPercentage());
            BigDecimal adjustPercentage = bigDecimal100.subtract(totalPercentage);
            if(adjustPercentage.compareTo(BigDecimal.ZERO) != 0) {
                if (fiveStarHotelCount > 0) {
                    recommendNearPoiStat.setFiveStarPercentage(recommendNearPoiStat.getFiveStarPercentage().add(adjustPercentage));
                } else if (quisaFiveStarHotelCount > 0) {
                    recommendNearPoiStat.setQuisaFiveStarPercentage(recommendNearPoiStat.getQuisaFiveStarPercentage().add(adjustPercentage));
                } else if (fourAndQuisaFourStarHotelCount > 0) {
                    recommendNearPoiStat.setFourAndQuisaFourStarPercentage(recommendNearPoiStat.getFourAndQuisaFourStarPercentage().add(adjustPercentage));
                } else if (threeAndQuisaThreeStarHotelCount > 0) {
                    recommendNearPoiStat.setThreeAndQuisaThreeStarPercentage(recommendNearPoiStat.getThreeAndQuisaThreeStarPercentage().add(adjustPercentage));
                } else if (quisaTwoStarHotelCount > 0) {
                    recommendNearPoiStat.setQuisaTwoStarPercentage(recommendNearPoiStat.getQuisaTwoStarPercentage().add(adjustPercentage));
                } else if (twoAndDownStarHotelCount > 0) {
                    recommendNearPoiStat.setTwoAndDownStarPercentage(recommendNearPoiStat.getTwoAndDownStarPercentage().add(adjustPercentage));
                }
            }
        }

        GeneratePoiStatVO generatePoiStatVO = new GeneratePoiStatVO();
        // 五星级 19
        generatePoiStatVO.setFiveStarHotelRoomNightCount(fiveStarHotelRoomNightCount);
        // 豪华型 (29)
        generatePoiStatVO.setQuisaFiveStarHotelRoomNightCount(quisaFiveStarHotelRoomNightCount);
        //  四星级/高档型百分比 (39,49)
        generatePoiStatVO.setFourAndQuisaFourStarHotelRoomNightCount(fourAndQuisaFourStarHotelRoomNightCount);
        //  三星/舒适型百分比 (59,64)
        generatePoiStatVO.setThreeAndQuisaThreeStarHotelRoomNightCount(threeAndQuisaThreeStarHotelRoomNightCount);
        //  经济型、(69)
        generatePoiStatVO.setQuisaTwoStarHotelRoomNightCount(quisaTwoStarHotelRoomNightCount);
        //  二星及以下/公寓、(66,79)
        generatePoiStatVO.setTwoAndDownStarHotelRoomNightCount(twoAndDownStarHotelRoomNightCount);

        // 五星级 19
        generatePoiStatVO.setFiveStarHotelIds(fiveStarHotelIds);
        // 豪华型 (29)
        generatePoiStatVO.setQuisaFiveStarHotelIds(quisaFiveStarHotelIds);
        //  四星级/高档型百分比 (39,49)
        generatePoiStatVO.setFourAndQuisaFourStarHotelIds(fourAndQuisaFourStarHotelIds);
        //  三星/舒适型百分比 (59,64)
        generatePoiStatVO.setThreeAndQuisaThreeStarHotelIds(threeAndQuisaThreeStarHotelIds);
        //  经济型、(69)
        generatePoiStatVO.setQuisaTwoStarHotelIds(quisaTwoStarHotelIds);
        //  二星及以下/公寓、(66,79)
        generatePoiStatVO.setTwoAndDownStarHotelIds(twoAndDownStarHotelIds);

        generatePoiStatVO.setRecommendNearPoiStat(recommendNearPoiStat);
        return generatePoiStatVO;
    }

    public Future<Response> generateAreaGatherRecommend(String statReferenceNo, UserSession userDTO, ProjectEntity project) {
        return null;
    }

    public Future<Response> generateNoPoiHotAreaRecommend(String statReferenceNo, UserSession userDTO, ProjectEntity project,
                                                          List<QueryHistoryProjectInfoResponse> historyProjectInfoList,
                                                          CountDownLatch countDownLatch) {
        return null;
    }

    private void addRecommendForLast12MonthRoomNight(List<Long> hotelIdList, String lastYearDateStr, String todayDateStr,
                                                     Map<Long, ProjectHistoryRecommendEntity> theSameLevelRecommendMap){
        // 一个高产酒店周边同档（同星级分组）的酒店，商旅近12个月产量超过600间夜推荐邀约（不限客户，看预订监控该酒店所有分销商总和间夜数）。
        Lists.partition(hotelIdList, 1000).forEach(subHotelIds -> {
            QueryRoomNightsDto queryRoomNightsDto = new QueryRoomNightsDto();
            queryRoomNightsDto.setHotelIds(hotelIdList);
            queryRoomNightsDto.setStartTime(lastYearDateStr);
            queryRoomNightsDto.setEndTime(todayDateStr);
            List<HotelRoomNightCountDto> hotelRoomNightCountDtoList = disHotelDailyOrderMapper.selectHotelRoomNights(queryRoomNightsDto);
            for(HotelRoomNightCountDto hotelRoomNightCountDto : hotelRoomNightCountDtoList){
                if (hotelRoomNightCountDto.getRoomNightCount() != null && hotelRoomNightCountDto.getRoomNightCount() >= 600) {
                    ProjectHistoryRecommendEntity projectHistoryRecommend = theSameLevelRecommendMap.get(hotelRoomNightCountDto.getHotelId());
                    if(projectHistoryRecommend != null && projectHistoryRecommend.getIsSameLevelFreqRecommend() == RfpConstant.constant_1){ //IsSameLevelRreqRecommend
                        List<Integer> recommends = JsonUtil.jsonToList(projectHistoryRecommend.getSameLevelFreqRecommends(), Integer.class);
                        recommends.add(RecommendFrequencySameLevelEnum.LAST_YEAR_ROOM_NIGHT_600.key);
                        projectHistoryRecommend.setSameLevelFreqRecommends(JsonUtil.objectToJson(recommends));
                    } else if(projectHistoryRecommend != null && projectHistoryRecommend.getIsSameLevelFreqRecommend() == RfpConstant.constant_0) {
                        projectHistoryRecommend.setIsSameLevelFreqRecommend(RfpConstant.constant_1);
                        List<Integer> recommends = new ArrayList<>();
                        recommends.add(RecommendFrequencySameLevelEnum.LAST_YEAR_ROOM_NIGHT_600.key);
                        projectHistoryRecommend.setSameLevelFreqRecommends(JsonUtil.objectToJson(recommends));
                    }
                }
            }
        });
    }

    private void addPoiNearRecommendForLast12MonthRoomNight(List<Long> hotelIdList, String lastYearDateStr, String todayDateStr,
                                                            Map<Long, ProjectHistoryRecommendEntity> projectHistoryRecommendMap){
        // 一个高产酒店周边同档（同星级分组）的酒店，商旅近12个月产量超过600间夜推荐邀约（不限客户，看预订监控该酒店所有分销商总和间夜数）。
        Lists.partition(hotelIdList, 1000).forEach(subHotelIds -> {
            QueryRoomNightsDto queryRoomNightsDto = new QueryRoomNightsDto();
            queryRoomNightsDto.setHotelIds(hotelIdList);
            queryRoomNightsDto.setStartTime(lastYearDateStr);
            queryRoomNightsDto.setEndTime(todayDateStr);
            List<HotelRoomNightCountDto> hotelRoomNightCountDtoList = disHotelDailyOrderMapper.selectHotelRoomNights(queryRoomNightsDto);
            for(HotelRoomNightCountDto hotelRoomNightCountDto : hotelRoomNightCountDtoList){
                if (hotelRoomNightCountDto.getRoomNightCount() != null && hotelRoomNightCountDto.getRoomNightCount() >= 600) {
                    ProjectHistoryRecommendEntity projectHistoryRecommend = projectHistoryRecommendMap.get(hotelRoomNightCountDto.getHotelId());
                    if(projectHistoryRecommend != null && projectHistoryRecommend.getIsPoiNearHotelRecommend() == RfpConstant.constant_1){ //IsPoiNearHotelRecommend
                        List<Integer> recommends = JsonUtil.json2Collection(projectHistoryRecommend.getPoiNearHotelRecommends(), Integer.class);
                        recommends.add(RecommendPoiNearHotelEnum.LAST_YEAR_ROOM_NIGHT_600.key);
                        projectHistoryRecommend.setPoiNearHotelRecommends(JsonUtil.objectToJson(recommends));
                    } else if(projectHistoryRecommend != null && projectHistoryRecommend.getIsPoiNearHotelRecommend() == RfpConstant.constant_0) {
                        projectHistoryRecommend.setIsPoiNearHotelRecommend(RfpConstant.constant_1);
                        List<Integer> recommends = new ArrayList<>();
                        recommends.add(RecommendPoiNearHotelEnum.LAST_YEAR_ROOM_NIGHT_600.key);
                        projectHistoryRecommend.setPoiNearHotelRecommends(JsonUtil.objectToJson(recommends));
                    }
                }
            }
        });
    }

    private BigDecimal queryMaxSavedAmountRate(Long hotelId, String startTime, String endTime){
        DisHotelDailyOrderRequest disHotelDailyOrderRequest = new DisHotelDailyOrderRequest();
        disHotelDailyOrderRequest.setHotelId(hotelId);
        disHotelDailyOrderRequest.setStartTime(startTime);
        disHotelDailyOrderRequest.setEndTime(endTime);
        List<HotelSavedAmountDto> hotelSavedAmountDtoList = disHotelDailyOrderMapper.selectHotelSavedAmount(disHotelDailyOrderRequest)
                .stream().filter(o -> o.getSavedAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        BigDecimal maxSaveAmountRate = BigDecimal.ZERO;

        if (CollUtil.isNotEmpty(hotelSavedAmountDtoList)) {
            for (HotelSavedAmountDto hotelSavedAmountDto : hotelSavedAmountDtoList) {
                BigDecimal savedAmountRate = hotelSavedAmountDto.getSavedAmount().divide(hotelSavedAmountDto.getTotalOtaPrice(), 2, RoundingMode.HALF_UP);
                if (maxSaveAmountRate.compareTo(BigDecimal.ZERO) == 0 || savedAmountRate.compareTo(maxSaveAmountRate) > 0) {
                    maxSaveAmountRate = savedAmountRate;
                }
            }
        }
        return maxSaveAmountRate;
    }

    /**
     * 生成混合价格
     */
    private BigDecimal generateAdjustLowestPrice(BigDecimal lowestPrice){
        BigDecimal adjustLowestPrice = lowestPrice;
        if(lowestPrice.compareTo(new BigDecimal("500")) > 0){
            adjustLowestPrice = adjustLowestPrice.add(new BigDecimal("50"));
        } else if(lowestPrice.compareTo(new BigDecimal("400")) > 0){
            adjustLowestPrice = adjustLowestPrice.add(new BigDecimal("30"));
        } else if(lowestPrice.compareTo(new BigDecimal("300")) > 0){
            adjustLowestPrice = adjustLowestPrice.add(new BigDecimal("20"));
        }
        return adjustLowestPrice;
    }

    // 是否完成统计
    private boolean isStatTaskFinished(Integer projectId, String statReferenceNo, String statName, int loopCount){
        boolean isFinishedSucceeded = false;
        ProjectRecommendStatLogEntity updateLowestPriceProjectRecommendStatLog = null;
        for(int i = 0; i < loopCount; i++){
            updateLowestPriceProjectRecommendStatLog = projectRecommendStatLogMapper.getProjectRecommendStatLog(projectId, statReferenceNo, statName);
            if(updateLowestPriceProjectRecommendStatLog == null){ // 为空 默认通过
                isFinishedSucceeded = true;
                break;
            }
            if(updateLowestPriceProjectRecommendStatLog.getIsFinished() == RfpConstant.constant_1 &&
                    updateLowestPriceProjectRecommendStatLog.getResult() == ResultEnum.SUCCESS.key){
                isFinishedSucceeded = true;
                break;
            }
            try {
                Thread.sleep(5000L);
            } catch (Exception ex){
                logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            }
        }
        if(!isFinishedSucceeded) {
            logger.info(statName + "失败，不能生成推荐 {}", JsonUtil.objectToJson(updateLowestPriceProjectRecommendStatLog));
        }
        return isFinishedSucceeded;
    }

    private Map<String, List<QueryHistoryProjectInfoResponse>> convertToHotelStarGroupMap(List<QueryHistoryProjectInfoResponse> historyProjectInfoList){
        Map<String, List<QueryHistoryProjectInfoResponse>> resultMap = new HashMap<>();
        Map<String, List<QueryHistoryProjectInfoResponse>> cityHotelStarGroupMap = historyProjectInfoList.stream().filter(o -> StringUtils.isNotEmpty(o.getHotelStar())).collect(Collectors.groupingBy(QueryHistoryProjectInfoResponse::getHotelStar));
        historyProjectInfoList.forEach(item -> {
            if(StringUtils.isEmpty(item.getHotelStar())){
                logger.error("存在空星级酒店 {}", JsonUtil.objectToJson(item));
            }
        });
        for(String hotelStar : cityHotelStarGroupMap.keySet()){
            HotelStarGroupEnum hotelStarGroupEnum = HotelStarGroupEnum.getEnumByHotelStar(hotelStar);
            if(hotelStarGroupEnum == null){
                logger.error("酒店星级分组为空 {}", hotelStar);
                continue;
            }
            if(resultMap.containsKey(hotelStarGroupEnum.name())){
                resultMap.get(hotelStarGroupEnum.name()).addAll(cityHotelStarGroupMap.get(hotelStar));
            } else {
                resultMap.put(hotelStarGroupEnum.name(), cityHotelStarGroupMap.get(hotelStar));
            }
        }

        return resultMap;
    }

    // 是否是同价位
    // 签约参考价  or   混淆价格 去找 历史交易数据里面所有酒店的  成交均价   进行匹配，在上面这个区间范围内的就是他的同价位
    private boolean isTheSameLevelPrice(BigDecimal basePrice,  QueryHistoryProjectInfoResponse historyProjectInfo){
        int avgPrice = historyProjectInfo.getTotalAmount().divide(BigDecimal.valueOf(historyProjectInfo.getRoomNightCount()), 2, RoundingMode.HALF_UP).intValue();
        // 记录日志
        if(Objects.equals(historyProjectInfo.getHotelId(), recordLogHotelId)){
            logger.info("avgPrice {} distanceHistoryProjectInfoResponse hotelId: {}", avgPrice, historyProjectInfo.getHotelId());
        }
        if (basePrice.subtract(new BigDecimal(avgPrice)).abs().intValue() <= 50) {
           return true;
        }
        return false;
    }
}
