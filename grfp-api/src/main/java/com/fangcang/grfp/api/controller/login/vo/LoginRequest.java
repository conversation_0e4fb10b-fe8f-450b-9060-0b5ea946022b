package com.fangcang.grfp.api.controller.login.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel("登录请求")
@Getter
@Setter
public class LoginRequest extends BaseVO {

    @ApiModelProperty(value="登录账号", required = true)
    @NotBlank
    private String account;

    @ApiModelProperty(value="登录类型 3:邮箱和验证码登录,4:邮箱和密码登录", required = true)
    @NotNull
    private Integer loginType;

    @ApiModelProperty(value="密码")
    private String password;

    @ApiModelProperty(value="验证码")
    private String verifyCode;
}
