package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.base.ImportErrorVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(description = "导入记录信息响应")
@Getter
@Setter
public class ImportRecordVO extends BaseVO {

    private static final long serialVersionUID = -8570176741752724825L;

    @ApiModelProperty(value = "导入记录ID")
    private Long sysImportRecordId;

    @ApiModelProperty(value = "导入名称")
    private String importName;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "导入 oss 路径")
    private String importPath;

    @ApiModelProperty(value = "失败原因")
    private ImportErrorVO failRemark;

    @ApiModelProperty(value = "状态 1:处理中 2:成功, 3:失败")
    private Integer status;

}
