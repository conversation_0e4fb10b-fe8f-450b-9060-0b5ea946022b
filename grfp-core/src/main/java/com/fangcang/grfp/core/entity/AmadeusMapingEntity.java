package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * amadeus酒店映射
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_amadeus_maping")
public class AmadeusMapingEntity extends BaseVO {


    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 酒店名称
     */
    @TableField("hotel_name")
    private String hotelName;

    /**
     * amaduesu 酒店ID
     */
    @TableField("sp_hotel_id")
    private String spHotelId;

    /**
     * amaduesu 酒店名称
     */
    @TableField("sp_hotel_name")
    private String spHotelName;


}
