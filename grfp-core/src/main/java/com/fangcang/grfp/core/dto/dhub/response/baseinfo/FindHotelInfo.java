package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 根据目的地查询酒店
 */
@Data
@ApiModel("根据目的地查询酒店信息")
public class FindHotelInfo extends BaseVO {

    /**
     * 城市编码
     */
    @ApiModelProperty("城市编码")
    private String cityCode;

    /**
     * 城市名称
     */
    @ApiModelProperty("城市名称")
    private String cityName;

    /**
     * 酒店ID
     */
    @ApiModelProperty("酒店ID")
    private Long hotelId;

    /**
     * 酒店名称
     */
    @ApiModelProperty("酒店名称")
    private String hotelName;

    /**
     * 酒店星级
     */
    @ApiModelProperty("酒店星级")
    private Integer hotelStar;

    /**
     * 酒店地址
     */
    @ApiModelProperty("酒店地址")
    private String hotelAddress;

    /**
     * 最低价
     */
    @ApiModelProperty("最低价")
    private BigDecimal lowestPrice;

    /**
     * 售价币种编码
     */
    @ApiModelProperty("售价币种编码")
    private String saleCurrency;

    /**
     * 售价币种
     */
    @ApiModelProperty("售价币种")
    private String saleCurrencyCode;

    /**
     * 主图
     */
    @ApiModelProperty("主图")
    private String mainUrl;
    /**
     * 行政区名称
     */
    @ApiModelProperty("行政区名称")
    private String districtName;
    /**
     * 商业区名称
     */
    @ApiModelProperty("商业区名称")
    private String businessName;

    /**
     * 酒店评分
     */
    @ApiModelProperty("酒店评分")
    private BigDecimal hotelScore;

    /**
     * 国家编码
     */
    @ApiModelProperty("国家编码")
    private String countryCode;

    /**
     *到店付费用
     */
    @ApiModelProperty("到店付费用")
    private BigDecimal payAtHotelFee;

    /**
     * 到店付币种编码
     */
    @ApiModelProperty("到店付币种编码")
    private String payAtHotelCurrency;

    /**
     * 到店付币种
     */
    @ApiModelProperty("到店付币种")
    private String payAtHotelCurrencyCode;
    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private BigDecimal longitude;
    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    private BigDecimal latitude;
    /**
     * 谷歌经度
     */
    @ApiModelProperty("谷歌经度")
    private BigDecimal lngGoogle;
    /**
     * 谷歌纬度
     */
    @ApiModelProperty("谷歌纬度")
    private BigDecimal latGoogle;

    /**
     * 酒店推荐id
     */
    @ApiModelProperty("酒店推荐id")
    private String hotelRecommendId;

    /**
     * 距离
     */
    @ApiModelProperty("距离")
    private BigDecimal distance;

    /**
     * 标签名称列表
     */
    @ApiModelProperty("标签名称列表")
    private List<String> hotelLabelNameList;

}
