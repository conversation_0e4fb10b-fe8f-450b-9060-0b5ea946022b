package com.fangcang.grfp.api.controller.hotelgroup;

import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.hotel.vo.GetBidTemplateRequest;
import com.fangcang.grfp.api.controller.hotelgroup.service.HotelGroupService;
import com.fangcang.grfp.api.controller.hotelgroup.vo.*;
import com.fangcang.grfp.api.controller.project.vo.ExportTenderPriceRequest;
import com.fangcang.grfp.api.controller.project.vo.ProjectBidStatCountVO;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.IdRequest;
import com.fangcang.grfp.core.base.IdVO;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.enums.OrgTypeEnum;
import com.fangcang.grfp.core.manager.BidMapManager;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.vo.BidHotelPriceGroupVO;
import com.fangcang.grfp.core.vo.BidHotelPriceLevelInfoVO;
import com.fangcang.grfp.core.vo.request.IntentHotelRequest;
import com.fangcang.grfp.core.vo.request.QueryBidMapHotelInfoRequest;
import com.fangcang.grfp.core.vo.request.QueryHotelGroupBidMapHotelListRequest;
import com.fangcang.grfp.core.vo.request.bidmap.*;
import com.fangcang.grfp.core.vo.request.hotelgroup.QueryHotelGroupApprovalRequest;
import com.fangcang.grfp.core.vo.request.hotelgroup.QueryInvitedSingleHotelRequest;
import com.fangcang.grfp.core.vo.request.hotelgroup.QueryProjectOverviewRequest;
import com.fangcang.grfp.core.vo.response.bidmap.GoToHotelBidResponse;
import com.fangcang.grfp.core.vo.response.bidmap.HotelBidDetailResponse;
import com.fangcang.grfp.core.vo.response.bidmap.QueryBidMapHotelInfoResponse;
import com.fangcang.grfp.core.vo.response.hotelgroup.*;
import com.fangcang.grfp.core.vo.response.recommendhotel.BidRecommendHotelInfoQueryResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@Api(tags="酒店集团")
@RestController
@RequestMapping("/Api/HotelGroup")
public class HotelGroupController extends BaseController {

    @Autowired
    private HotelGroupService hotelGroupService;
    @Autowired
    private BidMapManager bidMapManager;



    @ApiOperation("酒店集团查询报价统计数据")
    @PostMapping("/QueryBidStatCount")
    @ResponseBody
    public Result<QueryBidStatCountVO> queryBidStatCount(HttpServletRequest request) {
        return Result.ok(hotelGroupService.queryBidStatCount(request));
    }

    @ApiOperation("酒店集团签约项目列表查询")
    @PostMapping("/QueryProjectOverview")
    @ResponseBody
    public Result<PageVO<QueryProjectOverviewResponse>> queryProjectOverview(HttpServletRequest request, @Valid @RequestBody QueryProjectOverviewRequest queryProjectOverviewRequest) {
        return Result.ok(hotelGroupService.queryProjectOverview(request, queryProjectOverviewRequest));
    }

    @ApiOperation("报价模板查询 查询id:项目ID")
    @PostMapping("/QueryHotelGroupBidTemplate")
    public Result<QueryHotelGroupBidTemplateResponse> queryHotelGroupBidTemplate(HttpServletRequest request, @Valid @RequestBody IdRequest<Integer> req) {
        return Result.ok(hotelGroupService.queryHotelGroupBidTemplate(request, req.getId()));
    }

    @ApiOperation("更新报价模板")
    @PostMapping("/UpdateHotelGroupBidTemplate")
    @UserAuditLog("酒店集团-更新报价模板")
    public Result<Void> updateHotelGroupBidTemplate(HttpServletRequest request, @Valid @RequestBody UpdateHotelGroupBidTemplateRequest updateHotelGroupBidTemplateRequest) {
        hotelGroupService.updateHotelGroupBidTemplate(request, updateHotelGroupBidTemplateRequest);
        return Result.ok();
    }

    @ApiOperation("酒店集团报价查询推荐酒店")
    @PostMapping("/QueryBidMapRecommendHotelList")
    public Result<PageVO<BidRecommendHotelInfoQueryResponse>> queryBidMapRecommendHotelList(HttpServletRequest request, @Valid @RequestBody QueryHotelGroupBidMapHotelListRequest req) {
        return Result.ok(bidMapManager.queryBidMapRecommendHotelList(request, req));
    }

    @ApiOperation("查询酒店集团意向酒店")
    @PostMapping("/QueryBidMapInvitedHotelList")
    public Result<PageVO<BidRecommendHotelInfoQueryResponse>> queryBidMapInvitedHotelList(HttpServletRequest request, @Valid @RequestBody QueryHotelGroupBidMapHotelListRequest req) {
        return Result.ok(bidMapManager.queryBidMapInvitedHotelList(request, req));
    }

    @ApiOperation("查询酒店集团意向酒店城市统计")
    @PostMapping("/QueryProjectInviteCityStat")
    public Result<ProjectBidStatCountVO> queryProjectInviteCityStat(HttpServletRequest request, @Valid @RequestBody QueryProjectInviteCityStatRequest req) {
        return Result.ok(hotelGroupService.queryProjectInviteCityStat(request, req.getProjectId()));
    }




    @ApiOperation("酒店集团地图报价(右上角)")
    @PostMapping("/QueryHotelGroupHotelBidStatus")
    public Result<QueryHotelGroupHotelBidStatusResponse> queryHotelGroupHotelBidStatus(HttpServletRequest request, @Valid @RequestBody QueryBidMapHotelInfoRequest req) {
        return Result.ok(hotelGroupService.queryHotelGroupHotelBidStatus(req));
    }

    @ApiOperation("酒店集团通知酒店报价(右上角)")
    @PostMapping("/NotifyHotelBid")
    @UserAuditLog("酒店集团-酒店集团通知酒店报价")
    public Result<Integer> notifyHotelBid(HttpServletRequest request, @Valid @RequestBody QueryBidMapHotelInfoRequest req) {
        return Result.ok( bidMapManager.notifyHotelBid(req));
    }

    @ApiOperation("地图报价查询酒店信息")
    @PostMapping("/QueryBidMapHotelInfo")
    public Result<QueryBidMapHotelInfoResponse> queryBidMapHotelInfo(HttpServletRequest request, @Valid @RequestBody QueryHotelGroupBidMapHotelListRequest req) {
        return Result.ok(bidMapManager.queryBidMapHotelInfo(request, req));
    }

    @ApiOperation("地图酒店集团代酒店报价")
    @PostMapping("/GoToHotelBid")
    public Result<GoToHotelBidResponse> goToHotelBid(HttpServletRequest request, @Valid @RequestBody GoToHotelBidRequest req) {
        return Result.ok(bidMapManager.goToHotelBid(request, req));
    }

    @ApiOperation("新增酒店集团房档 返回房档ID")
    @PostMapping("/AddHotelPriceLevel")
    @UserAuditLog("酒店集团-新增酒店集团房档")
    public Result<Integer> addHotelPriceLevel(HttpServletRequest request, @Valid @RequestBody AddHotelPriceLevelRequest req) {
        return Result.ok(bidMapManager.addHotelPriceLevel(request, req));
    }

    @ApiOperation("查询酒店集团报价房档列表 返回房档")
    @PostMapping("/QueryBidHotelPriceLevelList")
    public Result<List<BidHotelPriceLevelInfoVO>> queryHotelPriceLevelList(HttpServletRequest request, @Valid @RequestBody QueryHotelPriceLevelListRequest req) {
        return Result.ok(bidMapManager.querBidHotelPriceLevelInfoList(UserSession.get(), AppUtility.getRequestHeaderLanguage(request), req.getProjectIntentHotelId(), null, req.getHotelId(), null));
    }

    @ApiOperation("新增活修改酒店集团房档房型 返回房档ID")
    @PostMapping("/AddOrUpdateHotelPriceLevelRoomInfo")
    @UserAuditLog("酒店集团-新增活修改酒店集团房档房型")
    public Result<Integer> addOrUpdateHotelPriceLevelRoomInfo(HttpServletRequest request, @Valid @RequestBody AddHotelPriceLevelRoomRequest req) {
        return Result.ok(bidMapManager.addOrUpdateHotelPriceLevelRoomInfo(request, req));
    }

    @ApiOperation("新增或者修改酒店集团价格组 返回价格组ID")
    @PostMapping("/AddOrUpdateHotelPriceGroup")
    @UserAuditLog("酒店集团-新增或者修改酒店集团价格组")
    public Result<Integer> addOrUpdateHotelPriceGroup(HttpServletRequest request, @Valid @RequestBody BidHotelPriceGroupVO req) {
        return Result.ok(bidMapManager.addOrUpdateHotelPriceGroup(request, req));
    }

    @ApiOperation("删除酒店集团价格组 传hotelPriceGroupId")
    @PostMapping("/DeleteHotelPriceGroup")
    @UserAuditLog("酒店集团-删除酒店集团价格组")
    public Result<Void> deleteHotelPriceGroup(HttpServletRequest request, @Valid @RequestBody IdRequest<Integer> req) {
        bidMapManager.deleteHotelPriceGroup(request, req);
        return Result.ok();
    }

    @ApiOperation("提交酒店集团报价")
    @PostMapping("/SubmitHotelBid")
    public Result<Integer> submitHotelBid(HttpServletRequest request, @Valid @RequestBody SubmitHotelBidRequest req) {
        return Result.ok(bidMapManager.submitHotelBid(request, req));
    }

    @ApiOperation("保存酒店报价模板")
    @PostMapping("/SaveBidTemplate")
    @UserAuditLog("酒店集团-保存酒店报价模板")
    public Result<Void> saveBidTemplate(HttpServletRequest request, @RequestBody SubmitHotelBidRequest req) {
        bidMapManager.saveBidTemplate(request, req);
        return Result.ok();
    }

    @ApiOperation("获取酒店报价模板")
    @PostMapping("/GetBidTemplate")
    public Result<SubmitHotelBidRequest> getBidTemplate(HttpServletRequest request, @Valid @RequestBody GetBidTemplateRequest req) {
        return Result.ok(bidMapManager.getBidTemplate(req.getProjectIntentHotelId()));
    }

    @ApiOperation("酒店集团查看报价详情")
    @PostMapping("/QueryHotelBidDetail")
    public Result<HotelBidDetailResponse> queryHotelBidDetail(HttpServletRequest request, @Valid @RequestBody HotelBidDetailRequest req) {
        return Result.ok(bidMapManager.queryHotelBidDetail(request, req));
    }

    @ApiOperation("查询酒店集团下被邀请的单体酒店")
    @PostMapping("/QueryInvitedSingleHotels")
    public Result<PageVO<InvitedSingleHotelResponse>> queryInvitedSingleHotels(HttpServletRequest request, @Valid @RequestBody QueryInvitedSingleHotelRequest req) {
        return Result.ok(hotelGroupService.queryInvitedSingleHotels(request, req));
    }

    @ApiOperation("查询酒店集团审核列表")
    @PostMapping("/QueryHotelGroupApprovalList")
    public Result<PageVO<HotelGroupApprovalResponse>> queryHotelGroupApprovalList(HttpServletRequest request, @Valid @RequestBody QueryHotelGroupApprovalRequest req) {
        return Result.ok(hotelGroupService.queryHotelGroupApprovalList(request, req));
    }

    @ApiOperation("酒店集团拒绝议价")
    @PostMapping("/RejectNegotiation")
    @UserAuditLog("酒店集团-酒店集团拒绝议价")
    public Result<Void> rejectNegotiation(HttpServletRequest request, @Valid @RequestBody RejectNegotiationRequest req) {
        bidMapManager.rejectNegotiation(req);
        return Result.ok();
    }

    @ApiOperation("酒店集团更新审核")
    @PostMapping("/UpdateGroupApprove")
    @UserAuditLog("酒店集团-酒店集团更新审核")
    public Result<Void> updateGroupApprove(HttpServletRequest request, @Valid @RequestBody UpdateGroupApproveRequest req) {
        hotelGroupService.updateOpenGroupApprove(req);
        return Result.ok();
    }

    @ApiOperation("酒店集团导出报价")
    @PostMapping("/ExportTenderPrice")
    @UserAuditLog("酒店集团-酒店集团导出报价")
    public void exportTenderPrice(@Valid @RequestBody ExportTenderPriceRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        hotelGroupService.exportTenderPrice(request, httpServletRequest, httpServletResponse);
    }

    @ApiOperation("酒店集团导入标准报价")
    @PostMapping("/UploadStandardBid")
    @UserAuditLog("酒店集团-酒店集团导入标准报价")
    public Result<IdVO<Long>> uploadStandardBid(@RequestParam("projectId") Integer projectId, @RequestParam("file") MultipartFile file) throws IOException {
        // 校验是否酒店集团
        UserSession userSession = UserSession.get();
        if (!userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.HOTELGROUP.key)) {
            AppUtility.serviceError(ErrorCode.PERMISSION_DENIED);
        }
        return Result.ok(hotelGroupService.uploadStandardBid(file, projectId));
    }

    @ApiOperation("酒店集团导入Lanyon报价")
    @PostMapping("/UploadLanyonBid")
    @UserAuditLog("酒店集团-酒店集团导入Lanyon报价")
    public Result<IdVO<Long>> uploadLanyonBid(@RequestParam("projectId") Integer projectId, @RequestParam("file") MultipartFile file) throws Exception {
        // 校验是否酒店集团
        UserSession userSession = UserSession.get();
        if (!userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.HOTELGROUP.key)) {
            AppUtility.serviceError(ErrorCode.PERMISSION_DENIED);
        }
        return Result.ok(hotelGroupService.uploadLanyonBid(file, projectId));
    }

    /**
     * 审核通过报价
     * @return
     */
    @ApiOperation("审核通过报价")
    @UserAuditLog("酒店集团-审核通过报价")
    @RequestMapping(value = "ApproveBid", method = RequestMethod.POST)
    public Result<Void> approveBid(@RequestBody ApproveBidRequest approveBidRequest) {
        hotelGroupService.hotelGroupApproveBid(approveBidRequest.getProjectIntentHotelId());
        return Result.ok();
    }

    /**
     * 审核拒绝报价
     * @return
     */
    @ApiOperation("审核拒绝报价")
    @UserAuditLog("酒店集团-审核拒绝报价")
    @RequestMapping(value = "RejectBid", method = RequestMethod.POST)
    public Result<Void> rejectBid(@RequestBody RejectBidRequest rejectBidRequest) {
        hotelGroupService.hotelGroupRejectBid(rejectBidRequest.getProjectIntentHotelId(), rejectBidRequest.getRemark());
        return Result.ok();
    }

    @ApiOperation("酒店集团修改酒店销售人员")
    @PostMapping("/UpdateBidHotelSales")
    @UserAuditLog("酒店集团-酒店集团修改酒店销售人员")
    public Result<Void> updateBidHotelSales(HttpServletRequest request, @Valid @RequestBody UpdateBidHotelSalesUserRequest req) {
        bidMapManager.updateBidHotelSales(req);
        return Result.ok();
    }

    @ApiOperation("检查报价项目信息")
    @PostMapping("/IsHotelSatisfyProjectBaseInfo")
    public Result<Void> isHotelSatisfyProjectBaseInfo(HttpServletRequest request, @Valid @RequestBody IntentHotelRequest req) {
        bidMapManager.isHotelSatisfyProjectBaseInfo(req);
        return Result.ok();
    }


}
