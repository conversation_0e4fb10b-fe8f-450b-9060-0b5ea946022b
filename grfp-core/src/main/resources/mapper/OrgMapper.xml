<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.OrgMapper">
    <select id="queryOrgVOPageList" resultType="com.fangcang.grfp.core.vo.ListOrgVO"
            parameterType="com.fangcang.grfp.core.vo.request.ListOrgRequest">
        SELECT
            t1.`org_id` AS orgId,
            t1.`org_name` AS orgName,
            t1.`org_type` AS orgType,
            t1.`contact_name` AS contactName,
            t1.`contact_mobile_area_code` AS contactMobileAreaCode,
            t1.`contact_mobile` AS contactMobile,
            t1.`contact_email` AS contactEmail,
            t1.`logo_url` AS logoUrl,
            t1.`state` AS state
        FROM
            `t_org` t1
        <where>
            t1.`org_type` != 1
            <if test="query.orgId != null and query.orgId != ''">
                AND `org_id` = #{query.orgId}
            </if>
            <if test="query.orgName != null and query.orgName != ''">
               AND t1.`org_name` like CONCAT('%',#{query.orgName},'%')
            </if>
            <if test="query.orgType != null and query.orgType > 0">
                AND t1.`org_type` = #{query.orgType}
            </if>
            <if test="query.hotelId != null and query.hotelId > 0">
                AND t1.`org_id` IN (SELECT `org_id` FROM `t_org_related_hotel` WHERE `hotel_id` = #{query.hotelId})
            </if>
        </where>
        ORDER BY t1.`create_time` DESC
    </select>

    <select id="queryOrgListByName" resultType="com.fangcang.grfp.core.vo.OrgNameVO">
        SELECT
            `org_id` AS orgId,
            `org_name` AS orgName,
            `org_type` AS orgType
        FROM
            `t_org`
        <where>
            `state`  = 1
            AND `org_name` like CONCAT('%',#{orgName},'%')
        <if test="orgTypeIdList != null and orgTypeIdList.size() > 0">
            AND `org_type` IN
            <foreach item="item" index="index" collection="orgTypeIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        </where>
            ORDER BY `org_name` ASC
        <if test="limitCount != null and limitCount > 0">
            LIMIT #{limitCount}
        </if>
    </select>

    <select id="queryHotelOrgList" resultType="com.fangcang.grfp.core.vo.HotelRelatedOrgContactInfoVO">
        SELECT
            t1.org_id AS orgId,
            t1.hotel_id AS hotelId,
            o.contact_name AS contactName,
            o.contact_mobile_area_code AS contactMobileAreaCode,
            o.contact_mobile AS contactMobile,
            o.contact_email AS contactEmail
            FROM t_org_related_hotel t1
        LEFT JOIN t_org o ON t1.org_id = o.org_id
        WHERE t1.hotel_id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getOrgByHotelId" resultType="com.fangcang.grfp.core.entity.OrgEntity" >
        select
         *
        from t_org
        where state = 1 and org_id in (
                SELECT org_id FROM T_ORG_RELATED_HOTEL WHERE hotel_id = #{hotelId,jdbcType=INTEGER}
        )
    </select>


    <!-- 根据酒店ID获取机构信息 -->
    <select id="getOrgByHotelIds" resultType="com.fangcang.grfp.core.entity.OrgEntity" parameterType="java.util.List">
        select *
        from t_org
        where state = 1
          and org_id in (
        SELECT org_id
        FROM T_ORG_RELATED_HOTEL WHERE hotel_id IN
        <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
            #{hotelId}
        </foreach>
        )
    </select>
</mapper>
