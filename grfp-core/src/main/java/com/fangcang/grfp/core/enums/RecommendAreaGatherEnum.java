 package com.fangcang.grfp.core.enums;

 /**
  * 散布聚量推荐原因
  */
 public enum RecommendAreaGatherEnum {

    AREA_ROOM_NIGHT_FIRST(1, "区域聚量，推荐去年员工预订最多酒店"), //当满足条件为区域聚量推荐，且推荐区域内去年员工预订最多的酒店
    AREA_LOWEST_PRICE(2, "区域聚量，推荐去年高性价比酒店"), //当满足条件为区域聚量推荐，且推荐区域内去年预订均价最低酒店
     CITY_ROOM_NIGHT_FIRST(3, "城市聚量，推荐去年员工预订最多酒店"), // 当满足条件为城市聚量推荐，且推荐该城市内去年员工预订最多的酒店
     CITY_LOWEST_PRICE(4, "城市聚量，推荐去年高性价比酒店"); // 当满足条件为城市聚量推荐，且推荐城市内去年预订均价最低酒店

    public int key;

    public String value;
    RecommendAreaGatherEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (RecommendAreaGatherEnum recommendAreaGatherEnum : RecommendAreaGatherEnum.values()) {
            if (recommendAreaGatherEnum.key == key) {
                value = recommendAreaGatherEnum.value;
                break;
            }
        }
        return value;
    }
}
