package com.fangcang.grfp.core.vo.request.project;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("分页查询项目意向酒店集团")
public class QueryInviteHotelGroupRequest extends PageQuery {

    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    @ApiModelProperty("集团名称")
    private String groupName;

    @ApiModelProperty("意向状态：null-全部，1-已加入，0-未加入")
    private Integer inviteState;
}
