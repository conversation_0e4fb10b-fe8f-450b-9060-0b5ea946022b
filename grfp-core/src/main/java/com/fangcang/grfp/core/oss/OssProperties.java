package com.fangcang.grfp.core.oss;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * cos 配置

 */
@Getter
@Setter
@ConfigurationProperties(prefix = "grfp.oss")
public class OssProperties {

    /**
     * oss 提供商 cos: 腾讯云 oss, tos: 火山云
     */
    private String provider;

    /**
     * endpoint
     */
    private String endpoint;

    /**
     * cos region
     */
    private String region;

    /**
     * cos id
     */
    private String accessKeyId;

    /**
     * cos 密钥
     */
    private String accessKeySecret;

    /**
     * 公开桶, 名称
     */
    private String bucketPublic;

    /**
     * 公开桶, 访问地址
     */
    private String bucketPublicDomain;

    /**
     * 临时桶名称
     */
    private String bucketTemp;

    /**
     * 临时桶访问地址
     */
    private String bucketTempDomain;
}
