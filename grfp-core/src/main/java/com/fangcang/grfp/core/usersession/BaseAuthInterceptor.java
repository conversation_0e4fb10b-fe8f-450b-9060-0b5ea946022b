package com.fangcang.grfp.core.usersession;


import com.fangcang.grfp.core.base.AppLogicException;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.util.SessionUtility;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证拦截器基类. @Anon 的方法不需要登录
 *
 */
public abstract class BaseAuthInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        // 未登录访问需登录的接口 没有Anon 并且session是nul
        if (!this.hasAnonAnnotation((HandlerMethod) handler) && SessionUtility.getAttribute(sessionKey()) == null) {
            throw new AppLogicException(Result.UNAUTHENTICATED_CODE, ErrorCode.INVALIDATE_USER_SESSION_PLEASE_LOGIN);
        }
        return true;
    }

    /**
     * 表示登录的 session attribute name. 不同类型的用户不能一样
     */
    public abstract String sessionKey();

    /**
     * 从方法和类上取 Anon 注解
     */
    protected boolean hasAnonAnnotation(HandlerMethod handlerMethod) {
        Boolean hasAnnotation = methodCache.get(handlerMethod);
        if (hasAnnotation != null) {
            return hasAnnotation.booleanValue();
        }

        Anon anon = handlerMethod.getMethod().getAnnotation(Anon.class);
        if (anon == null) {
            // 如果方法上的注解为空 则获取类的注解
            anon = handlerMethod.getMethod().getDeclaringClass().getAnnotation(Anon.class);
        }
        boolean result = anon != null;
        methodCache.put(handlerMethod, result);
        return result;
    }

    /**
     * 方法注解缓存
     */
    protected Map<HandlerMethod, Boolean> methodCache = new HashMap<>();

}
