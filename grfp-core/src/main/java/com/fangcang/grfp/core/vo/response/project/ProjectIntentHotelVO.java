package com.fangcang.grfp.core.vo.response.project;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.jackson.OssPublicJsonSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@ApiModel("项目意向酒店信息")
public class ProjectIntentHotelVO extends BaseVO {

    @ApiModelProperty("项目意向酒店ID")
    private Long projectIntentHotelId;

    @ApiModelProperty("项目ID")
    private Long projectId;

    @ApiModelProperty("酒店ID")
    private Long hotelId;

    @ApiModelProperty("酒店名称")
    private String hotelName;

    @ApiModelProperty(value = "酒店Logo", notes = "存储的是COS文件Key")
    @JsonSerialize(using = OssPublicJsonSerializer.class)
    private String hotelImageUrl;

    @ApiModelProperty("酒店星级")
    private String hotelStar;
    @ApiModelProperty("酒店星级描述")
    private String hotelStarDesc ;

    @ApiModelProperty("酒店评分")
    private BigDecimal rating;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("酒店地址")
    private String hotelAddress;
    
    @ApiModelProperty("酒店电话")
    private String telephone;

    @ApiModelProperty("近一年采购间夜数")
    private Integer lastYearRoomNight;
    
    @ApiModelProperty("采购均价")
    private BigDecimal tenderAvgPrice;

    @ApiModelProperty("酒店销售联系人")
    private String contactName;

    @ApiModelProperty("酒店销售联系人手机")
    private String contactMobile;

    @ApiModelProperty("酒店销售联系人邮箱")
    private String contactEmail;

    @ApiModelProperty(value = "币种代码")
    private String currencyCode;

    @ApiModelProperty(value = "企业跟进人")
    private String distributorContactName;

    @ApiModelProperty(value = "平台跟进人")
    private String platformContactName;
    
    @ApiModelProperty("投标状态(0：未投标，1：新标(酒店提交报价，企业还未处理)，2：议价中，3：已中标，4：已否决，5：放弃报价)")
    private Integer bidState;

    @ApiModelProperty("邀约邮件发送状态(0：未发送邀约邮件，1：已发送邀约邮件)")
    private Integer sendMailStatus;

    @ApiModelProperty("邀约状态(0：未邀请，1：已邀请)，企业主动邀请的记录默认就是已邀请，非企业邀请的酒店指派销售人时默认就是未邀请")
    private Integer inviteStatus;

    @ApiModelProperty(hidden = true)
    private String hotelNameEnUs;
    @ApiModelProperty(hidden = true)
    private String hotelNameZhCn;
    @ApiModelProperty(hidden = true)
    private String hotelAddressEnUs;
    @ApiModelProperty(hidden = true)
    private String hotelAddressZhCn;
    @ApiModelProperty(hidden = true)
    private String cityNameEnUs;
    @ApiModelProperty(hidden = true)
    private String cityNameZhCn;
    @ApiModelProperty(hidden = true)
    private String cityCode;
} 