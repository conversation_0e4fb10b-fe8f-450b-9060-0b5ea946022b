package com.fangcang.grfp.api.controller.user.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel("启用/停用用户请求")
@Getter
@Setter
public class UpdateUserStateRequest extends BaseVO {

    @ApiModelProperty(value = "用户ID")
    @NotNull
    private Integer userId;

    @ApiModelProperty(value = "用户状态")
    @NotNull
    private Integer state;

}
