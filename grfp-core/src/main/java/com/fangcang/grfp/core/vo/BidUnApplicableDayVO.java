package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 项目意向酒店集团默认适用日期
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@ApiModel("报价不适用日期")
@Getter
@Setter
public class BidUnApplicableDayVO extends BaseVO {


    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone=RfpConstant.TIME_ZONE)
    private Date startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone=RfpConstant.TIME_ZONE)
    private Date endDate;



}
