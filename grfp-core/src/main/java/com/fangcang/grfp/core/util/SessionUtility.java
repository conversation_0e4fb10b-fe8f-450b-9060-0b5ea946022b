package com.fangcang.grfp.core.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpSession;

/**
 * session 工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class SessionUtility {

    public static HttpSession getSession(boolean created) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            log.error("RequestContextHolder.getRequestAttributes() is null, spring mvc config error");
            return null;
        }

        return requestAttributes.getRequest().getSession(created);
    }

    public static HttpSession getSession() {
        return getSession(true);
    }

    @SuppressWarnings("unchecked")
    public static <T> T getAttribute(String key) {
        HttpSession session = getSession(false);
        return (null != session ? (T) session.getAttribute(key) : null);
    }

    public static void setAttribute(String key, Object value) {
        getSession().setAttribute(key, value);
    }

    public static void removeAttribute(String key) {
        getSession().removeAttribute(key);
    }

}
