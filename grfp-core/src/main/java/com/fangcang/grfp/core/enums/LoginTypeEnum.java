package com.fangcang.grfp.core.enums;

/**
 * <AUTHOR>
 * @ClassName LoginType
 * @Description 登录方式
 * @createTime 2022-08-26 20:18:00
 */
public enum LoginTypeEnum {

    MobileAndPwd(0, "手机号和密码登录"), MobileAndMsgCode(1, "手机号和验证码登录"), OutOrgAndMobile(2, "外部机构编码和手机号登录"),
    EmailAndMsgCode(3, "邮箱和验证码登录"), EmailAndPwd(4, "邮箱和密码登录")
    ;

    public Integer key;
    public String value;

    LoginTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static Integer getKeyByValue(String value) {
        Integer key = 0;
        for (LoginTypeEnum loginTypeEnum : LoginTypeEnum.values()) {
            if (loginTypeEnum.value.equals(value)) {
                key = loginTypeEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (LoginTypeEnum loginTypeEnum : LoginTypeEnum.values()) {
            if (loginTypeEnum.key.equals(key)) {
                value = loginTypeEnum.value;
                break;
            }
        }
        return value;
    }

    public static LoginTypeEnum getEnumByKey(Integer key) {
        LoginTypeEnum loginTypeEnum = null;
        for (LoginTypeEnum loginType : LoginTypeEnum.values()) {
            if (loginType.key.equals(key)) {
                loginTypeEnum = loginType;
                break;
            }
        }
        return loginTypeEnum;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
