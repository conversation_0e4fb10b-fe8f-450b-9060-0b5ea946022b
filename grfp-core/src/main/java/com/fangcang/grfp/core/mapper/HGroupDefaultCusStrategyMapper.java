package com.fangcang.grfp.core.mapper;

import com.fangcang.grfp.core.entity.HGroupDefaultCusStrategyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.vo.HGroupDefaultCusStrategyVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 酒店集团默认报价自定义策略 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
public interface HGroupDefaultCusStrategyMapper extends BaseMapper<HGroupDefaultCusStrategyEntity> {

    /**
     * 查询列表
     */
    List<HGroupDefaultCusStrategyVO> queryHGroupDefaultCusStrategy(int projectIntentHotelGroupId);

    /**
     * 批量更新或者信息
     */
    int batchMergeHotelGroupDefaultCustomStrategy(@Param("list") List<HGroupDefaultCusStrategyVO> hGroupDefaultCusStrategyList);
}
