package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 导入记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sys_import_record")
public class SysImportRecordEntity extends BaseVO {


    /**
     * 系统导入记录 ID
     */
    @TableId(value = "sys_import_record_id", type = IdType.AUTO)
    private Long sysImportRecordId;

    /**
     * 导入名称, 区别不同业务, 如导入POI
     */
    @TableField("import_name")
    private String importName;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 导入 oss 路径
     */
    @TableField("import_path")
    private String importPath;

    /**
     * 状态 1:处理中 2:成功, 3:失败
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 失败原因, 注意截断, 防止超长
     */
    @TableField("fail_remark")
    private String failRemark;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
