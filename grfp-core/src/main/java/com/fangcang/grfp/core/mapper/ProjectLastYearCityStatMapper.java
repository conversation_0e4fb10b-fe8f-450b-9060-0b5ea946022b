package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.ProjectLastYearCityStatEntity;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 项目去年城市统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
public interface ProjectLastYearCityStatMapper extends BaseMapper<ProjectLastYearCityStatEntity> {

    /**
     * 根据项目ID和城市编码查询
     */
    ProjectLastYearCityStatEntity selectByProjectIdAndCityCode(@Param("projectId") Integer projectId, @Param("cityCode") String cityCode);

    /**
     * 插入或更新
     */
    void upsert(ProjectLastYearCityStatEntity projectLastYearCityStat);
}
