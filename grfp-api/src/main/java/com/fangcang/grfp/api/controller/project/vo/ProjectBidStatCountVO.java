package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.ProjectBidStatCountItemVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel("报价统计信息")
@Getter
@Setter
public class ProjectBidStatCountVO extends BaseVO {

    @ApiModelProperty("项目ID")
    private Integer projectId;

    @ApiModelProperty("国家")
    private List<ProjectBidStatCountItemVO> countryList;

    @ApiModelProperty("城市")
    private List<ProjectBidStatCountItemVO> cityList;

    @ApiModelProperty("酒店集团")
    private List<ProjectBidStatCountItemVO> hotelGroupList;

    @ApiModelProperty("酒店品牌")
    private List<ProjectBidStatCountItemVO> hotelBrandList;
}
