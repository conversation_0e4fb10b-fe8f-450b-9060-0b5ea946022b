package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fangcang.grfp.core.entity.ProjectLanyonViewKeysEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 项目lanyon显示key Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface ProjectLanyonViewKeysMapper extends BaseMapper<ProjectLanyonViewKeysEntity> {

    default ProjectLanyonViewKeysEntity queryByProjectId(int projectId) {
        LambdaQueryWrapper<ProjectLanyonViewKeysEntity>lambdaQueryWrapper = new LambdaQueryWrapper<ProjectLanyonViewKeysEntity>();
        lambdaQueryWrapper.eq(ProjectLanyonViewKeysEntity::getProjectId, projectId);
        return this.selectOne(lambdaQueryWrapper);
    }
}
