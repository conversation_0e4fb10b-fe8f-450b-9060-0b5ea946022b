package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.core.entity.ProjectIntentHotelGroupEntity;
import com.fangcang.grfp.core.vo.BidStateCountVO;
import com.fangcang.grfp.core.vo.request.hotelgroup.QueryProjectOverviewRequest;
import com.fangcang.grfp.core.vo.request.project.QueryInviteHotelGroupRequest;
import com.fangcang.grfp.core.vo.response.hotelgroup.QueryProjectOverviewResponse;
import com.fangcang.grfp.core.vo.response.project.InviteHotelGroupVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-01
 */
public interface ProjectIntentHotelGroupMapper extends BaseMapper<ProjectIntentHotelGroupEntity> {

    default ProjectIntentHotelGroupEntity queryByProjectAndHotelGroupId(Integer projectId, Integer hotelGroupOrgId) {
        LambdaQueryWrapper<ProjectIntentHotelGroupEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectIntentHotelGroupEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectIntentHotelGroupEntity::getHotelGroupOrgId, hotelGroupOrgId);
        return selectOne(queryWrapper);
    }

    default List<ProjectIntentHotelGroupEntity> queryByProjectAndHotelGroupOrgId(Integer projectId, Integer hotelGroupOrgId) {
        LambdaQueryWrapper<ProjectIntentHotelGroupEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectIntentHotelGroupEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectIntentHotelGroupEntity::getHotelGroupOrgId, hotelGroupOrgId);
        return selectList(queryWrapper);
    }

    /**
     *  分页查询项目意向酒店集团
     */
    Page<InviteHotelGroupVO> queryInviteHotelGroup(IPage<?> page, @Param("request") QueryInviteHotelGroupRequest req);

    /**
     * 查询酒店集团被邀约数量
     */
    int selectInvitedCount(
            @Param("hotelGroupOrgId") Integer hotelGroupOrgId,
            @Param("hotelBrandIdList") Collection<Long> hotelBrandIdList,
            @Param("employeeUserId") Integer employeeUserId);

    /**
     * 查询酒店集团报价数量统计
     */
    List<BidStateCountVO> queryBidStatCount(
            @Param("hotelGroupOrgId") Integer hotelGroupOrgId,
            @Param("hotelGroupBrandIdList") Collection<Long> hotelGroupBrandIdList,
            @Param("employeeUserId") Integer employeeUserId);

    /**
     * 查询未报价数量
     */
    int queryNoBidCount(
            @Param("hotelGroupOrgId") Integer hotelGroupOrgId,
            @Param("hotelGroupBrandIdList") Collection<Long> hotelGroupBrandIdList,
            @Param("employeeUserId") Integer employeeUserId);


    /**
     * 查询签约项目列表
     */
    Page<QueryProjectOverviewResponse> queryProjectOverview(IPage<?> page, @Param("query") QueryProjectOverviewRequest queryProjectOverviewRequest);


    /**
     * 查询签约项目报价列表
     */
    Page<QueryProjectOverviewResponse> queryProjectBidInfo(IPage<?> page, @Param("query") QueryProjectOverviewRequest queryProjectOverviewRequest);

    /**
     * 查询最小的酒店集团意向ID
     */
    ProjectIntentHotelGroupEntity queryHotelGroupProjectIntentGroup(@Param("projectId") Integer projectId , @Param("hotelGroupOrgId") Integer hotelGroupOrgId);


    /**
     * 查询配置审核的酒店集团机构id
     */
    List<Integer> queryNeedApproveHotelGroupOrgId(@Param("projectId") Integer projectId);

}
