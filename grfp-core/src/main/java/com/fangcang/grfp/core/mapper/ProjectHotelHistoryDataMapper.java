package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.core.entity.ProjectHotelHistoryDataEntity;
import com.fangcang.grfp.core.vo.request.project.HotelHistoryTradeDataListRequest;
import com.fangcang.grfp.core.vo.response.project.BidHotelInfoQueryResponse;
import com.fangcang.grfp.core.vo.response.project.HotelHistoryTradeDataListVO;
import com.fangcang.grfp.core.vo.response.project.QueryHistoryProjectInfoResponse;
import org.apache.ibatis.annotations.Param;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 项目酒店历史交易表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
public interface ProjectHotelHistoryDataMapper extends BaseMapper<ProjectHotelHistoryDataEntity> {

    /**
     * 批量插入或更新
     */
    void batchUpsert(@Param("list") List<ProjectHotelHistoryDataEntity> list);

    /**
     * 查询项目酒店历史交易数据列表
     */
    Page<HotelHistoryTradeDataListVO> selectByCondition(Page<HotelHistoryTradeDataListVO> page, @Valid @Param("req") HotelHistoryTradeDataListRequest req);

    /**
     * 查询项目关联酒店历史数据
     */
    List<QueryHistoryProjectInfoResponse> queryHistoryProjectHotelList(@Param("projectId") Integer projectId, @Param("hotelId") Long hotelId, @Param("hotelIdList") List<Long> hotelIdList, @Param("cityCode") String cityCode, @Param("poiId") Integer poiId);

    /**
     * 更新项目酒店历史交易数据统计信息
     */
    void updateHistoryProjectDataStat(@Param("req") QueryHistoryProjectInfoResponse hotelHistoryData);

    /**
     * 批量更新项目酒店历史交易数据POI信息
     */
    void updateHistoryProjectPoi(@Param("req") QueryHistoryProjectInfoResponse hotelHistoryData);

    void updateLastStatReferenceNo(@Param("projectId") Integer projectId, @Param("statReferenceNo") String statReferenceNo, @Param("modifier") String modifier);


    void resetHistoryProjectHotelViolationsCount(Integer projectId);

    void updateHistoryProjectHotelViolationsCount(@Param("projectId") Integer projectId, @Param("violationsCount") Integer violationsCount, @Param("hotelIdList") List<Long> hotelIdList);

    void resetHistoryProjectHotelServicePoint(@Param("projectId") Integer projectId);

    void updateHistoryProjectHotelServicePoint(@Param("projectId") Integer projectId, @Param("servicePoint") BigDecimal servicePoint, @Param("hotelIdList") List<Long> hotelIdList);

    void updateHistoryProjectHotelMinMaxOtaPrice(@Param("projectId") Integer projectId, @Param("hotelId") Long hotelId, @Param("minPrice") BigDecimal minPrice,
                                                 @Param("maxPrice") BigDecimal maxPrice, @Param("minMaxOtaPriceDate") Date minMaxOtaPriceDate);

    List<Long> queryNeedUpdateLowestPriceHotelIds(@Param("projectId") Integer projectId);

    int updateHistoryProjectHotelLowestPrice(@Param("projectId") Integer projectId, @Param("hotelId") Long hotelId,
                                             @Param("lowestPrice") BigDecimal lowestPrice, @Param("adjustLowestPrice") BigDecimal adjustLowestPrice,
                                             @Param("lowestPriceDate") Date lowestPriceDate, @Param("lowestPriceItemInfo") String lowestPriceItemInfo);

    List<QueryHistoryProjectInfoResponse> queryAllRecommendHistoryProjectHotelList(@Param("projectId") Integer projectId,
                                                                                   @Param("cityCode") String cityCode);

    void clearHistoryProjectHotelRecommendLevel(@Param("projectId") Integer projectId);

    void updateRecommendLevelAndPriceLevelRoomNight(@Param("projectId") Integer projectId, @Param("hotelId") Long hotelId, @Param("recommendLevel") Integer recommendLevel,
                                                    @Param("priceLevelRoomNight") Long priceLevelRoomNight);

    void batchMergeHistoryResponse(@Param("historyProjects") List<QueryHistoryProjectInfoResponse> addQueryHistoryProjectInfoResponseList);

    List<QueryHistoryProjectInfoResponse> queryRecommendHistoryProjectHotelList(@Param("projectId") Integer projectId, @Param("cityCode") String cityCode);


}
