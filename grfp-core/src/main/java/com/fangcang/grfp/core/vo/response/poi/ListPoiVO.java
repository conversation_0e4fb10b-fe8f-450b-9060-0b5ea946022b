package com.fangcang.grfp.core.vo.response.poi;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel(description = "查询 POI 列表响应")
public class ListPoiVO extends BaseVO {

    private static final long serialVersionUID = -1807712627246473533L;

    @ApiModelProperty(value = "POI ID")
    private Long poiId;

    @ApiModelProperty(value = "POI 名称")
    private String poiName;

    @ApiModelProperty(value = "POI 地址")
    private String poiAddress;

    @ApiModelProperty(value = "机构 ID")
    private Integer orgId;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "地图 POI ID")
    private String mapPoiId;

    @ApiModelProperty(value = "Google 经度")
    private BigDecimal lngGoogle;

    @ApiModelProperty(value = "Google 纬度")
    private BigDecimal latGoogle;

    @ApiModelProperty(value = "状态(1：有效，0：无效)")
    private Integer state;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String modifier;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

}
