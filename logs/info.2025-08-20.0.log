2025-08-20 15:59:08.597|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 20192 (D:\RFP\grfp-project\grfp-api\target\classes started by <PERSON>hamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 15:59:08.611|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 15:59:10.274|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 15:59:10.277|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 15:59:10.325|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 32ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 15:59:11.504|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 15:59:11.520|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 15:59:11.520|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 15:59:11.693|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 15:59:11.693|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3014 ms  [] ------
2025-08-20 15:59:12.026|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 15:59:14.034|redisson-netty-2-26| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 15:59:15.328|redisson-netty-2-17| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 15:59:18.971|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 15:59:19.981|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 15:59:20.557|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 15:59:21.805|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 15:59:23.141|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 15:59:23.143|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 15:59:23.357|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 15:59:25.774|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 15:59:29.275|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 15:59:29.903|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 21.874 seconds (JVM running for 23.458)  [] ------
2025-08-20 15:59:29.917|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 15:59:30.200|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 15:59:52.480|main|ERROR|com.zaxxer.hikari.pool.HikariPool.throwPoolInitializationException:593|HikariPool-1 - Exception during pool initialization. com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:358)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:477)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:560)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:158)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:116)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:79)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy272.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy112.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy119.selectList(Unknown Source)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap(CachedHotelBrandServiceImpl.java:37)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl$$FastClassBySpringCGLIB$$e4014f52.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:53)
	at org.springframework.cache.interceptor.CacheAspectSupport.invokeOperation(CacheAspectSupport.java:366)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:421)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:346)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:61)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl$$EnhancerBySpringCGLIB$$c6594e29.getNameMap(<generated>)
	at com.fangcang.grfp.api.GRfpApiApplication.afterStarted(GRfpApiApplication.java:60)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:305)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:190)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:153)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:404)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:361)
	at org.springframework.boot.context.event.EventPublishingRunListener.running(EventPublishingRunListener.java:108)
	at org.springframework.boot.SpringApplicationRunListeners.running(SpringApplicationRunListeners.java:77)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:327)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at com.fangcang.grfp.api.GRfpApiApplication.main(GRfpApiApplication.java:46)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 73 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 76 common frames omitted
 [] ------
2025-08-20 15:59:52.531|main|ERROR|org.springframework.boot.SpringApplication.reportFailure:834|Application run failed org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/fangcang/grfp/core/mapper/HotelBrandMapper.java (best guess)
### The error may involve com.fangcang.grfp.core.mapper.HotelBrandMapper.selectList
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy112.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy119.selectList(Unknown Source)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap(CachedHotelBrandServiceImpl.java:37)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl$$FastClassBySpringCGLIB$$e4014f52.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:53)
	at org.springframework.cache.interceptor.CacheAspectSupport.invokeOperation(CacheAspectSupport.java:366)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:421)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:346)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:61)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl$$EnhancerBySpringCGLIB$$c6594e29.getNameMap(<generated>)
	at com.fangcang.grfp.api.GRfpApiApplication.afterStarted(GRfpApiApplication.java:60)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:305)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:190)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:153)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:404)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:361)
	at org.springframework.boot.context.event.EventPublishingRunListener.running(EventPublishingRunListener.java:108)
	at org.springframework.boot.SpringApplicationRunListeners.running(SpringApplicationRunListeners.java:77)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:327)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at com.fangcang.grfp.api.GRfpApiApplication.main(GRfpApiApplication.java:46)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/fangcang/grfp/core/mapper/HotelBrandMapper.java (best guess)
### The error may involve com.fangcang.grfp.core.mapper.HotelBrandMapper.selectList
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 41 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:82)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy272.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 48 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:358)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:477)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:560)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:158)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:116)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:79)
	... 60 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 73 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 76 common frames omitted
 [] ------
2025-08-20 15:59:52.900|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 15:59:52.901|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 15:59:52.902|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'generalExecutor'  [] ------
2025-08-20 15:59:52.918|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 15:59:52.922|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 15:59:52.922|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 15:59:52.922|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.onDestroy:71|GrfpApi v1.0.0-20250808 is already [***stopped***]  [] ------
2025-08-20 16:12:44.455|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 4604 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 16:12:44.457|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 16:12:46.172|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 16:12:46.175|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 16:12:46.226|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 34ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 16:12:47.639|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 16:12:47.659|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 16:12:47.660|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 16:12:47.833|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 16:12:47.834|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3294 ms  [] ------
2025-08-20 16:12:48.283|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 16:12:50.393|redisson-netty-2-22| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 16:12:51.892|redisson-netty-2-18| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 16:12:55.744|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 16:12:56.813|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 16:12:57.390|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 16:12:58.667|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 16:12:59.529|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 16:12:59.530|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 16:12:59.632|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 16:13:01.062|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 16:13:04.938|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 16:13:05.552|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 21.819 seconds (JVM running for 23.785)  [] ------
2025-08-20 16:13:05.562|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 16:13:05.811|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 16:13:07.168|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-20 16:13:08.183|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-20 16:13:08.908|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-20 16:13:09.912|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-20 16:13:10.511|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-20 16:13:25.681|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-20 16:13:41.122|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-20 16:15:49.137|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-20 16:15:49.137|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-20 16:15:49.158|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 20 ms  [] ------
2025-08-20 16:15:49.598|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a584350e00da4a94227f89] ------
2025-08-20 16:15:50.193|http-nio-6888-exec-1| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"401","data":null,"message":"Session expired, please login again","successful":false}  [68a584350e00da4a94227f89] ------
2025-08-20 16:15:50.353|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"401","data":null,"message":"Session expired, please login again","successful":false}  [] ------
2025-08-20 16:15:50.505|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 16:16:30.291|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a5845e0e00da4a94227f8a] ------
2025-08-20 16:16:30.484|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":5033155,"hotelWhiteType":1,"projectId":116}  [68a5845e0e00da4a94227f8a] ------
2025-08-20 16:16:31.017|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a5845e0e00da4a94227f8a] ------
2025-08-20 16:16:33.973|http-nio-6888-exec-5| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"BFB995EFF5FB7ADDF4D044490DDA089E","timestamp":1755677792630,"version":"1.0.0"}}  [68a5845e0e00da4a94227f8a] ------
2025-08-20 16:16:34.085|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: en-US  [68a5845e0e00da4a94227f8a] ------
2025-08-20 16:16:34.090|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a5845e0e00da4a94227f8a] ------
2025-08-20 16:16:35.799|http-nio-6888-exec-5| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"11C94DCEFCE97B5B6F27234930324BF4","timestamp":1755677795579,"version":"1.0.0"}}  [68a5845e0e00da4a94227f8a] ------
2025-08-20 16:16:35.801|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: zh-CN  [68a5845e0e00da4a94227f8a] ------
2025-08-20 16:16:36.035|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 5297  [68a5845e0e00da4a94227f8a] ------
2025-08-20 16:16:36.037|http-nio-6888-exec-5| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [68a5845e0e00da4a94227f8a] ------
2025-08-20 16:16:36.038|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [] ------
2025-08-20 16:16:36.924|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 16:19:10.537|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 21120 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 16:19:10.540|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 16:19:12.495|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 16:19:12.499|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 16:19:12.568|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 45ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 16:19:13.968|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 16:19:13.988|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 16:19:13.988|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 16:19:14.199|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 16:19:14.200|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3582 ms  [] ------
2025-08-20 16:19:14.773|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 16:19:17.879|redisson-netty-2-32| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 16:19:19.084|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 16:19:20.007|main| WARN|c.b.mybatisplus.core.metadata.TableInfoHelper.initTableFields:329|Can not find table primary key in Class: "com.fangcang.grfp.core.entity.AmadeusMapingEntity".  [] ------
2025-08-20 16:19:22.116|main| WARN|c.b.mybatisplus.core.metadata.TableInfoHelper.initTableFields:329|Can not find table primary key in Class: "com.fangcang.grfp.core.entity.ProjectRecommendStatLogEntity".  [] ------
2025-08-20 16:19:23.306|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 16:19:24.485|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 16:19:25.028|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 16:19:26.283|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 16:19:26.815|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 16:19:26.817|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 16:19:26.904|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 16:19:28.560|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 16:19:32.184|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 16:19:32.798|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 23.06 seconds (JVM running for 24.756)  [] ------
2025-08-20 16:19:32.809|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 16:19:33.071|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 16:19:34.905|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-20 16:45:03.315|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 21732 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 16:45:03.318|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 16:45:05.242|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 16:45:05.246|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 16:45:05.307|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 40ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 16:45:07.076|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 16:45:07.100|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 16:45:07.100|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 16:45:07.352|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 16:45:07.352|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3939 ms  [] ------
2025-08-20 16:45:07.743|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 16:45:09.985|redisson-netty-2-1| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 16:45:11.176|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 16:45:15.385|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 16:45:16.600|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 16:45:17.166|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 16:45:18.421|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 16:45:19.003|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 16:45:19.005|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 16:45:19.100|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 16:45:20.362|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 16:45:24.006|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 16:45:24.612|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 22.367 seconds (JVM running for 24.508)  [] ------
2025-08-20 16:45:24.624|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 16:45:24.884|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 16:45:26.312|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-20 16:45:27.170|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-20 16:45:27.744|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-20 16:45:28.258|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-20 16:45:28.718|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-20 16:45:42.036|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-20 16:45:56.016|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-20 16:46:38.619|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 12240 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 16:46:38.622|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 16:46:40.244|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 16:46:40.247|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 16:46:40.299|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 36ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 16:46:41.603|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 16:46:41.619|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 16:46:41.620|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 16:46:41.811|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 16:46:41.811|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3111 ms  [] ------
2025-08-20 16:46:42.220|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 16:46:44.490|redisson-netty-2-25| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 16:46:45.705|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 16:46:49.534|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 16:46:50.596|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 16:46:51.124|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 16:46:52.386|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 16:46:52.948|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 16:46:52.949|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 16:46:53.039|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 16:46:54.207|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 16:46:57.673|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 16:46:58.282|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 20.401 seconds (JVM running for 22.204)  [] ------
2025-08-20 16:46:58.292|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 16:46:58.547|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 16:47:00.002|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-20 16:47:00.911|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-20 16:47:01.473|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-20 16:47:01.948|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-20 16:47:02.353|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-20 16:47:15.778|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-20 16:47:29.990|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-20 16:47:44.400|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-20 16:47:44.400|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-20 16:47:44.421|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 21 ms  [] ------
2025-08-20 16:47:44.839|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a58bb00e0071477469e5f2] ------
2025-08-20 16:47:45.234|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":5033155,"hotelWhiteType":1,"projectId":116}  [68a58bb00e0071477469e5f2] ------
2025-08-20 16:47:45.872|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a58bb00e0071477469e5f2] ------
2025-08-20 16:47:48.302|http-nio-6888-exec-1| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"EF6397E0249DADB4742C775B70D5DF34","timestamp":1755679667343,"version":"1.0.0"}}  [68a58bb00e0071477469e5f2] ------
2025-08-20 16:47:48.402|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: en-US  [68a58bb00e0071477469e5f2] ------
2025-08-20 16:47:48.407|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a58bb00e0071477469e5f2] ------
2025-08-20 16:47:49.627|http-nio-6888-exec-1| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"43B9D446C07889AA3210945170263DC6","timestamp":1755679669505,"version":"1.0.0"}}  [68a58bb00e0071477469e5f2] ------
2025-08-20 16:47:49.630|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: zh-CN  [68a58bb00e0071477469e5f2] ------
2025-08-20 16:47:49.774|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 4178  [68a58bb00e0071477469e5f2] ------
2025-08-20 16:47:49.785|http-nio-6888-exec-1| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [68a58bb00e0071477469e5f2] ------
2025-08-20 16:47:49.837|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [] ------
2025-08-20 16:47:50.758|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 16:49:49.422|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a58c2d0e0071477469e5f3] ------
2025-08-20 16:49:49.481|http-nio-6888-exec-6| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":5033155,"hotelWhiteType":1,"projectId":116}  [68a58c2d0e0071477469e5f3] ------
2025-08-20 16:49:49.782|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a58c2d0e0071477469e5f3] ------
2025-08-20 16:49:51.521|http-nio-6888-exec-6| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"984AD53B8E4C31055DA4A1D176D0F131","timestamp":1755679791327,"version":"1.0.0"}}  [68a58c2d0e0071477469e5f3] ------
2025-08-20 16:49:51.523|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: en-US  [68a58c2d0e0071477469e5f3] ------
2025-08-20 16:49:51.524|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a58c2d0e0071477469e5f3] ------
2025-08-20 16:49:52.996|http-nio-6888-exec-6| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"B361693D37BCCA9B2C2C09FA5D8762EB","timestamp":1755679792877,"version":"1.0.0"}}  [68a58c2d0e0071477469e5f3] ------
2025-08-20 16:49:52.999|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: zh-CN  [68a58c2d0e0071477469e5f3] ------
2025-08-20 16:49:53.135|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 3653  [68a58c2d0e0071477469e5f3] ------
2025-08-20 16:49:53.137|http-nio-6888-exec-6| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [68a58c2d0e0071477469e5f3] ------
2025-08-20 16:49:53.138|http-nio-6888-exec-6| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [] ------
2025-08-20 16:49:54.060|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 16:51:43.632|http-nio-6888-exec-10| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a58c9f0e0071477469e5f4] ------
2025-08-20 16:51:43.690|http-nio-6888-exec-10| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":5033155,"hotelWhiteType":1,"projectId":116}  [68a58c9f0e0071477469e5f4] ------
2025-08-20 16:51:44.006|http-nio-6888-exec-10| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a58c9f0e0071477469e5f4] ------
2025-08-20 16:51:45.176|http-nio-6888-exec-10| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"6B601CABB5E502AD23C600B0FA56BC3B","timestamp":1755679905016,"version":"1.0.0"}}  [68a58c9f0e0071477469e5f4] ------
2025-08-20 16:51:45.180|http-nio-6888-exec-10| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: en-US  [68a58c9f0e0071477469e5f4] ------
2025-08-20 16:51:45.181|http-nio-6888-exec-10| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a58c9f0e0071477469e5f4] ------
2025-08-20 16:51:46.469|http-nio-6888-exec-10| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"7B55D7B8A0C1FC308DE2A04F1CACBB0B","timestamp":1755679906357,"version":"1.0.0"}}  [68a58c9f0e0071477469e5f4] ------
2025-08-20 16:51:46.471|http-nio-6888-exec-10| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: zh-CN  [68a58c9f0e0071477469e5f4] ------
2025-08-20 16:51:46.619|http-nio-6888-exec-10| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 2927  [68a58c9f0e0071477469e5f4] ------
2025-08-20 16:51:46.620|http-nio-6888-exec-10| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [68a58c9f0e0071477469e5f4] ------
2025-08-20 16:51:46.620|http-nio-6888-exec-10| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [] ------
2025-08-20 16:51:47.665|http-nio-6888-exec-10| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 16:57:15.212|http-nio-6888-exec-7| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a58deb0e0071477469e5f5] ------
2025-08-20 16:57:15.279|http-nio-6888-exec-7| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":5033155,"hotelWhiteType":1,"projectId":116}  [68a58deb0e0071477469e5f5] ------
2025-08-20 16:57:15.592|http-nio-6888-exec-7| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a58deb0e0071477469e5f5] ------
2025-08-20 16:57:16.853|http-nio-6888-exec-7| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"FEF8EA050F50C92DE522EF697EEBA99D","timestamp":1755680236604,"version":"1.0.0"}}  [68a58deb0e0071477469e5f5] ------
2025-08-20 16:57:16.858|http-nio-6888-exec-7| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: en-US  [68a58deb0e0071477469e5f5] ------
2025-08-20 16:57:16.859|http-nio-6888-exec-7| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a58deb0e0071477469e5f5] ------
2025-08-20 16:57:18.385|http-nio-6888-exec-7| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"3C31E55704B0EF0C08C7ED05297E9C3C","timestamp":1755680238256,"version":"1.0.0"}}  [68a58deb0e0071477469e5f5] ------
2025-08-20 16:57:18.387|http-nio-6888-exec-7| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: zh-CN  [68a58deb0e0071477469e5f5] ------
2025-08-20 16:57:18.516|http-nio-6888-exec-7| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 3236  [68a58deb0e0071477469e5f5] ------
2025-08-20 16:57:18.517|http-nio-6888-exec-7| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [68a58deb0e0071477469e5f5] ------
2025-08-20 16:57:18.517|http-nio-6888-exec-7| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [] ------
2025-08-20 16:57:19.437|http-nio-6888-exec-7| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 16:57:55.001|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 18968 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 16:57:55.004|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 16:57:56.915|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 16:57:56.920|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 16:57:56.992|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 46ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 16:57:58.494|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 16:57:58.513|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 16:57:58.513|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 16:57:58.721|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 16:57:58.722|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3628 ms  [] ------
2025-08-20 16:57:59.106|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 16:58:01.296|redisson-netty-2-25| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 16:58:02.483|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 16:58:06.428|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 16:58:07.512|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 16:58:08.023|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 16:58:09.196|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 16:58:09.712|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 16:58:09.713|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 16:58:09.801|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 16:58:10.999|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 16:58:14.534|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 16:58:15.143|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 21.014 seconds (JVM running for 23.47)  [] ------
2025-08-20 16:58:15.152|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 16:58:15.380|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 16:58:16.759|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-20 16:58:17.884|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-20 16:58:18.714|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-20 16:58:19.197|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-20 16:58:19.694|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-20 16:58:31.818|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-20 16:58:45.328|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-20 17:01:01.695|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 6080 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 17:01:01.697|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 17:01:03.272|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 17:01:03.275|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 17:01:03.328|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 35ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 17:01:04.621|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 17:01:04.638|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 17:01:04.639|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 17:01:04.806|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 17:01:04.806|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3033 ms  [] ------
2025-08-20 17:01:05.186|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 17:01:07.260|redisson-netty-2-27| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 17:01:08.442|redisson-netty-2-18| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 17:01:11.978|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 17:01:12.943|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 17:01:13.400|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 17:01:14.498|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 17:01:14.999|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 17:01:15.000|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 17:01:15.083|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 17:01:16.336|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 17:01:19.693|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 17:01:20.287|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 19.311 seconds (JVM running for 21.126)  [] ------
2025-08-20 17:01:20.298|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 17:01:20.550|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 17:01:21.840|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-20 17:01:22.959|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-20 17:01:23.445|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-20 17:01:23.889|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-20 17:01:24.328|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-20 17:01:37.125|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-20 17:01:51.436|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-20 17:02:02.164|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-20 17:02:02.164|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-20 17:02:02.182|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 18 ms  [] ------
2025-08-20 17:02:02.572|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a58f0a0e008b0a9bc2d650] ------
2025-08-20 17:02:02.950|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":5033155,"hotelWhiteType":1,"projectId":116}  [68a58f0a0e008b0a9bc2d650] ------
2025-08-20 17:02:03.569|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a58f0a0e008b0a9bc2d650] ------
2025-08-20 17:02:06.404|http-nio-6888-exec-1| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"12FF04E7EDE2F13CC517FE3F23E9F60B","timestamp":1755680525378,"version":"1.0.0"}}  [68a58f0a0e008b0a9bc2d650] ------
2025-08-20 17:02:06.496|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: en-US  [68a58f0a0e008b0a9bc2d650] ------
2025-08-20 17:02:06.500|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a58f0a0e008b0a9bc2d650] ------
2025-08-20 17:02:08.078|http-nio-6888-exec-1| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"F11CB5285C3F17511B0B2E77011F2073","timestamp":1755680527901,"version":"1.0.0"}}  [68a58f0a0e008b0a9bc2d650] ------
2025-08-20 17:02:08.081|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: zh-CN  [68a58f0a0e008b0a9bc2d650] ------
2025-08-20 17:02:08.399|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 5132  [68a58f0a0e008b0a9bc2d650] ------
2025-08-20 17:02:08.409|http-nio-6888-exec-1| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [68a58f0a0e008b0a9bc2d650] ------
2025-08-20 17:02:08.460|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [] ------
2025-08-20 17:02:09.505|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 17:18:06.138|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a592ce0e008b0a9bc2d651] ------
2025-08-20 17:18:06.197|http-nio-6888-exec-6| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":5033155,"hotelWhiteType":1,"projectId":116}  [68a592ce0e008b0a9bc2d651] ------
2025-08-20 17:18:06.509|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a592ce0e008b0a9bc2d651] ------
2025-08-20 17:18:08.500|http-nio-6888-exec-6| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"58A4F200D0C429A50BC8334BED789C60","timestamp":1755681488061,"version":"1.0.0"}}  [68a592ce0e008b0a9bc2d651] ------
2025-08-20 17:18:08.502|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: en-US  [68a592ce0e008b0a9bc2d651] ------
2025-08-20 17:18:08.502|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a592ce0e008b0a9bc2d651] ------
2025-08-20 17:18:09.649|http-nio-6888-exec-6| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"1C4E3A4D278111012E8187890A6F8569","timestamp":1755681489528,"version":"1.0.0"}}  [68a592ce0e008b0a9bc2d651] ------
2025-08-20 17:18:09.651|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: zh-CN  [68a592ce0e008b0a9bc2d651] ------
2025-08-20 17:18:09.797|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 3599  [68a592ce0e008b0a9bc2d651] ------
2025-08-20 17:18:09.798|http-nio-6888-exec-6| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [68a592ce0e008b0a9bc2d651] ------
2025-08-20 17:18:09.799|http-nio-6888-exec-6| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [] ------
2025-08-20 17:18:10.831|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 17:18:16.934|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a592d80e008b0a9bc2d652] ------
2025-08-20 17:18:17.098|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":5033155,"hotelWhiteType":1,"projectId":116}  [68a592d80e008b0a9bc2d652] ------
2025-08-20 17:18:23.864|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a592d80e008b0a9bc2d652] ------
2025-08-20 17:18:25.246|http-nio-6888-exec-5| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"4C1BE5F3FD09BCF46CDA04458586DBE7","timestamp":1755681505075,"version":"1.0.0"}}  [68a592d80e008b0a9bc2d652] ------
2025-08-20 17:18:25.253|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: en-US  [68a592d80e008b0a9bc2d652] ------
2025-08-20 17:18:25.253|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a592d80e008b0a9bc2d652] ------
2025-08-20 17:18:26.532|http-nio-6888-exec-5| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"7F53C4C9A73A85B338AAEEB9B75486B1","timestamp":1755681506360,"version":"1.0.0"}}  [68a592d80e008b0a9bc2d652] ------
2025-08-20 17:18:26.535|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: zh-CN  [68a592d80e008b0a9bc2d652] ------
2025-08-20 17:18:26.536|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 9437  [68a592d80e008b0a9bc2d652] ------
2025-08-20 17:18:26.538|http-nio-6888-exec-5| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [68a592d80e008b0a9bc2d652] ------
2025-08-20 17:18:26.539|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [] ------
2025-08-20 17:18:27.310|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 17:24:17.244|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 16996 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 17:24:17.247|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 17:24:20.425|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 17:24:20.428|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 17:24:20.482|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 38ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 17:24:22.002|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 17:24:22.020|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 17:24:22.020|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 17:24:22.193|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 17:24:22.193|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 4775 ms  [] ------
2025-08-20 17:24:22.519|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 17:24:24.633|redisson-netty-2-1| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 17:24:25.808|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 17:24:29.417|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 17:24:30.415|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 17:24:30.941|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 17:24:31.993|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 17:24:32.454|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 17:24:32.455|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 17:24:32.536|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 17:24:33.676|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 17:24:37.111|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 17:24:37.733|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 21.281 seconds (JVM running for 23.149)  [] ------
2025-08-20 17:24:37.744|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 17:24:37.960|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 17:24:39.121|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-20 17:24:40.556|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-20 17:24:41.663|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-20 17:24:42.788|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-20 17:24:43.636|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-20 17:24:56.938|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-20 17:25:08.179|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-20 17:25:08.180|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-20 17:25:08.206|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 26 ms  [] ------
2025-08-20 17:25:08.943|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a594740e00319f986dd493] ------
2025-08-20 17:25:09.580|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":5033155,"hotelWhiteType":1,"projectId":116}  [68a594740e00319f986dd493] ------
2025-08-20 17:25:14.302|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a594740e00319f986dd493] ------
2025-08-20 17:25:21.054|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-20 17:25:21.093|http-nio-6888-exec-1| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"F5BDB2F372F5263153C4B3A798D64B0A","timestamp":1755681917911,"version":"1.0.0"}}  [68a594740e00319f986dd493] ------
2025-08-20 17:25:21.227|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: en-US  [68a594740e00319f986dd493] ------
2025-08-20 17:25:21.233|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a594740e00319f986dd493] ------
2025-08-20 17:25:22.727|http-nio-6888-exec-1| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"B5CCCDACC8571B673D16BC6B92C37D6A","timestamp":1755681922555,"version":"1.0.0"}}  [68a594740e00319f986dd493] ------
2025-08-20 17:25:22.729|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: zh-CN  [68a594740e00319f986dd493] ------
2025-08-20 17:25:22.941|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 12919  [68a594740e00319f986dd493] ------
2025-08-20 17:25:22.952|http-nio-6888-exec-1| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [68a594740e00319f986dd493] ------
2025-08-20 17:25:23.004|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [] ------
2025-08-20 17:25:24.052|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 17:26:37.694|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a594cd0e00319f986dd494] ------
2025-08-20 17:26:37.754|http-nio-6888-exec-3| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":5033155,"hotelWhiteType":1,"projectId":116}  [68a594cd0e00319f986dd494] ------
2025-08-20 17:26:38.094|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a594cd0e00319f986dd494] ------
2025-08-20 17:26:39.962|http-nio-6888-exec-3| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"8794D723633632E8067B93B2C6E8E813","timestamp":1755681999748,"version":"1.0.0"}}  [68a594cd0e00319f986dd494] ------
2025-08-20 17:26:39.965|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: en-US  [68a594cd0e00319f986dd494] ------
2025-08-20 17:26:39.966|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a594cd0e00319f986dd494] ------
2025-08-20 17:26:41.276|http-nio-6888-exec-3| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"532C29011BCFBFEF58EE454479CA89B5","timestamp":1755682001136,"version":"1.0.0"}}  [68a594cd0e00319f986dd494] ------
2025-08-20 17:26:41.277|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: zh-CN  [68a594cd0e00319f986dd494] ------
2025-08-20 17:26:41.451|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 3696  [68a594cd0e00319f986dd494] ------
2025-08-20 17:26:41.453|http-nio-6888-exec-3| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [68a594cd0e00319f986dd494] ------
2025-08-20 17:26:41.454|http-nio-6888-exec-3| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [] ------
2025-08-20 17:26:42.381|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 17:27:00.393|http-nio-6888-exec-9| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a594e40e00319f986dd495] ------
2025-08-20 17:27:00.450|http-nio-6888-exec-9| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":5033155,"hotelWhiteType":1,"projectId":116}  [68a594e40e00319f986dd495] ------
2025-08-20 17:27:00.765|http-nio-6888-exec-9| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a594e40e00319f986dd495] ------
2025-08-20 17:27:02.219|http-nio-6888-exec-9| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"F6780891CBBCBC33353131AE49CF5FA5","timestamp":1755682021953,"version":"1.0.0"}}  [68a594e40e00319f986dd495] ------
2025-08-20 17:27:02.223|http-nio-6888-exec-9| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: en-US  [68a594e40e00319f986dd495] ------
2025-08-20 17:27:02.223|http-nio-6888-exec-9| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a594e40e00319f986dd495] ------
2025-08-20 17:27:03.637|http-nio-6888-exec-9| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"33917134F10F955C566615F4BF4ED946","timestamp":1755682023504,"version":"1.0.0"}}  [68a594e40e00319f986dd495] ------
2025-08-20 17:27:03.639|http-nio-6888-exec-9| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: zh-CN  [68a594e40e00319f986dd495] ------
2025-08-20 17:27:03.640|http-nio-6888-exec-9| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 3189  [68a594e40e00319f986dd495] ------
2025-08-20 17:27:03.640|http-nio-6888-exec-9| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [68a594e40e00319f986dd495] ------
2025-08-20 17:27:03.641|http-nio-6888-exec-9| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [] ------
2025-08-20 17:27:04.606|http-nio-6888-exec-9| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 17:28:00.167|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 19740 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 17:28:00.169|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 17:28:01.730|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 17:28:01.733|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 17:28:01.784|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 33ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 17:28:02.992|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 17:28:03.009|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 17:28:03.009|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 17:28:03.173|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 17:28:03.173|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 2938 ms  [] ------
2025-08-20 17:28:03.477|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 17:28:05.622|redisson-netty-2-26| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 17:28:06.964|redisson-netty-2-18| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 17:28:10.608|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 17:28:11.659|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 17:28:12.086|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 17:28:13.174|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 17:28:13.652|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 17:28:13.653|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 17:28:13.737|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 17:28:14.836|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 17:28:18.008|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 17:28:18.610|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 19.124 seconds (JVM running for 20.693)  [] ------
2025-08-20 17:28:18.621|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 17:28:18.843|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 17:28:19.970|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-20 17:28:21.661|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-20 17:28:23.197|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-20 17:28:23.747|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-20 17:28:24.242|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-20 17:28:36.697|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-20 17:28:50.203|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-20 17:32:47.710|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 7208 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 17:32:47.712|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 17:32:49.178|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 17:32:49.181|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 17:32:49.230|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 33ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 17:32:50.403|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 17:32:50.418|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 17:32:50.419|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 17:32:50.582|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 17:32:50.582|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 2808 ms  [] ------
2025-08-20 17:32:50.876|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 17:32:53.808|redisson-netty-2-14| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 17:32:54.081|redisson-netty-2-16| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 17:32:57.555|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 17:32:58.461|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 17:32:58.889|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 17:32:59.919|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 17:33:00.371|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 17:33:00.372|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 17:33:00.449|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 17:33:01.485|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 17:33:04.541|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 17:33:05.157|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 17.998 seconds (JVM running for 19.539)  [] ------
2025-08-20 17:33:05.167|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 17:33:05.402|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 17:33:06.675|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-20 17:33:07.874|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-20 17:33:08.370|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-20 17:33:08.779|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-20 17:33:09.312|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-20 17:33:21.798|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-20 17:33:35.330|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-20 17:34:47.767|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-20 17:34:47.768|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-20 17:34:47.783|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 15 ms  [] ------
2025-08-20 17:34:48.137|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a596b80e0065c5838ffd3c] ------
2025-08-20 17:34:48.493|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":5033155,"hotelWhiteType":1,"projectId":116}  [68a596b80e0065c5838ffd3c] ------
2025-08-20 17:34:49.086|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a596b80e0065c5838ffd3c] ------
2025-08-20 17:34:51.570|http-nio-6888-exec-1| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"0DBFF673D111F258E314409EA5B1C58B","timestamp":1755682490728,"version":"1.0.0"}}  [68a596b80e0065c5838ffd3c] ------
2025-08-20 17:34:51.662|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: en-US  [68a596b80e0065c5838ffd3c] ------
2025-08-20 17:34:51.666|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a596b80e0065c5838ffd3c] ------
2025-08-20 17:34:52.875|http-nio-6888-exec-1| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"D59F6D051263B616CD48FDDA61B74FDB","timestamp":1755682492744,"version":"1.0.0"}}  [68a596b80e0065c5838ffd3c] ------
2025-08-20 17:34:52.879|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: zh-CN  [68a596b80e0065c5838ffd3c] ------
2025-08-20 17:34:53.067|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 4295  [68a596b80e0065c5838ffd3c] ------
2025-08-20 17:34:53.076|http-nio-6888-exec-1| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [68a596b80e0065c5838ffd3c] ------
2025-08-20 17:34:53.123|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [] ------
2025-08-20 17:34:54.269|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 17:37:20.960|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [68a597500e0065c5838ffd3d] ------
2025-08-20 17:37:21.107|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":5033155,"hotelWhiteType":1,"projectId":116}  [68a597500e0065c5838ffd3d] ------
2025-08-20 17:37:21.388|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a597500e0065c5838ffd3d] ------
2025-08-20 17:37:22.783|http-nio-6888-exec-5| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"149157742BBF6A15BD870BF70379C28C","timestamp":1755682642546,"version":"1.0.0"}}  [68a597500e0065c5838ffd3d] ------
2025-08-20 17:37:22.785|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: en-US  [68a597500e0065c5838ffd3d] ------
2025-08-20 17:37:22.786|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [5033155]  [68a597500e0065c5838ffd3d] ------
2025-08-20 17:37:24.892|http-nio-6888-exec-5| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[5033155],"settings":[]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"62F9235BE4504709EA17651FCC54B89E","timestamp":1755682644664,"version":"1.0.0"}}  [68a597500e0065c5838ffd3d] ------
2025-08-20 17:37:24.893|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelInfos:272|No hotel info data found, hotel ids: [5033155], language: zh-CN  [68a597500e0065c5838ffd3d] ------
2025-08-20 17:37:25.867|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 4759  [68a597500e0065c5838ffd3d] ------
2025-08-20 17:37:25.870|http-nio-6888-exec-5| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/Project/AddProjectHotelWhite FINISH in -1ms returning {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [68a597500e0065c5838ffd3d] ------
2025-08-20 17:37:25.872|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Hotel does not exist","successful":false}  [] ------
2025-08-20 17:37:27.600|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Project/AddProjectHotelWhite]  [] ------
2025-08-20 22:38:52.576|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 12148 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 22:38:52.579|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 22:38:54.786|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 22:38:54.790|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 22:38:54.856|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 41ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 22:38:56.287|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 22:38:56.304|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 22:38:56.305|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 22:38:56.489|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 22:38:56.490|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3835 ms  [] ------
2025-08-20 22:38:56.864|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 22:38:58.940|redisson-netty-2-25| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 22:39:00.520|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 22:39:04.387|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 22:39:05.443|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 22:39:05.944|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 22:39:07.384|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 22:39:08.377|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 22:39:08.378|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 22:39:08.481|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 22:39:10.012|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 22:39:11.663|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-20 22:39:11.663|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-20 22:39:11.681|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 18 ms  [] ------
2025-08-20 22:39:12.048|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Common/QueryPopCurrency]  [68a5de10e9d215aa3a1f1195] ------
2025-08-20 22:39:13.206|http-nio-6888-exec-1| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [68a5de10e9d215aa3a1f1195] ------
2025-08-20 22:39:13.909|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 22:39:14.612|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 22.759 seconds (JVM running for 24.951)  [] ------
2025-08-20 22:39:14.626|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 22:39:15.614|http-nio-6888-exec-1| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [68a5de10e9d215aa3a1f1195] ------
2025-08-20 22:39:18.349|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-20 22:39:18.401|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"popCurrencyList":[]},"message":"Succeeded","successful":true}  [68a5de10e9d215aa3a1f1195] ------
2025-08-20 22:39:19.858|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-20 22:39:20.756|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-20 22:39:21.465|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Common/QueryPopCurrency]  [68a5de10e9d215aa3a1f1195] ------
2025-08-20 22:39:21.531|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-20 22:39:36.856|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-20 22:39:52.233|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-20 22:41:21.266|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Common/QueryPopCurrency]  [68a5de91e9d215aa3a1f1196] ------
2025-08-20 22:41:22.646|http-nio-6888-exec-3| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"popCurrencyList":[{"currencyCode":"CNY","currencyName":"\u4EBA\u6C11\u5E01"}, {"currencyCode":"EUR","currencyName":"\u6B27\u5143"}, {"currencyCode":"HKD","currencyName":"\u6E2F\u5E01"}, {"currencyCode":"SGD","currencyName":"\u65B0\u52A0\u5761\u5143"}, {"currencyCode":"USD","currencyName":"\u7F8E\u5143"}]},"message":"Succeeded","successful":true}  [68a5de91e9d215aa3a1f1196] ------
2025-08-20 22:41:24.109|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Common/QueryPopCurrency]  [68a5de91e9d215aa3a1f1196] ------
2025-08-20 22:45:41.470|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 14052 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 22:45:41.473|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 22:45:43.272|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 22:45:43.275|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 22:45:43.329|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 36ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 22:45:44.816|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 22:45:44.833|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 22:45:44.833|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 22:45:45.014|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 22:45:45.014|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3463 ms  [] ------
2025-08-20 22:45:45.396|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 22:45:47.601|redisson-netty-2-13| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 22:45:49.382|redisson-netty-2-18| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 22:45:59.397|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 12376 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 22:45:59.399|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 22:46:00.898|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 22:46:00.901|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 22:46:00.951|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 34ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 22:46:02.145|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 22:46:02.160|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 22:46:02.161|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 22:46:02.334|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 22:46:02.335|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 2875 ms  [] ------
2025-08-20 22:46:02.799|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 22:46:05.032|redisson-netty-2-26| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 22:46:06.782|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 22:46:10.358|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 22:46:11.271|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 22:46:11.705|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 22:46:12.777|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 22:46:13.244|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 22:46:13.245|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 22:46:13.326|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 22:46:14.423|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 22:46:17.509|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 22:46:18.116|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 19.319 seconds (JVM running for 20.9)  [] ------
2025-08-20 22:46:18.127|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 22:46:18.349|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 22:46:35.689|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-20 22:46:35.690|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-20 22:46:35.706|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 16 ms  [] ------
2025-08-20 22:46:36.037|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Common/QueryPopCurrency]  [68a5dfcce9d2b41a242b7f03] ------
2025-08-20 22:46:40.646|main|ERROR|com.zaxxer.hikari.pool.HikariPool.throwPoolInitializationException:593|HikariPool-1 - Exception during pool initialization. com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:358)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:477)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:560)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:158)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:116)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:79)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy273.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy112.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy119.selectList(Unknown Source)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap(CachedHotelBrandServiceImpl.java:37)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl$$FastClassBySpringCGLIB$$e4014f52.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:53)
	at org.springframework.cache.interceptor.CacheAspectSupport.invokeOperation(CacheAspectSupport.java:366)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:421)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:346)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:61)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl$$EnhancerBySpringCGLIB$$59b64f92.getNameMap(<generated>)
	at com.fangcang.grfp.api.GRfpApiApplication.afterStarted(GRfpApiApplication.java:60)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:305)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:190)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:153)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:404)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:361)
	at org.springframework.boot.context.event.EventPublishingRunListener.running(EventPublishingRunListener.java:108)
	at org.springframework.boot.SpringApplicationRunListeners.running(SpringApplicationRunListeners.java:77)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:327)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at com.fangcang.grfp.api.GRfpApiApplication.main(GRfpApiApplication.java:46)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 73 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 76 common frames omitted
 [] ------
2025-08-20 22:46:40.649|http-nio-6888-exec-1| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [68a5dfcce9d2b41a242b7f03] ------
2025-08-20 22:46:40.700|main|ERROR|org.springframework.boot.SpringApplication.reportFailure:834|Application run failed org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/fangcang/grfp/core/mapper/HotelBrandMapper.java (best guess)
### The error may involve com.fangcang.grfp.core.mapper.HotelBrandMapper.selectList
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy112.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy119.selectList(Unknown Source)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap(CachedHotelBrandServiceImpl.java:37)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl$$FastClassBySpringCGLIB$$e4014f52.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:53)
	at org.springframework.cache.interceptor.CacheAspectSupport.invokeOperation(CacheAspectSupport.java:366)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:421)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:346)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:61)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl$$EnhancerBySpringCGLIB$$59b64f92.getNameMap(<generated>)
	at com.fangcang.grfp.api.GRfpApiApplication.afterStarted(GRfpApiApplication.java:60)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:305)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:190)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:153)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:404)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:361)
	at org.springframework.boot.context.event.EventPublishingRunListener.running(EventPublishingRunListener.java:108)
	at org.springframework.boot.SpringApplicationRunListeners.running(SpringApplicationRunListeners.java:77)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:327)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at com.fangcang.grfp.api.GRfpApiApplication.main(GRfpApiApplication.java:46)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/fangcang/grfp/core/mapper/HotelBrandMapper.java (best guess)
### The error may involve com.fangcang.grfp.core.mapper.HotelBrandMapper.selectList
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 41 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:82)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy273.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 48 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:358)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:477)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:560)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:158)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:116)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:79)
	... 60 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 73 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 76 common frames omitted
 [] ------
2025-08-20 22:46:42.076|http-nio-6888-exec-1| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [68a5dfcce9d2b41a242b7f03] ------
2025-08-20 22:46:42.745|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"popCurrencyList":[{"currencyCode":"CNY","currencyName":"\u4EBA\u6C11\u5E01"}, {"currencyCode":"EUR","currencyName":"\u6B27\u5143"}, {"currencyCode":"HKD","currencyName":"\u6E2F\u5E01"}, {"currencyCode":"SGD","currencyName":"\u65B0\u52A0\u5761\u5143"}, {"currencyCode":"USD","currencyName":"\u7F8E\u5143"}]},"message":"Succeeded","successful":true}  [68a5dfcce9d2b41a242b7f03] ------
2025-08-20 22:46:43.318|http-nio-6888-exec-1|ERROR|c.f.grfp.api.exception.CustomRestExceptionHandler.handleException:256|controller error org.springframework.dao.InvalidDataAccessApiUsageException: java.lang.InterruptedException: ImmediateEventExecutor$ImmediatePromise@627c92b0(incomplete); nested exception is org.redisson.client.RedisException: java.lang.InterruptedException: ImmediateEventExecutor$ImmediatePromise@627c92b0(incomplete)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:52)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:195)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:190)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:356)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:722)
	at org.redisson.spring.data.connection.RedissonConnection.expire(RedissonConnection.java:310)
	at org.springframework.data.redis.core.RedisTemplate.lambda$expire$8(RedisTemplate.java:804)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.expire(RedisTemplate.java:799)
	at org.springframework.data.redis.core.DefaultBoundKeyOperations.expire(DefaultBoundKeyOperations.java:53)
	at org.springframework.session.data.redis.RedisSessionExpirationPolicy.onExpirationUpdated(RedisSessionExpirationPolicy.java:101)
	at org.springframework.session.data.redis.RedisIndexedSessionRepository$RedisSession.saveDelta(RedisIndexedSessionRepository.java:819)
	at org.springframework.session.data.redis.RedisIndexedSessionRepository$RedisSession.save(RedisIndexedSessionRepository.java:783)
	at org.springframework.session.data.redis.RedisIndexedSessionRepository$RedisSession.access$000(RedisIndexedSessionRepository.java:670)
	at org.springframework.session.data.redis.RedisIndexedSessionRepository.save(RedisIndexedSessionRepository.java:398)
	at org.springframework.session.data.redis.RedisIndexedSessionRepository.save(RedisIndexedSessionRepository.java:249)
	at org.springframework.session.web.http.SessionRepositoryFilter$SessionRepositoryRequestWrapper.commitSession(SessionRepositoryFilter.java:225)
	at org.springframework.session.web.http.SessionRepositoryFilter$SessionRepositoryRequestWrapper.access$100(SessionRepositoryFilter.java:192)
	at org.springframework.session.web.http.SessionRepositoryFilter$SessionRepositoryResponseWrapper.onResponseCommitted(SessionRepositoryFilter.java:179)
	at org.springframework.session.web.http.OnCommittedResponseWrapper.doOnResponseCommitted(OnCommittedResponseWrapper.java:227)
	at org.springframework.session.web.http.OnCommittedResponseWrapper.access$000(OnCommittedResponseWrapper.java:35)
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:498)
	at java.io.FilterOutputStream.flush(FilterOutputStream.java:140)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1178)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1008)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:346)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:277)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:123)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.fangcang.grfp.core.filter.TraceIdFilter.doFilter(TraceIdFilter.java:21)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.redisson.client.RedisException: java.lang.InterruptedException: ImmediateEventExecutor$ImmediatePromise@627c92b0(incomplete)
	at org.redisson.command.CommandAsyncService.get(CommandAsyncService.java:115)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:188)
	... 83 more
Caused by: java.lang.InterruptedException: ImmediateEventExecutor$ImmediatePromise@627c92b0(incomplete)
	at io.netty.util.concurrent.DefaultPromise.await(DefaultPromise.java:244)
	at org.redisson.misc.RedissonPromise.await(RedissonPromise.java:110)
	at org.redisson.misc.RedissonPromise.await(RedissonPromise.java:35)
	at org.redisson.command.CommandAsyncService.get(CommandAsyncService.java:112)
	... 84 more
  [68a5dfcce9d2b41a242b7f03] ------
2025-08-20 22:46:43.322|http-nio-6888-exec-1|ERROR|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:112|Exception occurs at ControllerLoggingUtility::logFinish() java.lang.NullPointerException: null
	at com.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish(ControllerLoggingUtility.java:103)
	at com.fangcang.grfp.api.exception.CustomRestExceptionHandler.handleException(CustomRestExceptionHandler.java:257)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.fangcang.grfp.core.filter.TraceIdFilter.doFilter(TraceIdFilter.java:21)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
 [68a5dfcce9d2b41a242b7f03] ------
2025-08-20 22:46:43.328|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"java.lang.InterruptedException: ImmediateEventExecutor$ImmediatePromise@627c92b0(incomplete); nested exception is org.redisson.client.RedisException: java.lang.InterruptedException: ImmediateEventExecutor$ImmediatePromise@627c92b0(incomplete)","successful":false}  [68a5dfcce9d2b41a242b7f03] ------
2025-08-20 22:46:43.329|http-nio-6888-exec-1| WARN|o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException:414|Failure in @ExceptionHandler com.fangcang.grfp.api.exception.CustomRestExceptionHandler#handleException(Exception) java.lang.NullPointerException: null
	at org.springframework.web.util.UrlPathHelper.getSanitizedPath(UrlPathHelper.java:355)
	at org.springframework.web.util.UrlPathHelper.decodeAndCleanUriString(UrlPathHelper.java:495)
	at org.springframework.web.util.UrlPathHelper.getOriginatingRequestUri(UrlPathHelper.java:440)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.addContentDispositionHeader(AbstractMessageConverterMethodProcessor.java:423)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:275)
	at org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor.handleReturnValue(HttpEntityMethodProcessor.java:219)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:123)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.fangcang.grfp.core.filter.TraceIdFilter.doFilter(TraceIdFilter.java:21)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
 [68a5dfcce9d2b41a242b7f03] ------
2025-08-20 22:46:43.374|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 22:46:43.375|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 22:46:43.375|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'generalExecutor'  [] ------
2025-08-20 22:46:43.386|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 22:46:43.390|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 22:46:43.391|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 22:46:43.391|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.onDestroy:71|GrfpApi v1.0.0-20250808 is already [***stopped***]  [] ------
2025-08-20 22:46:43.394|main| INFO|com.zaxxer.hikari.HikariDataSource.close:350|HikariPool-1 - Shutdown initiated...  [] ------
2025-08-20 22:46:43.402|main| INFO|com.zaxxer.hikari.HikariDataSource.close:352|HikariPool-1 - Shutdown completed.  [] ------
2025-08-20 22:46:51.276|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 17584 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 22:46:51.277|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 22:46:52.817|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 22:46:52.823|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 22:46:52.882|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 36ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 22:46:54.122|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 22:46:54.138|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 22:46:54.138|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 22:46:54.309|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 22:46:54.309|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 2970 ms  [] ------
2025-08-20 22:46:54.631|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 22:46:56.538|redisson-netty-2-25| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 22:46:57.753|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 22:47:01.489|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 22:47:02.436|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 22:47:02.886|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 22:47:03.978|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 22:47:04.434|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 22:47:04.435|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 22:47:04.516|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 22:47:05.611|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 22:47:08.919|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 22:47:09.521|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 18.888 seconds (JVM running for 20.272)  [] ------
2025-08-20 22:47:09.532|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 22:47:09.786|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 22:47:11.258|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-20 22:47:12.651|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-20 22:47:13.748|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-20 22:47:14.431|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-20 22:47:15.029|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-20 22:47:29.025|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-20 22:47:44.133|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-20 22:47:55.372|http-nio-6888-exec-2| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-20 22:47:55.372|http-nio-6888-exec-2| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-20 22:47:55.391|http-nio-6888-exec-2| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 18 ms  [] ------
2025-08-20 22:47:55.726|http-nio-6888-exec-2| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Common/QueryPopCurrency]  [68a5e01be9d2ef1295371403] ------
2025-08-20 22:47:56.571|http-nio-6888-exec-2| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"popCurrencyList":[{"currencyCode":"CNY","currencyName":"\u4EBA\u6C11\u5E01"}, {"currencyCode":"EUR","currencyName":"\u6B27\u5143"}, {"currencyCode":"HKD","currencyName":"\u6E2F\u5E01"}, {"currencyCode":"SGD","currencyName":"\u65B0\u52A0\u5761\u5143"}, {"currencyCode":"USD","currencyName":"\u7F8E\u5143"}]},"message":"Succeeded","successful":true}  [68a5e01be9d2ef1295371403] ------
2025-08-20 22:47:57.846|http-nio-6888-exec-2| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Common/QueryPopCurrency]  [68a5e01be9d2ef1295371403] ------
2025-08-20 22:48:15.295|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Common/QueryPopCurrency]  [68a5e02fe9d2ef1295371404] ------
2025-08-20 22:48:15.619|http-nio-6888-exec-6| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"popCurrencyList":[{"currencyCode":"CNY","currencyName":"\u4EBA\u6C11\u5E01"}, {"currencyCode":"EUR","currencyName":"\u6B27\u5143"}, {"currencyCode":"HKD","currencyName":"\u6E2F\u5E01"}, {"currencyCode":"SGD","currencyName":"\u65B0\u52A0\u5761\u5143"}, {"currencyCode":"USD","currencyName":"\u7F8E\u5143"}]},"message":"Succeeded","successful":true}  [68a5e02fe9d2ef1295371404] ------
2025-08-20 22:48:16.491|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Common/QueryPopCurrency]  [68a5e02fe9d2ef1295371404] ------
2025-08-20 22:48:27.968|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 19816 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 22:48:27.970|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 22:48:30.248|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 22:48:30.251|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 22:48:30.313|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 40ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 22:48:31.745|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 22:48:31.760|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 22:48:31.761|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 22:48:31.931|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 22:48:31.931|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3882 ms  [] ------
2025-08-20 22:48:32.237|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 22:48:34.256|redisson-netty-2-24| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 22:48:35.488|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 22:48:38.893|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 22:48:39.857|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 22:48:40.310|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 22:48:41.395|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 22:48:41.872|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 22:48:41.873|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 22:48:41.954|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 22:48:43.131|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 22:48:46.338|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 22:48:47.322|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 20.126 seconds (JVM running for 21.851)  [] ------
2025-08-20 22:48:47.333|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 22:48:47.555|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 22:48:49.822|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-20 22:48:51.708|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-20 22:48:52.866|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-20 22:48:53.561|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-20 22:48:54.277|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-20 22:48:58.311|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-20 22:48:58.311|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-20 22:48:58.329|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 18 ms  [] ------
2025-08-20 22:48:58.653|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Common/QueryPopCurrency]  [68a5e05ae9d2de9c9a5beec4] ------
2025-08-20 22:49:33.269|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"popCurrencyList":[{"currencyCode":"CNY","currencyName":"\u4EBA\u6C11\u5E01"}, {"currencyCode":"EUR","currencyName":"\u6B27\u5143"}, {"currencyCode":"HKD","currencyName":"\u6E2F\u5E01"}, {"currencyCode":"SGD","currencyName":"\u65B0\u52A0\u5761\u5143"}, {"currencyCode":"USD","currencyName":"\u7F8E\u5143"}]},"message":"Succeeded","successful":true}  [68a5e05ae9d2de9c9a5beec4] ------
2025-08-20 22:49:34.345|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Common/QueryPopCurrency]  [68a5e05ae9d2de9c9a5beec4] ------
2025-08-20 22:49:38.351|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-20 22:49:51.857|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-20 22:51:35.612|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 20900 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-20 22:51:35.614|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-20 22:51:37.423|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-20 22:51:37.429|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-20 22:51:37.501|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 49ms. Found 0 Redis repository interfaces.  [] ------
2025-08-20 22:51:38.916|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-20 22:51:38.933|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-20 22:51:38.933|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-20 22:51:39.112|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-20 22:51:39.112|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3428 ms  [] ------
2025-08-20 22:51:39.424|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-20 22:51:41.505|redisson-netty-2-17| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-20 22:51:42.744|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-20 22:51:46.373|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-20 22:51:47.426|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-20 22:51:47.883|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-20 22:51:49.020|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-20 22:51:49.608|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-20 22:51:49.609|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-20 22:51:49.725|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-20 22:51:50.981|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-20 22:51:54.233|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-20 22:51:54.781|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 19.872 seconds (JVM running for 21.564)  [] ------
2025-08-20 22:51:54.791|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-20 22:51:55.038|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-20 22:51:56.133|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-20 22:51:57.283|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-20 22:51:57.951|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-20 22:51:58.357|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-20 22:51:58.761|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-20 22:52:11.266|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-20 22:52:11.267|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-20 22:52:11.286|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 19 ms  [] ------
2025-08-20 22:52:11.650|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/Common/QueryPopCurrency]  [68a5e11be9d2dc9ad2edaf0a] ------
2025-08-20 22:52:13.210|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"popCurrencyList":[{"currencyCode":"USD","currencyName":"\u7F8E\u5143"}, {"currencyCode":"CNY","currencyName":"\u4EBA\u6C11\u5E01"}, {"currencyCode":"EUR","currencyName":"\u6B27\u5143"}, {"currencyCode":"HKD","currencyName":"\u6E2F\u5E01"}, {"currencyCode":"SGD","currencyName":"\u65B0\u52A0\u5761\u5143"}]},"message":"Succeeded","successful":true}  [68a5e11be9d2dc9ad2edaf0a] ------
2025-08-20 22:52:13.519|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-20 22:52:14.484|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/Common/QueryPopCurrency]  [68a5e11be9d2dc9ad2edaf0a] ------
