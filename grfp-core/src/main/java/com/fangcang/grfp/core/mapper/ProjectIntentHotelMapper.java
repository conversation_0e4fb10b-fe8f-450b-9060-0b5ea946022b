package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.core.entity.ProjectIntentHotelEntity;
import com.fangcang.grfp.core.enums.HotelBidStateEnum;
import com.fangcang.grfp.core.vo.*;
import com.fangcang.grfp.core.vo.request.QueryHotelGroupBidMapHotelListRequest;
import com.fangcang.grfp.core.vo.request.bidmap.BidHotelStatQueryRequest;
import com.fangcang.grfp.core.vo.request.bidmap.QueryMapHotelListByBidStateRequest;
import com.fangcang.grfp.core.vo.request.bidmap.UpdateBidHotelSalesUserRequest;
import com.fangcang.grfp.core.vo.request.bidmap.UpdateHotelBidStateRequest;
import com.fangcang.grfp.core.vo.request.hotel.ProjectIntentHotelRequest;
import com.fangcang.grfp.core.vo.request.hotelgroup.QueryHotelGroupApprovalRequest;
import com.fangcang.grfp.core.vo.request.hotelgroup.QueryHotelGroupBidPriceCountRequest;
import com.fangcang.grfp.core.vo.request.hotelgroup.QueryInvitedSingleHotelRequest;
import com.fangcang.grfp.core.vo.request.project.QueryProjectHotelListRequest;
import com.fangcang.grfp.core.vo.request.project.QueryProjectIntentHotelRequest;
import com.fangcang.grfp.core.vo.request.project.UpdateProjectIntentHotelRequest;
import com.fangcang.grfp.core.vo.response.bidmap.HotelBidStateResponse;
import com.fangcang.grfp.core.vo.response.bidmap.QueryMapBidHotelListResponse;
import com.fangcang.grfp.core.vo.response.hotel.ProjectHotelTentResponse;
import com.fangcang.grfp.core.vo.response.hotelgroup.HotelGroupApprovalResponse;
import com.fangcang.grfp.core.vo.response.hotelgroup.InvitedSingleHotelResponse;
import com.fangcang.grfp.core.vo.response.hotelgroup.ProjectHotelCountResponse;
import com.fangcang.grfp.core.vo.response.project.BidHotelInfoQueryResponse;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelListVO;
import com.fangcang.grfp.core.vo.response.project.ProjectIntentHotelVO;
import com.fangcang.grfp.core.vo.response.recommendhotel.BidRecommendHotelInfoQueryResponse;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 项目意向酒店 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-01
 */
public interface ProjectIntentHotelMapper extends BaseMapper<ProjectIntentHotelEntity> {

    default List<ProjectIntentHotelEntity> queryProjectIntentByHotelIdList(Integer projectId, Collection<Long> hotelIdList){
        LambdaQueryWrapper<ProjectIntentHotelEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectIntentHotelEntity::getProjectId, projectId);
        lambdaQueryWrapper.in(ProjectIntentHotelEntity::getHotelId, hotelIdList);
        return selectList(lambdaQueryWrapper);
    }


    // 更新酒店邀请信息
    default int updateInviteStatus(Integer projectId, List<Long> hotelIds, int inviteStatus, String operator){
        LambdaUpdateWrapper<ProjectIntentHotelEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ProjectIntentHotelEntity::getProjectId, projectId);
        lambdaUpdateWrapper.in(ProjectIntentHotelEntity::getHotelId, hotelIds);
        lambdaUpdateWrapper.set(ProjectIntentHotelEntity::getInviteStatus, inviteStatus);
        lambdaUpdateWrapper.set(ProjectIntentHotelEntity::getModifier, operator);
        return update(null, lambdaUpdateWrapper);
    }

    default List<ProjectIntentHotelEntity> selectInBiddingHotel(Integer projectId){
        LambdaQueryWrapper<ProjectIntentHotelEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectIntentHotelEntity::getProjectId, projectId);
        lambdaQueryWrapper.in(ProjectIntentHotelEntity::getBidState, Lists.newArrayList(HotelBidStateEnum.NEW_BID.bidState, HotelBidStateEnum.UNDER_NEGOTIATION.bidState));
        return selectList(lambdaQueryWrapper);
    }
    default List<ProjectIntentHotelEntity> queryProjectIntentByBidState(Integer projectId, Integer bidState){
        LambdaQueryWrapper<ProjectIntentHotelEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectIntentHotelEntity::getProjectId, projectId);
        lambdaQueryWrapper.eq(ProjectIntentHotelEntity::getBidState, bidState);
        return selectList(lambdaQueryWrapper);
    }

    // 更新企业联系人信息
    default int updateDistributorContactInfo(Integer projectId, List<Long> hotelIds, Integer distributorContactUid, String distributorContactName, String operator){
        LambdaUpdateWrapper<ProjectIntentHotelEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ProjectIntentHotelEntity::getProjectId, projectId);
        lambdaUpdateWrapper.in(ProjectIntentHotelEntity::getHotelId, hotelIds);
        lambdaUpdateWrapper.set(ProjectIntentHotelEntity::getDistributorContactUid, distributorContactUid);
        lambdaUpdateWrapper.set(ProjectIntentHotelEntity::getDistributorContactName, distributorContactName);
        lambdaUpdateWrapper.set(ProjectIntentHotelEntity::getModifier, operator);
        return update(null, lambdaUpdateWrapper);
    }

    default ProjectIntentHotelEntity queryByProjectAndHotelId(Integer projectId, Long hotelId){
        LambdaQueryWrapper<ProjectIntentHotelEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectIntentHotelEntity::getProjectId, projectId);
        lambdaQueryWrapper.eq(ProjectIntentHotelEntity::getHotelId, hotelId);
        return selectOne(lambdaQueryWrapper);
    }

    default ProjectIntentHotelEntity selectByProjectHotelId(Integer projectId, long hotelId) {
        LambdaQueryWrapper<ProjectIntentHotelEntity> queryWrapper = Wrappers.lambdaQuery(ProjectIntentHotelEntity.class);
        queryWrapper.eq(ProjectIntentHotelEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectIntentHotelEntity::getHotelId, hotelId);
        return selectOne(queryWrapper);
    }

    default int processingBidState(List<Integer> projectIntentHotelIdList, Integer bidState, String operator, String remark){
        LambdaUpdateWrapper<ProjectIntentHotelEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(ProjectIntentHotelEntity::getProjectIntentHotelId, projectIntentHotelIdList);
        lambdaUpdateWrapper.set(ProjectIntentHotelEntity::getBidState, bidState);
        lambdaUpdateWrapper.set(ProjectIntentHotelEntity::getModifier, operator);
        lambdaUpdateWrapper.set(ProjectIntentHotelEntity::getRemark, remark);
        return update(null, lambdaUpdateWrapper);
    }

    // 批量新增
    int batchInsert(@Param("list") List<ProjectIntentHotelEntity> projectIntentHotelEntityList);


    // 查询已经报价数量/邀约
    List<ProjectRelatedCount> selectProjectRelatedCountList(List<Integer> projectIdList);


    /**
     * 酒店集团报价统计
     *
     * @param queryHotelGroupBidPriceCountRequest
     * @return
     */
    List<ProjectHotelCountResponse> queryHotelGroupBidPriceCount(QueryHotelGroupBidPriceCountRequest queryHotelGroupBidPriceCountRequest);


    /**
     * 查询项目酒店集团推荐列表
     */
    Page<BidRecommendHotelInfoQueryResponse> queryHotelGroupRecommendHotelInfo(IPage<?> page, @Param("query") QueryHotelGroupBidMapHotelListRequest queryHotelGroupBidMapHotelListRequest);

    /**
     * 精准查找酒店推荐
     */
    BidRecommendHotelInfoQueryResponse queryNoRecommendHotelInfo(@Param("projectId") Integer projectId, @Param("hotelId") Long hotelId);

    /**
     * 根据操作类型和和项目ID，酒店ID，修改对应的字段
     * @param updateProjectIntentHotelRequest
     * @return
     */
   int updateProjectIntentHotel(UpdateProjectIntentHotelRequest updateProjectIntentHotelRequest);


    /**
     * 查询项目酒店集团意向列表
     */
    Page<BidRecommendHotelInfoQueryResponse> queryHotelGroupBidMapInvitedHotelList(IPage<?> page, @Param("query") QueryHotelGroupBidMapHotelListRequest queryHotelGroupBidMapHotelListRequest);

    /**
     * 查询酒店集团下被邀请的单体酒店（简洁版）
     */
    Page<InvitedSingleHotelResponse> queryInvitedSingleHotelsSimple(IPage<?> page, @Param("query") QueryInvitedSingleHotelRequest queryInvitedSingleHotelRequest);

    /**
     * 查询项目报价酒店信息
     */
    List<BidHotelInfoQueryResponse> queryProjectBidHotelInfo(Integer projectId, String cityCode);

    /**
     * 查询项目意向酒店列表
     */
    IPage<ProjectIntentHotelVO> queryProjectIntentHotelList(IPage<ProjectIntentHotelVO> page, @Param("req") QueryProjectIntentHotelRequest req);

    List<ProjectIntentHotelEntity> selectByProjectId(Integer projectId);
    /**
     * 查询项目酒店列表（签约）
     */
    IPage<ProjectHotelListVO> queryProjectContractHotelList(IPage<ProjectHotelListVO> page, @Param("req") QueryProjectHotelListRequest req, @Param("languageId") int languageId);

    /**
     * 更新酒店联系人信息
     */
    int updateBidContactInfo(ProjectIntentHotelEntity projectIntentHotel);
    /**
     * 查询酒店集团审核列表（待审核/审核驳回）
     */
    Page<HotelGroupApprovalResponse> queryHotelGroupApprovalList(IPage<?> page, @Param("query") QueryHotelGroupApprovalRequest queryHotelGroupApprovalRequest);
    List<ApproveStatusCountVO> queryHotelGroupApprovalCount(@Param("orgId") Integer orgId, @Param("hotelGroupBrandIdList") Collection<Long> hotelGroupBrandIdList);

    List<ApproveStatusCountVO> queryHotelApprovalCount(@Param("hotelIdList") List<Long> hotelIdList);

    List<ProjectIntentHotelEntity> selectProjectIntentHotelServicePoint(@Param("projectId") Integer projectId, @Param("hotelIdList") List<Long> hotelIdList);

    /**
     * 查询酒店机构投标列表
     */
    List<ProjectHotelTentResponse> selectHotelOrgTentList(ProjectIntentHotelRequest projectIntentHotelRequest);


    /**查询项目指派酒店 bidState=0 未全量查询  */
    List<ProjectIntentHotelEntity> selectProjectIntentHotelList(ProjectIntentHotelRequest projectIntentHotelRequest);

    /**
     * 查询投标列表
     */
    Page<ProjectHotelTentResponse> selectHotelTentList(IPage<?> page, @Param("query") ProjectIntentHotelRequest projectIntentHotelRequest);

    /**
     * 查询酒店未报价数量
     */
    int selectHotelNoBidCount(@Param("hotelIdList") List<Long> hotelIdList, @Param("lastYearTime") Date lastYearTime);
    List<ProjectIntentHotelEntity> queryByProjectIntentHotelIds(List<Long> projectIntentHotelIds);

    /**
     * 查询项目报价状态分组统计数量
     */
    List<HotelBidStateResponse> queryGroupByHotelBidState(BidHotelStatQueryRequest request);

    /**
     * 根据报价状态查询酒店报价信息
     */
    Page<QueryMapBidHotelListResponse> queryMapHotelListByBidState(IPage<?> page, @Param("query") QueryMapHotelListByBidStateRequest request);

    List<Long> queryInviteHotelList(@Param("projectId") int projectId);

    int updateToWaitNotify(@Param("projectId") Integer projectId, @Param("hotelIds") List<Long> hotelIds,  @Param("modifier")String modifier);

    int updateBidState(UpdateHotelBidStateRequest request);

    int updateHotelSalesContactInfo(UpdateBidHotelSalesUserRequest request);

    /**
     * 查询项目报价信息
     */
    List<TenderHotelPriceVO> queryHotelTenderInfo(@Param("projectId") Integer projectId, @Param("bidStates") Collection<Integer> bidStates);

    /**
     * 查询酒店集团项目报价信息
     */
    List<TenderHotelPriceVO> queryHotelGroupTenderInfo(@Param("projectId") Integer projectId, @Param("bidStates") Collection<Integer> bidStates,
                                                       @Param("hotelGroupOrgId") Integer hotelGroupOrgId, @Param("hotelGroupBrandIdList") Collection<Long> hotelGroupBrandIdList);


    int updateHotelGroupApprove(ProjectIntentHotelEntity projectIntentHotel);


    /**
     * 根据项目ID和酒店ID集合查询
     */
    default ProjectIntentHotelEntity selectByProjectIdAndHotelId(Integer projectId, Long hotelId) {
        if (Objects.isNull(projectId) && Objects.isNull(hotelId)) {
            return null;
        }
        LambdaQueryWrapper<ProjectIntentHotelEntity> queryWrapper = Wrappers.lambdaQuery(ProjectIntentHotelEntity.class);
        queryWrapper.eq(ProjectIntentHotelEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectIntentHotelEntity::getHotelId, hotelId);
        return selectOne(queryWrapper);
    }

    void updateContactInfoOnly(@Param("projectIntentHotel") ProjectIntentHotelEntity projectIntentHotel);


    void updateNotify(@Param("projectIntentHotel") ProjectIntentHotelEntity projectIntentHotel);

    List<BidStateCountVO> queryHotelBidStatCount(@Param("hotelIdList") List<Long> hotelIdList);

    List<ProjectBidBrandStatInfoVO> queryProjectBidBrandStatInfo(@Param("projectId") Integer projectId);


    List<ProjectBidBrandStatInfoVO> queryProjectInviteCityInfo(@Param("projectId") Integer projectId,
                                                               @Param("hotelGroupOrgId") Integer hotelGroupOrgId,
                                                               @Param("hotelGroupBrandIdList") Collection<Long> hotelGroupBrandIdList);
}
