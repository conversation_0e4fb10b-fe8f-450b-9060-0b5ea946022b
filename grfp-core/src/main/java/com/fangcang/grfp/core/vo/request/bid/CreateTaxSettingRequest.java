package com.fangcang.grfp.core.vo.request.bid;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class CreateTaxSettingRequest {

    /**
     * 客房增值税 计算方式 1:百分比，2:固定值
     */
    private Integer vatgstrmFeeType;

    /**
     * 客房增值税
     */
    private BigDecimal vatgstrmFeeValue;

    /**
     * 是否包含客房增值税
     */
    private Integer vatgstrmFeeIsInclude;

    /**
     * 餐饮增值税 计算方式 1:百分比，2:固定值
     */
    private Integer vatgstfbFeeType;

    /**
     * 餐饮增值税
     */
    private BigDecimal vatgstfbFeeValue;

    /**
     * 是否包含餐饮增值税
     */
    private Integer vatgstfbFeeIsInclude;

    /**
     * 服务费 计算方式 1:百分比，2:固定值
     */
    private Integer serviceFeeType;

    /**
     * 服务费
     */
    private BigDecimal serviceFeeValue;

    /**
     * 是否包含服务费
     */
    private Integer serviceFeeIsInclude;

    /**
     * 占用费 计算方式 1:百分比，2:固定值
     */
    private Integer occFeeType;

    /**
     * 占用费
     */
    private BigDecimal occFeeValue;

    /**
     * 是否包含占用费
     */
    private Integer occFeeIsInclude;

    /**
     * 其他税费1 计算方式 1:百分比，2:固定值
     */
    private Integer othertx1FeeType;

    /**
     * 其他税费1描述
     */
    private String othertx1FeeDesc;

    /**
     * 其他税费1
     */
    private BigDecimal othertx1FeeValue;

    /**
     * 是否包含其他税费1
     */
    private Integer othertx1FeeIsInclude;

    /**
     * 其他税费2 计算方式 1:百分比，2:固定值
     */
    private Integer othertx2FeeType;

    /**
     * 其他税费2描述
     */
    private String othertx2FeeDesc;

    /**
     * 其他税费2
     */
    private BigDecimal othertx2FeeValue;

    /**
     * 是否包含其他税费2
     */
    private Integer othertx2FeeIsInclude;

    /**
     * 其他税费3 计算方式 1:百分比，2:固定值
     */
    private Integer othertx3FeeType;

    /**
     * 其他税费3描述
     */
    private String othertx3FeeDesc;

    /**
     * 其他税费3
     */
    private BigDecimal othertx3FeeValue;

    /**
     * 是否包含其他税费3
     */
    private Integer othertx3FeeIsInclude;

    /**
     提前入住税计算方式 1:百分比，2:固定值
     */
    private Integer earlyckFeeType;

    /**
     * 提前入住税
     */
    private BigDecimal earlyckFeeValue;

    /**
     * 是否包含提前入住税
     */
    private Integer earlyckFeeIsInclude;

    /**
      入住税计算方式 1:百分比，2:固定值
     */
    private Integer lodgtxFeeType;

    /**
     * 入住税
     */
    private BigDecimal lodgtxFeeValue;

    /**
     * 是否包含入住税
     */
    private Integer lodgtxFeeIsInclude;

    /**
     国家税计算方式 1:百分比，2:固定值
     */
    private Integer statetxFeeType;

    /**
     * 国家税
     */
    private BigDecimal statetxFeeValue;

    /**
     * 是否包含国家税
     */
    private Integer statetxFeeIsInclude;

    /**
     城市税计算方式 1:百分比，2:固定值
     */
    private Integer citytxFeeType;

    /**
     * 城市税
     */
    private BigDecimal citytxFeeValue;

    /**
     * 是否包含城市税
     */
    private Integer citytxFeeIsInclude;


}
