package com.fangcang.grfp.api.thread;

import com.fangcang.grfp.core.manager.HotelHexagonLngLatManager;
import com.fangcang.grfp.core.mapper.HotelMapper;
import com.fangcang.grfp.core.util.HexagonStatUtil;
import com.fangcang.grfp.core.util.JsonUtil;
import com.fangcang.grfp.core.util.StopWatch;
import com.fangcang.grfp.core.vo.response.HexagonsStat;
import com.fangcang.grfp.core.vo.response.HotelResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 异步生产酒店六边形10公里范围坐标
 */
@Getter
@Setter
@Slf4j
public class GenerateHotelHexagonLngLatThread extends Thread {

    // 生成六边形r
    private List<Long> hotelIds;

    private HotelMapper hotelMapper;

    private HotelHexagonLngLatManager hotelHexagonLngLatManager;

    public GenerateHotelHexagonLngLatThread(List<Long> hotelIds, HotelMapper hotelMapper, HotelHexagonLngLatManager hotelHexagonLngLatManager) {
        this.hotelIds = hotelIds;
        this.hotelMapper = hotelMapper;
        this.hotelHexagonLngLatManager = hotelHexagonLngLatManager;
    }

    @Override
    public void run() {
        if (CollectionUtils.isEmpty(hotelIds)) {
            log.error("生成六边形坐标失败，hotelIds为空");
            return;
        } else if (hotelIds.size() > 1000) {
            log.error("生成六边形坐标失败，hotelIds size 大于1000");
            return;
        }
        try {
            StopWatch watch = new StopWatch();
            watch.start();

            List<HotelResponse> hotelResponseList = hotelMapper.selectHotelInfoByIds(hotelIds);
            for(HotelResponse hotelResponse : hotelResponseList){
                hotelHexagonLngLatManager.generateHotelIfNullThenInit(hotelResponse);
            }

            log.info("生成六边形坐标结束，总耗时 : {}, hotelIds : {}", watch.getStepSplitTime(), JsonUtil.objectToJson(hotelIds));
        } catch (Exception e) {
            log.error("生成六边形坐标异常，hotelIds : {}", JsonUtil.objectToJson(hotelIds), e);
        }

    }

    private List<HexagonsStat> generateHexagons(BigDecimal lngBaidu, BigDecimal latBaidu){
        List<HexagonsStat> uniqueHexagonList = new ArrayList<>();
        Set<String> uniqueHexagonStringSet = new HashSet<>();
        HexagonStatUtil.generateHexagons(new BigDecimal[]{lngBaidu, latBaidu}, 6, uniqueHexagonStringSet, uniqueHexagonList);
        return uniqueHexagonList;
    }
}
