package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("更新酒店销售人员-酒店端")
@Getter
@Setter
public class UpdateBidHotelSalesUserRequest extends BaseVO {

    @ApiModelProperty("项目意向酒店ID集合")
    @NotNull
    private List<Integer> projectIntentHotelIdList;

    @ApiModelProperty("酒店销售联系人姓名(默认取推荐酒店表数据)")
    @NotBlank
    private String hotelSalesContactName;

    @ApiModelProperty("酒店销售联系人电话")
    @NotBlank
    private String hotelSalesContactMobile;

    @ApiModelProperty("酒店销售联系人电邮")
    @NotBlank
    private String hotelSalesContactEmail;

    private String modifier;
}
