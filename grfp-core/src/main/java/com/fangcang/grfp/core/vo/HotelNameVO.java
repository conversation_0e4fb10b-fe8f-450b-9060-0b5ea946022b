package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("酒店品牌信息")
@Getter
@Setter
public class HotelNameVO extends BaseVO {

    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;

    @ApiModelProperty(value = "酒店英文名称")
    @JsonIgnore
    private String nameEnUs;

    @ApiModelProperty(value = "酒店中文名称")
    @JsonIgnore
    private String nameZhCn;

    @ApiModelProperty(value = "酒店名称")
    private String name;

    @ApiModelProperty(value = "酒店集团ID")
    private Long hotelGroupId;

    @ApiModelProperty(value = "酒店品牌ID")
    private Long hotelBrandId;

    @ApiModelProperty(value = "城市编号")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "国家编号")
    private String countryCode;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "省/州/邦编码")
    private String provinceCode;

    @ApiModelProperty(value = "省/州/邦名称")
    private String provinceName;


}
