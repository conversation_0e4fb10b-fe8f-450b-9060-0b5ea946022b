package com.fangcang.grfp.api.controller.org.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.api.controller.org.vo.*;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.base.IdVO;
import com.fangcang.grfp.core.base.ImportRowErrorVO;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.cached.CachedCityService;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.dto.excel.ImportExcelContext;
import com.fangcang.grfp.core.entity.HotelEntity;
import com.fangcang.grfp.core.entity.OrgEntity;
import com.fangcang.grfp.core.entity.OrgRelatedHotelBrandEntity;
import com.fangcang.grfp.core.entity.OrgRelatedHotelEntity;
import com.fangcang.grfp.core.enums.ImportBizTypeEnum;
import com.fangcang.grfp.core.enums.OrgTypeEnum;
import com.fangcang.grfp.core.enums.RoleCodeEnum;
import com.fangcang.grfp.core.enums.StateEnum;
import com.fangcang.grfp.core.manager.ExcelManager;
import com.fangcang.grfp.core.manager.HotelManager;
import com.fangcang.grfp.core.mapper.*;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.HotelUtility;
import com.fangcang.grfp.core.util.StringUtil;
import com.fangcang.grfp.core.util.ValidateUtil;
import com.fangcang.grfp.core.vo.ListOrgVO;
import com.fangcang.grfp.core.vo.OrgNameVO;
import com.fangcang.grfp.core.vo.OrgRelatedHotelBrandVO;
import com.fangcang.grfp.core.vo.OrgRelatedHotelVO;
import com.fangcang.grfp.core.vo.request.ListOrgRequest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrgService {

    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private OrgRelatedHotelMapper orgRelatedHotelMapper;
    @Autowired
    private OrgRelatedHotelBrandMapper orgRelatedHotelBrandMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private HotelManager hotelManager;
    @Autowired
    private ExcelManager excelManager;
    @Autowired
    private HotelMapper hotelMapper;
    @Autowired
    private CachedCityService cachedCityService;

    public PageVO<ListOrgVO> queryOrgVOPageList(HttpServletRequest request, ListOrgRequest listOrgRequest){
        IPage<ListOrgVO> page = new Page<>(listOrgRequest.getPageIndex(), listOrgRequest.getPageSize());
        page = orgMapper.queryOrgVOPageList(page, listOrgRequest);
        int languageId = AppUtility.getRequestHeaderLanguage(request);
        page.getRecords().forEach(item -> {
            item.setOrgTypeName(AppUtility.getText(languageId, OrgTypeEnum.getOrgTextCodeByKey(item.getOrgType())));
        });
        return new PageVO<>((int) page.getTotal(), (int) page.getPages(), page.getRecords());
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer addOrg(HttpServletRequest request, AddOrgRequest addOrgRequest){
        // 获取用户Session
        UserSession userSession = UserSession.get();
        // 检查酒店集团机构至少有一个品牌权限
        if(Objects.equals(addOrgRequest.getOrgType(), OrgTypeEnum.HOTELGROUP.key) && CollectionUtils.isEmpty(addOrgRequest.getHotelGroupBrandList())){
            AppUtility.serviceError(ErrorCode.PLEASE_SELECT_HOTEL_GROUP_BRAND);
        }
        if(!userSession.getRoleCode().equals(RoleCodeEnum.ADMIN.key)){
            AppUtility.serviceError(ErrorCode.ONLY_ADMIN_HAS_PERMISSION_ADD);
        }

        // 检查电邮是否正确
        if (!ValidateUtil.isValidEmail(addOrgRequest.getContactEmail())) {
            AppUtility.serviceError(ErrorCode.CONTACT_EMAIL_INVALIDATED);
        }

        // 酒店机构检查
        if(Objects.equals(addOrgRequest.getOrgType(), OrgTypeEnum.HOTEL.key)){
            if(addOrgRequest.getHotelId() == null){
                AppUtility.serviceError(ErrorCode.ADD_ORG_FAILED_DUE_TO_NULL_HOTEL_ID);
            }
        }

        // 检查是否存在相同名称的机构
        int orgCount = orgMapper.selectCountByName(addOrgRequest.getOrgName());
        if(orgCount > 0){
            AppUtility.serviceError(ErrorCode.EXIST_THE_SAME_ORG_NAME);
        }

        // 同一机构，酒店唯一
        if(Objects.equals(addOrgRequest.getOrgType(), OrgTypeEnum.HOTEL.getKey())){
            int hotelCount = orgRelatedHotelMapper.selectCountByHotelId(addOrgRequest.getHotelId());
            if(hotelCount > 0){
                AppUtility.serviceError(ErrorCode.HOTEL_EXIST_RELATED_ORG);
            }
        }

        // 新增机构
        Date now = new Date();
        OrgEntity org = new OrgEntity();
        BeanUtils.copyProperties(addOrgRequest, org);
        org.setState(StateEnum.Effective.key);
        org.setCreator(userSession.getUsername());
        org.setCreateTime(now);
        int insertCount = orgMapper.insert(org);
        if(insertCount == 0){
            AppUtility.serviceError(ErrorCode.ADD_FAILED);
        }

        int orgId = org.getOrgId();
        // 新增酒店集团关联品牌
        if(Objects.equals(org.getOrgType(), OrgTypeEnum.HOTELGROUP.getKey())){
            for(HotelGroupBrandVO hotelGroupBrandVO : addOrgRequest.getHotelGroupBrandList()){
                OrgRelatedHotelBrandEntity orgRelatedHotelBrandEntity = new OrgRelatedHotelBrandEntity();
                orgRelatedHotelBrandEntity.setOrgId(orgId);
                orgRelatedHotelBrandEntity.setHotelGroupId(hotelGroupBrandVO.getHotelGroupId());
                orgRelatedHotelBrandEntity.setHotelBrandId(hotelGroupBrandVO.getHotelBrandId());
                orgRelatedHotelBrandEntity.setCreator(userSession.getUsername());
                orgRelatedHotelBrandEntity.setCreateTime(now);
                orgRelatedHotelBrandMapper.insert(orgRelatedHotelBrandEntity);
            }
        }
        // 新增酒店机构关联酒店
        if(Objects.equals(org.getOrgType(), OrgTypeEnum.HOTEL.getKey())){
            if(addOrgRequest.getHotelInfo() == null){
                AppUtility.serviceError(ErrorCode.HOTEL_INFO_IS_NULL);
            }
            // 目的地接口快速返回酒店基础信息
            hotelManager.asyncInsertHotelByDestination(Collections.singletonList(addOrgRequest.getHotelInfo()), userSession.getUsername());
            OrgRelatedHotelEntity orgRelatedHotel = new OrgRelatedHotelEntity();
            orgRelatedHotel.setOrgId(orgId);
            orgRelatedHotel.setHotelId(addOrgRequest.getHotelId());
            orgRelatedHotel.setCreator(userSession.getUsername());
            orgRelatedHotel.setCreateTime(now);
            orgRelatedHotelMapper.insert(orgRelatedHotel);
        }

        return orgId;
    }

    public OrgInfoVO orgInfo(HttpServletRequest request, OrgInfoRequest orgInfoRequest){
        UserSession userSession = UserSession.get();
        if(!Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.PLATFORM.getKey()) &&
                !Objects.equals(userSession.getUserOrg().getOrgId(), orgInfoRequest.getOrgId())
        ){
            AppUtility.serviceError(ErrorCode.CANNOT_VIEW_OTHER_ORG_INFO);
        }
        OrgEntity org = orgMapper.selectById(orgInfoRequest.getOrgId());
        if(org == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }
        // 定义返回值
        OrgInfoVO orgInfoVO = new OrgInfoVO();
        BeanUtils.copyProperties(org, orgInfoVO);

        return orgInfoVO;
    }

    public void updateOrgInfo(HttpServletRequest request, UpdateOrgRequest updateOrgRequest){
        // 获取用户Session
        UserSession userSession = UserSession.get();

        OrgEntity dbOrg = orgMapper.selectById(updateOrgRequest.getOrgId());
        if(dbOrg == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }
        if(dbOrg.getOrgType().equals(OrgTypeEnum.PLATFORM.key)){
            AppUtility.serviceError(ErrorCode.CANNOT_UPDATE_PLATFORM_ORG);
        }

        if(!userSession.getRoleCode().equals(RoleCodeEnum.ADMIN.key)){
            AppUtility.serviceError(ErrorCode.ONLY_ADMIN_HAS_PERMISSION_UPDATE);
        }

        // 修改机构
        Date now = new Date();
        OrgEntity org = new OrgEntity();
        BeanUtils.copyProperties(updateOrgRequest, org);
        org.setLogoUrl(AppUtility.getFileKeyFromUrl(updateOrgRequest.getLogoUrl()));
        org.setModifier(userSession.getUsername());
        org.setModifyTime(now);
        int updateCount = orgMapper.updateById(org);
        if(updateCount == 0){
            AppUtility.serviceError(ErrorCode.UPDATE_FAILED);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void delete(HttpServletRequest request, DeleteOrgRequest deleteOrgRequest){
        // 获取用户Session
        UserSession userSession = UserSession.get();

        OrgEntity dbOrg = orgMapper.selectById(deleteOrgRequest.getOrgId());
        if(dbOrg == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }
        if(dbOrg.getOrgType().equals(OrgTypeEnum.PLATFORM.key)){
            AppUtility.serviceError(ErrorCode.CANNOT_UPDATE_PLATFORM_ORG);
        }

        if(!userSession.getRoleCode().equals(RoleCodeEnum.ADMIN.key)){
            AppUtility.serviceError(ErrorCode.ONLY_ADMIN_HAS_PERMISSION_UPDATE);
        }

        // 检查机构是否存在用户
        int userCount = userMapper.selectCount(deleteOrgRequest.getOrgId());
        if(userCount > 0){
            AppUtility.serviceError(ErrorCode.ORG_EXIST_USER);
        }
        // 修改机构状态
        Date now = new Date();
        OrgEntity org = new OrgEntity();
        org.setOrgId(deleteOrgRequest.getOrgId());
        org.setState(StateEnum.Invalid.key);
        org.setModifier(userSession.getUsername());
        org.setModifyTime(now);
        int updateCount = orgMapper.updateById(org);
        if(updateCount == 0){
            AppUtility.serviceError(ErrorCode.UPDATE_FAILED);
        }
        // 删除机构关联酒店
        if(dbOrg.getOrgType().equals(OrgTypeEnum.HOTEL.key)){
            orgRelatedHotelMapper.deleteByOrgId(org.getOrgId());
        }
        // 删除机构关联酒店集团
        if(dbOrg.getOrgType().equals(OrgTypeEnum.HOTELGROUP.key)){
            orgRelatedHotelBrandMapper.deleteByOrgId(org.getOrgId());
        }
    }
    public PageVO<OrgRelatedHotelBrandVO> orgRelatedBrandList(HttpServletRequest request, QueryOrgRelatedInfoRequest queryOrgRelatedInfoRequest){
        IPage<OrgRelatedHotelBrandVO> page = new Page<>(queryOrgRelatedInfoRequest.getPageIndex(), queryOrgRelatedInfoRequest.getPageSize());
        int languageId = AppUtility.getRequestHeaderLanguage(request);
        page = orgRelatedHotelBrandMapper.queryPageList(page, languageId, queryOrgRelatedInfoRequest.getOrgId(),
                queryOrgRelatedInfoRequest.getHotelGroupId(), queryOrgRelatedInfoRequest.getHotelBrandId());
        // 设置酒店集团和品牌名称
        for(OrgRelatedHotelBrandVO orgRelatedHotelBrandVO : page.getRecords()){
            if(orgRelatedHotelBrandVO.getHotelGroupId() != null){
                orgRelatedHotelBrandVO.setHotelGroupName(AppUtility.getHotelGroupName(languageId, orgRelatedHotelBrandVO.getHotelGroupId()));
            }
            if(orgRelatedHotelBrandVO.getHotelBrandId() != null){
                orgRelatedHotelBrandVO.setHotelBrandName(AppUtility.getHotelBrandName(languageId, orgRelatedHotelBrandVO.getHotelBrandId()));
            }

        }

        return new PageVO<>((int) page.getTotal(), (int) page.getPages(), page.getRecords());
    }

    @Transactional(rollbackFor = Exception.class)
    public void addRelatedHotelBrand( HttpServletRequest request, AddRelatedHotelBrandRequest addRelatedHotelBrandRequest){
        AppUtility.isPlatformUser(UserSession.get());
        if(addRelatedHotelBrandRequest.getHotelBrandId() != null && CollectionUtils.isEmpty(addRelatedHotelBrandRequest.getHotelBrandIdList())){
            addRelatedHotelBrandRequest.setHotelBrandIdList(Collections.singletonList(addRelatedHotelBrandRequest.getHotelBrandId()));
        }
        if(CollectionUtils.isEmpty(addRelatedHotelBrandRequest.getHotelBrandIdList())){
            AppUtility.serviceError(ErrorCode.REQUEST_PARAMETER_ERROR);
        }
        // 检查是否已经存在
         List<OrgRelatedHotelBrandEntity> dbOrgRelatedHotelBrandList = orgRelatedHotelBrandMapper.queryHotelGroupOrgRelatedBrandList(addRelatedHotelBrandRequest.getHotelGroupOrgId(), addRelatedHotelBrandRequest.getHotelGroupId());
         Map<Long, OrgRelatedHotelBrandEntity> dbOrgRelatedHotelBrandMap = dbOrgRelatedHotelBrandList.stream().collect(Collectors.toMap(OrgRelatedHotelBrandEntity::getHotelBrandId, Function.identity()));

         // 检查是否为机构
         OrgEntity org = orgMapper.selectById(addRelatedHotelBrandRequest.getHotelGroupOrgId());
         if(!org.getOrgType().equals(OrgTypeEnum.HOTELGROUP.key)){
             AppUtility.serviceError(ErrorCode.ORG_MUST_BE_HOTEL_GROUP);
         }
         for(Long hotelBrandId : addRelatedHotelBrandRequest.getHotelBrandIdList()){
             if(!dbOrgRelatedHotelBrandMap.containsKey(hotelBrandId)){
                 // Insert Data
                 OrgRelatedHotelBrandEntity orgRelatedHotelBrand = new OrgRelatedHotelBrandEntity();
                 orgRelatedHotelBrand.setOrgId(addRelatedHotelBrandRequest.getHotelGroupOrgId());
                 orgRelatedHotelBrand.setHotelGroupId(addRelatedHotelBrandRequest.getHotelGroupId());
                 orgRelatedHotelBrand.setHotelBrandId(hotelBrandId);
                 orgRelatedHotelBrand.setCreator(UserSession.get().getUsername());
                 AppUtility.doInsert(orgRelatedHotelBrandMapper.insert(orgRelatedHotelBrand));
             }
         }
    }

    public void deleteRelatedHotelBrand( HttpServletRequest request, DeleteRelatedHotelBrandRequest deleteRelatedHotelBrandRequest){
        orgRelatedHotelBrandMapper.delete(deleteRelatedHotelBrandRequest.getHotelGroupOrgId(), deleteRelatedHotelBrandRequest.getHotelGroupId(), deleteRelatedHotelBrandRequest.getHotelBrandId());
    }

    public PageVO<OrgRelatedHotelVO> orgRelatedHotelList(HttpServletRequest request, QueryOrgRelatedInfoRequest queryOrgRelatedInfoRequest){
        IPage<OrgRelatedHotelVO> page = new Page<>(queryOrgRelatedInfoRequest.getPageIndex(), queryOrgRelatedInfoRequest.getPageSize());
        int languageId = AppUtility.getRequestHeaderLanguage(request);
        page = orgRelatedHotelMapper.queryPageList(page, languageId, queryOrgRelatedInfoRequest.getOrgId());

        for(OrgRelatedHotelVO orgRelatedHotelVO : page.getRecords()){
            String hotelName = AppUtility.getHotelName(languageId, orgRelatedHotelVO.getHotelId());
            if(StringUtil.isValidString(hotelName)){
                orgRelatedHotelVO.setHotelName(hotelName);
            } else {
                HotelEntity hotel = hotelMapper.selectById(orgRelatedHotelVO.getHotelId());
                orgRelatedHotelVO.setHotelName(HotelUtility.getHotelName(languageId, hotel));
            }

           if(orgRelatedHotelVO.getHotelBrandId() != null){
               orgRelatedHotelVO.setHotelBrandName(AppUtility.getHotelBrandName(languageId, orgRelatedHotelVO.getHotelBrandId()));
           }
           if(orgRelatedHotelVO.getHotelGroupId() != null){
                orgRelatedHotelVO.setHotelGroupName(AppUtility.getHotelGroupName(languageId, orgRelatedHotelVO.getHotelGroupId()));
           }
           if(StringUtil.isValidString(orgRelatedHotelVO.getCityCode())){
               orgRelatedHotelVO.setCityName(AppUtility.getCityName(languageId, orgRelatedHotelVO.getCityCode()));
           }
        }
        return new PageVO<>((int) page.getTotal(), (int) page.getPages(), page.getRecords());
    }

    public void addRelatedHotel(HttpServletRequest request, AddRelatedHotelRequest addRelatedHotelRequest){
        // 检查是否已经存在
        OrgRelatedHotelEntity dbOrgRelatedHotel = orgRelatedHotelMapper.selectOne(addRelatedHotelRequest.getHotelOrgId(), addRelatedHotelRequest.getHotelInfo().getHotelId());
        if(dbOrgRelatedHotel != null){
            AppUtility.serviceError(ErrorCode.RECORD_ALREADY_EXISTED);
        }

        OrgEntity org = orgMapper.selectById(addRelatedHotelRequest.getHotelOrgId());
        if(!org.getOrgType().equals(OrgTypeEnum.HOTEL.key)){
            AppUtility.serviceError(ErrorCode.ORG_MUST_BE_HOTEL);
        }
        // 检查酒店是否存在
        int hotelCount = orgRelatedHotelMapper.selectCountByHotelId(addRelatedHotelRequest.getHotelInfo().getHotelId());
        if(hotelCount > 0){
            AppUtility.serviceError(ErrorCode.HOTEL_EXIST_RELATED_ORG);
        }
        // 同步酒店信息
        UserSession userSession = UserSession.get();
        hotelManager.asyncInsertHotelByDestination(Collections.singletonList(addRelatedHotelRequest.getHotelInfo()), userSession.getUsername());

        // Insert Data
        OrgRelatedHotelEntity orgRelatedHotelEntity = new OrgRelatedHotelEntity();
        orgRelatedHotelEntity.setOrgId(addRelatedHotelRequest.getHotelOrgId());
        orgRelatedHotelEntity.setHotelId(addRelatedHotelRequest.getHotelInfo().getHotelId());
        orgRelatedHotelEntity.setCreator(UserSession.get().getUsername());
        AppUtility.doInsert(orgRelatedHotelMapper.insert(orgRelatedHotelEntity));
    }

    public void deleteRelatedHotel( HttpServletRequest request, DeleteRelatedHotelRequest deleteRelatedHotelRequest){
        orgRelatedHotelMapper.delete(deleteRelatedHotelRequest.getHotelOrgId(), deleteRelatedHotelRequest.getHotelId());
    }

    public IdVO<Long> importHotelOrg(MultipartFile file) throws IOException {
        ImportExcelContext<ImportHotelOrgVO> importExcelContext = new ImportExcelContext<>();
        importExcelContext.setBizType(ImportBizTypeEnum.IMPORT_HOTEL_ORG);
        importExcelContext.setFile(file);
        importExcelContext.setUserSession(UserSession.get());
        importExcelContext.setImportVOClass(ImportHotelOrgVO.class);
        importExcelContext.setImportLogic(this::handlerImportHotelOrg);
        return new IdVO<>(excelManager.generalImport(importExcelContext));
    }

    /**
     * 导入逻辑
     */
    private List<ImportRowErrorVO> handlerImportHotelOrg(ImportExcelContext<ImportHotelOrgVO> importExcelContext, List<ImportHotelOrgVO> importHotelOrgList){
        List<ImportRowErrorVO> errors = new ArrayList<>();

        // 检查酒店ID是否有效
        Map<Long, ImportHotelOrgVO> callApiHotelMap = new HashMap<>();
        Map<Long, HotelEntity> addHotelMap = new HashMap<>();
        importHotelOrgList.forEach(item -> {
            Long hotelId = AppUtility.parseLong(item.getHotelId());
            if(hotelId == null){
                errors.add(new ImportRowErrorVO(item.getRowNum(), ErrorCode.HOTEL_ID_IS_NOT_NUMBER));
                return;
            }

            // 检查机构联系人是否为空
            if(!StringUtil.isValidString(item.getContactName())){
                errors.add(new ImportRowErrorVO(item.getRowNum(), ErrorCode.ORG_CONTACT_NAME_CANNOT_BE_EMPTY));
                return;
            }

            // 检查邮箱是否合法
            if(!StringUtil.isValidString(item.getContactName()) || !ValidateUtil.isValidEmail(item.getContactEmail())){
                errors.add(new ImportRowErrorVO(item.getRowNum(), ErrorCode.CONTACT_EMAIL_INVALIDATED));
                return;
            }

            // 检查手机号码是否为空
            if(!StringUtil.isValidString(item.getContactMobile())){
                errors.add(new ImportRowErrorVO(item.getRowNum(), ErrorCode.CONTACT_MOBILE_CANNOT_BE_EMPTY));
                return;
            }
            // 查询酒店id是否已经关联了机构
            int count = orgRelatedHotelMapper.selectCountByHotelId(hotelId);
            if(count > 0){
                errors.add(new ImportRowErrorVO(item.getRowNum(), ErrorCode.HOTEL_ID_HAS_RELATED_ORG));
                return;
            }
            HotelEntity hotelEntity = hotelMapper.selectById(hotelId);
            if(hotelEntity == null){
                callApiHotelMap.put(hotelId, item);
            } else {
                addHotelMap.put(hotelId, hotelEntity);
            }
        });

        // 检查不通过 返回错误信息
        if(!CollectionUtils.isEmpty(errors)){
            return errors;
        }

        // 同步酒店信息
        if(!callApiHotelMap.isEmpty()){
            List<Long> syncHotelIdList = new ArrayList<>(callApiHotelMap.keySet());
            Map<Long, HotelEntity> syncHotelMap = hotelManager.queryHotelBasicInfo(syncHotelIdList, true).stream().collect(Collectors.toMap(HotelEntity::getHotelId, Function.identity()));
            for(Long syncHotelId : syncHotelIdList){
                HotelEntity hotelEntity = syncHotelMap.get(syncHotelId);
                if(hotelEntity == null){
                    ImportHotelOrgVO importHotelOrgVO = callApiHotelMap.get(syncHotelId);
                    errors.add(new ImportRowErrorVO(importHotelOrgVO.getRowNum(), ErrorCode.HOTEL_ID_NOT_EXIST));
                } else {
                    addHotelMap.put(hotelEntity.getHotelId(), hotelEntity);
                }
            }
        }
        // 检查不通过 返回错误信息
        if(!CollectionUtils.isEmpty(errors)){
            return errors;
        }

        // 批量新增酒店
        UserSession userSession = importExcelContext.getUserSession();
        importHotelOrgList.forEach(
            item ->  {
                try {
                    HotelEntity hotelEntity = addHotelMap.get(Long.parseLong(item.getHotelId()));
                    // 新增机构
                    OrgEntity org = new OrgEntity();
                    org.setOrgType(OrgTypeEnum.HOTEL.key);
                    org.setOrgName(hotelEntity.getNameEnUs());
                    org.setContactName(item.getContactName());
                    org.setContactMobile(item.getContactMobile());
                    org.setContactEmail(item.getContactEmail());
                    org.setState(StateEnum.Effective.key);
                    org.setCreator(userSession.getUsername());
                    org.setModifier(userSession.getUsername());
                    int insertResult = orgMapper.insert(org);
                    if (insertResult == 0) {
                        errors.add(new ImportRowErrorVO(item.getRowNum(), ErrorCode.ADD_ORG_FAILED));
                        return;
                    }
                    // 新增机构关联酒店
                    OrgRelatedHotelEntity orgRelatedHotel = new OrgRelatedHotelEntity();
                    orgRelatedHotel.setHotelId(hotelEntity.getHotelId());
                    orgRelatedHotel.setOrgId(org.getOrgId());
                    orgRelatedHotel.setCreator(userSession.getUsername());
                    orgRelatedHotelMapper.insert(orgRelatedHotel);
                } catch (Exception ex){
                    log.error("新增机构异常", ex);
                    errors.add(new ImportRowErrorVO(item.getRowNum(), ErrorCode.ADD_FAILED));
                }
            });
        return errors;
    }

    public List<OrgNameVO> queryOrgByName(HttpServletRequest request, String orgName, List<Integer> orgTypeIdList, int limitCount){
        UserSession userSession = UserSession.get();
        if(!Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.PLATFORM.key)){
            OrgNameVO orgNameVO = new OrgNameVO();
            orgNameVO.setOrgType(userSession.getUserOrg().getOrgType());
            orgNameVO.setOrgId(userSession.getUserOrg().getOrgId());
            orgNameVO.setOrgName(userSession.getUserOrg().getOrgName());
            return Lists.newArrayList(orgNameVO);
        }
        if(!StringUtil.isValidString(orgName)){
            return Lists.newArrayList();
        }
        return orgMapper.queryOrgListByName(orgName.trim(), orgTypeIdList, limitCount);
    }
}
