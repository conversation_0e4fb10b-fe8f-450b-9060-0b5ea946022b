<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectHotelTaxSettingsMapper">

    <select id="queryBidHotelTaxSettingsVO" resultType="com.fangcang.grfp.core.vo.BidHotelTaxSettingsVO">
            SELECT
                earlyck_fee_type,
                earlyck_fee_value,
                earlyck_fee_is_include,
                lodgtx_fee_type,
                lodgtx_fee_value,
                lodgtx_fee_is_include,
                statetx_fee_type,
                statetx_fee_value,
                statetx_fee_is_include,
                citytx_fee_type,
                citytx_fee_value,
                citytx_fee_is_include,
                vatgstrm_fee_type,
                vatgstrm_fee_value,
                vatgstrm_fee_is_include,
                vatgstfb_fee_type,
                vatgstfb_fee_value,
                vatgstfb_fee_is_include,
                service_fee_type,
                service_fee_value,
                service_fee_is_include,
                occ_fee_type,
                occ_fee_value,
                occ_fee_is_include,
                othertx1_fee_type,
                othertx1_fee_desc,
                othertx1_fee_value,
                othertx1_fee_is_include,
                othertx2_fee_type,
                othertx2_fee_desc,
                othertx2_fee_value,
                othertx2_fee_is_include,
                othertx3_fee_type,
                othertx3_fee_desc,
                othertx3_fee_value,
                othertx3_fee_is_include
            FROM
                t_project_hotel_tax_settings
            WHERE
                project_intent_hotel_id = #{projectIntentHotelId}
    </select>

    <select id="queryBidHotelTaxSettingsVOByProjectHotelId" resultType="com.fangcang.grfp.core.vo.BidHotelTaxSettingsVO">
        SELECT
            earlyck_fee_type,
            earlyck_fee_value,
            earlyck_fee_is_include,
            lodgtx_fee_type,
            lodgtx_fee_value,
            lodgtx_fee_is_include,
            statetx_fee_type,
            statetx_fee_value,
            statetx_fee_is_include,
            citytx_fee_type,
            citytx_fee_value,
            citytx_fee_is_include,
            vatgstrm_fee_type,
            vatgstrm_fee_value,
            vatgstrm_fee_is_include,
            vatgstfb_fee_type,
            vatgstfb_fee_value,
            vatgstfb_fee_is_include,
            service_fee_type,
            service_fee_value,
            service_fee_is_include,
            occ_fee_type,
            occ_fee_value,
            occ_fee_is_include,
            othertx1_fee_type,
            othertx1_fee_desc,
            othertx1_fee_value,
            othertx1_fee_is_include,
            othertx2_fee_type,
            othertx2_fee_desc,
            othertx2_fee_value,
            othertx2_fee_is_include,
            othertx3_fee_type,
            othertx3_fee_desc,
            othertx3_fee_value,
            othertx3_fee_is_include
        FROM
            t_project_hotel_tax_settings
        WHERE
            project_id = #{projectId}
        AND hotel_id = #{hotelId}
    </select>
</mapper>
