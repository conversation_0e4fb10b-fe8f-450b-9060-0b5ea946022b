package com.fangcang.grfp.core.constant;

import com.fangcang.grfp.core.util.GenericAppUtility;

import java.util.ArrayList;
import java.util.List;

public class UserPermission {

    public static final String SYS_CONFIG = "SysConfig";  // 系统配置
    public static final String TEXT_RESOURCE = "TextResource"; // 文字资源
    public static final String BASIC_DATA = "BasicData"; // 基础数据 （城市，国家，省份，酒店，酒店品牌。酒店集团数据查询）
    public static final String ORG = "Org"; // 机构管理
    public static final String USER = "User"; // 用户管理
    public static final String USER_C_U_D = "User_C_U_D"; // 用户增删改
    public static final String PROJECT_C_U_D = "Project_C_U_D"; // 项目增删改
    public static final String PROJECT_CONTRACT_C_U_D = "ProjectContract_C_U_D"; // 报价签约增删改
    public static final String BID_TASK_C_U_D = "BID_TASK_C_U_D"; // 报价任务增删改

    public static final String POI = "Poi"; // POI 管理
    public static final String CURRENCY_EXCHANGE_RATE = "CurrencyExchangeRate";  // 币种汇率

    public static final String USER_PERMISSION_C_U_D = "UserPermission_C_U_D";  // 用户权限

    public static String getPermissionName(int languageId, String permission){
        return GenericAppUtility.getText(languageId, "USER_PERMISSION_" + permission);
    }
    public static List<String> userPermissionList = new ArrayList<String>() {{
        add(SYS_CONFIG);
        add(TEXT_RESOURCE);
        add(BASIC_DATA);
        add(ORG);
        add(USER);
        add(USER_C_U_D);
        add(PROJECT_C_U_D);
        add(PROJECT_CONTRACT_C_U_D);
        add(POI);
        add(CURRENCY_EXCHANGE_RATE);
        add(USER_PERMISSION_C_U_D);
    }};
}
