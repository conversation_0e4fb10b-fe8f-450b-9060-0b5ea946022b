package com.fangcang.grfp.core.vo.response.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @auther gang
 * @description 酒店报价（房档明细）返回实体
 * @date 2024/06/16
 */
@ApiModel("酒店报价（备注信息）返回实体")
@Getter
@Setter
public class ProjectHotelRemarkResponse extends BaseVO {

    // 项目报价ID
    @ApiModelProperty("项目报价ID")
    private Integer projectIntentHotelId;

    // 项目ID
    @ApiModelProperty("项目ID")
    private Integer projectId;

    // 酒店ID
    @ApiModelProperty("酒店ID")
    private Long hotelId;

    // 备注类型 议价中：UNDER_NEGOTIATION
    @ApiModelProperty("备注类型 议价中：UNDER_NEGOTIATION")
    private String remarkType;

    // 备注
    @ApiModelProperty("备注")
    private String remark;

    // 创建人
    @ApiModelProperty("创建人")
    private String creator;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;


}
