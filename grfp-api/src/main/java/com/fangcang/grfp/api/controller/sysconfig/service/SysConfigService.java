package com.fangcang.grfp.api.controller.sysconfig.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.api.controller.sysconfig.vo.AddSysConfigRequest;
import com.fangcang.grfp.api.controller.sysconfig.vo.DeleteSysConfigRequest;
import com.fangcang.grfp.api.controller.sysconfig.vo.SysConfigVO;
import com.fangcang.grfp.api.controller.sysconfig.vo.UpdateSysConfigRequest;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.cached.CachedSysConfigService;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.constant.OperateCode;
import com.fangcang.grfp.core.entity.SysConfigEntity;
import com.fangcang.grfp.core.entity.SysConfigLogEntity;
import com.fangcang.grfp.core.mapper.SysConfigLogMapper;
import com.fangcang.grfp.core.mapper.SysConfigMapper;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.ExceptionUtility;
import com.fangcang.grfp.core.vo.ListSysConfigVO;
import com.fangcang.grfp.core.vo.request.ListSysConfigRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class SysConfigService {


    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private SysConfigLogMapper sysConfigLogMapper;
    @Autowired
    private CachedSysConfigService cachedSysConfigService;

    public SysConfigVO getSysConfigVO(String sysConfigCode){
        SysConfigEntity sysConfig = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfigEntity>()
                .eq(SysConfigEntity::getSysConfigCode, sysConfigCode));
        if(sysConfig == null){
            AppUtility.serviceError(ErrorCode.RECORD_ALREADY_EXISTED);
        }
        SysConfigVO vo = new SysConfigVO();
        BeanUtils.copyProperties(sysConfig, vo);
        return vo;
    }

    public PageVO<ListSysConfigVO> querySysConfigVOPageList(ListSysConfigRequest request){
        IPage<ListSysConfigVO> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page = sysConfigMapper.querySysConfigVOPageList(page, request);
        return new PageVO<>((int) page.getTotal(), (int) page.getPages(), page.getRecords());
    }

    public ListSysConfigVO add(UserSession userSession, AddSysConfigRequest request){
        SysConfigEntity dbSysConfig = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfigEntity>()
                .eq(SysConfigEntity::getSysConfigCode, request.getSysConfigCode()));
        if(dbSysConfig != null){
            AppUtility.serviceError(ErrorCode.RECORD_ALREADY_EXISTED);
        }

        Date now = new Date();
        SysConfigEntity sysConfig = new SysConfigEntity();
        sysConfig.setSysConfigCode(request.getSysConfigCode());
        sysConfig.setSysConfigValue(request.getSysConfigValue());
        sysConfig.setCreator(userSession.getUsername());
        sysConfig.setCreateTime(now);
        sysConfig.setModifier(userSession.getUsername());
        sysConfig.setModifyTime(now);
        int insertResult = sysConfigMapper.insert(sysConfig);
        if(insertResult == 0){
            AppUtility.serviceError(ErrorCode.ADD_FAILED);
        }

        ListSysConfigVO vo = new ListSysConfigVO();
        BeanUtils.copyProperties(sysConfig, vo);

        // 新增日志
        insertSysConfigLog(OperateCode.OPERATE_ADD, vo.getSysConfigCode(), "", vo.toString(), userSession.getUsername(), now);

        return vo;
    }


    public ListSysConfigVO update(UserSession userSession, UpdateSysConfigRequest request){
        SysConfigEntity sysConfig = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfigEntity>()
                .eq(SysConfigEntity::getSysConfigCode, request.getSysConfigCode()));
        if(sysConfig == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }
        ListSysConfigVO beforeVO = new ListSysConfigVO();
        BeanUtils.copyProperties(sysConfig, beforeVO);

        Date now = new Date();
        sysConfig.setSysConfigCode(request.getSysConfigCode());
        sysConfig.setSysConfigValue(request.getSysConfigValue());
        sysConfig.setModifier(userSession.getUsername());
        sysConfig.setModifyTime(now);
        int insertResult = sysConfigMapper.updateById(sysConfig);
        if(insertResult == 0){
            AppUtility.serviceError(ErrorCode.UPDATE_FAILED);
        }
        // 清空cache
        cachedSysConfigService.clearValue(request.getSysConfigCode());

        ListSysConfigVO vo = new ListSysConfigVO();
        BeanUtils.copyProperties(sysConfig, vo);

        // 新增日志
        insertSysConfigLog(OperateCode.OPERATE_UPDATE, vo.getSysConfigCode(), beforeVO.toString(), vo.toString(), userSession.getUsername(), now);

        return vo;
    }

    public void delete(UserSession userSession, DeleteSysConfigRequest request){
        SysConfigEntity sysConfig = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfigEntity>()
                .eq(SysConfigEntity::getSysConfigCode, request.getSysConfigCode()));
        if(sysConfig == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }
        ListSysConfigVO beforeVO = new ListSysConfigVO();
        BeanUtils.copyProperties(sysConfig, beforeVO);

        int deleteResult = sysConfigMapper.deleteById(sysConfig.getSysConfigCode());
        if(deleteResult == 0){
            AppUtility.serviceError(ErrorCode.DELETE_FAILED);
        }

        // 新增日志
        insertSysConfigLog(OperateCode.OPERATE_DELETE, beforeVO.getSysConfigCode(), beforeVO.toString(), "", userSession.getUsername(), new Date());

    }

    private void insertSysConfigLog(String operateCode, String sysConfigCode, String beforeKvp, String afterKvp, String userName, Date operateTime){
        try {
            SysConfigLogEntity sysConfigLogEntity = new SysConfigLogEntity();
            sysConfigLogEntity.setOperateCode(operateCode);
            sysConfigLogEntity.setSysConfigCode(sysConfigCode);
            sysConfigLogEntity.setBeforeKvJson(beforeKvp);
            sysConfigLogEntity.setAfterKvJson(afterKvp);
            sysConfigLogEntity.setCreator(userName);
            sysConfigLogEntity.setCreateTime(operateTime);
            sysConfigLogMapper.insert(sysConfigLogEntity);
        } catch (Exception ex){
            log.error("新增系统配置日志异常", ExceptionUtility.getDetailedExceptionString(ex));
        }
    }

}
