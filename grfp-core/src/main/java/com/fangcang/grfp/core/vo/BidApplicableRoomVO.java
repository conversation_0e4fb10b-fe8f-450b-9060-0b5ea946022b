package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("报价可用房型")
@Getter
@Setter
public class BidApplicableRoomVO extends BaseVO {

    @ApiModelProperty("报价可用房型ID, 新增为空，修改传ID")
    private Integer priceApplicableRoomId;

    @ApiModelProperty("价格房档ID, 新增传ID，修改传null")
    private Integer hotelPriceLevelId;

    @ApiModelProperty("房型ID")
    private Long roomTypeId;

    @ApiModelProperty("排序")
    @NotNull
    private Integer displayOrder;

    @ApiModelProperty("房型名称")
    private String roomName;

    @ApiModelProperty("自定义房型名称")
    private String customRoomTypeName;

}
