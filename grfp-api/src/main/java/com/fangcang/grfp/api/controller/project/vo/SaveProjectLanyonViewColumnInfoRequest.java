package com.fangcang.grfp.api.controller.project.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@ApiModel("保持项目Lanyon显示")
@Getter
@Setter
public class SaveProjectLanyonViewColumnInfoRequest {

    // 唯一ID
    @ApiModelProperty("唯一ID")
    private Integer projectLanyonViewKeysId;

    // 项目ID
    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;

    // 基础信息
    @ApiModelProperty("基础信息")
    private String baseInfo;

    // 酒店认证信息
    @ApiModelProperty("酒店认证信息")
    private String hotelVerify;

    // 酒店设施
    @ApiModelProperty("酒店设施")
    private String hotelFacilities;

    // 基础协议价报价信息
    @ApiModelProperty("基础协议价报价信息")
    private String bidInfo;

    // 团房会议室报价信息
    @ApiModelProperty("团房会议室报价信息")
    private String meetingRoomBidInfo;

    // 长住房报价信息
    @ApiModelProperty("长住房报价信息")
    private String longBidInfo;

    // 酒店服务信息
    @ApiModelProperty("酒店服务信息")
    private String hotelService;

    // 基础协议定制问题
    @ApiModelProperty("基础协议定制问题")
    private String userDefined;

    // 会议室定制问题
    @ApiModelProperty("会议室定制问题")
    private String mtgUserDefined;

    // LRA价格不适用日期报价信息
    @ApiModelProperty("LRA价格不适用日期报价信息")
    private String larUnApplicableDayInfo;

    // 基础协议税费及协议相关信息
    @ApiModelProperty("基础协议税费及协议相关信息")
    private String baseServiceFee;


}

