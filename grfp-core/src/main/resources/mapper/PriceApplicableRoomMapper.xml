<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.PriceApplicableRoomMapper">
    <select id="selectBidApplicableRoom" resultType="com.fangcang.grfp.core.vo.BidApplicableRoomVO">
        SELECT price_applicable_room_id AS priceApplicableRoomId,
               hotel_price_level_id     AS hotelPriceLevelId,
               room_type_id             AS roomTypeId,
               custom_room_type_name    as customRoomTypeName,
               display_order            AS displayOrder
        FROM t_price_applicable_room
        WHERE project_intent_hotel_id = #{projectIntentHotelId}
        ORDER BY display_order
    </select>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO t_price_applicable_room
        (project_intent_hotel_id, project_id, hotel_id, hotel_price_level_id, room_type_id, display_order, custom_room_type_name, creator, modifier)
        VALUES
        <foreach item="item" collection="list" separator="," index="index">
            (#{item.projectIntentHotelId}, #{item.projectId}, #{item.hotelId}, #{item.hotelPriceLevelId}, #{item.roomTypeId}, #{item.displayOrder}, #{item.customRoomTypeName},
             #{item.creator}, #{item.modifier})
        </foreach>
        ON DUPLICATE KEY UPDATE display_order         = VALUES(display_order),
                                modifier              = VALUES(modifier),
                                custom_room_type_name = values(custom_room_type_name)
    </insert>

    <select id="queryPriceApplicableRoomInfoByProjectId" resultType="com.fangcang.grfp.core.vo.PriceApplicableRoomVO">
        select t1.price_applicable_room_id as priceApplicableRoomId,
               t1.project_intent_hotel_id  as projectIntentHotelId,
               t1.project_id               as projectId,
               t1.hotel_id                 as hotelId,
               t1.room_type_id             as roomTypeId,
               t2.room_level_no            as roomLevelNo,
               t1.display_order            as displayOrder,
               t1.creator                  as creator,
               t1.create_time              as createTime,
               t1.modifier                 as modifier,
               t1.modify_time              as modifyTime,
               t1.custom_room_type_name    as customRoomTypeName
        FROM t_price_applicable_room t1
                 inner join t_project_hotel_price_level t2 on t1.hotel_price_level_id = t2.hotel_price_level_id
        WHERE t1.PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </select>

    <insert id="batchInsert">
        insert into t_price_applicable_room (project_intent_hotel_id, project_id, hotel_id, hotel_price_level_id, room_type_id, display_order, custom_room_type_name, creator, modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectIntentHotelId}, #{item.projectId}, #{item.hotelId}, #{item.hotelPriceLevelId}, #{item.roomTypeId}, #{item.displayOrder}, #{item.customRoomTypeName},
             #{item.creator}, #{item.modifier})
        </foreach>
    </insert>
</mapper>
