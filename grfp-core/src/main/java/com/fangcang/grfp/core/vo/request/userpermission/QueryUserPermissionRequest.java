package com.fangcang.grfp.core.vo.request.userpermission;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("查询用户权限列表请求参数")
@Getter
@Setter
public class QueryUserPermissionRequest extends PageQuery {

    @ApiModelProperty("机构类型 机构类型 1：平台，2：酒店，3：企业，4酒店集团")
    private Integer orgType;

    @ApiModelProperty("角色编号")
    private String roleCode;

    @ApiModelProperty("权限")
    private String permission;
}
