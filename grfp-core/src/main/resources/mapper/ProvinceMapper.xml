<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProvinceMapper">
    <insert id="batchUpsert">
        insert into t_province(country_code, province_code, name_zh_cn, name_en_us, creator, modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.countryCode}, #{item.provinceCode}, #{item.nameZhCn}, #{item.nameEnUs}, #{item.creator}, #{item.modifier})
        </foreach>
        on duplicate key update country_code  = values(country_code),
                                province_code = values(province_code),
                                name_zh_cn    = values(name_zh_cn),
                                name_en_us    = values(name_en_us),
                                modifier      = values(modifier)
    </insert>

    <select id="listDataPage" resultType="com.fangcang.grfp.core.entity.ProvinceEntity">
        SELECT *
        FROM
        t_province
        <where>
            <if test="query.nameEnUs != null and query.nameEnUs != ''">
                AND `name_en_us` = #{query.nameEnUs}
            </if>
            <if test="query.nameZhCn != null and query.nameZhCn != ''">
                AND `name_zh_cn` = #{query.nameZhCn}
            </if>
            <if test="query.countryCode != null and query.countryCode != ''">
                AND `country_code` = #{query.countryCode}
            </if>
            <if test="query.provinceCode != null and query.provinceCode != ''">
                AND `province_code` = #{query.provinceCode}
            </if>
        </where>
        ORDER BY `name_en_us` ASC
    </select>

    <select id="selectProvinceNameList" resultType="com.fangcang.grfp.core.vo.ProvinceNameVO">
        SELECT
            country_code AS countryCode,
            province_code AS provinceCode,
            name_en_us AS nameEnUs,
            name_zh_cn AS nameZhCn
        FROM t_province
        WHERE (name_en_us LIKE CONCAT('%', #{provinceName}, '%') OR name_zh_cn LIKE CONCAT('%', #{provinceName}, '%'))
        <if test="countryCode != null and countryCode != ''">
            AND country_code = #{countryCode}
        </if>
        <if test="languageId != null and languageId == 1">
            ORDER BY `name_en_us` ASC
        </if>
        <if test="languageId != null and languageId == 2">
            ORDER BY `name_zh_cn` ASC
        </if>
        <if test="limitCount != null and limitCount > 0">
            LIMIT #{limitCount}
        </if>
    </select>
</mapper>
