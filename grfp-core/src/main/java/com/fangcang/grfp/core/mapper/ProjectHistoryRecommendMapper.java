package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.ProjectHistoryRecommendEntity;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 项目历史数据推荐 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface ProjectHistoryRecommendMapper extends BaseMapper<ProjectHistoryRecommendEntity> {

    void resetRecommendPoiNearHotelInfo(@Param("projectId") Integer projectId);

    int insertOrUpdate(ProjectHistoryRecommendEntity projectHistoryRecommend);

    void resetHighQuality(@Param("projectId") Integer projectId);

    void resetSavedHotel(Integer projectId);

    void resetRecommendFrequency(@Param("projectId") Integer projectId);

    void resetRecommendFrequencySameLevelInfo(@Param("projectId") Integer projectId);
}
