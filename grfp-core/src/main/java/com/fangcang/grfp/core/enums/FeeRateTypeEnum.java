package com.fangcang.grfp.core.enums;

public enum FeeRateTypeEnum {

    PERCENTAGE(1, "百分比"),
    FIXED(2, "固定");

    public Integer key;
    public String value;

    FeeRateTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (FeeRateTypeEnum feeRateTypeEnum : FeeRateTypeEnum.values()) {
            if (feeRateTypeEnum.key.equals(key)) {
                value = feeRateTypeEnum.value;
                break;
            }
        }
        return value;
    }

    public static Integer getKeyByLanyonUom(String uom) {
        if("P".equals(uom)){
            return PERCENTAGE.key;
        }
        if("F".equals(uom)){
            return FIXED.key;
        }
        return null;
    }
}
