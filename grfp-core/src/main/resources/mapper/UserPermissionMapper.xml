<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.UserPermissionMapper">

    <select id="queryUserPermissionPage" parameterType="com.fangcang.grfp.core.vo.request.userpermission.QueryUserPermissionRequest"
            resultType="com.fangcang.grfp.core.entity.UserPermissionEntity">
        SELECT
            *
        FROM
            t_user_permission
        <where>
            <if test="query.orgType != null">
                AND org_type = #{query.orgType}
            </if>
            <if test="query.roleCode != null">
                AND role_code = #{query.roleCode}
            </if>
            <if test="query.permission != null">
                AND permission = #{query.permission}
            </if>
        </where>
        ORDER BY org_type, role_code, permission
    </select>
</mapper>
