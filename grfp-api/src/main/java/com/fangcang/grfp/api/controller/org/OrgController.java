package com.fangcang.grfp.api.controller.org;

import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.org.service.OrgService;
import com.fangcang.grfp.api.controller.org.vo.*;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.IdVO;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.constant.UserPermission;
import com.fangcang.grfp.core.usersession.RequiresPermissions;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.vo.ListOrgVO;
import com.fangcang.grfp.core.vo.OrgNameVO;
import com.fangcang.grfp.core.vo.OrgRelatedHotelBrandVO;
import com.fangcang.grfp.core.vo.OrgRelatedHotelVO;
import com.fangcang.grfp.core.vo.request.ListOrgRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@Api(tags="机构接口")
@RestController
@RequestMapping("/Api/Org")
public class OrgController extends BaseController {

    @Autowired
    private OrgService orgService;

    @ApiOperation("机构列表")
    @PostMapping("/List")
    @ResponseBody
    @RequiresPermissions(UserPermission.ORG)
    public Result<PageVO<ListOrgVO>> list(HttpServletRequest request, @Valid @RequestBody ListOrgRequest listOrgRequest) {
        return Result.ok(orgService.queryOrgVOPageList(request, listOrgRequest));
    }

    @ApiOperation("新增机构")
    @PostMapping("/Add")
    @ResponseBody
    @UserAuditLog("机构-新增机构")
    @RequiresPermissions(UserPermission.ORG)
    public Result<Integer> add(HttpServletRequest request, @Valid @RequestBody AddOrgRequest addOrgRequest) {
        return Result.ok(orgService.addOrg(request, addOrgRequest));
    }

    @ApiOperation("获取机构信息")
    @PostMapping("/OrgInfo")
    @ResponseBody
    @RequiresPermissions(UserPermission.ORG)
    public Result<OrgInfoVO> orgInfo(HttpServletRequest request, @Valid @RequestBody OrgInfoRequest orgInfoRequest) {
        return Result.ok(orgService.orgInfo(request, orgInfoRequest));
    }

    @ApiOperation("编辑机构")
    @PostMapping("/UpdateOrg")
    @ResponseBody
    @RequiresPermissions(UserPermission.ORG)
    @UserAuditLog("机构-编辑机构")
    public Result<Void> updateOrg(HttpServletRequest request, @Valid @RequestBody UpdateOrgRequest updateOrgRequest) {
        orgService.updateOrgInfo(request, updateOrgRequest);
        return Result.ok();
    }

    @ApiOperation("获取机构关联酒店品牌信息")
    @PostMapping("/RelatedHotelBrandList")
    @ResponseBody
    public Result<PageVO<OrgRelatedHotelBrandVO>> relatedHotelBrandList(HttpServletRequest request, @Valid @RequestBody QueryOrgRelatedInfoRequest queryOrgRelatedInfoRequest) {
        return Result.ok(orgService.orgRelatedBrandList(request, queryOrgRelatedInfoRequest));
    }


    @ApiOperation("酒店集团机构新增酒店品牌")
    @PostMapping("/AddRelatedHotelBrand")
    @ResponseBody
    @RequiresPermissions(UserPermission.ORG)
    @UserAuditLog("机构-酒店集团机构新增酒店品牌")
    public Result<Void> addRelatedHotelBrand(HttpServletRequest request, @Valid @RequestBody AddRelatedHotelBrandRequest addHotelBrandRequest) {
        orgService.addRelatedHotelBrand(request, addHotelBrandRequest);
        return Result.ok();
    }

    @ApiOperation("酒店集团机构删除酒店品牌")
    @PostMapping("/DeleteRelatedHotelBrand")
    @ResponseBody
    @RequiresPermissions(UserPermission.ORG)
    @UserAuditLog("机构-酒店集团机构删除酒店品牌")
    public Result<Void> deleteRelatedHotelBrand(HttpServletRequest request, @Valid @RequestBody DeleteRelatedHotelBrandRequest deleteRelatedHotelBrandRequest) {
        AppUtility.isPlatformUser(UserSession.get());
        orgService.deleteRelatedHotelBrand(request, deleteRelatedHotelBrandRequest);
        return Result.ok();
    }

    @ApiOperation("获取机构关联酒店信息")
    @PostMapping("/RelatedHotelList")
    @ResponseBody
    public Result<PageVO<OrgRelatedHotelVO>> relatedHotelList(HttpServletRequest request, @Valid @RequestBody QueryOrgRelatedInfoRequest queryOrgRelatedInfoRequest) {
        return Result.ok(orgService.orgRelatedHotelList(request, queryOrgRelatedInfoRequest));
    }

    @ApiOperation("酒店机构新增酒店")
    @PostMapping("/AddRelatedHotel")
    @ResponseBody
    @RequiresPermissions(UserPermission.ORG)
    @UserAuditLog("机构-酒店机构新增酒店")
    public Result<Void> addRelatedHotel(HttpServletRequest request, @Valid @RequestBody AddRelatedHotelRequest addRelatedHotelRequest) {
        AppUtility.isPlatformUser(UserSession.get());
        orgService.addRelatedHotel(request, addRelatedHotelRequest);
        return Result.ok();
    }

    @ApiOperation("酒店机构删除酒店")
    @PostMapping("/DeleteRelatedHotel")
    @ResponseBody
    @RequiresPermissions(UserPermission.ORG)
    @UserAuditLog("机构-酒店机构删除酒店")
    public Result<Void> deleteRelatedHotel(HttpServletRequest request, @Valid @RequestBody DeleteRelatedHotelRequest deleteRelatedHotelRequest) {
        AppUtility.isPlatformUser(UserSession.get());
        orgService.deleteRelatedHotel(request, deleteRelatedHotelRequest);
        return Result.ok();
    }

    @ApiOperation("删除机构")
    @PostMapping("/DeleteOrg")
    @ResponseBody
    @RequiresPermissions(UserPermission.ORG)
    @UserAuditLog("机构-删除机构")
    public Result<Void> deleteOrg(HttpServletRequest request, @Valid @RequestBody DeleteOrgRequest deleteOrgRequest) {
        AppUtility.isPlatformUser(UserSession.get());
        orgService.delete(request, deleteOrgRequest);
        return Result.ok();
    }

    @ApiOperation("批量注册酒店机构")
    @PostMapping(value = "/ImportHotelOrg")
    @UserAuditLog("机构-批量注册酒店机构")
    @RequiresPermissions(UserPermission.ORG)
    public Result<IdVO<Long>> importHotelOrg(@RequestParam("file") MultipartFile file) throws IOException {
        AppUtility.isPlatformUser(UserSession.get());
        return Result.ok(orgService.importHotelOrg(file));
    }




}
