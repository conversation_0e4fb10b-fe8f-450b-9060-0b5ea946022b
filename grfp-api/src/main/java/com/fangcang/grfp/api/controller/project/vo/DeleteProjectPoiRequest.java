package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("删除项目 POI 请求")
@Getter
@Setter
public class DeleteProjectPoiRequest extends BaseVO {

    private static final long serialVersionUID = 7639244174520847078L;

    @ApiModelProperty(value = "项目 ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "POI ID", required = true)
    @NotNull
    private Long poiId;
}
