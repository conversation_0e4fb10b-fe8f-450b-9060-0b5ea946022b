 package com.fangcang.grfp.core.enums;

 /**
  * POI周边酒店推荐原因
  */
 public enum RecommendPoiNearHotelEnum {

    ROOM_NIGHT_MORE_THEN_100(1, "POI周边高产酒店，建议邀约"), // 如果酒店去年预订间夜超过100间夜
    SAME_LEVEL_ROOM_NIGHT_FIRST(2, "POI周边酒店，同档员工最常订酒店"), // 如果酒店在当前星级预订间夜量第一
    OTA_49(3, "POI周边超高评分酒店"), // 如果酒店评分4.9分及以上
    OPEN_IN_2_YEAR(4, "POI周边新开业酒店，建议邀约"), // 酒店为近2年开业
    LAST_YEAR_ROOM_NIGHT_600(5, "POI周边商旅常订酒店，建议邀约"); // 如果酒店商旅近12个月预订总间夜超过600间夜


    public int key;

    public String value;

    RecommendPoiNearHotelEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (RecommendPoiNearHotelEnum recommendPoiNearHotelEnum : RecommendPoiNearHotelEnum.values()) {
            if (recommendPoiNearHotelEnum.key == key) {
                value = recommendPoiNearHotelEnum.value;
                break;
            }
        }
        return value;
    }
}
