package com.fangcang.grfp.api.controller.login.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.usersession.UserSession;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("登录响应")
@Getter
@Setter
public class LoginResponse extends BaseVO {

	// ------------------------------------------------------------------------------------ Private Member Variable
	
	@ApiModelProperty("用户信息")
	private UserSession userInfo;
	@ApiModelProperty("验证令牌")
	private String authorization;
	@ApiModelProperty("是否需要重置密码")
	private boolean isResetPassword;

}
