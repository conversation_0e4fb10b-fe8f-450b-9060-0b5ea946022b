package com.fangcang.grfp.core.oss;

import cn.hutool.core.util.IdUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;

/**
 * OSS 接口
 */
public interface IOssClient {

    /**
     * 上传
     */
    void putObject(String bucket, String key, byte[] object, String contentType, String fileName);

    /**
     * 上传
     */
    void putObject(String bucket, String key, InputStream inputStream, String contentType, String fileName);

    /**
     * 下载
     */
    InputStream getObject(String bucket, String key);

    /**
     * 从私有桶拷贝到公有桶
     */
    void copyObject(String sourceBucket, String sourceKey, String destinationBucket, String destinationKey);


    /**
     * 生成临时访问 url
     */
    String generatePresignedUrl(String bucket, String key, int expirationInMinutes);

    /**
     * 生成访问 url. 用于公开桶
     */
    default String generatePublicUrl(String bucketPublicDomain, String key) {
        return bucketPublicDomain + "/" + key;
    }


    /**
     * 生成文件Key
     * /xx/xx/xxxxxxxxx.xx
     */
    default String generateFileKey(String directory, String originalFileName) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotEmpty(directory)) {
            sb.append(directory);
            sb.append("/");
        }
        sb.append(IdUtil.objectId());

        if (StringUtils.isNotEmpty(originalFileName) && StringUtils.contains(originalFileName, ".")) {
            sb.append(".");
            sb.append(StringUtils.substringAfterLast(originalFileName, "."));
        }
        return sb.toString();
    }


}
