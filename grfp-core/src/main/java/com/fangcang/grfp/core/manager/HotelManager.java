package com.fangcang.grfp.core.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.fangcang.grfp.core.base.ApplicationContextHolder;
import com.fangcang.grfp.core.cached.CachedCityService;
import com.fangcang.grfp.core.config.ThreadPoolAutoConfiguration;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.constant.SysConfig;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.HotelImageRequest;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.HotelInfoRequest;
import com.fangcang.grfp.core.dto.dhub.request.product.HotelLowestPriceRequest;
import com.fangcang.grfp.core.dto.dhub.response.Response;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.*;
import com.fangcang.grfp.core.dto.dhub.response.product.HotelLowestPrice;
import com.fangcang.grfp.core.dto.dhub.response.product.HotelLowestPriceResponse;
import com.fangcang.grfp.core.dto.dhub.response.product.HotelPriceItem;
import com.fangcang.grfp.core.entity.*;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.enums.ReturnResultEnum;
import com.fangcang.grfp.core.enums.YesOrNoEnum;
import com.fangcang.grfp.core.mapper.HotelDataMapper;
import com.fangcang.grfp.core.mapper.HotelImageMapper;
import com.fangcang.grfp.core.mapper.HotelLogMapper;
import com.fangcang.grfp.core.mapper.HotelMapper;
import com.fangcang.grfp.core.remote.tmchub.manager.TmcHubApiManager;
import com.fangcang.grfp.core.remote.tmchub.request.SignatureHelpRequestDto;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.ExceptionUtility;
import com.fangcang.grfp.core.util.GenericAppUtility;
import com.fangcang.grfp.core.util.JsonUtil;
import com.fangcang.grfp.core.util.StringUtil;
import com.fangcang.grfp.core.vo.DestinationHotelVO;
import com.fangcang.grfp.core.vo.response.HotelLowestPricesResponse;
import com.fangcang.grfp.core.vo.response.hotel.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
public class HotelManager {

    @Resource
    private TmcHubApiManager tmcHubApiManager;

    @Resource
    private HotelMapper hotelMapper;

    @Resource
    private HotelDataMapper hotelDataMapper;

    @Resource
    private HotelLogMapper hotelLogMapper;

    @Resource
    private CachedCityService cachedCityService;

    @Resource
    private HotelImageMapper hotelImageMapper;

    @Resource
    private CityManager cityManager;

    /**
     * 同步酒店信息
     * @param hotelIds 酒店 id
     * @return 有效的酒店 id
     */
    public Set<Long> syncHotelInfo(Collection<Long> hotelIds) {
        log.info("Start sync hotel info, hotel ids: {}", hotelIds);
        // 返回结果, 有效的酒店 id(从 GLink 那里能查到的)
        Set<Long> activeHotelIds = new HashSet<>();
        if (CollUtil.isEmpty(hotelIds)) {
            return activeHotelIds;
        }

        // 分批处理, GLink 每次最多查询 10 个
        CollUtil.split(hotelIds, 10).forEach(subHotelIds -> {
            // 酒店暂存容器
            Map<Long, HotelEntity> hotelMap = new HashMap<>(subHotelIds.size());
            Map<Long, HotelDataEntity> hotelDataMap = new HashMap<>(subHotelIds.size());

            // 按不同语言同步酒店信息
            Stream.of(LanguageEnum.values()).forEach(languageEnum -> {
                // 查询酒店信息
                List<HotelInfo> hotelInfos = queryHotelInfos(subHotelIds, languageEnum, buildSettings());
                if (CollUtil.isEmpty(hotelInfos)) {
                    return;
                }

                // 处理酒店信息
                hotelInfos.forEach(hotelInfo -> {
                    // 从缓存容器里拿酒店实体(为了一次性处理不同语言), 填充酒店实体信息
                    HotelEntity hotelEntity = hotelMap.computeIfAbsent(hotelInfo.getHotelId(), k -> new HotelEntity());
                    fillHotelEntity(languageEnum, hotelInfo, hotelEntity);

                    // 记录酒店日志
                    HotelDataEntity hotelDataEntity = hotelDataMap.computeIfAbsent(hotelInfo.getHotelId(), l -> new HotelDataEntity());
                    fillHotelDataEntity(languageEnum, hotelInfo, hotelDataEntity);

                    // 记录有效酒店 id
                    activeHotelIds.add(hotelInfo.getHotelId());
                });
            });

            // 查询酒店图片信息, 酒店图片信息不分语言
            List<HotelImage> hotelImages = queryHotelImages(subHotelIds);
            List<HotelImageEntity> hotelImageEntities = toHotelImageEntities(hotelImages);

            // 更新酒店图片信息
            if (CollUtil.isNotEmpty(hotelImageEntities)) {
                hotelImageMapper.batchUpsert(hotelImageEntities);
            }

            // 更新酒店
            if (CollUtil.isNotEmpty(hotelMap.values())) {
                hotelMapper.batchUpsert(new ArrayList<>(hotelMap.values()));
            }

            // 插入 hotel data 数据
            if (CollUtil.isNotEmpty(hotelDataMap.values())) {
                hotelDataMapper.batchUpsert(new ArrayList<>(hotelDataMap.values()));
            }

            // 记录酒店变更日志
            recordHotelLog(hotelMap);

            // 清除缓存
            hotelMap.clear();
            hotelDataMap.clear();
        });
        log.info("End sync hotel info, active hotel ids: {}", activeHotelIds);
        if(activeHotelIds.size() != hotelIds.size()){
            log.error("activeHotelIds {} is not the same with request hotelIds {}", activeHotelIds, hotelIds);
        }
        return activeHotelIds;
    }

    /**
     * 查询酒店基本信息(中英都会查)
     */
    public List<HotelEntity> queryHotelBasicInfo(Collection<Long> hotelIds, boolean isSync) {
        // 先查英文的
        List<HotelEntity> hotelListEnUs = this.queryHotelBasicInfo(LanguageEnum.EN_US, hotelIds, isSync);
        if (hotelListEnUs.size() != hotelIds.size()) {
            // 如果没有英文的, 再查中文的
            List<Long> hotelListEnUsIdList = hotelListEnUs.stream().map(HotelEntity::getHotelId).collect(Collectors.toList());
            List<Long> hotelListZhCnIdList = CollUtil.subtractToList(hotelIds, hotelListEnUsIdList);
            List<HotelEntity> hotelListZhCn = this.queryHotelBasicInfo(LanguageEnum.ZH_CN, hotelListZhCnIdList, isSync);
            hotelListEnUs.addAll(hotelListZhCn);
        }
        return hotelListEnUs;
    }

    /**
     * 查询酒店基本信息 (只需要基本信息)
     */
    public List<HotelEntity> queryHotelBasicInfo(LanguageEnum languageEnum, Collection<Long> hotelIds, boolean isSync) {
        log.info("queryHotelBasicInfo: {}", hotelIds);

        // 返回结果, 有效的酒店 id(从 GLink 那里能查到的)
        List<HotelEntity> result = new ArrayList<>();
        if (CollUtil.isEmpty(hotelIds)) {
            return result;
        }

        // 先查数据库, 存在的就不需要再去 GLink 查了
        List<HotelEntity> activeHotels = hotelMapper.selectActiveHotelById(hotelIds);
        result.addAll(activeHotels);
        // 需要去 GLink 查询的酒店 id
        Set<Long> activeHotelIds = activeHotels.stream().map(HotelEntity::getHotelId).collect(Collectors.toSet());
        Set<Long> needQueryHotelIds = hotelIds.stream().filter(o -> !activeHotelIds.contains(o)).collect(Collectors.toSet());

        // 分批处理, GLink 每次最多查询 10 个
        CollUtil.split(needQueryHotelIds, 10).forEach(subHotelIds -> {
            // 查询酒店信息
            List<HotelInfo> hotelInfos = queryHotelInfos(subHotelIds, languageEnum, new ArrayList<>());
            if (CollUtil.isEmpty(hotelInfos)) {
                return;
            }
            // 处理酒店信息
            hotelInfos.forEach(hotelInfo -> {
                HotelEntity hotelEntity = new HotelEntity();
                fillHotelEntity(languageEnum, hotelInfo, hotelEntity);
                result.add(hotelEntity);
            });
        });

        // 如果需要同步, 先落基础信息到数据库, 再去同步其它信息
        if (isSync && CollUtil.isNotEmpty(result)) {
            // 先落到表里
            hotelMapper.batchUpsert(result);

            HotelManager proxy = ApplicationContextHolder.getBean(HotelManager.class);
            proxy.syncHotelByHotelEntityList(result);
        }
        return result;
    }

    /**
     * 通过 hotelEntityList 先落数据库, 再全量同步酒店信息
     */
    @Async(ThreadPoolAutoConfiguration.SYNC_GLINK_EXECUTOR_NAME)
    public void syncHotelByHotelEntityList(List<HotelEntity> hotelEntityList) {
        if (CollUtil.isEmpty(hotelEntityList)) {
            return;
        }
        // 再异步同步酒店的其它信息
        Set<Long> hotelIds = hotelEntityList.stream().map(HotelEntity::getHotelId).collect(Collectors.toSet());
        log.info("syncHotelByHotelEntityList begin");
        try {
            // 同步酒店信息
            syncHotelInfo(hotelIds);
        } catch (Throwable e) {
            log.error("同步酒店信息失败, 酒店 ID : {} ", hotelIds, e);
        }
        log.info("syncHotelByHotelEntityList end");
    }

    /**
     * 将酒店图片响应转换为实体
     */
    private static List<HotelImageEntity> toHotelImageEntities(List<HotelImage> hotelImages) {
        return hotelImages.stream().map(hotelImage -> {
            HotelImageEntity hotelImageEntity = new HotelImageEntity();
            hotelImageEntity.setHotelId(hotelImage.getHotelId());
            hotelImageEntity.setData(JsonUtil.objectToJson(hotelImage));
            return hotelImageEntity;
        }).collect(Collectors.toList());
    }

    /**
     * 查询酒店图片信息
     */
    public List<HotelImage> queryHotelImages(List<Long> hotelIds) {
        HotelImageRequest request = HotelImageRequest.builder()
            .hotelIds(hotelIds)
            .build();

        Response<HotelImageResponse> response = tmcHubApiManager.queryHotelImage(request, signatureParam());
        if (Objects.isNull(response) || Objects.isNull(response.getBussinessResponse()) || CollUtil.isEmpty(response.getBussinessResponse().getHotelImages())) {
            log.info("No hotel image data found, hotel ids: {}", hotelIds);
            return Collections.emptyList();
        }
        return response.getBussinessResponse().getHotelImages();
    }

    /**
     * 查询酒店信息
     */
    private List<HotelInfo> queryHotelInfos(List<Long> hotelIds, LanguageEnum languageEnum, List<String> buildSettings) {
        HotelInfoRequest hotelInfoRequest = HotelInfoRequest.builder()
            .hotelIds(hotelIds)
            .settings(buildSettings)
            .language(languageEnum.value)
            .build();
        Response<HotelInfoResponse> hotelInfoResponse = tmcHubApiManager.queryHotelInfo(hotelInfoRequest, signatureParam());

        if (Objects.isNull(hotelInfoResponse) || Objects.isNull(hotelInfoResponse.getBussinessResponse()) || CollUtil.isEmpty(hotelInfoResponse.getBussinessResponse().getHotelInfos())) {
            log.info("No hotel info data found, hotel ids: {}, language: {}", hotelIds, languageEnum.value);
            return Collections.emptyList();
        }
        return hotelInfoResponse.getBussinessResponse().getHotelInfos();
    }

    /**
     * 构建查询酒店信息 setting
     */
    private List<String> buildSettings() {
        List<String> settings = new ArrayList<>();
        settings.add("hotelFacilityNew"); // 酒店设施（含房型设施）
        settings.add("breakfast"); // 酒店政策：早餐政策
        settings.add("importantNotices"); // 酒店政策：重要通知
        settings.add("parking"); // 酒店政策：停车场
        settings.add("chargingParking"); // 酒店政策：充电车位
        settings.add("hotelCertificates"); // 酒店资质，可能没有
        settings.add("comment"); // 酒店评分
        settings.add("hotelMeetingInfos"); // 酒店会议室信息
        settings.add("hotelVideoInfos"); // 酒店视频信息
        settings.add("hotelTextPolicies"); // 酒店文本政策”
        settings.add("hotelStructuredPolicies.childPolicy"); // 儿童政策
        settings.add("hotelStructuredPolicies.extraBedPolicy"); // 加床政策
        settings.add("hotelStructuredPolicies.petPolicy"); // 宠物政策
        return settings;
    }

    /**
     * 获取评分, 目前写死获取携程的
     */
    private String getRating(List<Comment> comments) {
        if(comments == null){
            return null;
        }
        return comments.stream().filter(comment -> "ctrip".equals(comment.getChannel()))
            .findFirst()
            .map(Comment::getAverage_score)
            .orElse(null);
    }

    /**
     * 填充 t_hotel_data 数据
     */
    private static void fillHotelDataEntity(LanguageEnum languageEnum, HotelInfo hotelInfo, HotelDataEntity hotelDataEntity) {
        if (hotelDataEntity == null || hotelInfo == null) {
            return;
        }
        hotelDataEntity.setHotelId(hotelInfo.getHotelId());
        if (languageEnum.value.equals(LanguageEnum.EN_US.value)) {
            hotelDataEntity.setDataEnUs(JsonUtil.objectToJson(hotelInfo));
        } else if (languageEnum.value.equals(LanguageEnum.ZH_CN.value)) {
            hotelDataEntity.setDataZhCn(JsonUtil.objectToJson(hotelInfo));
        }
    }

    /**
     * 根据语言和酒店信息填充酒店实体
     * @param languageEnum 语言
     * @param hotelInfo 酒店信息
     * @param hotelEntity 酒店实体
     */
    private void fillHotelEntity(LanguageEnum languageEnum, HotelInfo hotelInfo, HotelEntity hotelEntity) {
        if (hotelEntity == null || hotelInfo == null) {
            return;
        }
        hotelEntity.setHotelId(hotelInfo.getHotelId());
        hotelEntity.setCountryCode(hotelInfo.getCountryCode());
        CityEntity city = cachedCityService.getByCityCode(hotelInfo.getCity());
        hotelEntity.setProvinceCode(Optional.ofNullable(city).map(CityEntity::getProvinceCode).orElse(null));
        hotelEntity.setCityCode(hotelInfo.getCity());
        if (languageEnum.value.equals(LanguageEnum.EN_US.value)) {
            hotelEntity.setNameEnUs(hotelInfo.getHotelName());
            hotelEntity.setAddressEnUs(hotelInfo.getAddress());
            hotelEntity.setAddressEnUs(hotelInfo.getAddress());
            hotelEntity.setIntroduceEnUs(hotelInfo.getHotelIntroduce());
        } else if (languageEnum.value.equals(LanguageEnum.ZH_CN.value)) {
            hotelEntity.setNameZhCn(hotelInfo.getHotelName());
            hotelEntity.setAddressZhCn(hotelInfo.getAddress());
            hotelEntity.setAddressZhCn(hotelInfo.getAddress());
            hotelEntity.setIntroduceZhCn(hotelInfo.getHotelIntroduce());
        }
        hotelEntity.setTelephone(hotelInfo.getTelephone());
        hotelEntity.setEmail("");
        hotelEntity.setHotelStar(String.valueOf(hotelInfo.getHotelStar()));
        hotelEntity.setRating(getRating(hotelInfo.getComment()));
        hotelEntity.setOpeningDate(Optional.ofNullable(hotelInfo.getPraciceDate()).filter(StringUtils::isNotBlank).map(DateUtil::parseDate).orElse(null));
        hotelEntity.setFitmentDate(Optional.ofNullable(hotelInfo.getFitmentDate()).filter(StringUtils::isNotBlank).map(DateUtil::parseDate).orElse(null));
        hotelEntity.setHotelGroupId(Optional.ofNullable(hotelInfo.getParentHotelGroup()).filter(StringUtils::isNotBlank).filter(o -> !"null".equals(o)).map(Long::parseLong).orElse(null));
        hotelEntity.setHotelBrandId(Optional.ofNullable(hotelInfo.getPlateID()).filter(StringUtils::isNotBlank).filter(o -> !"null".equals(o)).map(Long::parseLong).orElse(null));
        hotelEntity.setMainPicUrl(hotelInfo.getAppearancePicUrl());
        hotelEntity.setCheckInTime(hotelInfo.getCheckInTime());
        hotelEntity.setCheckOutTime(hotelInfo.getCheckOutTime());
        hotelEntity.setRoomNum(Optional.ofNullable(hotelInfo.getRoomNum()).filter(NumberUtil::isInteger).map(Integer::parseInt).orElse(null));
        Optional.ofNullable(hotelInfo.getHotFacility()).ifPresent(hotFacility -> {
            hotelEntity.setHasMeetingRoom(hotFacility.getMeetingRoom());
            hotelEntity.setHasSwimmingPool(hotFacility.getSwimmingPool());
            hotelEntity.setHasGym(hotFacility.getFitnessCenter());
            hotelEntity.setHasLaundryRoom(hotFacility.getLaundryFacilities());
        });
        hotelEntity.setLngBaidu(Optional.ofNullable(hotelInfo.getLongitude()).map(NumberUtil::toBigDecimal).orElse(null));
        hotelEntity.setLatBaidu(Optional.ofNullable(hotelInfo.getLatitude()).map(NumberUtil::toBigDecimal).orElse(null));
        hotelEntity.setLngGoogle(Optional.ofNullable(hotelInfo.getLngGoogle()).map(NumberUtil::toBigDecimal).orElse(null));
        hotelEntity.setLatGoogle(Optional.ofNullable(hotelInfo.getLatGoogle()).map(NumberUtil::toBigDecimal).orElse(null));
        hotelEntity.setIsActive(YesOrNoEnum.YES.getKey());
        hotelEntity.setCreator(UserSession.SYSTEM_USER_NAME);
        hotelEntity.setModifier(UserSession.SYSTEM_USER_NAME);
        hotelEntity.setCurrencyCode(hotelInfo.getCurrency());
    }

    /**
     * 记录酒店变更日志
     */
    private void recordHotelLog(Map<Long, HotelEntity> hotelMap) {
        if (CollUtil.isEmpty(hotelMap)) {
            return;
        }
        // 查询每个酒店的最新数据
        Set<Long> hotelIds = hotelMap.keySet();
        List<HotelLogEntity> latestLogList = hotelLogMapper.selectLatestByHotelIds(hotelIds);
        Map<Long, HotelLogEntity> existedLogMap = latestLogList.stream()
            .collect(Collectors.toMap(HotelLogEntity::getHotelId, Function.identity(), (o, n) -> n));

        List<HotelLogEntity> changeLogList = new ArrayList<>();
        hotelMap.values().forEach(hotelEntity -> {
            HotelLogEntity existedLogEntity = existedLogMap.get(hotelEntity.getHotelId());

            // 第一次记录
            if (existedLogEntity == null) {
                HotelLogEntity logEntity = new HotelLogEntity();
                logEntity.setHotelId(hotelEntity.getHotelId());
                logEntity.setBeforeKvJson("");
                logEntity.setAfterKvJson(JsonUtil.objectToJson(hotelEntity));
                logEntity.setCreator(UserSession.SYSTEM_USER_NAME);
                changeLogList.add(logEntity);
                return;
            }

            // 比较数据
            String afterKvJson = existedLogEntity.getAfterKvJson();
            HotelEntity oldHotel = JsonUtil.jsonToBean(afterKvJson, HotelEntity.class);
            if (!hotelEntity.equals(oldHotel)) {
                HotelLogEntity logEntity = new HotelLogEntity();
                logEntity.setHotelId(hotelEntity.getHotelId());
                logEntity.setBeforeKvJson(afterKvJson);
                logEntity.setAfterKvJson(StringUtils.truncate(JsonUtil.objectToJson(hotelEntity), 7000));
                logEntity.setCreator(UserSession.SYSTEM_USER_NAME);
                changeLogList.add(logEntity);
            }
        });
        if (CollUtil.isNotEmpty(changeLogList)) {
            hotelLogMapper.batchInsert(changeLogList);
        }
    }


    /**
     * 异步同步Glink酒店信息
     */
    @Async
    public void asyncInsertHotelByDestination(List<DestinationHotelVO> destinationHotelVOList, String operator){
        // 查询数据库是否已经存在酒店基础信息
        List<Long> hotelIdList = destinationHotelVOList.stream().map(DestinationHotelVO::getHotelId).collect(Collectors.toList());
        Set<Long> dbHotelList = hotelMapper.selectActiveHotelIds(hotelIdList);

        // 同步酒店信息
        List<DestinationHotelVO> destinationHotelList = destinationHotelVOList.stream().filter(o -> !dbHotelList.contains(o.getHotelId())).collect(Collectors.toList());
        if(CollUtil.isEmpty(destinationHotelList)){
            return;
        }
        Set<Long> syncHotelIdSet = new HashSet<>();
        for(DestinationHotelVO destinationHotelVO : destinationHotelList){
            syncHotelIdSet.add(destinationHotelVO.getHotelId());
            // 同步酒店信息
            HotelEntity hotel = new HotelEntity();
            hotel.setHotelId(destinationHotelVO.getHotelId());
            hotel.setNameEnUs(destinationHotelVO.getHotelName());
            hotel.setNameZhCn(destinationHotelVO.getHotelName());
            hotel.setCountryCode(destinationHotelVO.getCountryCode());
            hotel.setProvinceCode(destinationHotelVO.getProvinceCode());
            hotel.setCityCode(destinationHotelVO.getCityCode());
            hotel.setIsActive(YesOrNoEnum.YES.getKey());
            hotel.setLngGoogle(StringUtil.isValidString(destinationHotelVO.getLngGoogle()) ? new BigDecimal(destinationHotelVO.getLngGoogle()) : null);
            hotel.setLatGoogle(StringUtil.isValidString(destinationHotelVO.getLatGoogle()) ? new BigDecimal(destinationHotelVO.getLatGoogle()) : null);
            hotel.setCreator(operator);
            try {
                hotelMapper.insert(hotel);
            } catch (Exception e) {
                log.error("新增酒店失败 {}", ExceptionUtility.getDetailedExceptionString(e));
            }
        }
        // 同步酒店详情
        syncHotelInfo(syncHotelIdSet);

    }

    /**
     * 根据酒店id集合异步同步Glink酒店信息
     */
    @Async
    public void asyncInsertHotelById(Collection<Long> hotelIdList, String operator){
        // 查询数据库是否已经存在酒店基础信息
        Set<Long> dbHotelList = new HashSet<>();
        CollUtil.split(hotelIdList, 1000).forEach(subList -> {
            Set<Long> subDbHotelList = hotelMapper.selectActiveHotelIds(subList);
            dbHotelList.addAll(subDbHotelList);
            }
        );

        // 同步酒店信息
        List<Long> syncHotelIdList = hotelIdList.stream().filter(o -> !dbHotelList.contains(o)).collect(Collectors.toList());
        if(CollUtil.isEmpty(syncHotelIdList)){
            return;
        }
        for(Long syncHotelId : syncHotelIdList){
            // 同步酒店信息
            HotelEntity hotel = new HotelEntity();
            hotel.setHotelId(syncHotelId);
            hotel.setIsActive(YesOrNoEnum.YES.getKey());
            hotel.setCreator(operator);
            try {
                hotelMapper.insert(hotel);
            } catch (Exception e) {
                log.error("新增酒店失败 {}", ExceptionUtility.getDetailedExceptionString(e));
            }
        }
        // 同步酒店详情
        syncHotelInfo(syncHotelIdList);

    }

    /**
     * Create a signature parameter
     */
    private SignatureHelpRequestDto signatureParam() {
        SignatureHelpRequestDto signatureParam = new SignatureHelpRequestDto();
        signatureParam.setPartnerCode(GenericAppUtility.getSysInfo(SysConfig.TMC_HUB_PARTNER_CODE));
        signatureParam.setSecurityKey(GenericAppUtility.getSysInfo(SysConfig.TMC_HUB_SECURITY_KEY));
        return signatureParam;
    }

    /**
     * 查询酒店房型信息
     */
    public List<RoomInfoVO> queryRoomInfoListByHotelId(int language, Long hotelId) {
        // 查询酒店数据
        HotelDataVO hotelData = hotelDataMapper.selectByHotelId(language, hotelId);
        if (Objects.isNull(hotelData)) {
            return Collections.emptyList();
        }

        // 转换数据
        // 处理酒店数据
        HotelInfo hotelInfo = JsonUtil.jsonToBean(hotelData.getHotelData(), HotelInfo.class);
        // 处理图片
        HotelImage hotelImage = JsonUtil.jsonToBean(hotelData.getImageData(), HotelImage.class);
        List<RoomImage> roomImages = Optional.ofNullable(hotelImage).map(HotelImage::getRoomImages).orElse(Collections.emptyList());
        Map<Long, List<Image>> roomIdImagesMap = roomImages.stream().collect(Collectors.toMap(RoomImage::getRoomId, RoomImage::getImages));

        // 转换
        if(CollectionUtil.isNotEmpty(hotelInfo.getRoomInfos())) {
            return hotelInfo.getRoomInfos().stream().map(roomInfo -> {
                RoomInfoVO room = new RoomInfoVO();
                room.setHotelId(hotelId);
                room.setRoomId(roomInfo.getRoomId());
                room.setRoomName(roomInfo.getRoomName());
                Image mainImage = getMainImage(roomIdImagesMap.get(roomInfo.getRoomId()));
                room.setMainImageId(Optional.ofNullable(mainImage).map(Image::getImageId).orElse(null));
                room.setMainImage(Optional.ofNullable(mainImage).map(Image::getUrl).orElse(null));
                return room;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }


    /**
     * 查询酒店房型和所有房型信息
     */
    public List<RoomInfoWithImgaesVO> queryRoomInfoWithImageListByHotelId(int language, Long hotelId) {
        // 查询酒店数据
        HotelDataVO hotelData = hotelDataMapper.selectByHotelId(language, hotelId);
        if (Objects.isNull(hotelData)) {
            return Collections.emptyList();
        }

        // 转换数据
        // 处理酒店数据
        HotelInfo hotelInfo = JsonUtil.jsonToBean(hotelData.getHotelData(), HotelInfo.class);
        // 处理图片
        HotelImage hotelImage = JsonUtil.jsonToBean(hotelData.getImageData(), HotelImage.class);
        List<RoomImage> roomImages = Optional.ofNullable(hotelImage).map(HotelImage::getRoomImages).orElse(Collections.emptyList());
        Map<Long, List<Image>> roomIdImagesMap = roomImages.stream().collect(Collectors.toMap(RoomImage::getRoomId, RoomImage::getImages));

        // 转换
        if(CollectionUtil.isNotEmpty(hotelInfo.getRoomInfos())) {
            return hotelInfo.getRoomInfos().stream().map(roomInfo -> {
                RoomInfoWithImgaesVO room = new RoomInfoWithImgaesVO();
                room.setHotelId(hotelId);
                room.setRoomId(roomInfo.getRoomId());
                room.setRoomName(roomInfo.getRoomName());
                room.setRoomIdImages(roomIdImagesMap.get(roomInfo.getRoomId()));
                return room;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }


    /**
     * 查询酒店房型信息
     */
    public List<RoomNameInfoVO> queryRoomNameListByHotelId(int language, Long hotelId) {
        // 查询酒店数据
        HotelDataVO hotelData = hotelDataMapper.selectByHotelId(language, hotelId);
        if (Objects.isNull(hotelData)) {
            return Collections.emptyList();
        }

        // 转换数据
        // 处理酒店数据
        HotelInfo hotelInfo = JsonUtil.jsonToBean(hotelData.getHotelData(), HotelInfo.class);

        if(CollectionUtil.isNotEmpty(hotelInfo.getRoomInfos())) {
            // 转换
            return hotelInfo.getRoomInfos().stream().map(roomInfo -> {
                RoomNameInfoVO room = new RoomNameInfoVO();
                room.setRoomId(roomInfo.getRoomId());
                room.setRoomName(roomInfo.getRoomName());
                return room;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 获取主图
     */
    private Image getMainImage(List<Image> images) {
        return images.stream().filter(image -> image.getIsMain() == 1).findFirst().orElse(null);
    }

    /**
     * 查询酒店房型名称列表
     */
    public List<RoomNameInfoVO> queryRoomNameListByHotelIds(int language, Collection<Long> hotelIds) {
        // 查询酒店数据
        List<HotelDataEntity> hotelDataList = hotelDataMapper.selectByHotelIds(hotelIds);
        if (CollectionUtil.isEmpty(hotelDataList)) {
            return Collections.emptyList();
        }

        // 处理酒店数据（修改点：使用 flatMap 平铺所有房型）
        return hotelDataList.stream().flatMap(hotelData -> {
                String dataStr = LanguageEnum.ZH_CN.key.equals(language) ? hotelData.getDataZhCn() : hotelData.getDataEnUs();
                HotelInfo hotelInfo = JsonUtil.jsonToBean(dataStr, HotelInfo.class);
                // 处理房型为空的情况，返回空流避免 NPE
                if (Objects.isNull(hotelInfo) || CollectionUtil.isEmpty(hotelInfo.getRoomInfos())) {
                    return Stream.empty();
                }
                // 将每个酒店的房型流展开到总流中
                return hotelInfo.getRoomInfos().stream()
                    .map(roomInfo -> {
                        RoomNameInfoVO room = new RoomNameInfoVO();
                        room.setRoomId(roomInfo.getRoomId());
                        room.setRoomName(roomInfo.getRoomName());
                        room.setHotelId(hotelInfo.getHotelId());
                        return room;
                    });
            }).collect(Collectors.toList());
    }


    public com.fangcang.grfp.core.base.Response queryHotelLowestPrice(List<Long> hotelIds, Date checkInDate, Date checkOutDate) {
        com.fangcang.grfp.core.base.Response response = new com.fangcang.grfp.core.base.Response();
        if (CollUtil.isEmpty(hotelIds)) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常，酒店id不能为空");
            return response;
        }
        //调起价接口
        HotelLowestPriceRequest hotelLowestPriceRequest = new HotelLowestPriceRequest();
        hotelLowestPriceRequest.setHotelIds(hotelIds);
        if(checkInDate != null && checkOutDate != null){
            hotelLowestPriceRequest.setCheckInDate(com.fangcang.grfp.core.util.DateUtil.dateToString(checkInDate));
            hotelLowestPriceRequest.setCheckOutDate(com.fangcang.grfp.core.util.DateUtil.dateToString(checkOutDate));
        }

        com.fangcang.grfp.core.dto.dhub.response.Response<HotelLowestPriceResponse> hotelLowestPriceResponseResponse = tmcHubApiManager.queryHotelLowestPrice(hotelLowestPriceRequest,
                null);
        List<HotelLowestPricesResponse> hotelLowestPricesResponses = null;
        List<HotelLowestPrice> hotelLowestPrices = null;
        if (hotelLowestPriceResponseResponse != null && RfpConstant.TMC_HUB_SUCCESS_RESULT_CODE.equals(hotelLowestPriceResponseResponse.getReturnCode())
                && hotelLowestPriceResponseResponse.getBussinessResponse() != null && CollUtil.isNotEmpty(hotelLowestPriceResponseResponse.getBussinessResponse().getHotelLowestPrices())) {
            hotelLowestPrices = hotelLowestPriceResponseResponse.getBussinessResponse().getHotelLowestPrices();
            hotelLowestPricesResponses = new ArrayList<>();
            for (HotelLowestPrice hotelLowestPrice : hotelLowestPrices) {
                HotelLowestPricesResponse hotelLowestPricesResponse = new HotelLowestPricesResponse();
                BigDecimal totalPrice = new BigDecimal(0);
                Long hotelId = hotelLowestPrice.getHotelId();
                List<HotelPriceItem> priceItems = hotelLowestPrice.getPriceItems();
                if (CollUtil.isNotEmpty(priceItems)) {
                    int manyDays = 0;
                    for (HotelPriceItem priceItem : priceItems) {
                        BigDecimal salePrice = priceItem.getSalePrice();
                        if (salePrice != null && salePrice.doubleValue() > 0) {
                            BigDecimal salesPrice = new BigDecimal(String.valueOf(salePrice));
                            totalPrice = totalPrice.add(salesPrice);
                            manyDays++;

                            // 设置最低价格
                            if(hotelLowestPricesResponse.getLowestPrice() == null || hotelLowestPricesResponse.getLowestPrice().compareTo(salesPrice) > 0){
                                hotelLowestPricesResponse.setLowestPrice(salesPrice);
                            }
                        }

                    }
                    if (manyDays > 0) {
                        hotelLowestPricesResponse.setHotelId(hotelId);
                        hotelLowestPricesResponse.setPriceItems(priceItems);
                        hotelLowestPricesResponse.setReferencePrice(totalPrice.divide(new BigDecimal(manyDays),2, RoundingMode.HALF_UP));
                        hotelLowestPricesResponses.add(hotelLowestPricesResponse);
                    }
                }
            }
        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(hotelLowestPricesResponses);
        return response;
    }

    /**
     * 查询酒店房型信息
     */
    public List<HotelRoomTypeVO> selectHotelRoomTypeInfo(int language, Collection<Long> hotelIds) {
        if (CollectionUtil.isEmpty(hotelIds)) {
            return Collections.emptyList();
        }

        // 查询酒店数据
        List<HotelDataEntity> hotelDataList = hotelDataMapper.selectByHotelIds(hotelIds);
        if (CollectionUtil.isEmpty(hotelDataList)) {
            return Collections.emptyList();
        }

        // 处理酒店数据（修改点：使用 flatMap 平铺所有房型）
        return hotelDataList.stream().map(hotelData -> {
            String dataStr = LanguageEnum.ZH_CN.key.equals(language) ? hotelData.getDataZhCn() : hotelData.getDataEnUs();
            HotelInfo hotelInfo = JsonUtil.jsonToBean(dataStr, HotelInfo.class);
            if (Objects.isNull(hotelInfo)) {
                return null;
            }

            // 设置酒店信息
            HotelRoomTypeVO hotelRoomInfo = new HotelRoomTypeVO();
            hotelRoomInfo.setHotelId(hotelInfo.getHotelId());
            hotelRoomInfo.setHotelName(hotelInfo.getHotelName());
            hotelRoomInfo.setTelephone(hotelInfo.getTelephone());
            hotelRoomInfo.setAddress(hotelInfo.getAddress());
            hotelRoomInfo.setCityCode(hotelInfo.getCity());

            // 处理房型为空的情况，返回空流避免 NPE
            if (CollectionUtil.isEmpty(hotelInfo.getRoomInfos())) {
                return hotelRoomInfo;
            }
            // 将每个酒店的房型流展开到总流中
            List<RoomNameInfoVO> collect = hotelInfo.getRoomInfos().stream().map(roomInfo -> convertRoomInfoVO(roomInfo, hotelInfo)).collect(Collectors.toList());
            hotelRoomInfo.setRoomTypes(collect);

            return hotelRoomInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

    }

    /**
     * 转换 vo
     */
    private static RoomNameInfoVO convertRoomInfoVO(RoomInfo roomInfo, HotelInfo hotelInfo) {
        RoomNameInfoVO room = new RoomNameInfoVO();
        room.setRoomId(roomInfo.getRoomId());
        room.setRoomName(roomInfo.getRoomName());
        room.setHotelId(hotelInfo.getHotelId());
        return room;
    }

}
