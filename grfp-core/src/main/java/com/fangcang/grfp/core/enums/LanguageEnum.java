package com.fangcang.grfp.core.enums;

/**
 * 语言枚举
 */
public enum LanguageEnum {

    EN_US(1,"en-US"),
    ZH_CN(2,"zh-CN");

    public Integer key;
    public String value;

    LanguageEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static Integer getKeyByValue(String value) {
        Integer key = 0;
        for (LanguageEnum languageEnum : LanguageEnum.values()) {
            if (languageEnum.value.equals(value)) {
                key = languageEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (LanguageEnum languageEnum : LanguageEnum.values()) {
            if (languageEnum.key.equals(key)) {
                value = languageEnum.value;
                break;
            }
        }
        return value;
    }

    public static LanguageEnum getEnumByKey(Integer key) {
        LanguageEnum result = null;
        for (LanguageEnum languageEnum : LanguageEnum.values()) {
            if (languageEnum.key.equals(key)) {
                result = languageEnum;
                break;
            }
        }
        return result;
    }


}
