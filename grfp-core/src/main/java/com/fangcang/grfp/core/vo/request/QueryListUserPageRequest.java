package com.fangcang.grfp.core.vo.request;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel("用户列表分页查询")
@Getter
@Setter
public class QueryListUserPageRequest extends PageQuery {

    @ApiModelProperty(value = "用户姓名")
    private String userName;
    @ApiModelProperty(value = "手机号码")
    private String mobile;
    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "状态")
    private Integer state;
    @ApiModelProperty(value = "机构类型 1平台，2酒店，3企业，4酒店集团")
    private Integer orgType;
    @ApiModelProperty(value = "机构名称")
    private String orgName;
    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;
    @ApiModelProperty(value = "用户ID")
    private Integer userId;
    @ApiModelProperty(value = "机构ID")
    private Integer orgId;
    // 用户管理机构ID
    private List<Integer> relatedOrgIdList;

}
