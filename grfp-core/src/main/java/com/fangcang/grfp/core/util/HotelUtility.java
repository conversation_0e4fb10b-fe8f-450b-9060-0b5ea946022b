package com.fangcang.grfp.core.util;

import com.fangcang.grfp.core.entity.HotelEntity;
import com.fangcang.grfp.core.enums.HotelStarEnum;
import com.fangcang.grfp.core.vo.LngLatGoogleVO;
import com.fangcang.grfp.core.vo.response.HotelResponse;
import com.fangcang.grfp.core.vo.response.project.QueryHistoryProjectInfoResponse;

import java.math.BigDecimal;
import java.util.Arrays;

public class HotelUtility {

    public static String getHotelStarString(int languageId, HotelEntity hotel){
        if(StringUtil.isValidString(hotel.getHotelStar())){
            return HotelStarEnum.getTextByKey(hotel.getHotelStar(), languageId);
        }
        return null;
    }

    public static String getHotelAddress(int languageId, HotelEntity hotel){
        return GenericAppUtility.getName(languageId, "", hotel.getAddressEnUs(), hotel.getAddressZhCn());
    }

    public static String getHotelName(int languageId, HotelEntity hotel){
        return GenericAppUtility.getName(languageId, hotel.getHotelId(), hotel.getNameEnUs(), hotel.getNameZhCn());
    }

    //计算google 经纬度
    public static void calculateNullGoogleLngLat(HotelEntity hotel){
        LngLatGoogleVO lngLatGoogleVO = CoordinateUtils.calculateNullGoogleLngLat(hotel.getLngGoogle(),
                hotel.getLatGoogle(),
                hotel.getLngBaidu(),
                hotel.getLatBaidu());
        hotel.setLngGoogle(lngLatGoogleVO.getLngGoogle());
        hotel.setLatGoogle(lngLatGoogleVO.getLatGoogle());
    }

    public static void calculateHotelResponseNullGoogleLngLat(HotelResponse hotel){
        LngLatGoogleVO lngLatGoogleVO = CoordinateUtils.calculateNullGoogleLngLat(hotel.getLngGoogle(),
                hotel.getLatGoogle(),
                hotel.getLngBaidu(),
                hotel.getLatBaidu());
        hotel.setLngGoogle(lngLatGoogleVO.getLngGoogle());
        hotel.setLatGoogle(lngLatGoogleVO.getLatGoogle());
    }

    //计算google 经纬度
    public static void calculateNullGoogleLngLat(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse){
        LngLatGoogleVO lngLatGoogleVO = CoordinateUtils.calculateNullGoogleLngLat(queryHistoryProjectInfoResponse.getLngGoogle(),
                queryHistoryProjectInfoResponse.getLatGoogle(),
                queryHistoryProjectInfoResponse.getLngBaidu(),
                queryHistoryProjectInfoResponse.getLatBaidu());
        queryHistoryProjectInfoResponse.setLngGoogle(lngLatGoogleVO.getLngGoogle());
        queryHistoryProjectInfoResponse.setLatGoogle(lngLatGoogleVO.getLatGoogle());
    }


}
