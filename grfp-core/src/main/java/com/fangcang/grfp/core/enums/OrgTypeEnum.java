package com.fangcang.grfp.core.enums;

/**
 * <AUTHOR>
 * @ClassName OrgTypeEnum
 * @Description 机构类型
 * @createTime 2022-08-26 20:18:00
 */
public enum OrgTypeEnum {

    PLATFORM(1, "平台"), HOTEL( 2, "供应商(酒店)"), DISTRIBUTOR(3, "分销商(企业)"), HOTELGROUP(4, "酒店集团");
    public Integer key;
    public String value;

    OrgTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static Integer getKeyByValue(String value) {
        Integer key = 0;
        for(OrgTypeEnum loginTypeEnum : OrgTypeEnum.values()) {
            if(loginTypeEnum.value .equals(value)) {
                key = loginTypeEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for(OrgTypeEnum loginTypeEnum : OrgTypeEnum.values()) {
            if(loginTypeEnum.key.equals(key)) {
                value = loginTypeEnum.value;
                break;
            }
        }
        return value;
    }

    public static OrgTypeEnum getEnumByKey(Integer key){
        OrgTypeEnum loginTypeEnum = null;
        for(OrgTypeEnum loginType : OrgTypeEnum.values()) {
            if(loginType.key.equals(key)) {
                loginTypeEnum = loginType;
                break;
            }
        }
        return loginTypeEnum;
    }

    public static String getOrgTextCodeByKey(Integer key) {
        return "ORG_TYPE_" + key;
    }


    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
