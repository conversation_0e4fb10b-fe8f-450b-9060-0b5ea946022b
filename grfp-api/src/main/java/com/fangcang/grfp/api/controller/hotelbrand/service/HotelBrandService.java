package com.fangcang.grfp.api.controller.hotelbrand.service;

import com.fangcang.grfp.api.controller.hotelbrand.vo.HotelBrandRequest;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.mapper.HotelBrandMapper;
import com.fangcang.grfp.core.util.StringUtil;
import com.fangcang.grfp.core.vo.HotelBrandVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Service
@Slf4j
public class HotelBrandService {

    @Autowired
    private HotelBrandMapper hotelBrandMapper;





}
