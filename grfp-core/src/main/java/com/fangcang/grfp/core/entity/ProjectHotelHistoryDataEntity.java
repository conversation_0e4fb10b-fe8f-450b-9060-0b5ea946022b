package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 项目酒店历史交易表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_hotel_history_data")
public class ProjectHotelHistoryDataEntity extends BaseVO {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 项目 id
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店 id
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 城市排名
     */
    @TableField("city_order")
    private Integer cityOrder;

    /**
     * 节省金额
     */
    @TableField("saved_amount")
    private BigDecimal savedAmount;

    /**
     * 节省率
     */
    @TableField("saved_amount_rate")
    private BigDecimal savedAmountRate;

    /**
     * 酒店同城最近 POI ID
     */
    @TableField("poi_id")
    private Long poiId;

    /**
     * 酒店距离 POI 距离
     */
    @TableField("poi_distance")
    private BigDecimal poiDistance;

    /**
     * 去年总违规数
     */
    @TableField("total_violations_count")
    private Integer totalViolationsCount;

    /**
     * 去年服务分
     */
    @TableField("service_point")
    private BigDecimal servicePoint;

    /**
     * OTA最低价格
     */
    @TableField("ota_min_price")
    private BigDecimal otaMinPrice;

    /**
     * OTA最高价格
     */
    @TableField("ota_max_price")
    private BigDecimal otaMaxPrice;

    /**
     * 商旅价格
     */
    @TableField("lowest_price")
    private BigDecimal lowestPrice;

    /**
     * 签约混合价
     */
    @TableField("adjust_lowest_price")
    private BigDecimal adjustLowestPrice;

    /**
     * 差旅价格信息
     */
    @TableField("lowest_price_item_info")
    private String lowestPriceItemInfo;

    /**
     * 商旅价格生成日期
     */
    @TableField("lowest_price_date")
    private Date lowestPriceDate;

    /**
     * OTA最低最高价格生成日期
     */
    @TableField("min_max_ota_price_date")
    private Date minMaxOtaPriceDate;

    /**
     * 最近统计参考编号
     */
    @TableField("last_stat_reference_no")
    private String lastStatReferenceNo;

    /**
     * 周边3公里同价位酒店
     */
    @TableField("price_level_room_night")
    private Integer priceLevelRoomNight;

    /**
     * 成交间夜数
     */
    @TableField("room_night_count")
    private Integer roomNightCount;

    /**
     * 成交金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 推荐等级: 1-SSS 2-SS 3-S 4-A 5-B 6-C
     */
    @TableField("recommend_level")
    private String recommendLevel;

    /**
     * 是否上传: 1-是 0-否
     */
    @TableField("is_uploaded")
    private Integer isUploaded;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
