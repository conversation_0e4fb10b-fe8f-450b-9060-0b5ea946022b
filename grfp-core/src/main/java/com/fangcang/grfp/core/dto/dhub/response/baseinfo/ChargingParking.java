package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import lombok.Data;

@Data
public class ChargingParking {

    /**
     * 位置 0-酒店内 1-酒店附近
     */
    private String chargingLocation;

    /**
     * 提供充电桩(以,分割) 0-一般充电桩 1-特斯拉目的地充电桩 2-特斯拉超级充电桩
     */
    private String chargingPile;

    /**
     * 充电方式 0-私人停车场 1-公共停车场
     */
    private String chargingType;

    /**
     * 充电桩有限，先到先得 0-不显示 1-显示
     */
    private String isLimitCharging;

}
