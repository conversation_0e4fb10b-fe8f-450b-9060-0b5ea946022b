package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fangcang.grfp.core.entity.ProjectEntity;
import com.fangcang.grfp.core.vo.ListProjectVO;
import com.fangcang.grfp.core.vo.request.ListProjectRequest;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 项目 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-01
 */
public interface ProjectMapper extends BaseMapper<ProjectEntity> {

    /**
     * 分页查下项目
     */
    IPage<ListProjectVO> queryProjectPage(IPage<ListProjectVO> page, @Param("query") ListProjectRequest query);

}
