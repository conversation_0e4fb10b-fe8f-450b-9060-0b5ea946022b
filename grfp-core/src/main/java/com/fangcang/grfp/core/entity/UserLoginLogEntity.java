package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 登录日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_user_login_log")
public class UserLoginLogEntity extends BaseVO {


    /**
     * 用户登录日志ID
     */
    @TableId(value = "user_login_log_id", type = IdType.AUTO)
    private Integer userLoginLogId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 登录时间
     */
    @TableField("login_date_time")
    private Date loginDateTime;

    /**
     * 登录token
     */
    @TableField("user_token")
    private String userToken;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 登录user-agent
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 登出时间
     */
    @TableField("logout_date_time")
    private Date logoutDateTime;

    /**
     * 登出原因
     */
    @TableField("logout_reason")
    private String logoutReason;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
