package com.fangcang.grfp.api.controller.currencyexchangerate.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@ApiModel("修改 美元兑币种汇率参数")
@Getter
@Setter
public class UpdateCurrencyExchangeVO extends BaseVO {

    @ApiModelProperty("币种code")
    @NotBlank
    private String currencyCode;

    @ApiModelProperty("币种名称")
    private String currencyName;

    @ApiModelProperty("美元兑币种汇率")
    @NotNull
    private BigDecimal exchangeRate;

    @ApiModelProperty("排序")
    private Integer displayOrder;

}
