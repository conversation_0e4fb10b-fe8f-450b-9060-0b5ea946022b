package com.fangcang.grfp.api.controller.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("枚举项")
@Getter
@Setter
public class EnumItemVO {

    @ApiModelProperty("枚举key")
    private Object key;

    @ApiModelProperty("枚举名称")
    private String name;

    @ApiModelProperty("国际化代码")
    private String textCode;

    @ApiModelProperty("枚举的国际化翻译")
    private String text;

    public EnumItemVO() {}

    public EnumItemVO(Object key, String name, String textCode, String text) {
        this.key = key;
        this.name = name;
        this.textCode = textCode;
        this.text = text;
    }
} 