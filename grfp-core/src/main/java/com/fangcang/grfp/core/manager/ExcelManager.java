package com.fangcang.grfp.core.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.fangcang.grfp.core.base.*;
import com.fangcang.grfp.core.config.ThreadPoolAutoConfiguration;
import com.fangcang.grfp.core.constant.RedisConstant;
import com.fangcang.grfp.core.constant.TextResourceType;
import com.fangcang.grfp.core.dto.excel.ExportExcelContext;
import com.fangcang.grfp.core.dto.excel.ImportExcelContext;
import com.fangcang.grfp.core.entity.SysImportRecordEntity;
import com.fangcang.grfp.core.entity.TextResourceEntity;
import com.fangcang.grfp.core.enums.*;
import com.fangcang.grfp.core.exception.ExcelHeaderException;
import com.fangcang.grfp.core.mapper.SysImportRecordMapper;
import com.fangcang.grfp.core.mapper.TextResourceMapper;
import com.fangcang.grfp.core.oss.OssManager;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.GenericAppUtility;
import com.fangcang.grfp.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Excel Manager
 */
@Component
@Slf4j
public class ExcelManager {

    @Resource
    @Lazy
    private OssManager ossManager;

    @Resource
    private SysImportRecordMapper sysImportRecordMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TextResourceMapper textResourceMapper;
    
    /**
     * 通用导入
     */
    public Long generalImport(ImportExcelContext<?> context) throws IOException {
        // 填充基础参数
        if (Objects.isNull(context.getUserSession())) {
            context.setUserSession(UserSession.get());
        }

        // 校验 context, 这里的错误属于开发人员传参的错误, 直接抛异常
        validateContext(context);

        // 校验行数, 现在写死最多 10000 条, 且默认表头只占 1 行
        checkImportLimit(context.getFile(), context.getHeadNum());

        // 处理预期表头
        if(context.getExpectedHeaders() == null) {
            context.setExpectedHeaders(buildExpectedHeaders(context.getImportVOClass(), context.getValidateHeadNum()));
        }

        UserSession userSession = context.getUserSession();
        MultipartFile file = context.getFile();

        // 上传到 oss
        String ossKey = ossManager.putObjectPublic(FileTypeAndPathEnum.IMPORT_FILE_OSS_DIR.filePath, file.getInputStream(), file.getContentType(), file.getOriginalFilename());

        // 创建导入记录
        SysImportRecordEntity importRecord = new SysImportRecordEntity();
        importRecord.setImportName(context.getBizType().getKey());
        importRecord.setFileName(file.getOriginalFilename());
        importRecord.setImportPath(ossKey);
        importRecord.setStatus(ImportStatusEnum.IN_PROCESS.getKey());
        importRecord.setCreator(userSession.getUsername());
        sysImportRecordMapper.insert(importRecord);

        // 异步导入
        ExcelManager proxy = ApplicationContextHolder.getBean(ExcelManager.class);
        context.setRecordId(importRecord.getSysImportRecordId());
        proxy.asyncImport(context);

        // 返回导入记录 ID
        return importRecord.getSysImportRecordId();
    }

    /**
     * 构建预期表头
     */
    public static Map<Integer, List<String>> buildExpectedHeaders(Class<?> importVOClass, int headNum) {
        Map<Integer, List<String>> expectedHeaders = new HashMap<>(headNum);
        for (int i = 0; i < headNum; i++) {
            // 取出第 i 行的表头
            int index = i;
            List<String> headers = Arrays.stream(importVOClass.getDeclaredFields())
                .filter(field -> field.isAnnotationPresent(ExcelProperty.class))
                .map(field -> field.getAnnotation(ExcelProperty.class).value()[index])
                .collect(Collectors.toList());
            expectedHeaders.put(i, headers);
        }
        return expectedHeaders;
    }

    /**
     * 异步导入
     */
    @Async(ThreadPoolAutoConfiguration.EXECUTOR_NAME)
    public <T extends ImportVO> void asyncImport(ImportExcelContext<T> context) {
        SysImportRecordEntity updateEntity = new SysImportRecordEntity();
        updateEntity.setSysImportRecordId(context.getRecordId());
        updateEntity.setStatus(ImportStatusEnum.SUCCESS.getKey());

        // 错误 VO
        ImportErrorVO importErrorVO = new ImportErrorVO();
        try {
            EasyExcel.read(context.getFile().getInputStream(), context.getImportVOClass(), new AnalysisEventListener<T>() {

                // 缓存
                final List<T> cache = new ArrayList<>();
                // 错误信息
                final List<ImportRowErrorVO> errors = new ArrayList<>();

                @Override
                public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext analysisContext) {
                    Integer rowIndex = analysisContext.readRowHolder().getRowIndex();
                    // 校验表头
                    List<String> actualHeaders = new ArrayList<>(headMap.values());
                    List<String> expectedHeaders = context.getExpectedHeaders().get(rowIndex);
                    if (Objects.nonNull(expectedHeaders) && !expectedHeaders.equals(actualHeaders)) {
                        throw new ExcelHeaderException("表头不匹配！预期：" + context.getExpectedHeaders() + "，实际：" + actualHeaders);
                    }
                }

                /**
                 * 这个每一条数据解析都会来调用
                 * 放入缓存 cache, 达到 batchSize 处理一次
                 */
                @Override
                public void invoke(T vo, AnalysisContext analysisContext) {
                    // 设置行号
                    vo.setRowNum(analysisContext.readRowHolder().getRowIndex() + 1);
                    cache.add(vo);
                    if (cache.size() >= context.getBatchSize()) {
                        // 批量处理
                        List<ImportRowErrorVO> subErrors = context.getImportLogic().apply(context, cache);
                        if (!subErrors.isEmpty()) {
                            errors.addAll(subErrors);
                        }
                        cache.clear();
                    }
                }

                /**
                 * 所有数据解析完成了都会来调用, 处理最后一批数据
                 */
                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (!cache.isEmpty()) {
                        List<ImportRowErrorVO> subErrors = context.getImportLogic().apply(context, cache);
                        if (!subErrors.isEmpty()) {
                            errors.addAll(subErrors);
                        }
                    }
                    // 更新导入记录状态
                    if (CollUtil.isNotEmpty(errors)) {
                        updateEntity.setStatus(ImportStatusEnum.FAIL.getKey());
                        importErrorVO.setType(ImportErrorTypeEnum.PARTIAL_FAIL.getType());
                        importErrorVO.setErrorCode(ImportErrorTypeEnum.PARTIAL_FAIL.getErrorCode());
                        // 只存前 1000 行
                        importErrorVO.setRowErrors(errors.subList(0, Math.min(1000, errors.size())));
                    }
                }
            }).sheet().autoTrim(true).headRowNumber(context.getHeadNum()).doRead();
        } catch (ExcelHeaderException e) {
            log.error("import header error, record id : {}", context.getRecordId(), e);
            importErrorVO.setType(ImportErrorTypeEnum.HEADER_ERROR.getType());
            importErrorVO.setErrorCode(ImportErrorTypeEnum.HEADER_ERROR.getErrorCode());
            updateEntity.setStatus(ImportStatusEnum.FAIL.getKey());
        } catch (Throwable e) {
            log.error("import error, record id : {}", context.getRecordId(), e);
            importErrorVO.setType(ImportErrorTypeEnum.UNKNOWN.getType());
            importErrorVO.setErrorCode(ImportErrorTypeEnum.UNKNOWN.getErrorCode());
            updateEntity.setStatus(ImportStatusEnum.FAIL.getKey());
        } finally {
            try {
                updateEntity.setFailRemark(JsonUtil.objectToJson(importErrorVO));
                sysImportRecordMapper.updateById(updateEntity);
                log.info("导入记录已更新，recordId: {}", context.getRecordId());
            } catch (Exception e) {
                log.error("更新导入记录失败，recordId: {}", context.getRecordId(), e);
            }
        }
    }

    /**
     * 校验
     */
    private void validateContext(ImportExcelContext<?> context) {
        if (Objects.isNull(context)) {
            throw new IllegalArgumentException("context is null");
        }
        if (Objects.isNull(context.getBizType())) {
            throw new IllegalArgumentException("bizType is null");
        }
        if (Objects.isNull(context.getImportVOClass())) {
            throw new IllegalArgumentException("importVOClass is null");
        }
        if (Objects.isNull(context.getImportLogic())) {
            throw new IllegalArgumentException("importLogic is null");
        }
        if (Objects.isNull(context.getFile())) {
            throw new IllegalArgumentException("file is null");
        }
    }

    /**
     * 校验导入行数限制
     */
    private void checkImportLimit(MultipartFile file, int headNum) {
        try (final Workbook workbook = WorkbookFactory.create(IoUtil.toMarkSupportStream(file.getInputStream()), null)) {
            // 取第一个 sheet
            final Sheet sheet = workbook.getSheetAt(0);
            // 取条数
            final int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();
            // 去掉表头
            if (physicalNumberOfRows - headNum > 10000) {
                GenericAppUtility.serviceError("The number of imported lines exceeds 10,000");
            }
        } catch (IOException e) {
            log.error("解析导入文件异常", e);
            GenericAppUtility.serviceError("Parsing file exception");
        }
    }

    /**
     * 通用导出
     */
    public <R> void generalExport(ExportExcelContext<R> context) {
        HttpServletResponse httpServletResponse = context.getHttpServletResponse();
        context.setFileName(StringUtils.defaultString(context.getFileName()));

        // 设置写入记录 key
        String key = RedisConstant.EXPORT_DATA_REQUEST_SEQUENCE_NO + context.getRequestNo();
        redissonClient.getBucket(key).set(ExportStatusEnum.EXPORTING.getCode(), RedisConstant.EXPORT_DATA_REQUEST_SEQUENCE_NO_EXPIRY_TIME, TimeUnit.SECONDS);

        // 动态表头
        Map<String, String> dynamicHeaderMap = ObjectUtils.defaultIfNull(context.getDynamicHeaderMap(), new HashMap<>());

        // 准备表头映射 map
        // 查询所有表头 key (不包含动态表头)
        Set<String> fixedHeaders = Arrays.stream(context.getExportVOClass().getDeclaredFields())
            .filter(field -> field.isAnnotationPresent(ExcelProperty.class))
            .map(field -> field.getAnnotation(ExcelProperty.class).value())
            .flatMap(Arrays::stream)
            // 动态表头的不需要国际化了
            .filter(field -> !dynamicHeaderMap.containsKey(field) && StringUtils.startsWith(field, "EXPORT_FIELD_"))
            .collect(Collectors.toSet());

        // 2. 批量查询对应的语言
        List<TextResourceEntity> textResources = textResourceMapper.selectByCodeAndType(TextResourceType.WEB, fixedHeaders);
        Map<String, String> fixedHeadersMap = textResources.stream().collect(Collectors.toMap(TextResourceEntity::getTextResourceCode, e ->
            LanguageEnum.ZH_CN.key.equals(context.getLanguage()) ? e.getValueZhCn() : e.getValueEnUs()));

        // 合并
        dynamicHeaderMap.putAll(fixedHeadersMap);


        // 导出数据
        try (ExcelWriter excelWriter = EasyExcel.write(httpServletResponse.getOutputStream(), context.getExportVOClass()).build()) {
            httpServletResponse.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            httpServletResponse.setCharacterEncoding("utf-8");
            String encodedFileName = URLEncoder.encode(context.getFileName() + ".xlsx", "UTF-8");
            httpServletResponse.setHeader(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment;filename=\"%s\";filename*=utf-8''%s", encodedFileName, encodedFileName));

            // 设置自定义处理器
            WriteSheet writeSheet = EasyExcel.writerSheet(0).registerWriteHandler(new CustomHeaderCellWriteHandler(dynamicHeaderMap)).build();

            // 写入数据
            excelWriter.write(context.getExportData(), writeSheet);
            excelWriter.finish();

            // 设置为成功
            redissonClient.getBucket(key).set(ExportStatusEnum.EXPORTED.getCode(), RedisConstant.EXPORT_DATA_REQUEST_SEQUENCE_NO_EXPIRY_TIME, TimeUnit.SECONDS);
        } catch (Throwable e) {
            log.error("export error, requestNo : {}", context.getRequestNo(), e);
            redissonClient.getBucket(key).set(ExportStatusEnum.EXPORT_FAILED.getCode(), RedisConstant.EXPORT_DATA_REQUEST_SEQUENCE_NO_EXPIRY_TIME, TimeUnit.SECONDS);
        }
    }

    /**
     * 自定义表头处理器
     * 处理动态表头和表头国际化
     */
    public static class CustomHeaderCellWriteHandler implements CellWriteHandler {

        private final Map<String, String> headerMap;

        public CustomHeaderCellWriteHandler(Map<String, String> dynamicHeaderMap) {
            this.headerMap = dynamicHeaderMap;
        }

        @Override
        public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
            // 非表头, 不处理
            if (!isHead) {
                return;
            }

            // 空校验
            final List<String> headNameList = head.getHeadNameList();
            if (CollUtil.isEmpty(headNameList)) {
                return;
            }

            // 转换
            List<String> newHeadNames = headNameList.stream().map(headName -> {
                    if (Objects.isNull(headerMap)) {
                        return headName;
                    }
                    return headerMap.getOrDefault(headName, headName);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            // 替换
            if (CollUtil.isNotEmpty(newHeadNames)) {
                head.setHeadNameList(newHeadNames);
            }
        }
    }

    /**
     * 填充行错误信息
     */
    public void fillRowErrorMessage(int language, Collection<ImportRowErrorVO> rowErrors) {
        if (CollUtil.isEmpty(rowErrors)) {
            return;
        }

        // 查询所有的 errorCode
        Set<String> errorCodes = rowErrors.stream().flatMap(rowError -> rowError.getErrors().stream()).map(ErrorDetail::getErrorCode).collect(Collectors.toSet());

        // fixme 兼容旧的每行只有一个 ErrorCode 的
        Set<String> externalErrorCode = rowErrors.stream().map(ImportRowErrorVO::getErrorCode).collect(Collectors.toSet());
        errorCodes.addAll(externalErrorCode);

        List<TextResourceEntity> textResources = textResourceMapper.selectByCodeAndType(TextResourceType.MESSAGE, errorCodes);
        Map<String, TextResourceEntity> errorCodeTextResourceMap = textResources.stream().collect(Collectors.toMap(TextResourceEntity::getTextResourceCode, Function.identity()));

        rowErrors.forEach(item -> {
            // fixme, 先暂时兼容, 等所有导入都调整完之后, 再修正
            if (Objects.nonNull(item.getErrorCode())) {
                // 如果存在error msg 不需要翻译
                if(Objects.nonNull(item.getErrorMsg())){
                    return;
                }
                TextResourceEntity textResourceEntity = errorCodeTextResourceMap.get(item.getErrorCode());
                if (Objects.isNull(textResourceEntity)) {
                    item.setErrorMsg(item.getErrorCode());
                    return;
                }

                String textResource = LanguageEnum.EN_US.key.equals(language) ? textResourceEntity.getValueEnUs() : textResourceEntity.getValueZhCn();
                item.setErrorMsg(MessageFormat.format(textResource, item.getParams()));
            } else {
                // 将每行的错误翻译后拼成一个错误
                String errorMsg = item.getErrors().stream().map(errorDetail -> {
                    TextResourceEntity textResourceEntity = errorCodeTextResourceMap.get(errorDetail.getErrorCode());
                    if (Objects.isNull(textResourceEntity)) {
                        return errorDetail.getErrorCode();
                    }
                    String textResource = LanguageEnum.EN_US.key.equals(language) ? textResourceEntity.getValueEnUs() : textResourceEntity.getValueZhCn();
                    return MessageFormat.format(textResource, errorDetail.getParams());
                }).collect(Collectors.joining(";"));
                item.setErrorMsg(errorMsg);
            }
        });
    }

}
