<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectMapper">

    <select id="queryProjectPage" resultType="com.fangcang.grfp.core.vo.ListProjectVO">
         SELECT
            p.project_id AS projectId,
            p.project_name AS projectName,
            p.project_state AS projectState,
            o.org_name AS orgName,
            p.project_type AS projectType,
            p.tender_count AS tenderCount,
            p.bid_start_time AS bidStartTime,
            p.bid_end_time AS bidEndTime,
            p.first_bid_start_time AS firstBidStartTime,
            p.first_bid_end_time AS firstBidEndTime,
            p.creator AS creator,
            p.create_time AS createTime
        FROM
            t_project p
        LEFT JOIN t_org o ON p.tender_org_id = o.org_id
        <where>
            <if test="query.tenderOrgId != null and query.tenderOrgId > 0">
                AND p.tender_org_id = #{query.tenderOrgId}
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND p.project_name like CONCAT('%', #{query.projectName}, '%')
            </if>
            <if test="query.creator != null and query.creator != ''">
                AND p.creator like CONCAT('%', #{query.creator}, '%')
            </if>
             <if test="query.projectState != null">
                  AND p.project_state = #{query.projectState}
             </if>
        </where>
         ORDER BY p.display_order DESC, p.create_time DESC
    </select>

</mapper>
