package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 酒店集团默认报价自定义策略
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_h_group_default_cus_strategy")
public class HGroupDefaultCusStrategyEntity extends BaseVO {


    /**
     * 自定义策略ID
     */
    @TableId(value = "custom_tend_strategy_id", type = IdType.ASSIGN_ID)
    private Integer customTendStrategyId;

    /**
     * 项目意向酒店集团ID
     */
    @TableField("project_intent_hotel_group_id")
    private Integer projectIntentHotelGroupId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 策略类型: 1-是或否 2-文本
     */
    @TableField("strategy_type")
    private Integer strategyType;

    /**
     * 策略名称
     */
    @TableField("strategy_name")
    private String strategyName;

    /**
     * 是否支持策略: 1-是 0-否
     */
    @TableField("support_strategy_name")
    private Integer supportStrategyName;

    /**
     * 策略回复文本
     */
    @TableField("support_strategy_text")
    private String supportStrategyText;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
