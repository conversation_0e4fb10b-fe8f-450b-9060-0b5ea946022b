package com.fangcang.grfp.core.remote.tmchub.manager;

import cn.hutool.core.date.DateUtil;
import com.fangcang.grfp.core.constant.SysConfig;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.*;
import com.fangcang.grfp.core.dto.dhub.request.product.HotelLowestPriceRequest;
import com.fangcang.grfp.core.dto.dhub.response.Response;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.*;
import com.fangcang.grfp.core.dto.dhub.response.product.HotelLowestPriceResponse;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.remote.tmchub.request.SignatureHelpRequestDto;
import com.fangcang.grfp.server.util.AppUtility;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest(classes = com.fangcang.grfp.server.GRfpServerApplication.class)
@Slf4j
public class TmcHubApiManagerTest {

    @Resource
    private TmcHubApiManager tmcHubApiManager;

    private SignatureHelpRequestDto signatureParam() {
        SignatureHelpRequestDto signatureParam = new SignatureHelpRequestDto();
        signatureParam.setPartnerCode(AppUtility.getSysInfo(SysConfig.TMC_HUB_PARTNER_CODE));
        signatureParam.setSecurityKey(AppUtility.getSysInfo(SysConfig.TMC_HUB_SECURITY_KEY));
        return signatureParam;
    }

    @Test
    void testQueryCountryList() {
        QueryCountryListRequest request = QueryCountryListRequest.builder()
            .language("en-US")
            .build();

        Response<QueryCountryListResponse> response = tmcHubApiManager.queryCountryList(request, signatureParam());
        Assertions.assertNotNull(response.getBussinessResponse());
    }

    @Test
    void testQueryCityList() {
        QueryCityListRequest request = QueryCityListRequest.builder()
            .language("en-US")
            .countryCode("CN")
            .build();

        Response<QueryCityListResponse> response = tmcHubApiManager.queryCityList(request, signatureParam());
        Assertions.assertNotNull(response.getBussinessResponse());
    }

    @Test
    void testQueryGroupBrandList() {
        QueryGroupBrandListRequest request = QueryGroupBrandListRequest.builder()
            .language("zh-CN")
            .build();

        Response<QueryGroupBrandListResponse> response = tmcHubApiManager.queryGroupBrandList(request, signatureParam());
        Assertions.assertNotNull(response.getBussinessResponse());
    }

    @Test
    void testQueryHotelInfo() {
        List<String> settings = new ArrayList<>();
        settings.add("hotelFacilityNew"); // 酒店设施（含房型设施）
        settings.add("breakfast"); // 酒店政策：早餐政策
        settings.add("importantNotices"); // 酒店政策：重要通知
        settings.add("parking"); // 酒店政策：停车场
        settings.add("chargingParking"); // 酒店政策：充电车位
        settings.add("hotelCertificates"); // 酒店资质，可能没有
        settings.add("comment"); // 酒店评分
        settings.add("hotelMeetingInfos"); // 酒店会议室信息
        settings.add("hotelVideoInfos"); // 酒店视频信息
        settings.add("hotelTextPolicies"); // 酒店文本政策”
        settings.add("hotelStructuredPolicies.childPolicy"); // 儿童政策
        settings.add("hotelStructuredPolicies.extraBedPolicy"); // 加床政策
        settings.add("hotelStructuredPolicies.petPolicy"); // 宠物政策

        HotelInfoRequest request = HotelInfoRequest.builder()
            .language(LanguageEnum.ZH_CN.value)
            .hotelIds(Lists.newArrayList(2022708L))
            .settings(settings)
            .build();

        Response<HotelInfoResponse> response = tmcHubApiManager.queryHotelInfo(request, signatureParam());
        Assertions.assertNotNull(response.getBussinessResponse());
    }

    @Test
    void testQueryHotelIncrement() {
        HotelIncrementRequest request = HotelIncrementRequest.builder()
            .startTime(DateUtil.parseDateTime("2025-05-16 00:00:00"))
            .endTime(DateUtil.parseDateTime("2025-05-23 22:00:00"))
            .pageNo(1)
            .build();

        Response<IncrementResponse> response = tmcHubApiManager.queryHotelIncrement(request, signatureParam());
        Assertions.assertNotNull(response.getBussinessResponse());
    }

    @Test
    void testGetDestination() {
        DestinationRequest request = DestinationRequest.builder()
            .keyWord("如家")
            .cityCode("CAN")
            .dataType(1)
            .build();

        Response<DestinationResponse> response = tmcHubApiManager.getDestination(request, null);
        Assertions.assertNotNull(response.getBussinessResponse());
    }

    @Test
    void testGetHotelSearch() {
        GetHotelSearchRequest request = GetHotelSearchRequest.builder()
                .destinationId("C3SZX")
                .language("en-US")
                .build();
        Response<GetHotelSearchResponse> response = tmcHubApiManager.getHotelSearch(request, null);
        Assertions.assertNotNull(response.getBussinessResponse());
    }

    @Test
    void testFindHotelList() {
        FindHotelListRequest request = FindHotelListRequest.builder()
                .destinationId("C3SZX")
                .checkInDate("2025-06-13")
                .checkOutDate("2025-06-14")
                .language("en-US")
                .build();
        Response<FindHotelListResponse> response = tmcHubApiManager.findHotelList(request, null);
        Assertions.assertNotNull(response.getBussinessResponse());
    }


    @Test
    void testQueryHotelIdList() {
        HotelIdListRequest request = HotelIdListRequest.builder()
            .build();

        Response<HotelIdListResponse> response = tmcHubApiManager.queryHotelIdList(request, signatureParam());
        Assertions.assertNotNull(response.getBussinessResponse());
    }

    @Test
    void testQueryHotelImage() {
        HotelImageRequest request = HotelImageRequest.builder()
            .hotelIds(Lists.newArrayList(110319L, 110320L))
            .language(LanguageEnum.ZH_CN.value)
            .build();

        Response<HotelImageResponse> response = tmcHubApiManager.queryHotelImage(request, signatureParam());
        Assertions.assertNotNull(response.getBussinessResponse());
    }

    @Test
    void testQueryHotelLowestPrice() {
        HotelLowestPriceRequest request = HotelLowestPriceRequest.builder()
            .hotelIds(Lists.newArrayList(110319L, 110320L))
            .build();

        Response<HotelLowestPriceResponse> response = tmcHubApiManager.queryHotelLowestPrice(request, signatureParam());
        Assertions.assertNotNull(response.getBussinessResponse());
    }

}
