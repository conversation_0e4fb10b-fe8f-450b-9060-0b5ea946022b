package com.fangcang.grfp.core.vo.request;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("国家列表分页查询")
@Getter
@Setter
public class ListCountryDataRequest extends PageQuery {

    @ApiModelProperty(value = "英文名称")
    private String nameEnUs;

    @ApiModelProperty(value = "中文名称")
    private String nameZhCn;

    @ApiModelProperty(value = "国家编号")
    private String countryCode;


}
