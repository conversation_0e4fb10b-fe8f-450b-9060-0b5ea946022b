package com.fangcang.grfp.core.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 导入错误信息 VO
 */
@Getter
@Setter
@ApiModel(description = "错误信息响应")
public class ImportErrorVO extends BaseVO {

    private static final long serialVersionUID = -5965362251919964965L;

    /**
     * 错误类型: 见 ImportErrorTypeEnum
     */
    @ApiModelProperty(value = "错误类型: 1-未知错误 2-表头错误 3-导入数据部分失败, type = 3 时, rowErrors 会有每行错误信息, 最多 1000 行")
    private Integer type;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorMsg;

    /**
     * 错误明细, 导入的时候每一行的报错信息
     */
    private List<ImportRowErrorVO> rowErrors;

}
