<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectCustomBidStrategyMapper">
    <insert id="batchMergeProjectCustomBidStrategy" parameterType="java.util.List">
        insert into t_project_custom_bid_strategy(custom_tend_strategy_id,
                                                  project_intent_hotel_id,
                                                  hotel_id,
                                                  project_id,
                                                  strategy_name,
                                                  strategy_type,
                                                  support_strategy_name,
                                                  support_strategy_text,
                                                  creator,
                                                  modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.customTendStrategyId},
             #{item.projectIntentHotelId},
             #{item.hotelId},
             #{item.projectId},
             #{item.strategyName},
             #{item.strategyType},
             #{item.supportStrategyName},
             #{item.supportStrategyText},
             #{item.creator},
             #{item.modifier})
        </foreach>
        on duplicate key update support_strategy_name = values(support_strategy_name),
                                support_strategy_text = values(support_strategy_text),
                                modifier              = values(modifier)
    </insert>

    <insert id="batchInsert">
        insert into t_project_custom_bid_strategy(custom_tend_strategy_id,
                                                  project_intent_hotel_id,
                                                  hotel_id,
                                                  project_id,
                                                  strategy_name,
                                                  strategy_type,
                                                  support_strategy_name,
                                                  support_strategy_text,
                                                  creator,
                                                  modifier)
        values
        <foreach collection="projectCustomBidStrategyList" item="item" separator=",">
            (#{item.customTendStrategyId},
             #{item.projectIntentHotelId},
             #{item.hotelId},
             #{item.projectId},
             #{item.strategyName},
             #{item.strategyType},
             #{item.supportStrategyName},
             #{item.supportStrategyText},
             #{item.creator},
             #{item.modifier})
        </foreach>
    </insert>
</mapper>
