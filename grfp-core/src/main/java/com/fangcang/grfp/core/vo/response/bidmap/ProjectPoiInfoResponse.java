package com.fangcang.grfp.core.vo.response.bidmap;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@ApiModel("项目POI信息")
@Getter
@Setter
public class ProjectPoiInfoResponse extends BaseVO {

    @ApiModelProperty("项目ID")
    private Integer projectId;

    @ApiModelProperty("POI ID")
    private Long poiId;


    /**
     * PIO名称
     */
    @ApiModelProperty("POI 名称")
    private String poiName;

    /**
     * POI地址
     */
    @ApiModelProperty("POI地址")
    private String poiAddress;

    /**
     * 城市名称
     */
    @ApiModelProperty("城市名称")
    private String cityName;


    /**
     * Google经度
     */
    @ApiModelProperty("Google经度")
    private BigDecimal lngGoogle;

    /**
     * Google经度
     */
    @ApiModelProperty("Google纬度")
    private BigDecimal latGoogle;

    // 3公里范围总间夜数
    @ApiModelProperty("3公里范围总间夜数")
    private Integer totalNightRoomCount;


    // 3公里范围总间成交金额
    @ApiModelProperty("3公里范围总间成交金额")
    private BigDecimal totalAmount;

    /**
     * 3公里范围城市占比
     */
    @ApiModelProperty("3公里范围城市占比")
    private Double cityRisk;

    // 5公里范围总间夜数
    @ApiModelProperty("5公里范围总间夜数")
    private Integer totalNightRoomCount5Km;


    // 5公里范围总间成交金额
    @ApiModelProperty("5公里范围总间成交金额")
    private BigDecimal totalAmount5Km;

    /**
     * 5公里范围城市占比
     */
    @ApiModelProperty("5公里范围城市占比")
    private Double cityRisk5Km;


    // 10公里范围总间夜数
    @ApiModelProperty("10公里范围总间夜数")
    private Integer totalNightRoomCount10Km;


    // 10公里范围总间成交金额
    @ApiModelProperty("10公里范围总间成交金额")
    private BigDecimal totalAmount10Km;

    /**
     * 10公里范围城市占比
     */
    @ApiModelProperty("10公里范围城市占比")
    private Double cityRisk10Km;


    /**
     * 城市编码
     */
    @ApiModelProperty("城市编码")
    private String cityCode;


}
