package com.fangcang.grfp.core.vo.request.project;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(description = "查询酒店项目招标权重配置请求")
public class QueryProjectHotelTendWeightRequest extends PageQuery {

    private static final long serialVersionUID = -1421938989349581513L;

    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull
    private Integer projectId;
}
