package com.fangcang.grfp.core.thread;


import com.fangcang.grfp.core.mapper.ProjectHotelHistoryDataMapper;
import com.fangcang.grfp.core.vo.request.QueryHistoryProjectInfoRequest;
import com.fangcang.grfp.core.vo.response.project.BidHotelInfoQueryResponse;
import com.fangcang.grfp.core.vo.response.project.QueryHistoryProjectInfoResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @date 2022/6/7 14:54
 */
public class ProjectHotelLastYearBidInfoQueryThread implements Callable<Map<Long, BidHotelInfoQueryResponse>> {

    private static final Logger logger = LoggerFactory.getLogger(ProjectHotelLastYearBidInfoQueryThread.class);

    private CountDownLatch countDownLatch;

    private Integer projectId;

    private Long baseCenterHotelId;

    private List<Long> hotelIdList;

    private ProjectHotelHistoryDataMapper projectHotelHistoryDataMapper;

    public ProjectHotelLastYearBidInfoQueryThread() {
    }

    public ProjectHotelLastYearBidInfoQueryThread(CountDownLatch countDownLatch,
                                                  Integer projectId,
                                                  Long baseCenterHotelId,
                                                  List<Long> hotelIdList,
                                                  ProjectHotelHistoryDataMapper projectHotelHistoryDataMapper) {
        this.projectId = projectId;
        this.baseCenterHotelId = baseCenterHotelId;
        this.countDownLatch = countDownLatch;
        this.hotelIdList = hotelIdList;
        this.projectHotelHistoryDataMapper = projectHotelHistoryDataMapper;
    }

    @Override
    public Map<Long, BidHotelInfoQueryResponse> call()  {
        try {
            if(projectId == null) {
                logger.error("ProjectHotelLastYearBidInfoQueryThread参数异常，项目id为空");
                return null;
            }
            if (CollectionUtils.isEmpty(hotelIdList)) {
                logger.error("ProjectHotelLastYearBidInfoQueryThread参数异常，酒店hotelIdList为空");
                return null;
            }
            Map<Long, BidHotelInfoQueryResponse> queryBidHotelInfoQueryResponseMap = new HashMap<>();
            List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoList = projectHotelHistoryDataMapper.queryHistoryProjectHotelList(projectId, null, hotelIdList, null, null);
            for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : queryHistoryProjectInfoList) {
                BidHotelInfoQueryResponse bidHotelInfoQueryResponse = new BidHotelInfoQueryResponse();
                if(queryHistoryProjectInfoResponse.getRoomNightCount() == 0 || queryHistoryProjectInfoResponse.getTotalAmount() == null || queryHistoryProjectInfoResponse.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
                    bidHotelInfoQueryResponse.setLatestYearRoomNight(0);
                    bidHotelInfoQueryResponse.setLastYearAvgPrice(BigDecimal.ZERO);
                    bidHotelInfoQueryResponse.setLastYearCityOrder(0);
                    continue;
                }
                bidHotelInfoQueryResponse.setLatestYearRoomNight(queryHistoryProjectInfoResponse.getRoomNightCount());
                bidHotelInfoQueryResponse.setLastYearAvgPrice(queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()),2, RoundingMode.HALF_UP));
                bidHotelInfoQueryResponse.setLastYearCityOrder(queryHistoryProjectInfoResponse.getCityOrder());
                queryBidHotelInfoQueryResponseMap.put(queryHistoryProjectInfoResponse.getHotelId(), bidHotelInfoQueryResponse);
            }

            int baseAvgPrice = 0;
            for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : queryHistoryProjectInfoList){
                if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), baseCenterHotelId)){
                    baseAvgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()),2, RoundingMode.HALF_UP).intValue();
                }
            }
            for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : queryHistoryProjectInfoList){
                if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), baseCenterHotelId)) {
                    continue;
                }
                // 判断是否为同档
                if(queryHistoryProjectInfoResponse.getRoomNightCount() > 0 && queryHistoryProjectInfoResponse.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                    int avgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()), 2, RoundingMode.HALF_UP).intValue();
                    if (baseAvgPrice > 0 && Math.abs(baseAvgPrice - avgPrice) <= 50) {
                        queryBidHotelInfoQueryResponseMap.get(queryHistoryProjectInfoResponse.getHotelId()).setTheSameLevelPrice(true);
                    }
                }
            }
            return queryBidHotelInfoQueryResponseMap;

        } finally {
            countDownLatch.countDown();
        }
    }


}
