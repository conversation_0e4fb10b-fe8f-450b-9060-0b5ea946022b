package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@ApiModel("获取下载模板地址")
@Getter
@Setter
public class GetTemplateFileUrlRequest extends BaseVO {


    @ApiModelProperty(value = "模板名称: <br>" +
            "批量注册酒店机构模板: batchRegisterHotelOrgTemplate.xlsx<br>" +
            "批量导入 POI 模板: BatchImportPoiTemplate.xlsx<br>" +
            "批量导入酒店集团意向酒店模板: HotelGroupIntentHotelTemplate.xlsx<br>" +
            "项目-邀请酒店-导入酒店: ImportProjectIntentHotelTemplate.xlsx<br>" +
            "批量导入项目酒店历史交易数据模板: BatchImportProjectHotelTradeDataTemplate.xlsx<br>" +
            "签约状态导入模板: ContractStatusImportTemplate.xlsx<br>" +
            "标准报价导入模板: StandardBidImportTemplate.xlsx<br>" +
            "Lanyon报价导入模板:LanyonBidImportTemplate.xlsx<br>"
    )
    @NotBlank
    private String templateFileName;

}
