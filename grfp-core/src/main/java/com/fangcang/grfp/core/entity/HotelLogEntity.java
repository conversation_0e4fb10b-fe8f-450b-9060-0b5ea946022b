package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 酒店日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_hotel_log")
public class HotelLogEntity extends BaseVO {


    /**
     * 酒店日志ID
     */
    @TableId(value = "hotel_log_id", type = IdType.AUTO)
    private Long hotelLogId;

    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 修改前值
     */
    @TableField("before_kv_json")
    private String beforeKvJson;

    /**
     * 修改后值
     */
    @TableField("after_kv_json")
    private String afterKvJson;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;


}
