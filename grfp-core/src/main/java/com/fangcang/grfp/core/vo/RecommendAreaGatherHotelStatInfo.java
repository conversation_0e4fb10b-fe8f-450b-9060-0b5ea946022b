package com.fangcang.grfp.core.vo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
  区域距离酒店信息
 */
public class RecommendAreaGatherHotelStatInfo implements Serializable {

    private static final long serialVersionUID = 2802719435015814525L;


    // 区域名称
    private String name;


    // 区域总间夜
    private Integer areaTotalRoomNight;

    // 距离
    private Double distance;

    // 最低价格
    private BigDecimal minPrice;

    //最高价格
    private BigDecimal maxPrice;

    // 星级分组
    private String starGroup;

    public RecommendAreaGatherHotelStatInfo(){

    }

    public Integer getAreaTotalRoomNight() {
        return areaTotalRoomNight;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(BigDecimal minPrice) {
        this.minPrice = minPrice;
    }

    public BigDecimal getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(BigDecimal maxPrice) {
        this.maxPrice = maxPrice;
    }

    public String getStarGroup() {
        return starGroup;
    }

    public void setStarGroup(String starGroup) {
        this.starGroup = starGroup;
    }

    public void setAreaTotalRoomNight(Integer areaTotalRoomNight) {
        this.areaTotalRoomNight = areaTotalRoomNight;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }
}
