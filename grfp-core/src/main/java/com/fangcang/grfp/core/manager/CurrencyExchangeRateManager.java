package com.fangcang.grfp.core.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.entity.CurrencyExchangeRateEntity;
import com.fangcang.grfp.core.entity.CurrencyExchangeRateLogEntity;
import com.fangcang.grfp.core.mapper.CurrencyExchangeRateLogMapper;
import com.fangcang.grfp.core.mapper.CurrencyExchangeRateMapper;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.JsonUtil;
import com.fangcang.grfp.core.vo.CurrencyCrossRateVO;
import com.fangcang.grfp.core.vo.CurrencyExchangeRateInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 */
@Slf4j
@Component
public class CurrencyExchangeRateManager {

    @Resource
    private CurrencyExchangeRateMapper currencyExchangeRateMapper;

    @Resource
    private CurrencyExchangeRateLogMapper currencyExchangeRateLogMapper;


    /**
     *  记录汇率变动日志
     *  record currency exchange log
     * @param currencyEntityList  xxJob传递的entity
     */
    public void recordCurrencyExchangeLog(List<CurrencyExchangeRateEntity> currencyEntityList) {

        List<String> currencyCodeList = currencyEntityList.stream().map(CurrencyExchangeRateEntity::getCurrencyCode).collect(Collectors.toList());

        List<CurrencyExchangeRateLogEntity> latestLogList = currencyExchangeRateLogMapper.selectLatestByCurrencyCodes(currencyCodeList);

        Map<String, CurrencyExchangeRateLogEntity> oldLogMap = latestLogList.stream().collect(Collectors.toMap(CurrencyExchangeRateLogEntity::getCurrencyCode, Function.identity()));
        List<CurrencyExchangeRateLogEntity> changeLogList = new ArrayList<>();

        for (int i = 0; i < currencyEntityList.size(); i++) {
            CurrencyExchangeRateEntity currencyEntity = currencyEntityList.get(i);
            String currencyCode = currencyEntity.getCurrencyCode();
            CurrencyExchangeRateLogEntity oldLogEntity = oldLogMap.get(currencyCode);
            // 没日志
            if (oldLogEntity == null) {
                CurrencyExchangeRateLogEntity logEntity = new CurrencyExchangeRateLogEntity();
                logEntity.setCurrencyCode(currencyCode);
                logEntity.setBeforeKvJson("");
                logEntity.setAfterKvJson(JsonUtil.objectToJson(currencyEntity));
                logEntity.setCreator(UserSession.SYSTEM_USER_NAME);
                changeLogList.add(logEntity);
            } else { // 有日志记录
                String afterKvJson = oldLogEntity.getAfterKvJson();
                CurrencyExchangeRateEntity oldCurrencyInfo = JsonUtil.jsonToBean(afterKvJson, CurrencyExchangeRateEntity.class);
                if (!currencyEntity.equals(oldCurrencyInfo)) {
                    CurrencyExchangeRateLogEntity logEntity = new CurrencyExchangeRateLogEntity();
                    logEntity.setCurrencyCode(currencyCode);
                    logEntity.setBeforeKvJson(afterKvJson);
                    logEntity.setAfterKvJson(JsonUtil.objectToJson(currencyEntity));
                    logEntity.setCreator(UserSession.SYSTEM_USER_NAME);
                    changeLogList.add(logEntity);
                }

            }

        }

        if (CollUtil.isNotEmpty(changeLogList)) {
            currencyExchangeRateLogMapper.batchInsert(changeLogList);
        }

    }

    /**
     *  币种汇率详情
     * @param currencyCode
     * @return
     */
    public CurrencyExchangeRateInfoVO getCurrencyExchangeRateInfo(String currencyCode) {
        CurrencyExchangeRateInfoVO infoVO = new CurrencyExchangeRateInfoVO();
        CurrencyExchangeRateEntity entity = currencyExchangeRateMapper.selectOne(new LambdaQueryWrapper<CurrencyExchangeRateEntity>()
                .eq(CurrencyExchangeRateEntity::getCurrencyCode, currencyCode));
        if (entity == null) {
            return infoVO;
        }
        infoVO.setCurrencyCode(entity.getCurrencyCode());
        infoVO.setCurrencyName(entity.getCurrencyName());
        infoVO.setExchangeRate(entity.getExchangeRate());
        infoVO.setInverseExchangeRate(entity.getInverseExchangeRate());
        infoVO.setDisplayOrder(entity.getDisplayOrder());
        infoVO.setIsAutoSync(entity.getIsAutoSync());
        return infoVO;
    }

    /**
     *  相除就是交叉汇率。 eg；人民币对欧元 =  eurRate/cnyRate
     * @param fromCurrencyCode
     * @param toCurrencyCode
     * @return
     */
    public CurrencyCrossRateVO getCurrencyCrossRate(String fromCurrencyCode, String toCurrencyCode) {
        log.info("Start getCurrencyCrossRate, currencyCode are: {},{}", fromCurrencyCode, toCurrencyCode);

        List<CurrencyExchangeRateEntity> currencyRateList = currencyExchangeRateMapper.getCurrencyRateInfoList(fromCurrencyCode, toCurrencyCode);
        // 如果同币种 sql 只会返回1条数据
        if (fromCurrencyCode.equals(toCurrencyCode)) {
            CurrencyExchangeRateEntity fromInfo = currencyRateList.get(0);
            CurrencyCrossRateVO crossRateVO = coverToRateVO(fromInfo, fromInfo);
            return crossRateVO;
        }

        if (currencyRateList.size() > 0) {
            CurrencyExchangeRateEntity fromInfo = currencyRateList.get(0);
            CurrencyExchangeRateEntity toInfo = currencyRateList.get(1);
            CurrencyCrossRateVO crossRateVO = coverToRateVO(fromInfo, toInfo);
            return crossRateVO;
        }
        return null;
    }

    private static CurrencyCrossRateVO coverToRateVO(CurrencyExchangeRateEntity fromInfo, CurrencyExchangeRateEntity toInfo) {
        CurrencyCrossRateVO crossRateVO = new CurrencyCrossRateVO();
        if(fromInfo != null && toInfo != null){
            crossRateVO.setCurrencyCodeFrom(fromInfo.getCurrencyCode());
            crossRateVO.setCurrencyNameFrom(fromInfo.getCurrencyName());
            crossRateVO.setCurrencyCodeTo(toInfo.getCurrencyCode());
            crossRateVO.setCurrencyNameTo(toInfo.getCurrencyName());
            crossRateVO.setExchangeRate(toInfo.getExchangeRate().divide(fromInfo.getExchangeRate(),10, RoundingMode.HALF_UP));
            crossRateVO.setInverseExchangeRate(new BigDecimal(1).divide(crossRateVO.getExchangeRate(),10, RoundingMode.HALF_UP));
            if (fromInfo.getModifyTime().after(toInfo.getModifyTime())) {
                crossRateVO.setModifyTime(fromInfo.getModifyTime());
            } else {
                crossRateVO.setModifyTime(toInfo.getModifyTime());
            }
        }
        return crossRateVO;
    }

}
