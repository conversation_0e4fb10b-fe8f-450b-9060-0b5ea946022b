package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.HotelLogEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 酒店日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface HotelLogMapper extends BaseMapper<HotelLogEntity> {

    /**
     * 根据酒店 ID 查询每个酒店 ID 最新的日志
     */
    List<HotelLogEntity> selectLatestByHotelIds(@Param("hotelIds") Collection<Long> hotelIds);

    /**
     * 批量插入
     */
    void batchInsert(@Param("list") List<HotelLogEntity> list);


}
