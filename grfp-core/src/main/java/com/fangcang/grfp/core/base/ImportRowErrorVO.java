package com.fangcang.grfp.core.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class ImportRowErrorVO {

    /**
     * 行数
     */
    private Integer rowNum;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 参数
     */
    private String[] params;

    /**
     * 错误详情列表
     */
    @JsonIgnore
    private List<ErrorDetail> errors = new ArrayList<>();

    public ImportRowErrorVO(Integer rowNum) {
        this.rowNum = rowNum;
    }

    /**
     * 历史原因, 理论上这个方法不应该再用了, 每一行的详细错误信息应该放在 errors 里面, 这样能兼容一行多个错误码
     */
    @Deprecated
    public ImportRowErrorVO(Integer rowNum, String errorCode) {
        this.rowNum = rowNum;
        this.errorCode = errorCode;
    }

    /**
     * 添加错误信息（无参数）
     */
    public void addError(String errorCode) {
        this.errors.add(new ErrorDetail(errorCode));
    }

    /**
     * 添加带参数的错误信息
     */
    public void addError(String errorCode, String... params) {
        this.errors.add(new ErrorDetail(errorCode, params));
    }

    public boolean hasError() {
        return !errors.isEmpty();
    }

}
