package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.HotelBrandEntity;
import com.fangcang.grfp.core.enums.YesOrNoEnum;
import com.fangcang.grfp.core.vo.HotelBrandVO;
import com.fangcang.grfp.core.vo.ListHotelBrandDataVO;
import com.fangcang.grfp.core.vo.request.ListHotelBrandDataRequest;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 酒店品牌 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface HotelBrandMapper extends BaseMapper<HotelBrandEntity> {

    void batchUpsert(@Param("list") Collection<HotelBrandEntity> hotelBrandEntities);

    /**
     * select all active brand id
     */
    default Set<Long> selectAllActiveBrandId() {
        LambdaQueryWrapper<HotelBrandEntity> queryWrapper = Wrappers.lambdaQuery(HotelBrandEntity.class)
            .eq(HotelBrandEntity::getIsActive, YesOrNoEnum.YES.getKey())
            .select(HotelBrandEntity::getHotelBrandId);
        return this.selectList(queryWrapper).stream().map(HotelBrandEntity::getHotelBrandId).collect(Collectors.toSet());
    }

    default void updateActiveByBrandIds(Collection<Long> brandIds, Integer active) {
        LambdaUpdateWrapper<HotelBrandEntity> updateWrapper = Wrappers.lambdaUpdate(HotelBrandEntity.class)
            .in(HotelBrandEntity::getHotelBrandId, brandIds)
            .set(HotelBrandEntity::getIsActive, active);
        this.update(null, updateWrapper);
    }

    /**
     * Select hotel brand list by hotel group id and name like
     */
    List<HotelBrandVO> selectHotelBrandNameList(@Param("languageId") Integer languageId,
                                            @Param("hotelGroupId")  Long hotelGroupId,
                                            @Param("hotelBrandName") String hotelBrandName,
                                            @Param("limitCount") Integer limitCount);


    IPage<ListHotelBrandDataVO> listDataPage(IPage<ListHotelBrandDataVO> page, @Param("languageId") Integer languageId, @Param("query") ListHotelBrandDataRequest query);

}
