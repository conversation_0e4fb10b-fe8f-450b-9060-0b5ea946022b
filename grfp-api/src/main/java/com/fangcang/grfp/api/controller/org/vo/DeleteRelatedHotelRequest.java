package com.fangcang.grfp.api.controller.org.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("删除机构关联酒店请求")
@Getter
@Setter
public class DeleteRelatedHotelRequest extends BaseVO {

    @ApiModelProperty("酒店机构Id")
    @NotNull
    private Integer hotelOrgId;

    @ApiModelProperty("酒店Id")
    @NotNull
    private Long hotelId;
}
