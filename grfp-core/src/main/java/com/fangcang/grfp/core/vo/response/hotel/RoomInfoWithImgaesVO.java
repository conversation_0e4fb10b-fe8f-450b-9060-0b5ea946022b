package com.fangcang.grfp.core.vo.response.hotel;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.Image;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class RoomInfoWithImgaesVO extends BaseVO {

    @ApiModelProperty(value = "酒店 ID")
    private Long hotelId;

    @ApiModelProperty(value = "房型 ID")
    private Long roomId;

    @ApiModelProperty(value = "房型名称")
    private String roomName;

    @ApiModelProperty(value = "房型图片集合")
    List<Image> roomIdImages;


}
