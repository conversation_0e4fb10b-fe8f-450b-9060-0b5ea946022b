package com.fangcang.grfp.api.controller.org.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fangcang.grfp.core.base.ImportVO;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ImportHotelOrgVO extends ImportVO {

    private static final long serialVersionUID = 6673184677160932559L;

    @ExcelProperty("HotelId")
    private String hotelId;

    @ExcelProperty("Org Contact UserName")
    private String contactName;

    @ExcelProperty("Org Contact Mobile")
    private String contactMobile;

    @ExcelProperty("Org Contact Email")
    private String contactEmail;


}
