package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@ApiModel("酒店意向操作请求")
public class JoinIntentHotelRequest extends BaseVO {

    @NotNull
    @ApiModelProperty(value = "项目ID", required = true)
    private Long projectId;

    @NotEmpty
    @ApiModelProperty(value = "项目酒店意向ids", required = true)
    private List<Long> projectIntentHotelIds;
}
