package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.ProjectHotelBidStrategyEntity;

import java.util.List;

/**
 * <p>
 * 项目酒店报价策略 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface ProjectHotelBidStrategyMapper extends BaseMapper<ProjectHotelBidStrategyEntity> {

    default ProjectHotelBidStrategyEntity queryProjectHotelBidStrategy(Integer projectId, Long hotelId) {
        LambdaQueryWrapper<ProjectHotelBidStrategyEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectHotelBidStrategyEntity::getProjectId, projectId);
        lambdaQueryWrapper.eq(ProjectHotelBidStrategyEntity::getHotelId, hotelId);
        return this.selectOne(lambdaQueryWrapper);
    }

    List<ProjectHotelBidStrategyEntity> queryByProjectIntentHotelIds(List<Integer> projectIntentHotelIds);

    default List<ProjectHotelBidStrategyEntity> selectByProjectId(Integer projectId) {
        LambdaQueryWrapper<ProjectHotelBidStrategyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHotelBidStrategyEntity::getProjectId, projectId);
        return selectList(queryWrapper);
    }

    default void deleteByProjectAndHotelId(Integer projectId, Long hotelId) {
        LambdaQueryWrapper<ProjectHotelBidStrategyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHotelBidStrategyEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectHotelBidStrategyEntity::getHotelId, hotelId);
        delete(queryWrapper);
    }
}
