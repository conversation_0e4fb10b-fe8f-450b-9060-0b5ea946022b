package com.fangcang.grfp.server;

import com.fangcang.grfp.core.cached.CachedSysConfigService;
import com.fangcang.grfp.core.cached.CachedTextResourceService;
import com.fangcang.grfp.core.constant.SysConfig;
import com.fangcang.grfp.server.util.AppUtility;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.annotation.PreDestroy;

@SpringBootApplication
@MapperScan(basePackages = {"com.fangcang.grfp.core.mapper"})
@ComponentScan(basePackages = {"com.fangcang.grfp.core", "com.fangcang.grfp.server"})
@EnableAsync
@Slf4j
public class GRfpServerApplication {

    public final static String VERSION = "v1.0.0-20250808";

    @Autowired
    private CachedSysConfigService cachedSysConfigService;

    @Autowired
    private CachedTextResourceService cachedTextResourceService;


    public static void main(String[] args) {
        SpringApplication.run(GRfpServerApplication.class, args);
    }

    @EventListener(ApplicationReadyEvent.class)
    public void afterStarted() {
        AppUtility.setCachedSysConfigService(cachedSysConfigService);
        AppUtility.setCachedTextResourceService(cachedTextResourceService);
        System.out.println();
        log.info("==============================GrfpServer 系统启动完成 @" + VERSION + "===========================================");
    }


    @PreDestroy
    public void onDestroy() throws Exception {
        log.info("GrfpServer " + VERSION + " is already [***stopped***]");
    }

}
