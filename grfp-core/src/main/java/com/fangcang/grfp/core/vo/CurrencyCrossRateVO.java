package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel("币种代码及名称列表")
@Getter
@Setter
public class CurrencyCrossRateVO extends BaseVO {

    @ApiModelProperty(value = "from币种编号")
    private String currencyCodeFrom;

    @ApiModelProperty(value = "from种名称")
    private String currencyNameFrom;

    @ApiModelProperty(value = "to币种编号")
    private String currencyCodeTo;

    @ApiModelProperty(value = "to币种名称")
    private String currencyNameTo;

    @ApiModelProperty(value = "正向汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "反向汇率（1/forwardExchangeRate）")
    private BigDecimal inverseExchangeRate;

    @ApiModelProperty("修改时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

}
