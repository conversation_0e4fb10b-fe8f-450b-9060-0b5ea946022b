package com.fangcang.grfp.api.controller.login;

import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.login.service.LoginService;
import com.fangcang.grfp.api.controller.login.vo.LoginRequest;
import com.fangcang.grfp.api.controller.login.vo.LoginByVerifyCodeRequest;
import com.fangcang.grfp.api.controller.login.vo.LoginResponse;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.usersession.Anon;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;


@Api(tags="登录接口")
@RestController
@RequestMapping("/Api")
public class LoginController extends BaseController {

    @Autowired
    private LoginService loginService;

    @Anon
    @ApiOperation("登录")
    @PostMapping("/Login")
    @ResponseBody
    public Result<LoginResponse> login(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody LoginRequest loginRequest, HttpSession session){
        LoginResponse loginResponse = loginService.login(session, request, response, loginRequest);
        return Result.ok(loginResponse);
    }

    @Anon
    @ApiOperation("登出")
    @PostMapping("/Logout")
    @ResponseBody
    public Result<Void> logout(HttpServletRequest request, HttpServletResponse response){
        return Result.ok();
    }


}
