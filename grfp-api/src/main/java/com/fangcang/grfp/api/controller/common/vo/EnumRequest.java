package com.fangcang.grfp.api.controller.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel("枚举类型查询请求")
@Getter
@Setter
public class EnumRequest {

    @ApiModelProperty(value = "枚举类型列表, 支持的枚举类型" +
            "HotelBidStateEnum - 报价状态枚举" +
            "HotelStarEnum - 酒店星级枚举 " +
            "BidUploadSourceEnum - 报价来源枚举" +
            "HotelGroupApproveStatusEnum - 酒店集团审核状态枚举" +
            "ProjectStateEnum` - 项目状态枚举" +
            "ProjectTypeEnum` - 项目类型枚举" +
            "RecommendLevelEnum` - 推荐等级枚举" +
            "YesOrNoEnum` - 是否枚举" +
            "HotelBidNotifyStatusEnum` - 酒店报价通知状态枚举" +
            "HotelBidNotifyStatusEnum` - 报价机构类型枚举" +
            "GeneratedBidStatusEnum` - 生成报价状态枚举" +
            "BidOrgTypeEnum` - 酒店星级分组枚举", required = true, example = "[\"HotelBidStateEnum\", \"HotelStarEnum\"]")
    private List<String> enumTypes;
} 