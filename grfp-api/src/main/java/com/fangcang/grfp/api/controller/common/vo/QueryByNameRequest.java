package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@ApiModel("根据名称查询请求")
@Getter
@Setter
public class QueryByNameRequest extends BaseVO {

    @ApiModelProperty(value="名称", required = true)
    @NotBlank
    private String name;

}
