package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.ProjectCustomStrategyOptionEntity;
import com.fangcang.grfp.core.vo.response.project.CustomStrategyOptionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 项目自定义策略选项表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface ProjectCustomStrategyOptionMapper extends BaseMapper<ProjectCustomStrategyOptionEntity> {

    /**
     * 批量插入
     */
    void batchInsert(@Param("list") List<ProjectCustomStrategyOptionEntity> optionList);

    /**
     * 根据策略 id 查询
     */
    List<CustomStrategyOptionVO> selectByStrategyIds(@Param("strategyIds") Set<Long> strategyIds);

    /**
     * 批量更新
     */
    int batchUpdate(@Param("list") List<ProjectCustomStrategyOptionEntity> list);

    /**
     * 根据策略 id 删除
     */
    default void deleteByStrategyId(Integer strategyId) {
        LambdaQueryWrapper<ProjectCustomStrategyOptionEntity> queryWrapper = Wrappers.lambdaQuery(ProjectCustomStrategyOptionEntity.class);
        queryWrapper.eq(ProjectCustomStrategyOptionEntity::getStrategyId, strategyId);
        delete(queryWrapper);
    }
}
