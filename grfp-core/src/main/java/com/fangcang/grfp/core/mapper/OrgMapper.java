package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fangcang.grfp.core.entity.OrgEntity;
import com.fangcang.grfp.core.enums.StateEnum;
import com.fangcang.grfp.core.vo.HotelRelatedOrgContactInfoVO;
import com.fangcang.grfp.core.vo.ListOrgVO;
import com.fangcang.grfp.core.vo.OrgNameVO;
import com.fangcang.grfp.core.vo.request.ListOrgRequest;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 机构 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface OrgMapper extends BaseMapper<OrgEntity> {

    default int selectCountByName(String orgName){
        LambdaQueryWrapper<OrgEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrgEntity::getOrgName, orgName);
        lambdaQueryWrapper.eq(OrgEntity::getState, StateEnum.Effective.key);
        return selectCount(lambdaQueryWrapper);

    }

    IPage<ListOrgVO> queryOrgVOPageList(IPage<ListOrgVO> page, @Param("query") ListOrgRequest query);

    /**
     * 根据名称查询机构 导入
     */
    default List<OrgEntity> selectByOrgNames(Collection<String> orgNames){
        LambdaQueryWrapper<OrgEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrgEntity::getOrgName, orgNames);
        queryWrapper.eq(OrgEntity::getState, StateEnum.Effective.key);
        return selectList(queryWrapper);
    }

    /**
     * 根据名称查询机构
     */
    List<OrgNameVO> queryOrgListByName(@Param("orgName") String orgName, @Param("orgTypeIdList") List<Integer> orgTypeIdList,  @Param("limitCount") Integer limitCount);


    /**
     * 根据酒店查询关联机构
     * @param list
     * @return
     */
    List<HotelRelatedOrgContactInfoVO> queryHotelOrgList(@Param("list") Collection<Long> list);

    /**
     * 根据酒店ID查询机构
     * @param hotelId
     * @return
     */
    OrgEntity getOrgByHotelId(Long hotelId);

    /**
     * 根据酒店查询关联机构
     */
    List<OrgEntity> getOrgByHotelIds(@Param("hotelIds") Collection<Long> hotelIds);
}
