package com.fangcang.grfp.server.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fangcang.grfp.core.constant.SysConfig;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.HotelIncrementRequest;
import com.fangcang.grfp.core.dto.dhub.response.Response;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.IncrementResponse;
import com.fangcang.grfp.core.enums.YesOrNoEnum;
import com.fangcang.grfp.core.manager.HotelManager;
import com.fangcang.grfp.core.mapper.HotelMapper;
import com.fangcang.grfp.core.remote.tmchub.manager.TmcHubApiManager;
import com.fangcang.grfp.core.remote.tmchub.request.SignatureHelpRequestDto;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.server.util.AppUtility;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;


@Component
@Slf4j
@JobHandler(value = SyncHotelIncrementJob.TASK_NAME)
public class SyncHotelIncrementJob extends IJobHandler {

    public static final String TASK_NAME = "SyncHotelIncrementJob";

    @Resource
    private TmcHubApiManager tmcHubApiManager;

    @Resource
    private HotelMapper hotelMapper;

    @Resource
    private HotelManager hotelManager;

    /**
     * Synchronize country data to the database
     */
    @Override
    public ReturnT<String> execute(String s) {
        log.info("Sync hotel increment data job start");
        try {
            int pageIndex = 1;
            // 查询时间范围必须小于当前时间, 为确保查询时间范围有效, 结束时间往前挪 10 分钟, 开始时间为 25 小时前(有重叠, 确保不漏)
            DateTime endTime = DateUtil.offsetMinute(new Date(), -10);
            DateTime startTime = DateUtil.offsetHour(endTime, -25);
            while (true) {
                List<Long> incrementalHotelIds = queryIncrementalHotelIds(pageIndex, startTime, endTime);
                if (CollUtil.isEmpty(incrementalHotelIds)) {
                    log.info("No hotel increment data found");
                    break;
                }

                // 查询数据库, 数据库存在的酒店才需要同步
                Set<Long> existedHotelIds = hotelMapper.selectActiveHotelIds(incrementalHotelIds);
                if (CollUtil.isEmpty(existedHotelIds)) {
                    log.info("No active hotel found for incremental data, pageIndex: {}", pageIndex);
                    // 如果没有有效的酒店, 直接下一页
                    pageIndex++;
                    continue;
                }

                // 同步酒店信息
                Set<Long> activeHotelIds = hotelManager.syncHotelInfo(existedHotelIds);

                // 移除有效的酒店, 剩下的就是无效的
                existedHotelIds.removeAll(activeHotelIds);
                // 更新找不到的酒店为无效酒店
                if (CollUtil.isNotEmpty(existedHotelIds)) {
                    hotelMapper.updateActiveByHotelIds(existedHotelIds, YesOrNoEnum.NO.getKey(), UserSession.SYSTEM_USER_NAME);
                }

                // 下一页
                pageIndex++;
            }

        } catch (Exception e) {
            log.error("Sync hotel increment data job error", e);
            return ReturnT.FAIL;
        }
        log.info("Sync hotel increment data end");
        return ReturnT.SUCCESS;
    }






    /**
     * 查询增量酒店 id
     */
    private List<Long> queryIncrementalHotelIds(int pageIndex, DateTime startTime, DateTime endTime) {
        HotelIncrementRequest request = HotelIncrementRequest.builder()
            .pageNo(pageIndex)
            .startTime(startTime)
            .endTime(endTime)
            .build();
        Response<IncrementResponse> response = tmcHubApiManager.queryHotelIncrement(request, signatureParam());
        if (Objects.isNull(response) || Objects.isNull(response.getBussinessResponse()) || CollUtil.isEmpty(response.getBussinessResponse().getIncrements())) {
            return Collections.emptyList();
        }
        return response.getBussinessResponse().getIncrements();
    }

    /**
     * Create a signature parameter
     */
    private SignatureHelpRequestDto signatureParam() {
        SignatureHelpRequestDto signatureParam = new SignatureHelpRequestDto();
        signatureParam.setPartnerCode(AppUtility.getSysInfo(SysConfig.TMC_HUB_PARTNER_CODE));
        signatureParam.setSecurityKey(AppUtility.getSysInfo(SysConfig.TMC_HUB_SECURITY_KEY));
        return signatureParam;
    }



}
