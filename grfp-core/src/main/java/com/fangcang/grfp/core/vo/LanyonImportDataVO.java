package com.fangcang.grfp.core.vo;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel("Lanyon导入数据")
@Getter
@Setter
public class LanyonImportDataVO {

    // lanyon 导入数据ID

    private Integer lanyonImportDataId;

    // 项目iD
    private Integer projectId;

    // 酒店ID
    private Long hotelId;


    // 数据类型 (1:导入数据1，2:导入数据2)
    private Integer dataType;


    // Json数据
    private String jsonData;

    private String creator;

    private Date createTime;

    private String modifier;

    private Date modifyTime;
}
