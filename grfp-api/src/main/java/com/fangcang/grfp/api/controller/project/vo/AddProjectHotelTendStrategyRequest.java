package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@ApiModel(description = "新增或修改酒店项目招标采购策略请求")
public class AddProjectHotelTendStrategyRequest extends BaseVO {

    private static final long serialVersionUID = -7766167070973080147L;

    @ApiModelProperty(value = "项目 ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "自定义采购策略")
    @Valid
    private List<UpdateProjectCustomTendStrategyRequest> projectCustomTendStrategies;

    @ApiModelProperty(value = "酒店是否需支持 VCC 公司统一支付: 1-是 0-否")
    private Integer supportVccPay = 0;

    @ApiModelProperty(value = "酒店是否须支持员工到店付款: 1-是 0-否")
    private Integer supportPayAtHotel = 0;

    @ApiModelProperty(value = "酒店是否须支持提供入住明细信息: 1-是 0-否")
    private Integer supportCheckinInfo = 0;

    @ApiModelProperty(value = "酒店是否须支持到店付免担保: 1-是 0-否")
    private Integer supportNoGuarantee = 0;

    @ApiModelProperty(value = "酒店是否须支持提前离店按实际入住金额收款: 1-是 0-否")
    private Integer supportPayEarlyCheckout = 0;

    @ApiModelProperty(value = "报价是否需要包括税费和服务费: 1-是 0-否")
    private Integer supportIncludeTaxService = 0;

    @ApiModelProperty(value = "酒店房间是否需提供免费 WIFI 服务: 1-是 0-否")
    private Integer supportWifi = 0;

    @ApiModelProperty(value = "酒店是否全部报价金额限制范围内报价：1-是 0-否")
    private Integer supportPriceLimit = 0;

    @ApiModelProperty(value = "限制最低报价，有报价限制时必须有值")
    private BigDecimal limitMinPrice;

    @ApiModelProperty(value = "限制最高报价，有报价限制时必须有值")
    private BigDecimal limitMaxPrice;

    @ApiModelProperty(value = "限制报价币种")
    private String limitPriceCurrencyCode = "USD";

    @ApiModelProperty(value = "酒店全部报价中是否支持N天M点前免费取消: 1-是 0-否")
    private Integer supportCancel = 0;

    @ApiModelProperty(value = "免费取消限制天数")
    private Integer supportCancelDay;

    @ApiModelProperty(value = "免费取消限制时间, 格式: HH:mm")
    private String supportCancelTime;

    @ApiModelProperty(value = "酒店投标是否支持lra的报价: 1-是 0-否")
    private Integer supportLra = 0;

    @ApiModelProperty(value = "酒店投标是否须支持报价中产品不适用日期数总和不能超过N天: 1-是 0-否")
    private Integer supportMaxNotApplicableDay = 0;

    @ApiModelProperty(value = "酒店报价中产品不适用日期数总和不能超过N天")
    private Integer maxNotApplicableDay;

    @ApiModelProperty(value = "是否支持最大房型数量限制: 1-是 0-否")
    private Integer supportMaxRoomTypeCount = 0;

    @ApiModelProperty(value = "酒店报价中允许的最大房型种类数量")
    private Integer maxRoomTypeCount;

    @ApiModelProperty(value = "是否支持SeasonDay限制: 1-是 0-否")
    private Integer supportSeasonDayLimit = 0;

    @ApiModelProperty(value = "最大 Season 日期数")
    private Integer maxSeasonDay;

    @ApiModelProperty(value = "是否必须提供无早单人价: 1-是 0-否")
    private Integer isRequireNoBreakfastSingle = 0;

    @ApiModelProperty(value = "是否必须提供无早双人价: 1-是 0-否")
    private Integer isRequireNoBreakfastDouble = 0;

    @ApiModelProperty(value = "是否必须提供含早单人价: 1-是 0-否")
    private Integer isRequireWithBreakfastSingle = 0;

    @ApiModelProperty(value = "是否必须提供含早双人价: 1-是 0-否")
    private Integer isRequireWithBreakfastDouble = 0;

    @ApiModelProperty(value = "是否建议提供无早单人价: 1-是 0-否")
    private Integer isRecommendNoBreakfastSingle = 0;

    @ApiModelProperty(value = "是否建议提供无早单人价: 1-是 0-否")
    private Integer isRecommendNoBreakfastDouble = 0;

    @ApiModelProperty(value = "是否建议提供含早单人价: 1-是 0-否")
    private Integer isRecommendWithBreakfastSingle = 0;

    @ApiModelProperty(value = "是否建议提供含早双人价: 1-是 0-否")
    private Integer isRecommendWithBreakfastDouble = 0;

    @ApiModelProperty(value = "是否提供全周适用价格: 1-是 0-否")
    private Integer isIncludeAllWeeklyDay = 0;

    @ApiModelProperty(value = "酒店报价是否必须填写税费明细")
    private Integer isRequireTaxDetails = 0;

    @ApiModelProperty(value = "酒店报价是否必须包含全部税费")
    private Integer isRequireIncludeAllTaxes = 0;

    @ApiModelProperty(value = "酒店报价是否建议包含全部税费")
    private Integer isRecommendIncludeAllTaxes = 0;

}
