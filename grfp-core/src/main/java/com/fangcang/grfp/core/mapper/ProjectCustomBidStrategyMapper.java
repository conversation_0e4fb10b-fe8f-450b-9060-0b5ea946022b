package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.ProjectCustomBidStrategyEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 项目自定义报价策略 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface ProjectCustomBidStrategyMapper extends BaseMapper<ProjectCustomBidStrategyEntity> {

    default List<ProjectCustomBidStrategyEntity> queryByProjectIntentHotelId(Integer projectIntentHotelId) {
        LambdaQueryWrapper<ProjectCustomBidStrategyEntity>queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProjectCustomBidStrategyEntity::getProjectIntentHotelId, projectIntentHotelId);
        return selectList(queryWrapper);
    }

    int batchMergeProjectCustomBidStrategy(@Param("list") List<ProjectCustomBidStrategyEntity> projectCustomBidStrategyList);

    default List<ProjectCustomBidStrategyEntity> selectByProjectId(Integer projectId) {
        LambdaQueryWrapper<ProjectCustomBidStrategyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectCustomBidStrategyEntity::getProjectId, projectId);
        return selectList(queryWrapper);
    }

    default void deleteByProjectIdAndHotelId(Integer projectId, Long hotelId) {
        LambdaQueryWrapper<ProjectCustomBidStrategyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectCustomBidStrategyEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectCustomBidStrategyEntity::getHotelId, hotelId);
        delete(queryWrapper);
    }

    void batchInsert(@Param("projectCustomBidStrategyList") List<ProjectCustomBidStrategyEntity> projectCustomBidStrategyList);

}
