package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 房型热门设施
 */
@Data
@ApiModel("房型热门设施")
public class RoomFacility {

    // 热门设施编码
    @ApiModelProperty("热门设施编码")
    private String facilityCode;

    // 热门设施名称
    @ApiModelProperty("热门设施名称")
    private String facilityName;

    // 设施类型
    @ApiModelProperty("设施类型")
    private Integer facilityType;

}
