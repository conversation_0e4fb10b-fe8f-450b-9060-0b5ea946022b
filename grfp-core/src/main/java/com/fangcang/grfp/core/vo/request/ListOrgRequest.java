package com.fangcang.grfp.core.vo.request;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


@ApiModel("机构列表查询")
@Getter
@Setter
public class ListOrgRequest extends PageQuery {

    @ApiModelProperty(value = "机构ID")
    private String orgId;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "机构类型 1平台，2酒店，3企业，4酒店集团")
    private Integer orgType;

    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;

}
