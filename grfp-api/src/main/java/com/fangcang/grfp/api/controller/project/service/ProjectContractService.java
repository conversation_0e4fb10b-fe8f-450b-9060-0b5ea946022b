package com.fangcang.grfp.api.controller.project.service;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.api.controller.org.vo.OrgInfoVO;
import com.fangcang.grfp.api.controller.project.vo.*;
import com.fangcang.grfp.api.controller.project.vo.ProjectBidStatCountVO;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.base.ImportRowErrorVO;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Response;
import com.fangcang.grfp.core.bo.RedisService;
import com.fangcang.grfp.core.cached.CachedCountryService;
import com.fangcang.grfp.core.cached.CachedCurrencyService;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.constant.RedisConstant;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.dto.excel.ImportExcelContext;
import com.fangcang.grfp.core.entity.*;
import com.fangcang.grfp.core.entity.ApplicableWeeksTypeEnum;
import com.fangcang.grfp.core.enums.*;
import com.fangcang.grfp.core.manager.BidMapManager;
import com.fangcang.grfp.core.manager.ExcelManager;
import com.fangcang.grfp.core.manager.HotelManager;
import com.fangcang.grfp.core.mapper.*;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.*;
import com.fangcang.grfp.core.vo.*;
import com.fangcang.grfp.core.vo.project.ImportContractStatusVO;
import com.fangcang.grfp.core.vo.request.QueryHotelGroupBidMapHotelListRequest;
import com.fangcang.grfp.core.vo.request.bidmap.*;
import com.fangcang.grfp.core.vo.request.project.QueryProjectHotelListRequest;
import com.fangcang.grfp.core.vo.request.project.QueryProjectHotelTendWeightRequest;
import com.fangcang.grfp.core.vo.response.bidmap.*;
import com.fangcang.grfp.core.vo.response.bidprice.PriceApplicableRoomInfoResponse;
import com.fangcang.grfp.core.vo.response.hotel.RoomNameInfoVO;
import com.fangcang.grfp.core.vo.response.hotelprice.HotelMinPriceResponse;
import com.fangcang.grfp.core.vo.response.project.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProjectContractService {

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private ProjectIntentHotelMapper projectIntentHotelMapper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectPoiMapper projectPoiMapper;

    @Autowired
    private ProjectHotelTendStrategyMapper projectHotelTendStrategyMapper;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private HttpServletRequest request;
    @Resource
    private ExcelManager excelManager;

    @Autowired
    private RedisService redisService;

    @Autowired
    private BidOperateLogMapper bidOperateLogMapper;
    @Autowired
    private ProjectHotelPriceMapper projectHotelPriceMapper;
    @Autowired
    private ProjectHotelPriceLevelMapper projectHotelPriceLevelMapper;
    @Autowired
    private PriceApplicableDayMapper priceApplicableDayMapper;
    @Autowired
    private PriceUnapplicableDayMapper priceUnapplicableDayMapper;
    @Autowired
    private PriceApplicableRoomMapper priceApplicableRoomMapper;
    @Autowired
    private HotelManager hotelManager;
    @Autowired
    private BidMapManager bidMapManager;
    @Autowired
    private ProjectHotelPriceGroupMapper projectHotelPriceGroupMapper;
    @Autowired
    private ProjectHotelHistoryDataMapper projectHotelHistoryDataMapper;
    @Autowired
    private CachedCountryService cachedCountryService;
    @Resource
    private CachedCurrencyService cachedCurrencyService;

    public PageVO<ProjectHotelListVO> queryProjectHotelList(QueryProjectHotelListRequest req) {
        ProjectEntity project = projectMapper.selectById(req.getProjectId());
        if (project == null) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        int languageId = AppUtility.getRequestHeaderLanguage(request);
        IPage<ProjectHotelListVO> page = new Page<>(req.getPageIndex(), req.getPageSize());
        page = projectIntentHotelMapper.queryProjectContractHotelList(page, req, languageId);

        // 计算酒店与项目POI之间最短距离
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            List<ProjectPoiVO> projectPoiList = projectPoiMapper.selectPoiInfoByProjectId(req.getProjectId().intValue(), null);
            Map<String, List<ProjectPoiVO>> projectPoiMap = projectPoiList.stream().collect(Collectors.groupingBy(ProjectPoiVO::getCityCode));
            if (CollectionUtils.isNotEmpty(projectPoiList)) {
                for (ProjectHotelListVO hotel : page.getRecords()) {
                    // BigDecimal lngGoogle, BigDecimal latGoogle, BigDecimal lngBaidu, BigDecimal latBaidu){
                    LngLatGoogleVO lngLatGoogleVO = CoordinateUtils.calculateNullGoogleLngLat(hotel.getLngGoogle(), hotel.getLatGoogle(), hotel.getLngBaidu(), hotel.getLatBaidu());
                    hotel.setLngGoogle(lngLatGoogleVO.getLngGoogle());
                    hotel.setLatGoogle(lngLatGoogleVO.getLatGoogle());
                    List<ProjectPoiVO> cityPoiList = projectPoiMap.get(hotel.getCityCode());
                    MinDistancePoiInfo minDistancePoiInfo = PoiUtility.filteMinDistancePoiInfo(hotel.getLngGoogle(), hotel.getLatGoogle(), cityPoiList);
                    if(minDistancePoiInfo.getMinDistance() != null){
                        hotel.setDistanceToPoi(minDistancePoiInfo.getMinDistanceKmFormat());
                        hotel.setPoiName(minDistancePoiInfo.getPoiName());
                    }
                }
            }
            // 设置项目状态
            List<Long> hotelIdList = new ArrayList<>();
            for (ProjectHotelListVO record : page.getRecords()) {
                hotelIdList.add(record.getHotelId());
                record.setProjectStatus(project.getProjectState());
                record.setProjectStatusDesc(ProjectStateEnum.geTextByKey(project.getProjectState(), languageId));
                record.setBidStateDesc(HotelBidStateEnum.getTextByKey(record.getBidState(), languageId));
                if (record.getHotelStar() != null) {
                    record.setHotelStarDesc(HotelStarEnum.getTextByKey(record.getHotelStar(), languageId));
                }
                // 设置通知状态描述
                if (record.getNotifyStatus() != null) {
                    record.setNotifyStatusDesc(HotelBidNotifyStatusEnum.getTextByKey(record.getNotifyStatus(), languageId));
                }
                // 设置报价机构类型描述
                if (record.getBidOrgType() != null) {
                    record.setBidOrgTypeDesc(BidOrgTypeEnum.getTextByKey(record.getBidOrgType(), languageId));
                }
            }
            // 查询去年采购间夜数和均价
            List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoResponseList =  projectHotelHistoryDataMapper.queryHistoryProjectHotelList(project.getProjectId(), null, hotelIdList, null, null);
            if (CollectionUtils.isNotEmpty(queryHistoryProjectInfoResponseList)) {
                Map<Long, QueryHistoryProjectInfoResponse> queryHistoryProjectInfoResponseMap = queryHistoryProjectInfoResponseList.stream().collect(Collectors.toMap(QueryHistoryProjectInfoResponse::getHotelId, Function.identity()));
                for (ProjectHotelListVO record : page.getRecords()) {
                    QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse = queryHistoryProjectInfoResponseMap.get(record.getHotelId());
                    if(queryHistoryProjectInfoResponse == null){
                        continue;
                    }
                    record.setLastYearRoomNight(queryHistoryProjectInfoResponse.getRoomNightCount());
                    if(queryHistoryProjectInfoResponse.getRoomNightCount() > 0) {
                        BigDecimal avgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(new BigDecimal(queryHistoryProjectInfoResponse.getRoomNightCount()), 2, RoundingMode.HALF_UP);
                        record.setMyPurchaseLastYearAvgPrice(avgPrice);
                    }
                }
            }
        }

        return new PageVO<>(page.getTotal(), page.getPages(), page.getRecords());
    }

    public ProjectBidStatCountVO queryProjectBidStatCount(HttpServletRequest request, QueryProjectBidStatCountRequest req){
        ProjectBidStatCountVO result = new ProjectBidStatCountVO();
        result.setProjectId(req.getProjectId());
        // 查询统计信息
        List<ProjectBidBrandStatInfoVO> projectBidBrandStatCountVOList = projectIntentHotelMapper.queryProjectBidBrandStatInfo(req.getProjectId());
        if(CollectionUtils.isEmpty(projectBidBrandStatCountVOList)){
            return result;
        }

        int languageId = AppUtility.getRequestHeaderLanguage(request);
        // 统计国家
        Map<String, ProjectBidStatCountItemVO> countryCountMap = new HashMap<>();
        Map<String, List<ProjectBidBrandStatInfoVO>> countryListMap = projectBidBrandStatCountVOList.stream().collect(Collectors.groupingBy(ProjectBidBrandStatInfoVO::getCountryCode));
        for(String countryCode : countryListMap.keySet()){
            if(!countryCountMap.containsKey(countryCode)){
                ProjectBidStatCountItemVO itemVO = new ProjectBidStatCountItemVO();
                itemVO.setCode(countryCode);
                itemVO.setName(countryCode);
                itemVO.setCount(0);
                CountryEntity countryEntity = cachedCountryService.getByCountryCode(countryCode);
                if(countryEntity != null){
                    itemVO.setName(AppUtility.getName(languageId, countryEntity.getCountryCode(), countryEntity.getNameEnUs(), countryEntity.getNameZhCn()));
                }
                countryCountMap.put(countryCode, itemVO);
            }
            ProjectBidStatCountItemVO countryCountVO = countryCountMap.get(countryCode);
            for(ProjectBidBrandStatInfoVO record : countryListMap.get(countryCode)){
                countryCountVO.setCount(countryCountVO.getCount() + record.getCountryBidCount());
            }
        }
        result.setCountryList(new ArrayList<>(countryCountMap.values()));

        // 城市统计
        Map<String, ProjectBidStatCountItemVO> cityCountMap = new HashMap<>();
        Map<String, List<ProjectBidBrandStatInfoVO>> cityListMap = projectBidBrandStatCountVOList.stream().collect(Collectors.groupingBy(ProjectBidBrandStatInfoVO::getCityCode));
        for(String cityCode : cityListMap.keySet()){
            if(!cityCountMap.containsKey(cityCode)){
                ProjectBidStatCountItemVO itemVO = new ProjectBidStatCountItemVO();
                itemVO.setCode(cityCode);
                itemVO.setName(AppUtility.getCityName(languageId, cityCode));
                itemVO.setCount(0);
                cityCountMap.put(cityCode, itemVO);
            }
            ProjectBidStatCountItemVO cityCountVO = cityCountMap.get(cityCode);
            for(ProjectBidBrandStatInfoVO record : cityListMap.get(cityCode)){
                cityCountVO.setCount(cityCountVO.getCount() + record.getCityBidCount());
            }
        }
        result.setCityList(new ArrayList<>(cityCountMap.values()));

        // 酒店集团统计
        projectBidBrandStatCountVOList = projectBidBrandStatCountVOList.stream().filter(item -> item.getHotelGroupId() != null).collect(Collectors.toList());
        Map<Long, List<ProjectBidBrandStatInfoVO>> hotelGroupListMap = projectBidBrandStatCountVOList.stream().collect(Collectors.groupingBy(ProjectBidBrandStatInfoVO::getHotelGroupId));
        Map<Long, ProjectBidStatCountItemVO> hotelGroupCountMap = new HashMap<>();
        for(Long hotelGroupId : hotelGroupListMap.keySet()){
            if(!hotelGroupCountMap.containsKey(hotelGroupId)){
                ProjectBidStatCountItemVO itemVO = new ProjectBidStatCountItemVO();
                itemVO.setId(hotelGroupId);
                itemVO.setName(AppUtility.getHotelGroupName(languageId, hotelGroupId));
                itemVO.setCount(0);
                hotelGroupCountMap.put(hotelGroupId, itemVO);
            }
            ProjectBidStatCountItemVO hotelGroupCountVO = hotelGroupCountMap.get(hotelGroupId);
            for(ProjectBidBrandStatInfoVO record : hotelGroupListMap.get(hotelGroupId)){
                hotelGroupCountVO.setCount(hotelGroupCountVO.getCount() + record.getHotelGroupBidCount());
            }
        }
        result.setHotelGroupList(new ArrayList<>(hotelGroupCountMap.values()));

        // 酒店品牌统计
        projectBidBrandStatCountVOList = projectBidBrandStatCountVOList.stream().filter(item -> item.getHotelBrandId() != null).collect(Collectors.toList());
        Map<Long, List<ProjectBidBrandStatInfoVO>> hotelBrandListMap = projectBidBrandStatCountVOList.stream().collect(Collectors.groupingBy(ProjectBidBrandStatInfoVO::getHotelBrandId));
        Map<Long, ProjectBidStatCountItemVO> hotelBrandCountMap = new HashMap<>();
        for(Long hotelBrandId : hotelBrandListMap.keySet()){
            if(!hotelBrandCountMap.containsKey(hotelBrandId)){
                ProjectBidStatCountItemVO itemVO = new ProjectBidStatCountItemVO();
                itemVO.setId(hotelBrandId);
                itemVO.setName(AppUtility.getHotelBrandName(languageId, hotelBrandId));
                itemVO.setCount(0);
                hotelBrandCountMap.put(hotelBrandId, itemVO);
            }
            ProjectBidStatCountItemVO hotelBrandCountVO = hotelBrandCountMap.get(hotelBrandId);
            for(ProjectBidBrandStatInfoVO record : hotelBrandListMap.get(hotelBrandId)){
                hotelBrandCountVO.setCount(hotelBrandCountVO.getCount() + record.getHotelBrandBidCount());
            }
        }
        result.setHotelBrandList(new ArrayList<>(hotelBrandCountMap.values()));

        // 排序
        result.getCountryList().sort(Comparator.comparingInt(ProjectBidStatCountItemVO::getCount).reversed());
        result.getCityList().sort(Comparator.comparingInt(ProjectBidStatCountItemVO::getCount).reversed());
        result.getHotelGroupList().sort(Comparator.comparingInt(ProjectBidStatCountItemVO::getCount).reversed());
        result.getHotelBrandList().sort(Comparator.comparingInt(ProjectBidStatCountItemVO::getCount).reversed());

        return result;

    }
    public BidHeaderDetailsVO queryBidHeaderDetails(Long projectId) {
        if (projectId == null || projectId <= 0) {
            AppUtility.serviceError(ErrorCode.REQUEST_PARAMETER_ERROR);
        }
        ProjectEntity project = projectMapper.selectById(projectId);
        if (project == null) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }
        // 权重
        ProjectHotelTendWeightVO projectHotelTendWeight = projectService.queryProjectHotelTendWeight(new QueryProjectHotelTendWeightRequest() {{ setProjectId(project.getProjectId()); }});
        BidHeaderDetailsVO vo = new BidHeaderDetailsVO();
        // 判断当前处于第几轮投标
        Date now = new Date();
        if (project.getFirstBidEndTime() != null && com.fangcang.grfp.core.util.DateUtil.getDay(project.getFirstBidEndTime(), now) <= 0) {
            vo.setStage(1); // 第一轮
            vo.setFirstBidStartTime(project.getFirstBidStartTime());
            vo.setFirstBidEndTime(project.getFirstBidEndTime());
        } else if (project.getSecondBidEndTime() != null && com.fangcang.grfp.core.util.DateUtil.getDay(project.getSecondBidStartTime(), now) >= 0 && com.fangcang.grfp.core.util.DateUtil.getDay(project.getSecondBidEndTime(), now) <= 0) {
            vo.setStage(2); // 第二轮
            vo.setSecondBidStartTime(project.getSecondBidStartTime());
            vo.setSecondBidEndTime(project.getSecondBidEndTime());
        } else if (project.getThirdBidEndTime() != null && com.fangcang.grfp.core.util.DateUtil.getDay(project.getThirdBidStartTime(), now) >= 0 && com.fangcang.grfp.core.util.DateUtil.getDay(project.getThirdBidEndTime(), now) <= 0) {
            vo.setStage(3); // 第三轮
            vo.setThirdBidStartTime(project.getThirdBidStartTime());
            vo.setThirdBidEndTime(project.getThirdBidEndTime());
        } else {
            if (project.getThirdBidEndTime() != null) {
                vo.setStage(3);
                vo.setThirdBidStartTime(project.getThirdBidStartTime());
                vo.setThirdBidEndTime(project.getThirdBidEndTime());
            } else if (project.getSecondBidEndTime() != null) {
                vo.setStage(2);
                vo.setSecondBidStartTime(project.getSecondBidStartTime());
                vo.setSecondBidEndTime(project.getSecondBidEndTime());
            } else {
                vo.setStage(1);
                vo.setFirstBidStartTime(project.getFirstBidStartTime());
                vo.setFirstBidEndTime(project.getFirstBidEndTime());
            }
        }
        vo.setProjectId(projectId);
        vo.setProjectName(project.getProjectName());
        vo.setProjectType(project.getProjectType());
        int languageId = AppUtility.getRequestHeaderLanguage(request);
        vo.setProjectTypeDesc(ProjectTypeEnum.getTextByKey(project.getProjectType(), languageId));
        vo.setTenderType(project.getTenderType());
        vo.setTenderTypeDesc(TenderTypeEnum.geTextByKey(project.getTenderType(), languageId));
        vo.setProjectState(project.getProjectState());
        vo.setProjectStateDesc(ProjectStateEnum.geTextByKey(project.getProjectState(), languageId));
        vo.setTenderCount(project.getTenderCount());
        vo.setTenderOrgId(project.getTenderOrgId());
        vo.setWhtTotalWeight(projectHotelTendWeight != null ? projectHotelTendWeight.getWhtTotalWeight() : BigDecimal.ZERO);
        // 采购总价、已投标酒店数、已中标酒店数、已投标总金额
        BigDecimal totalPurchasePrice = BigDecimal.ZERO;
        int tenderedHotelCount = 0;
        int bidWinningHotelCount = 0;
        BigDecimal tenderPurchaseTotalAmount = BigDecimal.ZERO;
        Map<Long, Integer> tenderedHotelMap = new HashMap<>();
        List<ProjectIntentHotelEntity> projectIntentHotels = projectIntentHotelMapper.selectList(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<ProjectIntentHotelEntity>().eq("project_id", projectId));
        if (projectIntentHotels != null && !projectIntentHotels.isEmpty()) {
            for (ProjectIntentHotelEntity hotel : projectIntentHotels) {
                Integer latestYearRoomNight = hotel.getLastYearRoomNight();
                BigDecimal tenderAvgPrice = hotel.getTenderAvgPrice();
                if (latestYearRoomNight != null && tenderAvgPrice != null) {
                    totalPurchasePrice = totalPurchasePrice.add(new BigDecimal(latestYearRoomNight).multiply(tenderAvgPrice));
                }
                Integer bidState = hotel.getBidState();
                if (bidState != null && !Objects.equals(bidState, HotelBidStateEnum.NO_BID.bidState)) {
                    tenderedHotelCount++;
                }
                if (bidState != null && Objects.equals(bidState, HotelBidStateEnum.BID_WINNING.bidState)) {
                    bidWinningHotelCount++;
                }
                if (bidState != null && (Objects.equals(bidState, HotelBidStateEnum.NEW_BID.bidState)
                        || Objects.equals(bidState, HotelBidStateEnum.UNDER_NEGOTIATION.bidState)
                        || Objects.equals(bidState, HotelBidStateEnum.BID_WINNING.bidState))) {
                    if (latestYearRoomNight != null && tenderAvgPrice != null) {
                        tenderPurchaseTotalAmount = tenderPurchaseTotalAmount.add(new BigDecimal(latestYearRoomNight).multiply(tenderAvgPrice));
                        tenderedHotelMap.put(hotel.getHotelId(), latestYearRoomNight);
                    }
                }
            }
        }
        // 采购金额比率
        String purchaseAmountRatio = "0.00%";
        // 已投标完成率
        String tenderedHotelCountRatio = "0.00%";
        if (project.getTenderCount() != null && project.getTenderCount() > 0) {
            tenderedHotelCountRatio = new BigDecimal(tenderedHotelCount).divide(new BigDecimal(project.getTenderCount()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%";
        }
        // 已确认签约酒店完成率
        String signingConfirmedHotelCountRatio = "0.00%";
        if (project.getTenderCount() != null && project.getTenderCount() > 0) {
            signingConfirmedHotelCountRatio = new BigDecimal(bidWinningHotelCount).divide(new BigDecimal(project.getTenderCount()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%";
        }
        // 投标总金额
        BigDecimal tenderTotalAmount = BigDecimal.ZERO;
        if (!tenderedHotelMap.isEmpty()) {
            for (Map.Entry<Long, Integer> entry : tenderedHotelMap.entrySet()) {
                ProjectIntentHotelEntity hotel = projectIntentHotels.stream().filter(h -> h.getHotelId().equals(entry.getKey())).findFirst().orElse(null);
                if (hotel != null && hotel.getTenderAvgPrice() != null) {
                    tenderTotalAmount = tenderTotalAmount.add(hotel.getTenderAvgPrice().multiply(new BigDecimal(entry.getValue())));
                }
            }
        }
        // 投标总金额同比增长率
        String tenderTotalGrowthRate = "0.00%";
        if (tenderPurchaseTotalAmount.doubleValue() > 0) {
            tenderTotalGrowthRate = (tenderTotalAmount.subtract(tenderPurchaseTotalAmount)).divide(tenderPurchaseTotalAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%";
        }
        vo.setTotalPurchasePrice(totalPurchasePrice);
        vo.setPurchaseAmountRatio(purchaseAmountRatio);
        vo.setTenderedHotelCount(tenderedHotelCount);
        vo.setTenderedHotelCountRatio(tenderedHotelCountRatio);
        vo.setSigningConfirmedHotelCount(bidWinningHotelCount);
        vo.setSigningConfirmedHotelCountRatio(signingConfirmedHotelCountRatio);
        vo.setTenderTotalAmount(tenderTotalAmount);
        vo.setTenderTotalGrowthRate(tenderTotalGrowthRate);
        return vo;
    }

    public ProjectDetailsVO queryProjectDetails(Long projectId) {
        if (projectId == null || projectId <= 0) {
            AppUtility.serviceError(ErrorCode.REQUEST_PARAMETER_ERROR);
        }
        
        // 查询项目基本信息
        ProjectEntity project = projectMapper.selectById(projectId);
        if (project == null) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }
        
        // 查询机构信息
        OrgEntity org = orgMapper.selectById(project.getTenderOrgId());
        if (org == null) {
            AppUtility.serviceError(ErrorCode.NOT_EXIST_ORG);
        }
        
        // 构建返回对象
        ProjectDetailsVO result = new ProjectDetailsVO();
        
        // 设置项目基本信息
        ProjectBasicInfoVO projectBasicInfo = new ProjectBasicInfoVO();
        BeanUtils.copyProperties(project, projectBasicInfo);
        // 设置机构名称
        projectBasicInfo.setTenderOrgName(org.getOrgName());
        // 设置项目简介（已包含在project中）
        result.setProjectBasicInfo(projectBasicInfo);
        
        // 设置机构信息
        OrgInfoVO orgInfo = new OrgInfoVO();
        BeanUtils.copyProperties(org, orgInfo);
        result.setOrg(orgInfo);
        
        // 查询项目POI信息
        List<ProjectPoiVO> projectPoiList = projectPoiMapper.selectPoiInfoByProjectId(projectId.intValue(), null);
        result.setProjectPoiInfoList(projectPoiList);
        
        // 查询项目采购策略
        ProjectHotelTendStrategyVO projectHotelTendStrategy = projectHotelTendStrategyMapper.selectByProjectId(projectId.intValue());
        result.setProjectHotelTendStrategy(projectHotelTendStrategy);
        
        return result;
    }

    public Long importContractStatus(MultipartFile file, Long projectId) throws IOException {
        ImportExcelContext<ImportContractStatusVO> context = new ImportExcelContext<>();
        context.setFile(file);
        context.setBizType(ImportBizTypeEnum.IMPORT_CONTRACT_STATUS);
        context.setImportVOClass(ImportContractStatusVO.class);
        context.setImportLogic(this::importContractStatus);
        context.getExtraParams().put("projectId", projectId);
        return excelManager.generalImport(context);
    }

    private List<ImportRowErrorVO> importContractStatus(ImportExcelContext<ImportContractStatusVO> context, List<ImportContractStatusVO> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        Long projectId = (Long) context.getExtraParams().get("projectId");
        UserSession userSession = context.getUserSession();
        List<ImportRowErrorVO> errors = new ArrayList<>();

        List<Long> hotelIds = data.stream().map(ImportContractStatusVO::getHotelId).filter(Objects::nonNull).collect(Collectors.toList());

        Map<Long, ProjectIntentHotelEntity> existingHotels = new HashMap<>();
        if (CollectionUtils.isNotEmpty(hotelIds)) {
            QueryWrapper<ProjectIntentHotelEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ProjectIntentHotelEntity::getProjectId, projectId).in(ProjectIntentHotelEntity::getHotelId, hotelIds);
            List<ProjectIntentHotelEntity> projectIntentHotels = projectIntentHotelMapper.selectList(queryWrapper);
            existingHotels = projectIntentHotels.stream().collect(Collectors.toMap(ProjectIntentHotelEntity::getHotelId, Function.identity()));
        }

        for (ImportContractStatusVO item : data) {
            if (item.getHotelId() == null) {
                errors.add(new ImportRowErrorVO(item.getRowNum(), "Hotel ID cannot be empty"));
                continue;
            }
            if (!existingHotels.containsKey(item.getHotelId())) {
                errors.add(new ImportRowErrorVO(item.getRowNum(), "Hotel ID does not exist in this project"));
                continue;
            }

            HotelBidStateEnum anEnum = null;
            if (!StringUtils.hasText(item.getBidStateName())) {
                errors.add(new ImportRowErrorVO(item.getRowNum(), "Contract status cannot be empty"));
                continue;
            } else {
                anEnum = HotelBidStateEnum.getEnumByDesc(item.getBidStateName());
                if (anEnum == null) {
                    errors.add(new ImportRowErrorVO(item.getRowNum(), "Invalid contract status"));
                    continue;
                }
            }

            if (StringUtils.hasText(item.getRemark()) && item.getRemark().length() > 500) {
                errors.add(new ImportRowErrorVO(item.getRowNum(), "Remark cannot exceed 500 characters"));
                continue;
            }

            ProjectIntentHotelEntity entityToUpdate = existingHotels.get(item.getHotelId());
            entityToUpdate.setBidState(anEnum.bidState);
            if (StringUtils.hasText(item.getRemark())) {
                entityToUpdate.setRemark(item.getRemark());
            }
            entityToUpdate.setModifier(userSession.getUsername());
            projectIntentHotelMapper.updateById(entityToUpdate);
        }
        return errors;
    }

    /**
     * 通知报价人
     */
    public String notifyBidder(NotifyBidderRequest req) {
        if (req == null || CollectionUtils.isEmpty(req.getProjectIntentHotels())) {
            AppUtility.serviceError(ErrorCode.REQUEST_PARAMETER_ERROR);
        }

        UserSession userSession = UserSession.get();
        String operator = userSession.getUsername();

        // 校验报价状态
        for (NotifyBidderRequest.NotifyBidderItem item : req.getProjectIntentHotels()) {
            if (HotelBidStateEnum.NO_BID.bidState.intValue() == item.getBidState().intValue()) {
                AppUtility.serviceError(ErrorCode.NOTIFY_BIDDER_CONTAINS_NO_BID_HOTEL);
            }
        }

        String key = RedisConstant.NOTIFY_BIDDER;
        
        // 加入Redis通知队列
        for (NotifyBidderRequest.NotifyBidderItem item : req.getProjectIntentHotels()) {
            try {
                String redisValue = item.getProjectId() + "_" + item.getProjectIntentHotelId() + "_" + operator;
                redisService.sadd(key, redisValue);
            } catch (Exception e) {
                log.error("加入通知投标人队列失败,项目ID：" + item.getProjectId() + "邀请酒店ID：" + item.getProjectIntentHotelId(), e);
            }
        }

        return "Sending Email";
    }


    public List<HotelBidStateResponse> queryMapGroupByHotelBidState(BidHotelStatQueryRequest bidHotelStatQueryRequest){
        UserSession userSession = UserSession.get();
        if(!userSession.getRoleCode().equals(RoleCodeEnum.ADMIN.key)){
            bidHotelStatQueryRequest.setUserIds(Lists.newArrayList(userSession.getUserId()));
        }
        return projectIntentHotelMapper.queryGroupByHotelBidState(bidHotelStatQueryRequest);
    }

    public PageVO<QueryMapBidHotelListResponse> queryMapHotelListByBidState(HttpServletRequest request, QueryMapHotelListByBidStateRequest req){
        UserSession userSession = UserSession.get();
        int languageId = GenericAppUtility.getRequestHeaderLanguage(request);
        if(!userSession.getRoleCode().equals(RoleCodeEnum.ADMIN.key)){
            req.setUserIds(Lists.newArrayList(userSession.getUserId()));
        }
        Page<QueryMapBidHotelListResponse> page = new Page<>(req.getPageIndex(), req.getPageSize());
        page = projectIntentHotelMapper.queryMapHotelListByBidState(page, req);
        if(CollectionUtils.isNotEmpty(page.getRecords())){
            List<Integer> projectIntegerHotelIdList = page.getRecords().stream().map(QueryMapBidHotelListResponse::getProjectIntentHotelId).collect(Collectors.toList());
            Map<Integer, HotelMinPriceResponse> hotelMinPriceResponsesMap = bidMapManager.queryHotelMinPrice(languageId, projectIntegerHotelIdList, true).stream().collect(Collectors.toMap(HotelMinPriceResponse::getProjectIntentHotelId, Function.identity()));
            page.getRecords().forEach(item -> {
                LngLatGoogleVO lngLatGoogle =  CoordinateUtils.calculateNullGoogleLngLat(item.getLngGoogle(), item.getLatGoogle(), item.getLngBaidu(), item.getLatBaidu());
                item.setLngGoogle(lngLatGoogle.getLngGoogle());
                item.setLatGoogle(lngLatGoogle.getLatGoogle());
                item.setHotelStarName(HotelStarEnum.getTextByKey(item.getHotelStar(), languageId));
                item.setHotelName(GenericAppUtility.getName(languageId, item.getHotelId(), item.getNameEnUs(), item.getNameZhCn()));
                item.setHotelAddress(GenericAppUtility.getName(languageId, item.getHotelId(), item.getAddressEnUs(), item.getAddressZhCn()));
                item.setCityName(GenericAppUtility.getCityName(languageId, item.getCityCode()));
                CountryEntity countryEntity = cachedCountryService.getByCountryCode(item.getCountryCode());
                if(countryEntity != null){
                    item.setCountryName(GenericAppUtility.getName(languageId, item.getCountryCode(), countryEntity.getNameEnUs(), countryEntity.getNameZhCn()));
                }

                HotelMinPriceResponse hotelMinPriceResponse = hotelMinPriceResponsesMap.get(item.getProjectIntentHotelId());
                if(hotelMinPriceResponse != null) {
                    this.convertCurrency(item, hotelMinPriceResponse.getMinPrice());
                    item.setBreakfastNum(BreakfastNumEnum.getTextByKey(hotelMinPriceResponse.getBreakfastNum(), languageId));
                    List<PriceApplicableRoomInfoResponse> priceApplicableRoomInfoResponses = hotelMinPriceResponse.getRoomResponseList();
                    if(CollectionUtils.isNotEmpty(priceApplicableRoomInfoResponses)) {
                        item.setRoomNameDesc(priceApplicableRoomInfoResponses.get(0).getRoomTypeName());
                        if(priceApplicableRoomInfoResponses.size() > 1){
                            item.setRoomNameDesc(item.getRoomNameDesc() + ",..");
                        }
                    } else{
                        item.setRoomNameDesc(hotelMinPriceResponse.getRoomTypeDesc());
                    }
                }
            });

        }
        return new PageVO<>(page.getTotal(), page.getPages(), page.getRecords());
    }

    /**
     * 按币种转换金额
     */
    private void convertCurrency(QueryMapBidHotelListResponse item, BigDecimal sourceMinPrice) {
        CurrencyExchangeRateEntity viewCurrency = cachedCurrencyService.getCurrencyRateInfo(GenericAppUtility.getRequestHeaderCurrency());
        CurrencyExchangeRateEntity bidCurrency = cachedCurrencyService.getCurrencyRateInfo(item.getCurrencyCode());
        if (Objects.nonNull(viewCurrency) && Objects.nonNull(bidCurrency)) {
            item.setMinPrice(NumberUtil.div(NumberUtil.mul(sourceMinPrice, viewCurrency.getExchangeRate()), bidCurrency.getExchangeRate(), 2));
            item.setCurrencyCode(viewCurrency.getCurrencyCode());
            return;
        }
        item.setMinPrice(sourceMinPrice);
    }


    public PageVO<ProjectPoiInfoResponse> queryProjectMapPoiInfo(HttpServletRequest request, QueryProjectMapPoiInfoRequest req){
        Page<ProjectPoiInfoResponse> page = new Page<>(req.getPageIndex(), req.getPageSize());
        // 查询项目3公里范围内POI
        projectPoiMapper.selectMapProjectPoiInfoPage(page, req.getProjectId(), null, req.getCityCode(), req.getDistance());
        int languageId = GenericAppUtility.getRequestHeaderLanguage(request);
        page.getRecords().forEach(item -> {
            item.setCityName(AppUtility.getCityName(languageId, item.getCityCode()));
        });

        return new PageVO<>(page.getTotal(), page.getPages(), page.getRecords());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateProjectIntentHotelBidContactInfo(UpdateProjectIntentHotelBidContactInfoRequest req){
        UserSession userSession = UserSession.get();
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.selectById(req.getProjectIntentHotelId());
        if(projectIntentHotel == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }
        ProjectIntentHotelEntity updateProjectIntentHotel = new ProjectIntentHotelEntity();
        updateProjectIntentHotel.setProjectIntentHotelId(req.getProjectIntentHotelId());
        updateProjectIntentHotel.setHotelBidContactName(req.getHotelBidContactName());
        updateProjectIntentHotel.setHotelBidContactMobile(req.getHotelBidContactMobile());
        updateProjectIntentHotel.setHotelBidContactEmail(req.getHotelBidContactEmail());
        updateProjectIntentHotel.setModifier(userSession.getUsername());
        int updateRecord = projectIntentHotelMapper.updateBidContactInfo(updateProjectIntentHotel);
        if(updateRecord <= 0){
            AppUtility.serviceError(ErrorCode.UPDATE_FAILED);
        }

        // 记录操作日志
        BidOperateLogEntity bidOperateLog = new BidOperateLogEntity();
        bidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        bidOperateLog.setProjectId(projectIntentHotel.getProjectId());
        bidOperateLog.setHotelId(projectIntentHotel.getHotelId());
        bidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
        bidOperateLog.setCreator(userSession.getUsername());
        StringBuffer operateContentSb = new StringBuffer();
        operateContentSb.append("Update HotelBidContactName");
        operateContentSb.append(" ");
        if(StringUtil.isValidString(projectIntentHotel.getHotelBidContactName())) {
            operateContentSb.append(projectIntentHotel.getHotelBidContactName());
            operateContentSb.append(", ");
        }
        if(StringUtil.isValidString(projectIntentHotel.getHotelBidContactMobile())) {
            operateContentSb.append(projectIntentHotel.getHotelBidContactMobile());
            operateContentSb.append(", ");
        }
        if(StringUtil.isValidString(projectIntentHotel.getHotelBidContactEmail())) {
            operateContentSb.append(projectIntentHotel.getHotelBidContactEmail());
        }
        operateContentSb.append(" To ");
        if(StringUtil.isValidString(updateProjectIntentHotel.getHotelBidContactName())) {
            operateContentSb.append(updateProjectIntentHotel.getHotelBidContactName());
            operateContentSb.append(", ");
        }
        if(StringUtil.isValidString(updateProjectIntentHotel.getHotelBidContactMobile())) {
            operateContentSb.append(updateProjectIntentHotel.getHotelBidContactMobile());
            operateContentSb.append(", ");
        }
        if(StringUtil.isValidString(updateProjectIntentHotel.getHotelBidContactEmail())) {
            operateContentSb.append(updateProjectIntentHotel.getHotelBidContactEmail());
        }
        bidOperateLog.setOperateContent(operateContentSb.toString());
        bidOperateLogMapper.insert(bidOperateLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateProjectIntentHotelGroupBidContactInfo(UpdateProjectIntentHotelGroupBidContactInfoRequest req){
        UserSession userSession = UserSession.get();
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.selectById(req.getProjectIntentHotelId());
        if(projectIntentHotel == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }
        ProjectIntentHotelEntity updateProjectIntentHotel = new ProjectIntentHotelEntity();
        updateProjectIntentHotel.setProjectIntentHotelId(req.getProjectIntentHotelId());
        updateProjectIntentHotel.setHotelGroupBidContactName(req.getHotelGroupBidContactName());
        updateProjectIntentHotel.setHotelGroupBidContactMobile(req.getHotelGroupBidContactMobile());
        updateProjectIntentHotel.setHotelGroupBidContactEmail(req.getHotelGroupBidContactEmail());
        updateProjectIntentHotel.setModifier(UserSession.get().getUsername());
        int updateRecord = projectIntentHotelMapper.updateBidContactInfo(updateProjectIntentHotel);
        if(updateRecord <= 0){
            AppUtility.serviceError(ErrorCode.UPDATE_FAILED);
        }

        // 记录操作日志
        BidOperateLogEntity bidOperateLog = new BidOperateLogEntity();
        bidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        bidOperateLog.setProjectId(projectIntentHotel.getProjectId());
        bidOperateLog.setHotelId(projectIntentHotel.getHotelId());
        bidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
        bidOperateLog.setCreator(userSession.getUsername());
        StringBuffer operateContentSb = new StringBuffer();
        operateContentSb.append("Update HotelGroupBidContactName ");
        operateContentSb.append(" ");
        if(StringUtil.isValidString(projectIntentHotel.getHotelGroupBidContactName())) {
            operateContentSb.append(projectIntentHotel.getHotelGroupBidContactName());
            operateContentSb.append(", ");
        }
        if(StringUtil.isValidString(projectIntentHotel.getHotelGroupBidContactMobile())) {
            operateContentSb.append(projectIntentHotel.getHotelGroupBidContactMobile());
            operateContentSb.append(", ");
        }
        if(StringUtil.isValidString(projectIntentHotel.getHotelGroupBidContactEmail())) {
            operateContentSb.append(projectIntentHotel.getHotelGroupBidContactEmail());
        }
        operateContentSb.append(" To ");
        if(StringUtil.isValidString(updateProjectIntentHotel.getHotelGroupBidContactName())) {
            operateContentSb.append(updateProjectIntentHotel.getHotelGroupBidContactName());
            operateContentSb.append(", ");
        }
        if(StringUtil.isValidString(updateProjectIntentHotel.getHotelGroupBidContactMobile())) {
            operateContentSb.append(updateProjectIntentHotel.getHotelGroupBidContactMobile());
            operateContentSb.append(", ");
        }
        if(StringUtil.isValidString(updateProjectIntentHotel.getHotelGroupBidContactEmail())) {
            operateContentSb.append(updateProjectIntentHotel.getHotelGroupBidContactEmail());
        }
        bidOperateLog.setOperateContent(operateContentSb.toString());
        bidOperateLogMapper.insert(bidOperateLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBidDateSetting(UpdateBidDateSettingRequest req){
        UserSession userSession = UserSession.get();
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.selectById(req.getProjectIntentHotelId());
        if(projectIntentHotel == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }

        // 比较PriceApplyDate
        Map<Integer, List<BidApplicableDayVO>> priceUpdateBidApplyDayMap = req.getBidApplicableDayList().stream().collect(Collectors.groupingBy(BidApplicableDayVO::getPriceType));

        PriceApplicableDayEntity queryPriceApplicableDay = new PriceApplicableDayEntity();
        queryPriceApplicableDay.setProjectId(projectIntentHotel.getProjectId());
        queryPriceApplicableDay.setHotelId(projectIntentHotel.getHotelId());
        List<PriceApplicableDayEntity> priceApplicableDayList = priceApplicableDayMapper.selectByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        Map<Integer, List<PriceApplicableDayEntity>> priceApplyDayMap = priceApplicableDayList.stream().collect(Collectors.groupingBy(PriceApplicableDayEntity::getPriceType));

        // 操作日志
        List<BidOperateLogEntity> bidOperateLogList = new ArrayList<>();
        for(Integer priceType : priceApplyDayMap.keySet()){
            List<PriceApplicableDayEntity> priceTypeApplicableDayList = priceApplyDayMap.get(priceType);
            // DB price applyDay content
            StringBuilder applicableDayContent = new StringBuilder();
            for(PriceApplicableDayEntity priceApplicableDay : priceTypeApplicableDayList){
                applicableDayContent.append(DateUtil.dateToString(priceApplicableDay.getStartDate()));
                applicableDayContent.append("To");
                applicableDayContent.append(DateUtil.dateToString(priceApplicableDay.getEndDate()));
                applicableDayContent.append(",");
            }

            List<PriceApplicableDayEntity> addPriceApplicableDayList = new ArrayList<>();
            if(priceUpdateBidApplyDayMap.containsKey(priceType)){
                StringBuilder updateApplicableDayContent = new StringBuilder();
                for(BidApplicableDayVO updateBidApplyDayDto : priceUpdateBidApplyDayMap.get(priceType)){
                    updateApplicableDayContent.append(DateUtil.dateToString(updateBidApplyDayDto.getStartDate()));
                    updateApplicableDayContent.append("To");
                    updateApplicableDayContent.append(DateUtil.dateToString(updateBidApplyDayDto.getEndDate()));
                    updateApplicableDayContent.append(",");
                    PriceApplicableDayEntity priceApplicableDay = new PriceApplicableDayEntity();
                    priceApplicableDay.setProjectId(projectIntentHotel.getProjectId());
                    priceApplicableDay.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                    priceApplicableDay.setHotelId(projectIntentHotel.getHotelId());
                    priceApplicableDay.setStartDate(updateBidApplyDayDto.getStartDate());
                    priceApplicableDay.setEndDate(updateBidApplyDayDto.getEndDate());
                    priceApplicableDay.setPriceType(updateBidApplyDayDto.getPriceType());
                    priceApplicableDay.setCreator(userSession.getUsername());
                    addPriceApplicableDayList.add(priceApplicableDay);
                }
                if(!applicableDayContent.toString().contentEquals(updateApplicableDayContent)){
                    // 删除旧日期
                    priceApplicableDayMapper.deleteByProjectIntentHotelAndPriceType(projectIntentHotel.getProjectIntentHotelId(), priceType);
                    // 新增新日期
                    priceApplicableDayMapper.insertBatch(addPriceApplicableDayList);

                    BidOperateLogEntity updateBidOperateLog = new BidOperateLogEntity();
                    updateBidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                    updateBidOperateLog.setProjectId(projectIntentHotel.getProjectId());
                    updateBidOperateLog.setHotelId(projectIntentHotel.getHotelId());
                    updateBidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
                    updateBidOperateLog.setCreator(userSession.getUsername());
                    updateBidOperateLog.setOperateContent("Update" + HotelPriceTypeEnum.getDateNameByKey(priceType) + ": " +
                            applicableDayContent.substring(0, applicableDayContent.length()-1) + " To " +  updateApplicableDayContent.substring(0, updateApplicableDayContent.length()-1));
                    bidOperateLogList.add(updateBidOperateLog);
                }
                // 删除价格类型对应日期
            } else {
                // 删除旧日期
                priceApplicableDayMapper.deleteByProjectIntentHotelAndPriceType(projectIntentHotel.getProjectIntentHotelId(), priceType);
                BidOperateLogEntity deleteBidOperateLog = new BidOperateLogEntity();
                deleteBidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                deleteBidOperateLog.setProjectId(projectIntentHotel.getProjectId());
                deleteBidOperateLog.setHotelId(projectIntentHotel.getHotelId());
                deleteBidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
                deleteBidOperateLog.setCreator(userSession.getUsername());
                deleteBidOperateLog.setOperateContent("Delete" + HotelPriceTypeEnum.getDateNameByKey(priceType) + ": " +
                        applicableDayContent.substring(0,applicableDayContent.length()-1));
                bidOperateLogList.add(deleteBidOperateLog);
            }
        }
        // 检查是否存在新增报价类型
        for(Integer priceType : priceUpdateBidApplyDayMap.keySet()){
            if(priceApplyDayMap.containsKey(priceType)){
                continue;
            }
            // 新增
            StringBuilder applicableDayContent = new StringBuilder();
            List<PriceApplicableDayEntity> addPriceApplicableDayList = new ArrayList<>();
            for(BidApplicableDayVO updateBidApplyDayDto : priceUpdateBidApplyDayMap.get(priceType)){
                PriceApplicableDayEntity priceApplicableDay = new PriceApplicableDayEntity();
                priceApplicableDay.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                priceApplicableDay.setProjectId(projectIntentHotel.getProjectId());
                priceApplicableDay.setHotelId(projectIntentHotel.getHotelId());
                priceApplicableDay.setStartDate(updateBidApplyDayDto.getStartDate());
                priceApplicableDay.setEndDate(updateBidApplyDayDto.getEndDate());
                priceApplicableDay.setPriceType(updateBidApplyDayDto.getPriceType());
                priceApplicableDay.setCreator(userSession.getUsername());
                addPriceApplicableDayList.add(priceApplicableDay);

                applicableDayContent.append(DateUtil.dateToString(priceApplicableDay.getStartDate()));
                applicableDayContent.append("To");
                applicableDayContent.append(DateUtil.dateToString(priceApplicableDay.getEndDate()));
                applicableDayContent.append(",");
            }
            BidOperateLogEntity addBidOperateLog = new BidOperateLogEntity();
            addBidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            addBidOperateLog.setProjectId(projectIntentHotel.getProjectId());
            addBidOperateLog.setHotelId(projectIntentHotel.getHotelId());
            addBidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
            addBidOperateLog.setCreator(userSession.getUsername());
            addBidOperateLog.setOperateContent("Add" + HotelPriceTypeEnum.getDateNameByKey(priceType) + ": " +
                    applicableDayContent.substring(0, applicableDayContent.length()-1));
            bidOperateLogList.add(addBidOperateLog);
            priceApplicableDayMapper.insertBatch(addPriceApplicableDayList);
        }

        // 不适用日期
        List<PriceUnapplicableDayEntity> unapplicableDayList = priceUnapplicableDayMapper.selectByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        StringBuilder unApplicableDayContent = new StringBuilder();
        if(CollectionUtils.isNotEmpty(unapplicableDayList)){
            for(PriceUnapplicableDayEntity priceUnapplicableDay : unapplicableDayList) {
                unApplicableDayContent.append(DateUtil.dateToString(priceUnapplicableDay.getStartDate()));
                unApplicableDayContent.append("To");
                unApplicableDayContent.append(DateUtil.dateToString(priceUnapplicableDay.getEndDate()));
                unApplicableDayContent.append(",");
            }
        }
        StringBuilder updateUnApplicableDayContent = new StringBuilder();
        if(CollectionUtils.isNotEmpty(req.getBidUnApplicableDayList())) {
            for(BidUnApplicableDayVO updateBidApplyDayDto : req.getBidUnApplicableDayList()) {
                updateUnApplicableDayContent.append(DateUtil.dateToString(updateBidApplyDayDto.getStartDate()));
                updateUnApplicableDayContent.append("To");
                updateUnApplicableDayContent.append(DateUtil.dateToString(updateBidApplyDayDto.getEndDate()));
                updateUnApplicableDayContent.append(",");
            }
        }

        if(!unApplicableDayContent.toString().contentEquals(updateUnApplicableDayContent)){
            BidOperateLogEntity addBidOperateLog = new BidOperateLogEntity();
            addBidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            addBidOperateLog.setProjectId(projectIntentHotel.getProjectId());
            addBidOperateLog.setHotelId(projectIntentHotel.getHotelId());
            addBidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
            addBidOperateLog.setCreator(userSession.getUsername());

            List<PriceUnapplicableDayEntity> addPriceUnapplicableDayList = new ArrayList<>();
            for(BidUnApplicableDayVO updateBidApplyDayDto : req.getBidUnApplicableDayList()) {
                PriceUnapplicableDayEntity priceUnapplicableDay = new PriceUnapplicableDayEntity();
                priceUnapplicableDay.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                priceUnapplicableDay.setProjectId(projectIntentHotel.getProjectId());
                priceUnapplicableDay.setHotelId(projectIntentHotel.getHotelId());
                priceUnapplicableDay.setStartDate(updateBidApplyDayDto.getStartDate());
                priceUnapplicableDay.setEndDate(updateBidApplyDayDto.getEndDate());
                priceUnapplicableDay.setCreator(userSession.getUsername());
                addPriceUnapplicableDayList.add(priceUnapplicableDay);
            }
            // 新增不适用日期集合
            if(unApplicableDayContent.length() == 0  && updateUnApplicableDayContent.length() != 0){
                priceUnapplicableDayMapper.batchInsert(addPriceUnapplicableDayList);
                addBidOperateLog.setOperateContent("Add UnapplicableDay: " +
                        updateUnApplicableDayContent.substring(0, updateUnApplicableDayContent.length()-1));
                bidOperateLogList.add(addBidOperateLog);
            } else if(unApplicableDayContent.length() != 0  && updateUnApplicableDayContent.length() == 0){
                priceUnapplicableDayMapper.deleteByProjectIdAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
                addBidOperateLog.setOperateContent("Delete UnapplicableDay: " +
                        unApplicableDayContent.substring(0, unApplicableDayContent.length()-1));
                bidOperateLogList.add(addBidOperateLog);
            } else {
                priceUnapplicableDayMapper.deleteByProjectIdAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
                priceUnapplicableDayMapper.batchInsert(addPriceUnapplicableDayList);
                addBidOperateLog.setOperateContent("Update UnapplicableDay: " +
                        unApplicableDayContent.substring(0, unApplicableDayContent.length()-1) + " To " +
                        updateUnApplicableDayContent.substring(0, updateUnApplicableDayContent.length()-1)
                );
                bidOperateLogList.add(addBidOperateLog);
            }
        }

        // 新增操作日志
        if(CollectionUtils.isNotEmpty(bidOperateLogList)){
            bidOperateLogList.forEach(item -> {
                bidOperateLogMapper.insert(item);
            });
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBidLevelRoomInfo(HttpServletRequest request, UpdateBidLevelRoomInfoRequest req){
        Integer languageId = GenericAppUtility.getRequestHeaderLanguage(request);
        UserSession userSession = UserSession.get();
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.selectById(req.getProjectIntentHotelId());
        if(projectIntentHotel == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }

        // 查询房档信息
        ProjectHotelPriceLevelEntity projectHotelPriceLevel = projectHotelPriceLevelMapper.selectById(req.getHotelPriceLevelId());
        if(projectHotelPriceLevel == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }

        // 修改房型总数
        if(!Objects.equals(projectHotelPriceLevel.getTotalRoomCount(), req.getTotalRoomCount()) ||
                !Objects.equals(projectHotelPriceLevel.getBigBedRoomCount(), req.getBigBedRoomCount()) ||
                !Objects.equals(projectHotelPriceLevel.getDoubleBedRoomCount(), req.getDoubleBedRoomCount())
        ) {
            BidOperateLogEntity updateRoomCountBidOperateLog = new BidOperateLogEntity();
            updateRoomCountBidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            updateRoomCountBidOperateLog.setProjectId(projectIntentHotel.getProjectId());
            updateRoomCountBidOperateLog.setHotelId(projectIntentHotel.getHotelId());
            updateRoomCountBidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
            updateRoomCountBidOperateLog.setCreator(userSession.getUsername());
            String stringBuilder = "Update" +
                    RoomLevelEnum.getValueByKey(projectHotelPriceLevel.getRoomLevelNo()) +
                    "RoomCount " +
                    "BigBedRoomCount" +
                    (projectHotelPriceLevel.getBigBedRoomCount() == null ? "null" : projectHotelPriceLevel.getBigBedRoomCount()) +
                    ", DoubleBedRoomCount" +
                    (projectHotelPriceLevel.getDoubleBedRoomCount() == null ? "null" : projectHotelPriceLevel.getDoubleBedRoomCount()) +
                    ", TotalRoomCount" +
                    (projectHotelPriceLevel.getTotalRoomCount() == null ? "null" : projectHotelPriceLevel.getTotalRoomCount()) +
                    " To " +
                    "BigBedRoomCount" +
                    (req.getBigBedRoomCount() == null ? "null" : req.getBigBedRoomCount()) +
                    ", DoubleBedRoomCount" +
                    (req.getDoubleBedRoomCount() == null ? "null" : req.getDoubleBedRoomCount()) +
                    ", TotalRoomCount" +
                    (req.getTotalRoomCount() == null ? "null" : req.getTotalRoomCount());
            updateRoomCountBidOperateLog.setOperateContent(stringBuilder);
            projectHotelPriceLevel.setTotalRoomCount(req.getTotalRoomCount());
            projectHotelPriceLevel.setBigBedRoomCount(req.getBigBedRoomCount());
            projectHotelPriceLevel.setDoubleBedRoomCount(req.getDoubleBedRoomCount());
            projectHotelPriceLevel.setModifier(userSession.getUsername());
            projectHotelPriceLevelMapper.updateRoomCount(projectHotelPriceLevel);
            bidOperateLogMapper.insert(updateRoomCountBidOperateLog);
        }

        // 修改房型
        List<PriceApplicableRoomEntity> dbPriceApplicableRoomList = priceApplicableRoomMapper.selectByProjectIntentHotelIds(Collections.singletonList(projectIntentHotel.getProjectIntentHotelId()));
        // 其他房档房型 id
        Set<Long> otherLevelRoomTypeIdList = dbPriceApplicableRoomList.stream()
            .filter(o -> !Objects.equals(o.getHotelPriceLevelId(), req.getHotelPriceLevelId()))
            .map(PriceApplicableRoomEntity::getRoomTypeId).filter(Objects::nonNull).collect(Collectors.toSet());
        // 其他房档自定义房型名称
        Set<String> otherLevelApplicableRoomNameList = dbPriceApplicableRoomList.stream()
            .filter(o -> !Objects.equals(o.getHotelPriceLevelId(), req.getHotelPriceLevelId()))
            .map(PriceApplicableRoomEntity::getCustomRoomTypeName).filter(Objects::nonNull).collect(Collectors.toSet());
        for (BidApplicableRoomVO updateBidLevelRoomDto : req.getRoomList()) {
            // 校验房型 id 是否重复
            if (Objects.nonNull(updateBidLevelRoomDto.getRoomTypeId()) && otherLevelRoomTypeIdList.contains(updateBidLevelRoomDto.getRoomTypeId())) {
                log.error("房型 id : {} 包含在其他房档 : {}", updateBidLevelRoomDto.getRoomTypeId(), otherLevelRoomTypeIdList);
                AppUtility.serviceError(ErrorCode.ROOM_TYPE_EXIST_IN_OTHER_PRICE_LEVEL);
            }
            // 校验自定义房型名称是否重复
            else if (otherLevelApplicableRoomNameList.contains(updateBidLevelRoomDto.getCustomRoomTypeName()) && org.apache.commons.lang3.StringUtils.isNotBlank(updateBidLevelRoomDto.getCustomRoomTypeName())) {
                log.error("房型名称 : {} 包含在其他房档 : {}", updateBidLevelRoomDto.getCustomRoomTypeName(), otherLevelApplicableRoomNameList);
                AppUtility.serviceError(ErrorCode.ROOM_TYPE_EXIST_IN_OTHER_PRICE_LEVEL);
            }
        }

        dbPriceApplicableRoomList = dbPriceApplicableRoomList.stream().filter(o -> Objects.equals(o.getHotelPriceLevelId(), req.getHotelPriceLevelId())).collect(Collectors.toList());
        List<Long> applicabeRoomTypeIdList = dbPriceApplicableRoomList.stream().map(PriceApplicableRoomEntity::getRoomTypeId).collect(Collectors.toList());
        BidOperateLogEntity updateBidOperateLog = new BidOperateLogEntity();
        updateBidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        updateBidOperateLog.setProjectId(projectIntentHotel.getProjectId());
        updateBidOperateLog.setHotelId(projectIntentHotel.getHotelId());
        updateBidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
        updateBidOperateLog.setCreator(userSession.getUsername());

        List<RoomNameInfoVO> roomNameInfoList = hotelManager.queryRoomNameListByHotelId(LanguageEnum.EN_US.key, projectIntentHotel.getHotelId());
        Map<Long, RoomNameInfoVO> roomNameInfoMap = roomNameInfoList.stream().collect(Collectors.toMap(RoomNameInfoVO::getRoomId, Function.identity()));

        // 组装操作日期内容
        String applicableRoomOperateContent = "";
        if(CollectionUtils.isNotEmpty(dbPriceApplicableRoomList)) {
            StringBuilder applicableRoomContent = new StringBuilder();
            for (PriceApplicableRoomEntity priceApplicableRoomInfoResponse : dbPriceApplicableRoomList) {
                // 构建房型名称, 可能是房型 id 对应的名称, 也可能是自定义房型名称
                String roomTypeName = "";
                if (Objects.nonNull(priceApplicableRoomInfoResponse.getRoomTypeId())) {
                    RoomNameInfoVO roomNameInfoVO = roomNameInfoMap.get(priceApplicableRoomInfoResponse.getRoomTypeId());
                    roomTypeName = Objects.nonNull(roomNameInfoVO) ? roomNameInfoVO.getRoomName() : null;
                } else if (org.apache.commons.lang3.StringUtils.isNotBlank(priceApplicableRoomInfoResponse.getCustomRoomTypeName())) {
                    roomTypeName = priceApplicableRoomInfoResponse.getCustomRoomTypeName();
                }
                applicableRoomContent.append(roomTypeName);
                applicableRoomContent.append("(").append(priceApplicableRoomInfoResponse.getRoomTypeId()).append(")");
                applicableRoomContent.append(",");
            }
            if(applicableRoomContent.length() > 0){
                applicableRoomOperateContent = applicableRoomContent.substring(0, applicableRoomContent.length()-1);
            }
        }
        String updateRoomOperateContent = "";
        if (CollectionUtils.isNotEmpty(req.getRoomList())) {
            StringBuilder updateRoomContent = new StringBuilder();
            for (BidApplicableRoomVO updateBidLevelRoomDto : req.getRoomList()) {
                // Lanyon upload room type id is null
                if (Objects.isNull(updateBidLevelRoomDto.getRoomTypeId()) && org.apache.commons.lang3.StringUtils.isBlank(updateBidLevelRoomDto.getCustomRoomTypeName())) {
                    continue;
                }
                String roomTypeName = "";
                if (Objects.nonNull(updateBidLevelRoomDto.getRoomTypeId())) {
                    RoomNameInfoVO roomNameInfoVO = roomNameInfoMap.get(updateBidLevelRoomDto.getRoomTypeId());
                    roomTypeName = Objects.nonNull(roomNameInfoVO) ? roomNameInfoVO.getRoomName() : null;
                } else if (org.apache.commons.lang3.StringUtils.isNotBlank(updateBidLevelRoomDto.getCustomRoomTypeName())) {
                    roomTypeName = updateBidLevelRoomDto.getCustomRoomTypeName();
                }
                updateRoomContent.append(roomTypeName);
                updateRoomContent.append("(").append(updateBidLevelRoomDto.getRoomTypeId()).append(")");
                updateRoomContent.append(",");
            }
            if (updateRoomContent.length() > 0) {
                updateRoomOperateContent = updateRoomContent.substring(0, updateRoomContent.length() - 1);
            }
        }

        List<PriceApplicableRoomEntity> priceApplicableRoomList = new ArrayList<>();
        int i=0;
        for(BidApplicableRoomVO updateBidLevelRoomDto : req.getRoomList()) {
            if (Objects.isNull(updateBidLevelRoomDto.getRoomTypeId()) && org.apache.commons.lang3.StringUtils.isBlank(updateBidLevelRoomDto.getCustomRoomTypeName())) {
                continue;
            }
            PriceApplicableRoomEntity addPriceApplicableRoom = new PriceApplicableRoomEntity();
            addPriceApplicableRoom.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            addPriceApplicableRoom.setProjectId(projectIntentHotel.getProjectId());
            addPriceApplicableRoom.setHotelPriceLevelId(req.getHotelPriceLevelId());
            addPriceApplicableRoom.setHotelId(projectIntentHotel.getHotelId());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(updateBidLevelRoomDto.getCustomRoomTypeName())) {
                addPriceApplicableRoom.setCustomRoomTypeName(updateBidLevelRoomDto.getCustomRoomTypeName());
            } else if (Objects.nonNull(updateBidLevelRoomDto.getRoomTypeId())) {
                addPriceApplicableRoom.setRoomTypeId(updateBidLevelRoomDto.getRoomTypeId());
            }
            addPriceApplicableRoom.setCreator(userSession.getUsername());
            addPriceApplicableRoom.setDisplayOrder(++i);
            priceApplicableRoomList.add(addPriceApplicableRoom);
        }

        if(CollectionUtils.isEmpty(req.getRoomList()) && CollectionUtils.isNotEmpty(applicabeRoomTypeIdList)){
            updateBidOperateLog.setOperateContent("Delete " + RoomLevelEnum.getValueByKey(projectHotelPriceLevel.getRoomLevelNo()) + " Room Type " + applicableRoomOperateContent);
            priceApplicableRoomMapper.deleteByHotelPriceLevelId(req.getHotelPriceLevelId());
        } else if(CollectionUtils.isNotEmpty(priceApplicableRoomList) && CollectionUtils.isNotEmpty(req.getRoomList()) && CollectionUtils.isEmpty(applicabeRoomTypeIdList)){
            updateBidOperateLog.setOperateContent("Add " + RoomLevelEnum.getValueByKey(projectHotelPriceLevel.getRoomLevelNo()) + " Room Type  " + updateRoomOperateContent);
            priceApplicableRoomMapper.batchInsert(priceApplicableRoomList);
        } else if(!applicableRoomOperateContent.equals(updateRoomOperateContent)){
            priceApplicableRoomMapper.deleteByHotelPriceLevelId(req.getHotelPriceLevelId());
            priceApplicableRoomMapper.batchInsert(priceApplicableRoomList);
            updateBidOperateLog.setOperateContent("Update " + RoomLevelEnum.getValueByKey(projectHotelPriceLevel.getRoomLevelNo()) + " Room Type  " + applicableRoomOperateContent + " To " + updateRoomOperateContent);
        }
        // 记录操作日志
        if(StringUtil.isValidString(updateBidOperateLog.getOperateContent())){
            bidOperateLogMapper.insert(updateBidOperateLog);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBidHotelPriceGroup(HttpServletRequest request, UpdateBidHotelPriceGroupRequest req){
        UserSession userSession = UserSession.get();

        // 查询db price group
        ProjectHotelPriceGroupEntity dbProjectHotelPriceGroup = projectHotelPriceGroupMapper.selectById(req.getHotelPriceGroupId());

        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.selectById(dbProjectHotelPriceGroup.getProjectIntentHotelId());

        // 平台更新不需要检查
        if(!Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.PLATFORM.key)) {
            // 检查 同房档group是否重复 比较价格星期是否有交叉重叠
            Response compareResponse = bidMapManager.compareApplicableWeeks(projectIntentHotel, dbProjectHotelPriceGroup.getHotelPriceLevelId(), dbProjectHotelPriceGroup.getHotelPriceGroupId(), req.getLra(), req.getApplicableWeeks(), true);
            if (compareResponse != null) {
                AppUtility.serviceError(request, compareResponse.getMsg());
            }
        }
        // 更新价格
        List<Integer> updatePriceIdList = req.getBidHotelPriceList().stream().map(BidHotelPriceVO::getHotelPriceId).filter(Objects::nonNull).collect(Collectors.toList());;
        // 平台修改记录操作日志

        // 锁定报价不能修改
        if(!Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.PLATFORM.key) && Objects.equals(dbProjectHotelPriceGroup.getIsLocked(), RfpConstant.constant_1)) {
            AppUtility.serviceError(ErrorCode.CANNOT_UPDATE_LOCKED_PRICE);
        }

        List<ProjectHotelPriceEntity> dbProjectHotelPriceList = null;
        if(Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.PLATFORM.key)) {
            dbProjectHotelPriceList = projectHotelPriceMapper.selectByProjectPriceGroupId(dbProjectHotelPriceGroup.getHotelPriceGroupId());

            // 记录价格组操作日志
            List<BidOperateLogEntity> bidOperateLogList = new ArrayList<>();
            if (!Objects.equals(dbProjectHotelPriceGroup.getApplicableWeeks(), req.getApplicableWeeks())) {
                StringBuilder operateContent = new StringBuilder();
                String[] split = dbProjectHotelPriceGroup.getApplicableWeeks().split(",");
                StringBuffer buffer = new StringBuffer();
                for (String s : split) {
                    String valueByKey = ApplicableWeeksTypeEnum.getValueByKey(Integer.valueOf(s));
                    buffer.append(valueByKey + ",");
                }
                String applicableWeeksDesc = buffer.substring(0, buffer.length() - 1);
                operateContent.append("Update ApplicableWeeks ").append(applicableWeeksDesc).append(" To ").append(ApplicableWeeksTypeEnum.getApplicableWeeksDesc(req.getApplicableWeeks()));
                BidOperateLogEntity bidOperateLog = new BidOperateLogEntity();
                bidOperateLog.setProjectIntentHotelId(dbProjectHotelPriceGroup.getProjectIntentHotelId());
                bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                bidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
                bidOperateLog.setCreator(userSession.getUsername());
                bidOperateLog.setOperateContent(operateContent.toString());
                bidOperateLogList.add(bidOperateLog);

            }
            if (!Objects.equals(dbProjectHotelPriceGroup.getLra(), req.getLra())) {
                String operateContent = "Update LRA From " + (dbProjectHotelPriceGroup.getLra() == RfpConstant.constant_1 ? "YES" : "NO") + " TO " + (req.getLra() == RfpConstant.constant_1 ? "YES" : "NO");
                BidOperateLogEntity bidOperateLog = new BidOperateLogEntity();
                bidOperateLog.setProjectIntentHotelId(dbProjectHotelPriceGroup.getProjectIntentHotelId());
                bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                bidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
                bidOperateLog.setCreator(userSession.getUsername());
                bidOperateLog.setOperateContent(operateContent);
                bidOperateLogList.add(bidOperateLog);
            }
            if ((StringUtil.isValidString(dbProjectHotelPriceGroup.getRemark()) || StringUtil.isValidString(req.getRemark())) && !Objects.equals(dbProjectHotelPriceGroup.getRemark(), req.getRemark())) {
                String operateContent = "Update Remark From " + (StringUtil.isValidString(dbProjectHotelPriceGroup.getRemark()) ? dbProjectHotelPriceGroup.getRemark() : "Null") + " To " + (StringUtil.isValidString(req.getRemark()) ? req.getRemark() : " Null ");
                BidOperateLogEntity bidOperateLog = new BidOperateLogEntity();
                bidOperateLog.setProjectIntentHotelId(dbProjectHotelPriceGroup.getProjectIntentHotelId());
                bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                bidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
                bidOperateLog.setCreator(userSession.getUsername());
                bidOperateLog.setOperateContent(operateContent);
                bidOperateLogList.add(bidOperateLog);
            }

            // 记录删除了的价格日志
            Map<Integer, ProjectHotelPriceEntity> dbPriceMap = dbProjectHotelPriceList.stream().collect(Collectors.toMap(ProjectHotelPriceEntity::getHotelPriceId, Function.identity()));
            for (Integer priceId : dbPriceMap.keySet()) {
                if (updatePriceIdList.contains(priceId)) {
                    continue;
                }
                ProjectHotelPriceEntity deleteProjectHotelPrice = dbPriceMap.get(priceId);
                // 记录删除日志
                BidOperateLogEntity bidOperateLog = new BidOperateLogEntity();
                bidOperateLog.setProjectIntentHotelId(dbProjectHotelPriceGroup.getProjectIntentHotelId());
                bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                bidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
                bidOperateLog.setCreator(userSession.getUsername());
                bidOperateLog.setOperateContent("Delete " + HotelPriceTypeEnum.getEnumByKey(deleteProjectHotelPrice.getPriceType()).value + "-One Person " + deleteProjectHotelPrice.getOnePersonPrice() + "-Two Person " + deleteProjectHotelPrice.getTwoPersonPrice());
                bidOperateLogList.add(bidOperateLog);
            }

            // 记录新增/更新日志
            for (BidHotelPriceVO projectHotelPrice : req.getBidHotelPriceList()) {
                if (projectHotelPrice.getHotelPriceId() == null) {
                    // 记录新增日志
                    BidOperateLogEntity bidOperateLog = new BidOperateLogEntity();
                    bidOperateLog.setProjectIntentHotelId(dbProjectHotelPriceGroup.getProjectIntentHotelId());
                    bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                    bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                    bidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
                    bidOperateLog.setCreator(userSession.getUsername());
                    bidOperateLog.setOperateContent("Add " + HotelPriceTypeEnum.getEnumByKey(projectHotelPrice.getPriceType()).value + "-One Person " + projectHotelPrice.getOnePersonPrice() + "-Two Person " + projectHotelPrice.getTwoPersonPrice());
                    bidOperateLogList.add(bidOperateLog);
                } else {
                    ProjectHotelPriceEntity updateDbProjectHotelPrice = dbPriceMap.get(projectHotelPrice.getHotelPriceId());
                    if (!Objects.equals(updateDbProjectHotelPrice.getOnePersonPrice(), projectHotelPrice.getOnePersonPrice()) ||
                            !Objects.equals(updateDbProjectHotelPrice.getTwoPersonPrice(), projectHotelPrice.getTwoPersonPrice())
                    ) {
                        BidOperateLogEntity bidOperateLog = new BidOperateLogEntity();
                        bidOperateLog.setProjectIntentHotelId(dbProjectHotelPriceGroup.getProjectIntentHotelId());
                        bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                        bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                        bidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
                        bidOperateLog.setCreator(userSession.getUsername());
                        bidOperateLog.setOperateContent("Update " + HotelPriceTypeEnum.getEnumByKey(updateDbProjectHotelPrice.getPriceType()).value + "-One Person  " + updateDbProjectHotelPrice.getOnePersonPrice() + "-Two Person  " + updateDbProjectHotelPrice.getTwoPersonPrice() + " Update To " +
                                HotelPriceTypeEnum.getEnumByKey(projectHotelPrice.getPriceType()).value + "-One Person  " + projectHotelPrice.getOnePersonPrice() + "-Two Person  " + projectHotelPrice.getTwoPersonPrice()
                        );
                        bidOperateLogList.add(bidOperateLog);
                    }
                }
            }

            // 更新是否含早
            if(!Objects.equals(dbProjectHotelPriceGroup.getIsIncludeBreakfast(), req.getIsIncludeBreakfast())){
                BidOperateLogEntity bidOperateLog = new BidOperateLogEntity();
                bidOperateLog.setProjectIntentHotelId(dbProjectHotelPriceGroup.getProjectIntentHotelId());
                bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                bidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
                bidOperateLog.setCreator(userSession.getUsername());
                String operateContent = dbProjectHotelPriceGroup.getIsIncludeBreakfast() == RfpConstant.constant_1 ? "Update Include Breakfast to No Include Breakfast" : "Update No Include Breakfast to Include Breakfast";
                bidOperateLog.setOperateContent(operateContent);
                bidOperateLogList.add(bidOperateLog);
            }
            // 记录操作日期
            if (CollectionUtils.isNotEmpty(bidOperateLogList)) {
                bidOperateLogList.forEach(item -> {
                    bidOperateLogMapper.insert(item);
                });
            }
        }

        // 更新group
        ProjectHotelPriceGroupEntity projectHotelPriceGroup = new ProjectHotelPriceGroupEntity();
        projectHotelPriceGroup.setHotelPriceGroupId(req.getHotelPriceGroupId());
        projectHotelPriceGroup.setApplicableWeeks(req.getApplicableWeeks());
        projectHotelPriceGroup.setLra(req.getLra());
        projectHotelPriceGroup.setRemark(req.getRemark());
        projectHotelPriceGroup.setIsIncludeBreakfast(req.getIsIncludeBreakfast());
        projectHotelPriceGroup.setModifier(userSession.getUsername());
        int updateRecord = projectHotelPriceGroupMapper.updateById(projectHotelPriceGroup);
        if(updateRecord == 0){
           AppUtility.serviceError(ErrorCode.UPDATE_FAILED);
        }

        // 删除不需要更新的price
        projectHotelPriceMapper.deleteByGroupIdAndExcludePriceIds(projectHotelPriceGroup.getHotelPriceGroupId(), updatePriceIdList);

        // 更新或新增价格
        for(BidHotelPriceVO projectHotelPrice : req.getBidHotelPriceList()){
            ProjectHotelPriceEntity projectHotelPriceEntity = new ProjectHotelPriceEntity();
            projectHotelPriceEntity.setHotelPriceId(projectHotelPrice.getHotelPriceId());
            projectHotelPriceEntity.setProjectIntentHotelId(dbProjectHotelPriceGroup.getProjectIntentHotelId());
            projectHotelPriceEntity.setProjectId(dbProjectHotelPriceGroup.getProjectId());
            projectHotelPriceEntity.setHotelId(dbProjectHotelPriceGroup.getHotelId());
            projectHotelPriceEntity.setHotelPriceLevelId(dbProjectHotelPriceGroup.getHotelPriceLevelId());
            projectHotelPriceEntity.setHotelPriceGroupId(dbProjectHotelPriceGroup.getHotelPriceGroupId());
            projectHotelPriceEntity.setOnePersonPrice(projectHotelPrice.getOnePersonPrice());
            projectHotelPriceEntity.setTwoPersonPrice(projectHotelPrice.getTwoPersonPrice());
            if(projectHotelPrice.getHotelPriceId() != null){
                projectHotelPriceEntity.setModifier(userSession.getUsername());
                projectHotelPriceMapper.updateById(projectHotelPriceEntity);
            } else {
                projectHotelPriceEntity.setCreator(userSession.getUsername());
                projectHotelPriceMapper.insert(projectHotelPriceEntity);
            }
        }
    }

    public void updatePriceGroupLocked(UpdatePriceGroupLockedRequest req){
        int updateCount = projectHotelPriceGroupMapper.updateLocked(req.getHotelPriceGroupId(), req.getIsLocked(), UserSession.get().getUsername());
        if(updateCount == 0){
            AppUtility.serviceError(ErrorCode.UPDATE_FAILED);
        }
    }

    public HotelBidDetailResponse queryHotelBidDetail(HttpServletRequest request, HotelBidDetailRequest req) {
        return bidMapManager.queryHotelBidDetail(request, req);
    }

    public QueryBidMapHotelInfoResponse queryBidMapHotelInfo(HttpServletRequest request, @Valid QueryHotelGroupBidMapHotelListRequest req) {
        return bidMapManager.queryBidMapHotelInfo(request, req);
    }

    public List<BidHotelPriceLevelInfoVO> querBidHotelPriceLevelInfoList(HttpServletRequest request, QueryHotelPriceLevelListRequest req) {
        return bidMapManager.querBidHotelPriceLevelInfoList(UserSession.get(), AppUtility.getRequestHeaderLanguage(request), req.getProjectIntentHotelId(), null, req.getHotelId(), null);
    }
}