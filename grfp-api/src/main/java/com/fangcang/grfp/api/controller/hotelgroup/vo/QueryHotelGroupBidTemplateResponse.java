package com.fangcang.grfp.api.controller.hotelgroup.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.*;
import com.fangcang.grfp.core.vo.response.project.ProjectBidCustomTendStrategyVO;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelTendStrategyVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@ApiModel("酒店集团项目报价模板")
@Getter
@Setter
public class QueryHotelGroupBidTemplateResponse extends BaseVO {

    @ApiModelProperty("项目ID")
    private Integer projectId;

    /**
     * 意向酒店集团ID
     */
    @ApiModelProperty("意向酒店集团ID")
    private Integer projectIntentHotelGroupId;

    /**
     * 酒店集团机构ID
     */
    @ApiModelProperty("酒店集团机构ID")
    private Integer hotelGroupOrgId;

    /**
     * 酒店集团销售联系人姓名
     */
    @ApiModelProperty("酒店集团销售联系人姓名")
    private String hotelGroupBidContactName;

    /**
     * 酒店集团销售联系人电话
     */
    @ApiModelProperty("酒店集团销售联系人电话")
    private String hotelGroupBidContactMobile;

    /**
     * 酒店集团销售联系人邮箱
     */
    @ApiModelProperty("酒店集团销售联系人邮箱")
    private String hotelGroupBidContactEmail;

    @ApiModelProperty("币种")
    private String currencyCode;

    @ApiModelProperty("酒店集团默认报价策略")
    private BidProjectStrategyVO bidProjectStrategy;

    @ApiModelProperty("项目酒店服务承诺")
    private ProjectHotelTendStrategyVO projectHotelTendStrategy;

    @ApiModelProperty("项目酒店服务其他承诺")
    private List<ProjectBidCustomTendStrategyVO> projectCustomTendStrategyList;

    @ApiModelProperty("酒店集团其他承诺")
    private List<HGroupDefaultCusStrategyVO> hotelGroupDefaultCusStrategyList;

    @ApiModelProperty("适用日期列表")
    private List<BidApplicableDayVO> applicableDayList;

    @ApiModelProperty("不适应日期列表")
    private List<HotelGroupDefaultUnApplicableDayVO> unapplicableDayList;




}
