package com.fangcang.grfp.core.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 报价操作日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */

@ApiModel("操作日志VO")
@Getter
@Setter
public class BidOperateLogVO extends BaseVO {


    /**
     * 报价操作日志ID
     */
    @ApiModelProperty("报价操作日志ID")
    private Long bidOperateLogId;

    /**
     * 项目酒店意向ID
     */
    @ApiModelProperty("项目酒店意向ID")
    private Integer projectIntentHotelId;

    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    private Integer projectId;

    /**
     * 酒店ID
     */
    @ApiModelProperty("酒店ID")
    private Long hotelId;

    /**
     * 机构类型ID
     */
    @ApiModelProperty("机构类型ID")
    private Integer orgTypeId;

    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    private String operator;

    /**
     * 操作内容
     */
    @ApiModelProperty("操作内容")
    private String operateContent;

    /**
     * 操作内容
     */
    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;


}
