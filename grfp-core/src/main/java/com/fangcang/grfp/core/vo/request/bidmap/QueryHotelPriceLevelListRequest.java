package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("查询价格房档列表")
@Getter
@Setter
public class QueryHotelPriceLevelListRequest extends BaseVO {

    @ApiModelProperty("项目意向酒店ID")
    @NotNull
    private Integer projectIntentHotelId;
    @ApiModelProperty("酒店ID")
    @NotNull
    private Long hotelId;

}
