package com.fangcang.grfp.api.controller.currencyexchangerate;

import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.currencyexchangerate.service.CurrencyExchangeRateService;
import com.fangcang.grfp.api.controller.currencyexchangerate.vo.*;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.constant.UserPermission;
import com.fangcang.grfp.core.usersession.RequiresPermissions;
import com.fangcang.grfp.core.vo.CurrencyCrossRateVO;
import com.fangcang.grfp.core.vo.CurrencyExchangeRateInfoVO;
import com.fangcang.grfp.core.vo.request.ListCurrencyExchangeRateRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;


@Api(tags = "币种汇率")
@RestController
@RequestMapping("/Api/CurrencyExchangeRate")
public class CurrencyExchangeRateController extends BaseController {

    @Autowired
    private CurrencyExchangeRateService currencyExchangeRateService;


    @ApiOperation("币种兑换汇率分页")
    @PostMapping("/CurrencyExchangeRatePage")
    @ResponseBody
    @RequiresPermissions(UserPermission.CURRENCY_EXCHANGE_RATE)
    public Result<PageVO<CurrencyCrossRateVO>> currencyExchangeRatePage(HttpServletRequest request, @RequestBody ListCurrencyExchangeRateRequest exchangeRateRequest) {
        return Result.ok(currencyExchangeRateService.currencyExchangeRatePage(request, exchangeRateRequest));
    }

    @ApiOperation("币种详情信息")
    @PostMapping("/CurrencyExchangeRateInfo")
    @ResponseBody
    @RequiresPermissions(UserPermission.CURRENCY_EXCHANGE_RATE)
    public Result<CurrencyExchangeRateInfoVO> currencyExchangeRateInfo(HttpServletRequest request, @Valid @RequestBody QueryCurrencyExchangeRequest query) {
        return Result.ok(currencyExchangeRateService.currencyExchangeRateInfo(request, query.getCurrencyCode()));
    }

    @ApiOperation("新增汇率(USD为基准)")
    @PostMapping("/AddCurrencyExchangeRate")
    @ResponseBody
    @RequiresPermissions(UserPermission.CURRENCY_EXCHANGE_RATE)
    @UserAuditLog("币种汇率-新增汇率")
    public Result<String> addCurrencyExchangeRate(HttpServletRequest request, @Valid @RequestBody AddCurrencyExchangeRequest addCurrencyExchangeRequest) {
        return Result.ok(currencyExchangeRateService.addCurrencyExchangeRate(request, addCurrencyExchangeRequest));
    }

    @ApiOperation("修改汇率(USD为基准)")
    @PostMapping("/UpdateCurrencyExchangeRate")
    @ResponseBody
    @RequiresPermissions(UserPermission.CURRENCY_EXCHANGE_RATE)
    @UserAuditLog("币种汇率-修改汇率")
    public Result<List<CurrencyExchangeRateInfoVO>> updateCurrencyExchangeRate(HttpServletRequest request, @Valid @RequestBody UpdateCurrencyExchangeRequest updateCurrencyExchangeRequest) {
        return Result.ok(currencyExchangeRateService.updateCurrencyExchangeRate(request, updateCurrencyExchangeRequest));
    }

    @ApiOperation("删除币种汇率")
    @PostMapping("/DeleteCurrencyExchangeRate")
    @ResponseBody
    @RequiresPermissions(UserPermission.CURRENCY_EXCHANGE_RATE)
    @UserAuditLog("币种汇率-删除汇率")
    public Result<Void> deleteCurrencyExchangeRate(HttpServletRequest request, @Valid @RequestBody QueryCurrencyExchangeRequest queryCurrencyExchangeRequest) {
        currencyExchangeRateService.deleteCurrencyExchangeRate(request, queryCurrencyExchangeRequest.getCurrencyCode());
        return Result.ok();
    }

    @ApiOperation("币种兑换汇率变化记录分页")
    @PostMapping("/CurrencyExchangeRateLogPage")
    @ResponseBody
    @RequiresPermissions(UserPermission.CURRENCY_EXCHANGE_RATE)
    public Result<PageVO<CurrencyExchangeRateLogVO>> currencyExchangeRateLogPage(HttpServletRequest request, @Valid @RequestBody CurrencyExchangeRateLogPageRequest currencyExchangeRateLogPageRequest) {
        return Result.ok(currencyExchangeRateService.currencyExchangeRateLogPage(request, currencyExchangeRateLogPageRequest));
    }

}
