package com.fangcang.grfp.server.job;

import com.fangcang.grfp.server.GRfpServerApplication;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(classes = GRfpServerApplication.class)
@Slf4j
public class SyncCurrencyDataJobTest {

    @Resource
    private SyncCurrencyExchangeRateJob syncCurrencyExchangeRateJob;

    @Test
    void testExecute() {
        ReturnT<String> res = syncCurrencyExchangeRateJob.execute("");
        Assertions.assertEquals(ReturnT.SUCCESS, res);
    }

}
