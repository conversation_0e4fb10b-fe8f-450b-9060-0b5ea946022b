package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.sql.Blob;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 项目lanyon显示key
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_lanyon_view_keys")
public class ProjectLanyonViewKeysEntity extends BaseVO {


    /**
     * 主键ID
     */
    @TableId(value = "project_lanyon_view_keys_id", type = IdType.AUTO)
    private Integer projectLanyonViewKeysId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 基础信息
     */
    @TableField("base_info")
    private String baseInfo;

    /**
     * 酒店认证
     */
    @TableField("hotel_verify")
    private String hotelVerify;

    /**
     * 酒店基础设施
     */
    @TableField("hotel_facilities")
    private String hotelFacilities;

    /**
     * 报价信息
     */
    @TableField("bid_info")
    private String bidInfo;

    /**
     * 会议室报价信息
     */
    @TableField("meeting_room_bid_info")
    private String meetingRoomBidInfo;

    /**
     * 长报价信息
     */
    @TableField("long_bid_info")
    private String longBidInfo;

    /**
     * 酒店服务
     */
    @TableField("hotel_service")
    private String hotelService;

    /**
     * 用户自定义
     */
    @TableField("user_defined")
    private String userDefined;

    /**
     * mtg用户自定义
     */
    @TableField("mtg_user_defined")
    private String mtgUserDefined;

    /**
     * LRY不可以信息
     */
    @TableField("lar_un_applicable_day_info")
    private String larUnApplicableDayInfo;


    /**
     * 基础服务费
     */
    @TableField("base_service_fee")
    private String baseServiceFee;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
