2025-08-03 22:23:23.268|main| INFO|c.f.g.c.remote.tmchub.manager.TmcHubApiManagerTest.logStarting:55|Starting TmcHubApiManagerTest on LAPTOP-BVT1ALEM with PID 7004 (started by <PERSON><PERSON><PERSON><PERSON> in D:\RFP\grfp-project\grfp-server)  [] ------
2025-08-03 22:23:23.273|main| INFO|c.f.g.c.remote.tmchub.manager.TmcHubApiManagerTest.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-03 22:23:24.985|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-03 22:23:24.989|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-03 22:23:25.020|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 15ms. Found 0 Redis repository interfaces.  [] ------
2025-08-03 22:23:26.302|main| WARN|c.b.mybatisplus.core.metadata.TableInfoHelper.initTableFields:327|Can not find table primary key in Class: "com.fangcang.grfp.core.entity.AmadeusMapingEntity".  [] ------
2025-08-03 22:23:28.104|main| WARN|c.b.mybatisplus.core.metadata.TableInfoHelper.initTableFields:327|Can not find table primary key in Class: "com.fangcang.grfp.core.entity.ProjectRecommendStatLogEntity".  [] ------
2025-08-03 22:23:29.201|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-03 22:23:31.723|redisson-netty-2-17| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for 192.168.2.174/192.168.2.174:6379  [] ------
2025-08-03 22:23:33.983|redisson-netty-2-18| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for 192.168.2.174/192.168.2.174:6379  [] ------
2025-08-03 22:23:34.264|main| INFO|c.fangcang.grfp.server.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-03 22:23:35.399|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-03 22:23:36.616|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-03 22:23:36.618|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-03 22:23:36.620|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-03 22:23:36.641|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:InviteHotelConsumerJob, jobHandler:com.fangcang.grfp.server.job.InviteHotelConsumerJob@57bac3f0  [] ------
2025-08-03 22:23:36.642|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:InviteHotelGroupConsumerJob, jobHandler:com.fangcang.grfp.server.job.InviteHotelGroupConsumerJob@55f6f965  [] ------
2025-08-03 22:23:36.642|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:NotifyBidderConsumerJob, jobHandler:com.fangcang.grfp.server.job.NotifyBidderConsumerJob@40ddf339  [] ------
2025-08-03 22:23:36.642|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncCountryDataJob, jobHandler:com.fangcang.grfp.server.job.SyncCountryDataJob@c83ed77  [] ------
2025-08-03 22:23:36.642|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncCurrencyExchangeRateJob, jobHandler:com.fangcang.grfp.server.job.SyncCurrencyExchangeRateJob@d271a54  [] ------
2025-08-03 22:23:36.642|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncGroupBrandDataJob, jobHandler:com.fangcang.grfp.server.job.SyncGroupBrandDataJob@6ff8e744  [] ------
2025-08-03 22:23:36.643|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncHotelIncrementJob, jobHandler:com.fangcang.grfp.server.job.SyncHotelIncrementJob@409395b9  [] ------
2025-08-03 22:23:36.643|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncProvinceCityDataJob, jobHandler:com.fangcang.grfp.server.job.SyncProvinceCityDataJob@53e82089  [] ------
2025-08-03 22:23:36.682|main| INFO|c.xxl.rpc.remoting.provider.XxlRpcProviderFactory.addService:197|>>>>>>>>>>> xxl-rpc, provider factory add service success. serviceKey = com.xxl.job.core.biz.ExecutorBiz, serviceBean = class com.xxl.job.core.biz.impl.ExecutorBizImpl  [] ------
2025-08-03 22:23:36.736|Thread-10| INFO|com.xxl.rpc.remoting.net.Server.run:66|>>>>>>>>>>> xxl-rpc remoting server start success, nettype = com.xxl.rpc.remoting.net.impl.netty_http.server.NettyHttpServer, port = 9999  [] ------
2025-08-03 22:23:37.768|main| INFO|o.s.b.a.s.s.UserDetailsServiceAutoConfiguration.getOrDeducePassword:86|

Using generated security password: 9dadbab3-d7da-4da3-ba08-fc03ebc2fc94
  [] ------
2025-08-03 22:23:38.003|main| INFO|o.s.security.web.DefaultSecurityFilterChain.<init>:43|Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1e36baca, org.springframework.security.web.context.SecurityContextPersistenceFilter@52b891de, org.springframework.security.web.header.HeaderWriterFilter@12eaa2cd, org.springframework.security.web.csrf.CsrfFilter@526fc044, org.springframework.security.web.authentication.logout.LogoutFilter@c86c486, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@355b53cc, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@51aa2a58, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@3dfa2f64, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@5e0442dd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5f32ab17, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@776a7ec6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@60484429, org.springframework.security.web.session.SessionManagementFilter@2b06681c, org.springframework.security.web.access.ExceptionTranslationFilter@5c34b0f2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1c66cd02]  [] ------
2025-08-03 22:23:38.431|main| INFO|c.f.g.c.remote.tmchub.manager.TmcHubApiManagerTest.logStarted:61|Started TmcHubApiManagerTest in 15.624 seconds (JVM running for 17.06)  [] ------
2025-08-03 22:23:38.438|main| INFO|com.fangcang.grfp.server.GRfpServerApplication.afterStarted:44|==============================GrfpServer 系统启动完成 @v1.0.0-2025072801===========================================  [] ------
2025-08-03 22:23:39.030|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-03 22:23:40.904|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-03 22:23:40.910|main|DEBUG|c.f.grfp.core.mapper.SysConfigMapper.selectOne.debug:137|==>  Preparing: SELECT sys_config_code,sys_config_id,sys_config_value,creator,create_time,modifier,modify_time FROM t_sys_config WHERE (sys_config_code = ?)  [] ------
2025-08-03 22:23:40.925|main|DEBUG|c.f.grfp.core.mapper.SysConfigMapper.selectOne.debug:137|==> Parameters: TMC_HUB_PARTNER_CODE(String)  [] ------
2025-08-03 22:23:41.088|main|DEBUG|c.f.grfp.core.mapper.SysConfigMapper.selectOne.debug:137|<==      Total: 1  [] ------
2025-08-03 22:23:41.426|main|DEBUG|c.f.grfp.core.mapper.SysConfigMapper.selectOne.debug:137|==>  Preparing: SELECT sys_config_code,sys_config_id,sys_config_value,creator,create_time,modifier,modify_time FROM t_sys_config WHERE (sys_config_code = ?)  [] ------
2025-08-03 22:23:41.427|main|DEBUG|c.f.grfp.core.mapper.SysConfigMapper.selectOne.debug:137|==> Parameters: TMC_HUB_SECURITY_KEY(String)  [] ------
2025-08-03 22:23:41.584|main|DEBUG|c.f.grfp.core.mapper.SysConfigMapper.selectOne.debug:137|<==      Total: 1  [] ------
2025-08-03 22:23:43.005|main| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryCountryList, Request Body: {"businessRequest":{"language":"en-US"},"header":{"partnerCode":"TP210000944","requestType":"queryCountryList","signature":"FA9D6331005FEE31E0CF5DE0FEF499B6","timestamp":1754231022506,"version":"1.0.0"}}  [] ------
2025-08-03 22:23:43.044|Thread-10| INFO|com.xxl.rpc.remoting.net.Server.run:74|>>>>>>>>>>> xxl-rpc remoting server stop.  [] ------
2025-08-03 22:23:43.205|xxl-job, executor ExecutorRegistryThread| INFO|com.xxl.job.core.thread.ExecutorRegistryThread.run:87|>>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='mps-server', registryValue='10.8.10.58:9999'}, registryResult:ReturnT [code=200, msg=null, content=null]  [] ------
2025-08-03 22:23:43.205|xxl-job, executor ExecutorRegistryThread| INFO|com.xxl.job.core.thread.ExecutorRegistryThread.run:105|>>>>>>>>>>> xxl-job, executor registry thread destory.  [] ------
2025-08-03 22:23:43.205|SpringContextShutdownHook| INFO|com.xxl.rpc.remoting.net.Server.stop:110|>>>>>>>>>>> xxl-rpc remoting server destroy success.  [] ------
2025-08-03 22:23:43.206|xxl-job, executor JobLogFileCleanThread| INFO|com.xxl.job.core.thread.JobLogFileCleanThread.run:99|>>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.  [] ------
2025-08-03 22:23:43.207|xxl-job, executor TriggerCallbackThread| INFO|com.xxl.job.core.thread.TriggerCallbackThread.run:96|>>>>>>>>>>> xxl-job, executor callback thread destory.  [] ------
2025-08-03 22:23:43.207|Thread-9| INFO|com.xxl.job.core.thread.TriggerCallbackThread.run:126|>>>>>>>>>>> xxl-job, executor retry callback thread destory.  [] ------
2025-08-03 22:23:43.208|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'auditLogExecutor'  [] ------
2025-08-03 22:23:43.208|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-03 22:23:43.208|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'generalExecutor'  [] ------
2025-08-03 22:23:43.216|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-03 22:23:43.390|SpringContextShutdownHook| INFO|com.fangcang.grfp.server.GRfpServerApplication.onDestroy:50|GrfpServer v1.0.0-2025072801 is already [***stopped***]  [] ------
2025-08-03 22:23:43.392|SpringContextShutdownHook| INFO|com.zaxxer.hikari.HikariDataSource.close:350|HikariPool-1 - Shutdown initiated...  [] ------
2025-08-03 22:23:43.401|SpringContextShutdownHook| INFO|com.zaxxer.hikari.HikariDataSource.close:352|HikariPool-1 - Shutdown completed.  [] ------
2025-08-03 22:24:25.343|main| INFO|c.f.g.c.remote.tmchub.manager.TmcHubApiManagerTest.logStarting:55|Starting TmcHubApiManagerTest on LAPTOP-BVT1ALEM with PID 2264 (started by ShamGod in D:\RFP\grfp-project\grfp-server)  [] ------
2025-08-03 22:24:25.346|main| INFO|c.f.g.c.remote.tmchub.manager.TmcHubApiManagerTest.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-03 22:24:27.463|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-03 22:24:27.469|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-03 22:24:27.518|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 24ms. Found 0 Redis repository interfaces.  [] ------
2025-08-03 22:24:29.288|main| WARN|c.b.mybatisplus.core.metadata.TableInfoHelper.initTableFields:327|Can not find table primary key in Class: "com.fangcang.grfp.core.entity.AmadeusMapingEntity".  [] ------
2025-08-03 22:24:31.565|main| WARN|c.b.mybatisplus.core.metadata.TableInfoHelper.initTableFields:327|Can not find table primary key in Class: "com.fangcang.grfp.core.entity.ProjectRecommendStatLogEntity".  [] ------
2025-08-03 22:24:33.356|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-03 22:24:36.125|redisson-netty-2-1| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for 192.168.2.174/192.168.2.174:6379  [] ------
2025-08-03 22:24:37.970|redisson-netty-2-18| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for 192.168.2.174/192.168.2.174:6379  [] ------
2025-08-03 22:24:38.571|main| INFO|c.fangcang.grfp.server.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-03 22:24:39.585|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-03 22:24:41.324|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-03 22:24:41.325|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-03 22:24:41.326|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-03 22:24:41.369|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:InviteHotelConsumerJob, jobHandler:com.fangcang.grfp.server.job.InviteHotelConsumerJob@7e4c0bc7  [] ------
2025-08-03 22:24:41.370|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:InviteHotelGroupConsumerJob, jobHandler:com.fangcang.grfp.server.job.InviteHotelGroupConsumerJob@36570936  [] ------
2025-08-03 22:24:41.371|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:NotifyBidderConsumerJob, jobHandler:com.fangcang.grfp.server.job.NotifyBidderConsumerJob@775fec4  [] ------
2025-08-03 22:24:41.371|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncCountryDataJob, jobHandler:com.fangcang.grfp.server.job.SyncCountryDataJob@c7d173f  [] ------
2025-08-03 22:24:41.371|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncCurrencyExchangeRateJob, jobHandler:com.fangcang.grfp.server.job.SyncCurrencyExchangeRateJob@12f12744  [] ------
2025-08-03 22:24:41.371|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncGroupBrandDataJob, jobHandler:com.fangcang.grfp.server.job.SyncGroupBrandDataJob@1c618295  [] ------
2025-08-03 22:24:41.371|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncHotelIncrementJob, jobHandler:com.fangcang.grfp.server.job.SyncHotelIncrementJob@344acc03  [] ------
2025-08-03 22:24:41.372|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncProvinceCityDataJob, jobHandler:com.fangcang.grfp.server.job.SyncProvinceCityDataJob@50eae15a  [] ------
2025-08-03 22:24:41.446|main| INFO|c.xxl.rpc.remoting.provider.XxlRpcProviderFactory.addService:197|>>>>>>>>>>> xxl-rpc, provider factory add service success. serviceKey = com.xxl.job.core.biz.ExecutorBiz, serviceBean = class com.xxl.job.core.biz.impl.ExecutorBizImpl  [] ------
2025-08-03 22:24:41.520|Thread-9| INFO|com.xxl.rpc.remoting.net.Server.run:66|>>>>>>>>>>> xxl-rpc remoting server start success, nettype = com.xxl.rpc.remoting.net.impl.netty_http.server.NettyHttpServer, port = 9999  [] ------
2025-08-03 22:24:43.080|main| INFO|o.s.b.a.s.s.UserDetailsServiceAutoConfiguration.getOrDeducePassword:86|

Using generated security password: b9dc723d-e47a-4a2c-bb66-28d9804756a7
  [] ------
2025-08-03 22:24:43.453|main| INFO|o.s.security.web.DefaultSecurityFilterChain.<init>:43|Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1ff65426, org.springframework.security.web.context.SecurityContextPersistenceFilter@dcf64d2, org.springframework.security.web.header.HeaderWriterFilter@63574d76, org.springframework.security.web.csrf.CsrfFilter@46f3d0f7, org.springframework.security.web.authentication.logout.LogoutFilter@30ae3c46, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@4be6531a, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@3393aa46, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@3bdf6d63, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@16ac67e3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@20bc3e7f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7aecff85, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6d4bb60f, org.springframework.security.web.session.SessionManagementFilter@586a9fe1, org.springframework.security.web.access.ExceptionTranslationFilter@39329d81, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2c555efb]  [] ------
2025-08-03 22:24:44.199|main| INFO|c.f.g.c.remote.tmchub.manager.TmcHubApiManagerTest.logStarted:61|Started TmcHubApiManagerTest in 19.346 seconds (JVM running for 21.293)  [] ------
2025-08-03 22:24:44.215|main| INFO|com.fangcang.grfp.server.GRfpServerApplication.afterStarted:44|==============================GrfpServer 系统启动完成 @v1.0.0-2025072801===========================================  [] ------
2025-08-03 22:24:51.295|main| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryCountryList, Request Body: {"businessRequest":{"language":"en-US"},"header":{"partnerCode":"TP210000944","requestType":"queryCountryList","signature":"1C4CFE4CEE5E5B42677912514A3F36D3","timestamp":1754231090689,"version":"1.0.0"}}  [] ------
2025-08-03 22:24:51.384|Thread-9| INFO|com.xxl.rpc.remoting.net.Server.run:74|>>>>>>>>>>> xxl-rpc remoting server stop.  [] ------
2025-08-03 22:24:51.594|xxl-job, executor ExecutorRegistryThread| INFO|com.xxl.job.core.thread.ExecutorRegistryThread.run:87|>>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='mps-server', registryValue='10.8.10.58:9999'}, registryResult:ReturnT [code=200, msg=null, content=null]  [] ------
2025-08-03 22:24:51.594|xxl-job, executor ExecutorRegistryThread| INFO|com.xxl.job.core.thread.ExecutorRegistryThread.run:105|>>>>>>>>>>> xxl-job, executor registry thread destory.  [] ------
2025-08-03 22:24:51.594|SpringContextShutdownHook| INFO|com.xxl.rpc.remoting.net.Server.stop:110|>>>>>>>>>>> xxl-rpc remoting server destroy success.  [] ------
2025-08-03 22:24:51.595|xxl-job, executor JobLogFileCleanThread| INFO|com.xxl.job.core.thread.JobLogFileCleanThread.run:99|>>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.  [] ------
2025-08-03 22:24:51.596|xxl-job, executor TriggerCallbackThread| INFO|com.xxl.job.core.thread.TriggerCallbackThread.run:96|>>>>>>>>>>> xxl-job, executor callback thread destory.  [] ------
2025-08-03 22:24:51.596|Thread-8| INFO|com.xxl.job.core.thread.TriggerCallbackThread.run:126|>>>>>>>>>>> xxl-job, executor retry callback thread destory.  [] ------
2025-08-03 22:24:51.597|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'auditLogExecutor'  [] ------
2025-08-03 22:24:51.597|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-03 22:24:51.598|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'generalExecutor'  [] ------
2025-08-03 22:24:51.612|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-03 22:24:51.780|SpringContextShutdownHook| INFO|com.fangcang.grfp.server.GRfpServerApplication.onDestroy:50|GrfpServer v1.0.0-2025072801 is already [***stopped***]  [] ------
2025-08-03 22:25:47.734|main| INFO|c.f.g.c.remote.tmchub.manager.TmcHubApiManagerTest.logStarting:55|Starting TmcHubApiManagerTest on LAPTOP-BVT1ALEM with PID 25692 (started by ShamGod in D:\RFP\grfp-project\grfp-server)  [] ------
2025-08-03 22:25:47.736|main| INFO|c.f.g.c.remote.tmchub.manager.TmcHubApiManagerTest.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-03 22:25:49.636|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-03 22:25:49.642|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-03 22:25:49.690|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 23ms. Found 0 Redis repository interfaces.  [] ------
2025-08-03 22:25:51.395|main| WARN|c.b.mybatisplus.core.metadata.TableInfoHelper.initTableFields:327|Can not find table primary key in Class: "com.fangcang.grfp.core.entity.AmadeusMapingEntity".  [] ------
2025-08-03 22:25:53.959|main| WARN|c.b.mybatisplus.core.metadata.TableInfoHelper.initTableFields:327|Can not find table primary key in Class: "com.fangcang.grfp.core.entity.ProjectRecommendStatLogEntity".  [] ------
2025-08-03 22:25:55.752|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-03 22:25:58.345|redisson-netty-2-23| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for 192.168.2.174/192.168.2.174:6379  [] ------
2025-08-03 22:25:59.629|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for 192.168.2.174/192.168.2.174:6379  [] ------
2025-08-03 22:26:00.103|main| INFO|c.fangcang.grfp.server.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-03 22:26:01.086|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-03 22:26:02.866|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-03 22:26:02.868|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-03 22:26:02.869|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-03 22:26:02.915|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:InviteHotelConsumerJob, jobHandler:com.fangcang.grfp.server.job.InviteHotelConsumerJob@5f8a29ca  [] ------
2025-08-03 22:26:02.915|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:InviteHotelGroupConsumerJob, jobHandler:com.fangcang.grfp.server.job.InviteHotelGroupConsumerJob@4f93a8f6  [] ------
2025-08-03 22:26:02.915|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:NotifyBidderConsumerJob, jobHandler:com.fangcang.grfp.server.job.NotifyBidderConsumerJob@492d38a2  [] ------
2025-08-03 22:26:02.916|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncCountryDataJob, jobHandler:com.fangcang.grfp.server.job.SyncCountryDataJob@3fc2e100  [] ------
2025-08-03 22:26:02.916|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncCurrencyExchangeRateJob, jobHandler:com.fangcang.grfp.server.job.SyncCurrencyExchangeRateJob@31885b4b  [] ------
2025-08-03 22:26:02.916|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncGroupBrandDataJob, jobHandler:com.fangcang.grfp.server.job.SyncGroupBrandDataJob@638e8194  [] ------
2025-08-03 22:26:02.916|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncHotelIncrementJob, jobHandler:com.fangcang.grfp.server.job.SyncHotelIncrementJob@43c64d6f  [] ------
2025-08-03 22:26:02.916|main| INFO|com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:211|>>>>>>>>>>> xxl-job register jobhandler success, name:SyncProvinceCityDataJob, jobHandler:com.fangcang.grfp.server.job.SyncProvinceCityDataJob@6b247ef6  [] ------
2025-08-03 22:26:02.988|main| INFO|c.xxl.rpc.remoting.provider.XxlRpcProviderFactory.addService:197|>>>>>>>>>>> xxl-rpc, provider factory add service success. serviceKey = com.xxl.job.core.biz.ExecutorBiz, serviceBean = class com.xxl.job.core.biz.impl.ExecutorBizImpl  [] ------
2025-08-03 22:26:03.062|Thread-9| INFO|com.xxl.rpc.remoting.net.Server.run:66|>>>>>>>>>>> xxl-rpc remoting server start success, nettype = com.xxl.rpc.remoting.net.impl.netty_http.server.NettyHttpServer, port = 9999  [] ------
2025-08-03 22:26:04.697|main| INFO|o.s.b.a.s.s.UserDetailsServiceAutoConfiguration.getOrDeducePassword:86|

Using generated security password: cfb6ac8c-99c6-4e5f-9c64-08c15c99625f
  [] ------
2025-08-03 22:26:05.079|main| INFO|o.s.security.web.DefaultSecurityFilterChain.<init>:43|Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@74e111a8, org.springframework.security.web.context.SecurityContextPersistenceFilter@530aa75c, org.springframework.security.web.header.HeaderWriterFilter@96271d8, org.springframework.security.web.csrf.CsrfFilter@2125086c, org.springframework.security.web.authentication.logout.LogoutFilter@69c53b72, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@738b93a8, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@1f9a5d3b, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@6e6ec9c, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@20bc3e7f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4a0a93ce, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3509f32d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7aecff85, org.springframework.security.web.session.SessionManagementFilter@7b8d1537, org.springframework.security.web.access.ExceptionTranslationFilter@67e12e28, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6812708d]  [] ------
2025-08-03 22:26:05.790|main| INFO|c.f.g.c.remote.tmchub.manager.TmcHubApiManagerTest.logStarted:61|Started TmcHubApiManagerTest in 18.526 seconds (JVM running for 20.351)  [] ------
2025-08-03 22:26:05.804|main| INFO|com.fangcang.grfp.server.GRfpServerApplication.afterStarted:44|==============================GrfpServer 系统启动完成 @v1.0.0-2025072801===========================================  [] ------
2025-08-03 22:26:29.060|main| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryCountryList, Request Body: {"businessRequest":{"language":"en-US"},"header":{"partnerCode":"TP210000944","requestType":"queryCountryList","signature":"8FBFA81F797B2C7225AD673E5651E9D9","timestamp":1754231172585,"version":"1.0.0"}}  [] ------
2025-08-03 22:26:49.728|Thread-9| INFO|com.xxl.rpc.remoting.net.Server.run:74|>>>>>>>>>>> xxl-rpc remoting server stop.  [] ------
2025-08-03 22:26:49.870|xxl-job, executor ExecutorRegistryThread| INFO|com.xxl.job.core.thread.ExecutorRegistryThread.run:87|>>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='mps-server', registryValue='10.8.10.58:9999'}, registryResult:ReturnT [code=200, msg=null, content=null]  [] ------
2025-08-03 22:26:49.870|xxl-job, executor ExecutorRegistryThread| INFO|com.xxl.job.core.thread.ExecutorRegistryThread.run:105|>>>>>>>>>>> xxl-job, executor registry thread destory.  [] ------
2025-08-03 22:26:49.870|SpringContextShutdownHook| INFO|com.xxl.rpc.remoting.net.Server.stop:110|>>>>>>>>>>> xxl-rpc remoting server destroy success.  [] ------
2025-08-03 22:26:49.871|xxl-job, executor JobLogFileCleanThread| INFO|com.xxl.job.core.thread.JobLogFileCleanThread.run:99|>>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.  [] ------
2025-08-03 22:26:49.871|xxl-job, executor TriggerCallbackThread| INFO|com.xxl.job.core.thread.TriggerCallbackThread.run:96|>>>>>>>>>>> xxl-job, executor callback thread destory.  [] ------
2025-08-03 22:26:49.872|Thread-8| INFO|com.xxl.job.core.thread.TriggerCallbackThread.run:126|>>>>>>>>>>> xxl-job, executor retry callback thread destory.  [] ------
2025-08-03 22:26:49.872|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'auditLogExecutor'  [] ------
2025-08-03 22:26:49.873|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-03 22:26:49.873|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'generalExecutor'  [] ------
2025-08-03 22:26:49.886|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-03 22:26:50.100|SpringContextShutdownHook| INFO|com.fangcang.grfp.server.GRfpServerApplication.onDestroy:50|GrfpServer v1.0.0-2025072801 is already [***stopped***]  [] ------
