package com.fangcang.grfp.core.enums;

/**
 * 酒店集团审核通过状态
 */
public enum HotelGroupApproveStatusEnum {
    NO_NEED(0, "不需要审核"),
    WAITING(1, "等待审核"),
    APPROVED(2, "审核通过"),
    REJECT_APPROVED(3, "审核驳回");

    public Integer key;
    public String value;

    HotelGroupApproveStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (HotelGroupApproveStatusEnum groupApproveEnum : HotelGroupApproveStatusEnum.values()) {
            if (groupApproveEnum.key.equals(key)) {
                value = groupApproveEnum.value;
                break;
            }
        }
        return value;
    }


}
