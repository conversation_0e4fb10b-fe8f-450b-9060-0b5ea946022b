package com.fangcang.grfp.server.job;

import cn.hutool.core.util.StrUtil;
import com.fangcang.grfp.core.bo.RedisService;
import com.fangcang.grfp.core.cached.CachedProjectService;
import com.fangcang.grfp.core.constant.RedisConstant;
import com.fangcang.grfp.core.entity.OrgEntity;
import com.fangcang.grfp.core.entity.ProjectEntity;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.enums.ProjectStateEnum;
import com.fangcang.grfp.core.enums.ProjectTypeEnum;
import com.fangcang.grfp.core.enums.TenderTypeEnum;
import com.fangcang.grfp.core.manager.MailManager;
import com.fangcang.grfp.core.mapper.OrgMapper;
import com.fangcang.grfp.core.mapper.ProjectMapper;
import com.fangcang.grfp.core.oss.OssManager;
import com.fangcang.grfp.core.util.DateUtil;
import com.fangcang.grfp.core.util.ExceptionUtility;
import com.fangcang.grfp.core.vo.AttachmentFileVO;
import com.fangcang.grfp.core.vo.response.common.AttachmentInfoResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.thymeleaf.context.Context;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;


/**
 * 邀请酒店集团队列消费者
 */
@Component
@Slf4j
@JobHandler(value = InviteHotelGroupConsumerJob.TASK_NAME)
public class InviteHotelGroupConsumerJob extends IJobHandler {

    public static final String TASK_NAME = "InviteHotelGroupConsumerJob";

    @Resource
    private RedisService redisService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private OrgMapper orgMapper;
    @Resource
    private MailManager mailManager;
    @Resource
    private JavaMailSender javaMailSender;
    @Resource
    private CachedProjectService cachedProjectService;
    @Autowired
    private OssManager ossManager;


    @Override
    public ReturnT<String> execute(String s) {
        log.info("启动消费邀请酒店集团...");

        Integer notifyConsumberCount = 10;
        String key = RedisConstant.INVITE_HOTEL_GROUP;

        if (redisService.scard(key) == null || redisService.scard(key) <= 0) {
            XxlJobLogger.log("邀请酒店集团队列为空，无需发送邀请");
            return SUCCESS;
        }
        for (int i = 0; i < notifyConsumberCount; i++) {
            // spop = 项目ID_酒店集团机构ID
            String spop = redisService.spop(key);
            if (!StringUtil.isEmpty(spop)) {
                String[] arr = spop.split("_");
                try {
                    String response = inviteHotelGroupConsumer(Long.valueOf(arr[0]), Long.valueOf(arr[1]));
                    if (!"SUCCESS".equals(response)) {
                        log.error("启动消费邀请酒店集团队列失败,项目ID：{} 酒店集团机构ID：{}, response：{}", arr[0], arr[1], response);
                    }
                    Thread.sleep(100);
                } catch (Exception e) {
                    log.error(ExceptionUtility.getDetailedExceptionString(e));
                    log.error("启动消费邀请酒店集团队列异常,项目ID：{} 酒店集团机构ID：{}", arr[0], arr[1], e);
                }
            }
        }
        log.info("结束消费邀请酒店集团..");
        return ReturnT.SUCCESS;
    }

    private String inviteHotelGroupConsumer(Long projectId, Long orgId) {
        String response = "SUCCESS";
        if (projectId == null) {
            response = "项目id为空";
            return response;
        }
        if (orgId == null) {
            response = "酒店集团机构id为空";
            return response;
        }
        ProjectEntity project = projectMapper.selectById(projectId);
        if (project == null) {
            response = "未查询到项目信息";
            return response;
        }
        OrgEntity org = orgMapper.selectById(project.getTenderOrgId());
        if (org == null) {
            response = "未查询到企业机构信息";
            return response;
        }

        OrgEntity hotelGroupOrg = orgMapper.selectById(orgId);
        if (hotelGroupOrg == null) {
            response = "未查询到酒店集团机构信息";
            return response;
        }

        String contactEmail = hotelGroupOrg.getContactEmail();
        if (StrUtil.isBlank(contactEmail)) {
            response = "酒店报价联系人电邮为空";
            return response;
        }

        //发送邮件
        try {
            Context context = new Context();
            context.setVariable("hotelName", hotelGroupOrg.getOrgName());
            context.setVariable("projectName", project.getProjectName());
            context.setVariable("projectTypeValue", ProjectTypeEnum.getTextByKey(project.getProjectType(), LanguageEnum.EN_US.key));
            context.setVariable("projectStateValue", ProjectStateEnum.geTextByKey(project.getProjectState(), LanguageEnum.EN_US.key));
            context.setVariable("enrollStartTime", DateUtil.dateToString(project.getBidStartTime()));
            context.setVariable("enrollEndTime", DateUtil.dateToString(project.getBidEndTime()));
            context.setVariable("diffMinAmount", project.getDiffMinAmount() == null ? "" : project.getDiffMinAmount().toString());
            context.setVariable("diffMaxAmount", project.getDiffMaxAmount() == null ? "" : project.getDiffMaxAmount().toString());
            context.setVariable("introduction", StringUtil.isEmpty(project.getIntroduction()) ? "" : project.getIntroduction());
            context.setVariable("orgName", org.getOrgName());
            context.setVariable("companyProfile", StringUtil.isEmpty(org.getCompanyProfile()) ? "" : org.getCompanyProfile());
            context.setVariable("logoUrl", StringUtil.isEmpty(org.getLogoUrl()) ? "" : ossManager.generateUrlPublic(org.getLogoUrl()));
            context.setVariable("tenderValue", TenderTypeEnum.geTextByKey(project.getTenderType(), LanguageEnum.EN_US.key));

            // 去签约连接
            String goBidUrl = "https://www.agree-ease.com/?isEmailInvited=1";
            context.setVariable("goBidUrl", goBidUrl);
            // 查询项目是否有附件
            List<AttachmentFileVO> projectEmailAttachments = cachedProjectService.queryProjectEmailAttachmentList(project.getProjectId());

            mailManager.sendTemplateMail("Contract Signing Invitation",  contactEmail, "inviteHotelSendMail", context, projectEmailAttachments);
        } catch (Exception e) {
            log.error("发送邮件出现异常,项目id：{},酒店集团机构id：{}", projectId, orgId, e);
        }
        return response;
    }
}
