package com.fangcang.grfp.core.vo.request.hotelgroup;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;

@ApiModel("酒店集团审核列表查询请求")
@Getter
@Setter
public class QueryHotelGroupApprovalRequest extends PageQuery {

    @ApiModelProperty("审核状态：1-待审核，2-审核通过，3-审核驳回")
    private Integer approvalStatus;

    @ApiModelProperty("项目名称")
    private String projectName;


    @ApiModelProperty("报价来源")
    private Integer bidOrgType;

    @ApiModelProperty("招标机构ID")
    private Integer publishOrgId;

    @ApiModelProperty("酒店ID")
    private Long hotelId;

    // 以下字段用于内部业务逻辑，不在API文档中暴露
    @ApiModelProperty(hidden = true)
    private Collection<Long> hotelGroupBrandIdList;
    
    @ApiModelProperty(hidden = true)
    private Integer orgId;
} 