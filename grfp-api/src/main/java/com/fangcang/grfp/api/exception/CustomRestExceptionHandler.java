package com.fangcang.grfp.api.exception;

import com.fangcang.grfp.core.base.AppLogicException;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.cached.CachedTextResourceService;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.util.ControllerLoggingUtility;
import com.fangcang.grfp.core.util.ExceptionUtility;
import com.fangcang.grfp.core.util.GenericAppUtility;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.TypeMismatchException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.naming.AuthenticationException;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.nio.file.AccessDeniedException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * Custom Exception Handler
 * <AUTHOR>
 *
 */
@ControllerAdvice
@Slf4j
public class CustomRestExceptionHandler {


	// ---------------------------------------------------------------------------------------------------- Private Member Variables

	@Autowired
	private CachedTextResourceService cachedTextResourceService;
	
	// ---------------------------------------------------------------------------------------------------- Private Method
	
	private String getMsgText(int languageId, String textResourceCode) {
		return cachedTextResourceService.getMsgValue(languageId, textResourceCode);
	}

	// ---------------------------------------------------------------------------------------------------- Public Method
	
	//400
	@ExceptionHandler(MethodArgumentNotValidException.class)
	public ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException e){
		HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
		int languageId = GenericAppUtility.getRequestHeaderLanguage(httpServletRequest);
		final List<String> errors = new ArrayList<String>();
		for(final FieldError error : e.getBindingResult().getFieldErrors()) {
            String errorMsg = handlerValidateErrorMsg(languageId, error);
            errors.add(errorMsg);
		}
		for(final ObjectError error : e.getBindingResult().getGlobalErrors()) {
			errors.add(getMsgText(languageId, error.getDefaultMessage()));
		}
		Result<Object> response = Result.validateParameterError(errors.toString());

	    ControllerLoggingUtility.logFinish(httpServletRequest, null, response);
	        
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	// 400
	@ExceptionHandler(BindException.class)
    public ResponseEntity<Object> handleBindException(BindException e) {
        final List<String> errors = new ArrayList<>();
        for (final FieldError error : e.getBindingResult().getFieldErrors()) {
            errors.add(error.getField() + ": " + error.getDefaultMessage());
        }
        for (final ObjectError error : e.getBindingResult().getGlobalErrors()) {
            errors.add(error.getObjectName() + ": " + error.getDefaultMessage());
        }

        Result<Object>response = Result.error(errors.toString());

        HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
        ControllerLoggingUtility.logFinish(httpServletRequest, null, response);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }
	
    // 400
    @ExceptionHandler(ServletRequestBindingException.class)
    public ResponseEntity<Object> handleServletRequestBindingException(ServletRequestBindingException e) {
    	Result<Object>response = Result.error(e.getMessage());
    	
    	 HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
         ControllerLoggingUtility.logFinish(httpServletRequest, null, response);
         
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
	
    // 400
    @ExceptionHandler(TypeMismatchException.class)
    public ResponseEntity<Object> handleTypeMismatch(TypeMismatchException e) {
        final String error = e.getValue() + " value for " + e.getPropertyName() + " should be of type " + e.getRequiredType();
        Result<Object>response = Result.error(error);
        
        HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
        ControllerLoggingUtility.logFinish(httpServletRequest, null, response);
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    // 400
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Object> handleConstraintViolation(ConstraintViolationException e) {
        final List<String> errors = new ArrayList<>();
        for (final ConstraintViolation<?> violation : e.getConstraintViolations()) {
            errors.add(violation.getRootBeanClass().getName() + " " + violation.getPropertyPath() + ": " + violation.getMessage());
        }
        Result<Object>response = Result.error(errors.toString());
        
        HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
        ControllerLoggingUtility.logFinish(httpServletRequest, null, response);
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    // 400
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<Object> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        Result<Object>response = Result.error( "Request Parameter Error");
        log.error("controller error {}", ExceptionUtility.getDetailedExceptionString(e));
        
        HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
        ControllerLoggingUtility.logFinish(httpServletRequest, null, response);
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    //Spring security 401
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<Object> handleException(AuthenticationException e) {
        log.error("controller error {}", ExceptionUtility.getDetailedExceptionString(e));
    	HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
		int languageId = GenericAppUtility.getRequestHeaderLanguage(httpServletRequest);
    	Result<Object>response = Result.unauthenticated();
    	response.setMessage(getMsgText(languageId, response.getMessage()));
        
    	ControllerLoggingUtility.logFinish(httpServletRequest, null, response);
    	 
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    // Spring security 403
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<Object> handleException(AccessDeniedException e) {
        log.error("controller error {}", ExceptionUtility.getDetailedExceptionString(e));
    	Result<Object>response = Result.unauthorized();
    	HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
    	int languageId = GenericAppUtility.getRequestHeaderLanguage(httpServletRequest);
    	response.setMessage(getMsgText(languageId, response.getMessage()));
        
        ControllerLoggingUtility.logFinish(httpServletRequest, null, response);
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    // 404
    // spring boot default configuration, 404 not throw NoHandlerFoundException. this method invalidate
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<Object> handleNoHandlerFoundException(NoHandlerFoundException e) {
        String error = "No handler found for " + e.getHttpMethod() + " " + e.getRequestURL();

       Result<Object>response = Result.error(error);
       
       HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
       ControllerLoggingUtility.logFinish(httpServletRequest, null, response);
       
       return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    // 405
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<Object> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e) {
        StringBuilder builder = new StringBuilder();
        builder.append(e.getMethod());
        builder.append(" method is not supported for this request. Supported methods are ");
        Set<HttpMethod> methods = e.getSupportedHttpMethods();
        if (methods != null) {
            methods.forEach(t -> builder.append(t).append(" "));
        }
        
        Result<Object>response = Result.error(builder.toString());
        
        log.error("controller error {}", ExceptionUtility.getDetailedExceptionString(e));
        HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
        ControllerLoggingUtility.logFinish(httpServletRequest, null, response);
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    // 415
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<Object> handleHttpMediaTypeNotSupported(HttpMediaTypeNotSupportedException e) {
        StringBuilder builder = new StringBuilder();
        builder.append(e.getContentType());
        builder.append(" media type is not supported. Supported media types are ");
        e.getSupportedMediaTypes().forEach(t -> builder.append(t).append(" "));
        
        Result<Object>response = Result.error(e.toString());
        log.error("controller error {}", ExceptionUtility.getDetailedExceptionString(e));
        HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
        ControllerLoggingUtility.logFinish(httpServletRequest, null, response);
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    // 500
    @ExceptionHandler(AppLogicException.class)
    public ResponseEntity<Object> handleApplicationException(AppLogicException e) {
    	// Handler 401 code message
    	HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
    	Result<Object> response = null;
        int languageId = GenericAppUtility.getRequestHeaderLanguage(httpServletRequest);
        // 403 no permission or  401 null session
    	if(Result.UNAUTHENTICATED_CODE.equals(e.getErrorCode()) || Result.UNAUTHORIZED_CODE.equals(e.getErrorCode())) {
    		response = new Result<Object>(e.getErrorCode(), getMsgText(languageId, e.getMessage()));
    	} else {
    		response = Result.error(e.getMessage());
    	}
    	
    	// Record log
    	ControllerLoggingUtility.logFinish(httpServletRequest, null, response);
    	
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    // 500
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleException(Exception e) {
    	HttpServletRequest httpServletRequest = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
    	Result<Object>response = Result.error(e.getMessage());
    	// Record log
    	log.error("controller error {}", ExceptionUtility.getDetailedExceptionString(e));
    	ControllerLoggingUtility.logFinish(httpServletRequest, null, response);
    	
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    private String handlerValidateErrorMsg(int languageId, FieldError error){
        String errorMsg = "";
        if(Objects.equals(error.getDefaultMessage(), ErrorCode.VALIDATE_REQUEST_PARAMETER_CANNOT_BE_EMPTY) || error.getDefaultMessage().equals(ErrorCode.VALIDATE_REQUEST_PARAMETER_CANNOT_BE_NULL)
                || Objects.equals(error.getDefaultMessage(), ErrorCode.VALIDATE_REQUEST_ENUM_PARAMETER_ERROR)
        ){
            errorMsg =(String.format(getMsgText(languageId, error.getDefaultMessage()), error.getField()));
        } else if((error.getDefaultMessage().equals(ErrorCode.VALIDATE_REQUEST_PARAMETER_LENGTH_MUST_BE_BETWEEN_X_AND_Y) ||
                error.getDefaultMessage().equals(ErrorCode.VALIDATE_REQUEST_PARAMETER_RANGE_MUST_BE_BETWEEN_X_AND_Y))
                && error.getArguments() != null && error.getArguments().length == 3){
            // 大小值相等
            if(Objects.equals(error.getArguments()[1].toString(), error.getArguments()[2].toString())){
                if(error.getDefaultMessage().equals(ErrorCode.VALIDATE_REQUEST_PARAMETER_LENGTH_MUST_BE_BETWEEN_X_AND_Y)){
                     errorMsg = String.format(getMsgText(languageId, ErrorCode.VALIDATE_REQUEST_PARAMETER_LENGTH_MUST_BE_X), error.getField(), error.getArguments()[1]);
                } else  if(error.getDefaultMessage().equals(ErrorCode.VALIDATE_REQUEST_PARAMETER_RANGE_MUST_BE_BETWEEN_X_AND_Y)){
                     errorMsg = String.format(getMsgText(languageId, ErrorCode.VALIDATE_REQUEST_PARAMETER_MUST_BE_BETWEEN_X), error.getField(), error.getArguments()[1]);
                }  else {
                    errorMsg = String.format(getMsgText(languageId, error.getDefaultMessage()), error.getField(), error.getArguments()[2], error.getArguments()[1]);
                }
            } else {
                errorMsg = String.format(getMsgText(languageId, error.getDefaultMessage()), error.getField(), error.getArguments()[2], error.getArguments()[1]);
            }
        } else if((error.getDefaultMessage().equals(ErrorCode.VALIDATE_REQUEST_PARAMETER_MUST_BE_GREATER_THAN_OR_EQUAL_TO_X) ||
                error.getDefaultMessage().equals(ErrorCode.VALIDATE_REQUEST_PARAMETER_MUST_BE_LESS_THAN_OR_EQUAL_TO_X) ||
                error.getDefaultMessage().equals(ErrorCode.VALIDATE_REQUEST_PARAMETER_MUST_MATCH_PATTERN))
                && error.getArguments() != null && error.getArguments().length == 2
        ) {
            errorMsg = String.format(getMsgText(languageId, error.getDefaultMessage()), error.getField(), error.getArguments()[1]);
        } else {
            errorMsg = getMsgText(languageId, error.getDefaultMessage());
        }
        return errorMsg;
    }

    
}
