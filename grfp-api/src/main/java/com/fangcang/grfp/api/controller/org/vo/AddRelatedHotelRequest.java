package com.fangcang.grfp.api.controller.org.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.DestinationHotelVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("新增机构关联酒店请求")
@Getter
@Setter
public class AddRelatedHotelRequest extends BaseVO {

    @ApiModelProperty("酒店机构Id")
    @NotNull
    private Integer hotelOrgId;

    @ApiModelProperty(value = "酒店信息 (新增酒店机构传参)")
    @NotNull
    private DestinationHotelVO hotelInfo;
}
