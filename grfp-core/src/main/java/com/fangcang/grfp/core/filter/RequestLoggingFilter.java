package com.fangcang.grfp.core.filter;

import com.fangcang.grfp.core.util.HttpServletRequestUtility;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.filter.AbstractRequestLoggingFilter;

import javax.servlet.http.HttpServletRequest;

/**
 * 请求日志过滤器
 */
@Slf4j
public class RequestLoggingFilter extends AbstractRequestLoggingFilter {

    public RequestLoggingFilter() {
        this.setIncludeClientInfo(false);
        this.setIncludeHeaders(false);
        this.setIncludeQueryString(true);
    }

    @Override
    protected void beforeRequest(HttpServletRequest request, String message) {
        log.info(message);
    }

    @Override
    protected void afterRequest(HttpServletRequest request, String message) {
        log.info(message);
    }

    @Override
    protected String createMessage(HttpServletRequest request, String prefix, String suffix) {
        prefix += "client=" + HttpServletRequestUtility.extractRequestIpAddress(request) + ", ";
        return super.createMessage(request, prefix, suffix);
    }
}
