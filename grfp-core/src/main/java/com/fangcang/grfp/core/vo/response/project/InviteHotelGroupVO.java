package com.fangcang.grfp.core.vo.response.project;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.jackson.OssPublicJsonSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("分页查询项目意向酒店集团")
public class InviteHotelGroupVO extends BaseVO {

    @ApiModelProperty("酒店集团ID")
    private Integer hotelGroupOrgId;
    @ApiModelProperty("酒店集团名称")
    private String orgName;
    @ApiModelProperty("酒店集团logo")
    @JsonSerialize(using = OssPublicJsonSerializer.class)
    private String logoUrl;

    @ApiModelProperty("联系人")
    private String contactName;
    @ApiModelProperty("联系人手机")
    private String contactMobile;
    @ApiModelProperty("联系人邮箱")
    private String contactEmail;

    @ApiModelProperty("邀请状态 1=已加入，0=未加入")
    private Integer inviteState;
    @ApiModelProperty("发送邮件状态 0=未发送，1=已发送")
    private Integer sendMailStatus;
    @ApiModelProperty("是否品牌限制 1=是，0=否")
    private Integer isBrandLimit;
    @ApiModelProperty("项目意向酒店集团ID")
    private Long projectIntentHotelGroupId;
}
