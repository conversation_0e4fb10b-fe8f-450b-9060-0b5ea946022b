package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 项目酒店白名单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_hotel_white")
public class ProjectHotelWhiteEntity extends BaseVO {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 项目 id
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店 id
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 白名单类型: 1-报价免控白名单 2-周末节假日免履约监控白名单 3-仅每周一履约监控白名单
     */
    @TableField("hotel_white_type")
    private Integer hotelWhiteType;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
