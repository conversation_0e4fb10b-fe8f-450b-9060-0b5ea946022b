package com.fangcang.grfp.core.vo;

import java.io.Serializable;
import java.math.BigDecimal;

public class RecommendFrequencySameLevelInfo implements Serializable {

    private static final long serialVersionUID = 2802719435015814525L;

    // 酒店名称
    private String hotelName;

    // 距离
    private BigDecimal distance;

    // 间夜数
    private Integer roomNightCount;

    // 均价
    private BigDecimal avgPrice;

    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    public BigDecimal getDistance() {
        return distance;
    }

    public void setDistance(BigDecimal distance) {
        this.distance = distance;
    }

    public Integer getRoomNightCount() {
        return roomNightCount;
    }

    public void setRoomNightCount(Integer roomNightCount) {
        this.roomNightCount = roomNightCount;
    }

    public BigDecimal getAvgPrice() {
        return avgPrice;
    }

    public void setAvgPrice(BigDecimal avgPrice) {
        this.avgPrice = avgPrice;
    }
}
