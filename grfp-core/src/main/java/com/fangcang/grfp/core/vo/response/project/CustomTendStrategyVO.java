package com.fangcang.grfp.core.vo.response.project;

import com.fangcang.grfp.core.base.BaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(description = "自定义采购策略响应")
public class CustomTendStrategyVO extends BaseVO {

    private static final long serialVersionUID = 2824316955809951355L;

    @ApiModelProperty(value = "自定义采购策略ID")
    private Long id;

    @ApiModelProperty(value = "自定义采购策略名称")
    private String strategyName;

    @ApiModelProperty(value = "策略类型: 1-是或否 2-文本 3-多选 4-单选")
    private Integer strategyType;

    @ApiModelProperty(value = "排序")
    private Integer displayOrder;

    @ApiModelProperty(value = "是否支持策略: 1-是 0-否")
    private Integer supportStrategyName;

    @ApiModelProperty(value = "是否启用权重: 1-是 0-否")
    private Integer whtStrategyNameState;

    @ApiModelProperty(value = "权重分值")
    private Integer whtStrategyName;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "选项内容, 策略类型为回复选项时有值")
    private List<CustomStrategyOptionVO> options;
}
