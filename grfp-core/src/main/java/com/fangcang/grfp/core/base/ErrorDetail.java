package com.fangcang.grfp.core.base;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ErrorDetail extends BaseVO {

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误信息（可选，可由错误码转换）
     */
    private String errorMsg;

    /**
     * 参数数组
     */
    private String[] params;

    public ErrorDetail(String errorCode) {
        this.errorCode = errorCode;
    }

    public ErrorDetail(String errorCode, String[] params) {
        this.errorCode = errorCode;
        this.params = params;
    }

}
