package com.fangcang.grfp.core.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 币种汇率表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_currency_exchange_rate")
public class CurrencyExchangeRateEntity extends BaseVO {


    /**
     * 币种编码
     */
    @TableId(value = "currency_code", type = IdType.ASSIGN_ID)
    private String currencyCode;

    /**
     * 币种名称
     */
    @TableField("currency_name")
    private String currencyName;

    /**
     * 美元对币种汇率
     */
    @TableField("exchange_rate")
    private BigDecimal exchangeRate;

    /**
     * 币种对美元汇率
     */
    @TableField("inverse_exchange_rate")
    private BigDecimal inverseExchangeRate;

    /**
     * 显示顺序
     */
    @TableField("display_order")
    private Integer displayOrder;

    /**
     * 是否自动同步
     */
    @TableField("is_auto_sync")
    private Integer isAutoSync;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
