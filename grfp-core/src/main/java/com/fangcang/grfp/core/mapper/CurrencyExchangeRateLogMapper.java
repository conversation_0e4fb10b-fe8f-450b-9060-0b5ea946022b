package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fangcang.grfp.core.entity.CurrencyExchangeRateLogEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.HotelLogEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 币种汇率日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-31
 */
public interface CurrencyExchangeRateLogMapper extends BaseMapper<CurrencyExchangeRateLogEntity> {

    /**
     * 根据currencyCode 查询每个币种最新的日志
     */
    List<CurrencyExchangeRateLogEntity> selectLatestByCurrencyCodes(@Param("currencyCodes") Collection<String> currencyCodes);


    /**
     * 批量插入
     */
    void batchInsert(@Param("list") List<CurrencyExchangeRateLogEntity> list);

    IPage<CurrencyExchangeRateLogEntity> queryPageList(IPage<CurrencyExchangeRateLogEntity> page, @Param("currencyCode") String currencyCode);


}
