package com.fangcang.grfp.core.oss;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;
import java.util.List;

/**
 * OSS Manager
 * key 统一用 uuid, 避免重复覆盖
 */
public class OssManager {

    private final IOssClient ossClient;

    private final OssProperties ossProperties;

    public OssManager(IOssClient ossClient, OssProperties properties) {
        this.ossClient = ossClient;
        this.ossProperties = properties;
    }

    /**
     * 从文件完整路径取出 key
     */
    public String getKeyFromUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }

        if (url.contains("//")) {
            // 去掉 http 协议
            url = StringUtils.substringAfter(url, "//");
            // 去掉域名
            url = StringUtils.substringAfter(url, "/");
        }
        if (url.contains("?")) {
            // 去掉参数
            url = StringUtils.substringBefore(url, "?");
        }

        return url;
    }

    /**
     * 上传文件. 临时桶
     */
    public String putObjectTemp(String directory, byte[] object, String contentType, String originalFileName) {
        String key = this.getKey(directory, originalFileName);
        ossClient.putObject(ossProperties.getBucketTemp(), key, object, contentType, originalFileName);
        return key;
    }

    /**
     * 上传文件. 临时桶
     */
    public String putObjectTemp(String directory, InputStream object, String contentType, String originalFileName) {
        String key = this.getKey(directory, originalFileName);
        ossClient.putObject(ossProperties.getBucketTemp(), key, object, contentType, originalFileName);
        return key;
    }

    /**
     * 上传文件. 公开桶
     */
    public String putObjectPublic(String directory, byte[] object, String contentType, String originalFileName) {
        String key = this.getKey(directory, originalFileName);
        ossClient.putObject(ossProperties.getBucketPublic(), key, object, contentType, originalFileName);
        return key;
    }

    /**
     * 上传文件. 公开桶
     */
    public String putObjectPublic(String directory, InputStream object, String contentType, String originalFileName) {
        String key = this.getKey(directory, originalFileName);
        ossClient.putObject(ossProperties.getBucketPublic(), key, object, contentType, originalFileName);
        return key;
    }

    /**
     * 下载文件. 公开桶
     */
    public InputStream getObjectPublic(String key) {
        return ossClient.getObject(ossProperties.getBucketPublic(), key);
    }

    /**
     * 下周文件, 临时桶
     */
    public InputStream getObjectTemp(String key) {
        return ossClient.getObject(ossProperties.getBucketTemp(), key);
    }

    /**
     * 生成访问 url. 公开桶
     */
    public String generateUrlPublic(String key) {
        return ossClient.generatePublicUrl(ossProperties.getBucketPublicDomain(), key);
    }

    /**
     * 生成访问 url. 临时桶
     */
    public String generateUrlTemp(String key) {
        return ossClient.generatePublicUrl(ossProperties.getBucketTempDomain(), key);
    }

    /**
     * 从临时桶拷贝到公有桶
     */
    public void tempToPublic(String tempKey) {
        ossClient.copyObject(ossProperties.getBucketTemp(), tempKey, ossProperties.getBucketPublic(), tempKey);
    }


    /**
     * 生成uid命名的文件key
     */
    private String getKey(String directory, String originalFileName) {
        String key = directory + "/" + IdUtil.objectId();
        if (StringUtils.contains(originalFileName, ".")) {
            key = key + "." + StringUtils.substringAfterLast(originalFileName, ".");
        }
        return key;
    }

    public boolean isTempUrl(String url) {
        return StringUtils.contains(url, ossProperties.getBucketTempDomain());
    }

    public String replaceTempUrlToPublic(List<String> fileKeys, String tempUrl){
        if(CollectionUtils.isNotEmpty(fileKeys) && tempUrl.contains(ossProperties.getBucketTempDomain())){
            // 拷贝附件tmp fileKey到public桶
            for(String fileKey : fileKeys) {
                this.tempToPublic(fileKey);
            }
            // 替换tmp file url
            return RegExUtils.replaceAll(tempUrl, ossProperties.getBucketTempDomain(), ossProperties.getBucketPublicDomain());
        }

        return tempUrl;
    }

    public String replaceTempUrlToPublicUrl(String tempUrl){
        if(StringUtils.isEmpty(tempUrl)){
            return tempUrl;
        }
        if(tempUrl.contains(ossProperties.getBucketTempDomain())){
            // 拷贝附件tmp fileKey到public桶
            String tempKey = this.getKeyFromUrl(tempUrl);
            this.tempToPublic(tempKey);
            // 替换tmp file url
            return RegExUtils.replaceAll(tempUrl, ossProperties.getBucketTempDomain(), ossProperties.getBucketPublicDomain());
        }
        return tempUrl;
    }

}
