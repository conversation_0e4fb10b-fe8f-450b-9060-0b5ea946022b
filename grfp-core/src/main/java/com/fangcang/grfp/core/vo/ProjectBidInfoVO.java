package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("项目报价信息")
@Getter
@Setter
public class ProjectBidInfoVO extends BaseVO {

    @ApiModelProperty("项目ID")
    private Integer projectId;

    @ApiModelProperty("是否去年报价  1:是，0:")
    private Integer isBidLastYear;

    @ApiModelProperty("去年绑定项目ID")
    private Integer lastYearProjectId;

    @ApiModelProperty("酒店报价是否需要校验 true:是, false: 不需要")
    private Boolean bidNeedValidateBid;
}
