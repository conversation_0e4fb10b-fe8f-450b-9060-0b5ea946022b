package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.HotelDataEntity;
import com.fangcang.grfp.core.vo.response.hotel.HotelDataVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 酒店数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface HotelDataMapper extends BaseMapper<HotelDataEntity> {

    /**
     * 批量插入
     */
    void batchUpsert(@Param("hotelDataEntities") Collection<HotelDataEntity> hotelDataEntities);

    /**
     * 查询酒店房型信息列表
     */
    HotelDataVO selectByHotelId(@Param("language") int language, @Param("hotelId") Long hotelId);

    /**
     * 根据酒店 id 批量查询
     */
    default List<HotelDataEntity> selectByHotelIds(Collection<Long> hotelIds) {
        if (CollectionUtils.isEmpty(hotelIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HotelDataEntity> queryWrapper = Wrappers.lambdaQuery(HotelDataEntity.class);
        queryWrapper.in(HotelDataEntity::getHotelId, hotelIds);
        return selectList(queryWrapper);
    }

}
