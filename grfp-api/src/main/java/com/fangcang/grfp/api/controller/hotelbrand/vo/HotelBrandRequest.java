package com.fangcang.grfp.api.controller.hotelbrand.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("酒店品牌查询")
@Getter
@Setter
public class HotelBrandRequest extends BaseVO {

    @ApiModelProperty(value = "酒店集团ID")
    private Long hotelGroupId;

    @ApiModelProperty(value = "酒店品牌名称")
    private String hotelBrandName;

    @ApiModelProperty(value = "返回数量 默认10")
    private Integer limitCount = 10;

}
