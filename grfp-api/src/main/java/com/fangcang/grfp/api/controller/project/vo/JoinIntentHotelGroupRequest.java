package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel("酒店集团意向操作请求")
public class JoinIntentHotelGroupRequest extends BaseVO {

    @NotNull
    @ApiModelProperty(value = "项目ID", required = true)
    private Long projectId;

    @NotNull
    @ApiModelProperty(value = "酒店集团机构ID", required = true)
    private Long hotelGroupOrgId;
}
