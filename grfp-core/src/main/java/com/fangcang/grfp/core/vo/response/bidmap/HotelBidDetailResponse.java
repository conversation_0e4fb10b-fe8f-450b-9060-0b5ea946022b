package com.fangcang.grfp.core.vo.response.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.Image;
import com.fangcang.grfp.core.vo.*;
import com.fangcang.grfp.core.vo.response.hotel.RoomNameInfoVO;
import com.fangcang.grfp.core.vo.response.project.ProjectBidCustomTendStrategyVO;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelTendStrategyVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@ApiModel("酒店报价详情响应")
@Getter
@Setter
public class HotelBidDetailResponse extends BaseVO {

    @ApiModelProperty("项目ID")
    private Integer projectId;

    @ApiModelProperty("酒店ID")
    private Long hotelId;

    @ApiModelProperty("币种")
    private String currencyCode;

    @ApiModelProperty("酒店信息")
    private HotelInfoVO hotelInfo;

    @ApiModelProperty("酒店主图")
    private String mainPicUrl;

    @ApiModelProperty("最近poi名称")
    private String poiName;

    @ApiModelProperty("最近poi名称 KM")
    private String poiDistance;

    @ApiModelProperty("项目酒店报价信息")
    private ProjectIntentHotelBidVO projectIntentHotelBid;

    @ApiModelProperty("项目报价策略")
    private ProjectHotelTendStrategyVO projectHotelTendStrategy;

    @ApiModelProperty("项目酒店报价策略")
    private BidProjectStrategyVO bidProjectStrategy;

    @ApiModelProperty("项目自定义报价策略")
    private List<ProjectBidCustomTendStrategyVO> projectCustomTendStrategyList;

    @ApiModelProperty("项目酒店自定义报价策略")
    private List<BidCustomStrategyVO> bidCustomStrategyList;

    @ApiModelProperty("可用日期列表")
    private List<BidApplicableDayVO> bidApplicableDayList;

    @ApiModelProperty("不可用日期列表")
    private List<BidUnApplicableDayVO> bidUnApplicableDayList;

    @ApiModelProperty("税费设置")
    private BidHotelTaxSettingsVO bidHotelTaxSettings;

    @ApiModelProperty("房档价格信息")
    private List<BidHotelPriceLevelInfoVO> bidHotelPriceLevelInfoVOList;

    @ApiModelProperty("操作日志")
    private List<BidOperateLogVO> bidOperateLogList;

    @ApiModelProperty("酒店房型列表")
    private List<RoomNameInfoVO> roomNameList;

    @ApiModelProperty("酒店图片列表")
    private List<Image> hotelImageList;

    @ApiModelProperty("项目酒店报价备注信息")
    private List<ProjectHotelRemarkResponse> projectHotelRemarkResponseList;

    @ApiModelProperty("是否Lanyon导入")
    private Integer isLanyonImport;

    @ApiModelProperty("Lanyon导入数据数量")
    private Integer lanyonImportDataCount;

    @ApiModelProperty("展示币种")
    private String viewCurrencyCode;

    @ApiModelProperty("报价币种对展示币种汇率")
    private BigDecimal viewCurrencyExchangeRate;
}
