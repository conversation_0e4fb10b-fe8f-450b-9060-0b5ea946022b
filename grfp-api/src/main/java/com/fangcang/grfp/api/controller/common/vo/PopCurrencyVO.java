package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.CurrencyNameVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel(description = "常用币种响应")
public class PopCurrencyVO extends BaseVO {

    @ApiModelProperty(value = "常用币种列表")
    private List<CurrencyNameVO> popCurrencyList;

}
