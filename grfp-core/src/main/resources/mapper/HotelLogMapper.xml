<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HotelLogMapper">
    <select id="selectLatestByHotelIds" resultType="com.fangcang.grfp.core.entity.HotelLogEntity">
        SELECT t1.*
        FROM t_hotel_log t1
            JOIN (
        SELECT hotel_id, MAX(hotel_log_id) AS max_id
        FROM t_hotel_log
        WHERE hotel_id IN
        <foreach collection="hotelIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY hotel_id
        ) t2 ON t1.hotel_id = t2.hotel_id AND t1.hotel_log_id = t2.max_id
    </select>

    <insert id="batchInsert">
        insert into t_hotel_log(hotel_id, before_kv_json, after_kv_json, creator)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.hotelId}, #{item.beforeKvJson}, #{item.afterKvJson}, #{item.creator})
        </foreach>
    </insert>
</mapper>
