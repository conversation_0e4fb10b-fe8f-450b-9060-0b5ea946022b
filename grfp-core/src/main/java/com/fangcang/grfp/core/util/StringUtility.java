package com.fangcang.grfp.core.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.text.NumberFormat;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;


public class StringUtility {
	
	// ---------------------------------------------------------------------------------------------------- Public Static Member Variables
	
	public static final String HIVEN = "-";
	
	public static final String COMMA = ",";
	
	public static final String FULL_STOP = ".";
	
	public static final String SEMICOLON = ";";
	
	public static final String SPACE = " ";
	
	// ---------------------------------------------------------------------------------------------------- Private Static Member Variables
	
	protected static SecureRandom secureRandom = new SecureRandom();
	
	// ---------------------------------------------------------------------------------------------------- Public Static Method
	
	public static boolean notNullAndNotEmpty(String s) {
		return ((s != null) && (!s.equals("")));
	}
	
	public static String parseNullAndNotEmpty(String s) {
		return parseNullAndNotEmpty(s, "");
	}
	
	public static String parseNullAndNotEmpty(String s, String stringForNullOrEmpty) {
		if (notNullAndNotEmpty(s) == false) {
			return stringForNullOrEmpty;
		} else {
			return s;
		}
	}
	
	// ----------------------------------------------------------------------------------------------------
	
	public static String constructQuestionMarksSeparatedByComma(int noOfQuestionMark) {
		StringBuffer sb = new StringBuffer();
		
		if (noOfQuestionMark > 1) {
			for (int i=0; i<noOfQuestionMark-1; i++) {
				sb.append("?, ");
			}
			
			sb.append("?");
		} else if (noOfQuestionMark == 1) {
			sb.append("?");
		} else {
			sb.append("");
		}
		
		return sb.toString();
	}
	
	// ----------------------------------------------------------------------------------------------------
	
	public static String generateSimplePassword(int minLength, int maxLength) {
        Random r = new Random(); 
        MessageDigest md = null; 
        int length = 0;
        
        if ((minLength > 0) && (maxLength < 21)) {
        	while (length < minLength) {
        		length = r.nextInt(maxLength + 1);
        	}
        } else {
        	while (length < 6) {
        		length = r.nextInt(8 + 1);
        	}
        }        
 
        try { 
            md = MessageDigest.getInstance("MD5"); 
        } catch ( NoSuchAlgorithmException e ) { 
            System.out.println( "Unsupported Algorithm!" ); 
            return null; 
        } 
 
        byte[] entropy = new byte[1024]; 
        r.nextBytes(entropy); 
        md.update( entropy , 0, 1024 ); 
 
        return new BigInteger(1, md.digest()).toString(16).substring(0, length); 
	}
	
	public static String maskPassword(String password) {
		StringBuffer maskedPassword = new StringBuffer();
		
		if (password != null) {
			if (password.length() <= 4) {
				maskedPassword.append("***");
			} else {
				for (int i=0; i<password.length(); i++) {
					if ((i==0) || (i==(password.length()-1))) {
						maskedPassword.append(password.charAt(i));
					} else {
						maskedPassword.append("*");
					}
				}
			}
		}
		
		return maskedPassword.toString();
	}
	
	// ----------------------------------------------------------------------------------------------------
	
	public static String generateNumericPassword(int minLength, int maxLength) {
		String result = null;
        Random r = new Random();
		
        if ((minLength >= 1) && (minLength <= maxLength)) {
        	int length = 0;
        	while (length < minLength) {
        		length = r.nextInt(maxLength + 1);
        	}
        	
        	result = "";
        	for (int i=0; i<length; i++) {
        		int n = r.nextInt(10);
        		result = result + String.valueOf(n);
        	}
        }
        
        return result;
	}
	
	// --------------------------------------------------------------------------------------------------
	
	public static String convertStringToHex(String str) {
		if (str == null) {
			return null;
		} else {
			char[] chars = str.toCharArray();
			StringBuffer hex = new StringBuffer();
			
			for (int i=0; i<chars.length; i++) {
				hex.append(Integer.toHexString((int)chars[i]));
			}
			
			return hex.toString();
		}
	}
	
	public static String convertHexToString(String hex){
		StringBuilder sb = new StringBuilder();
		StringBuilder temp = new StringBuilder();
		
		if (hex == null) {
			return null;
		} else {
			for (int i=0; i<hex.length()-1; i+=2 ){
				// grab the hex in pairs
				String output = hex.substring(i, (i + 2));
				
				// convert hex to decimal
				int decimal = Integer.parseInt(output, 16);
				
				// convert the decimal to character
				sb.append((char)decimal);
				temp.append(decimal);
			}
			
			return sb.toString();
		}
	}
	  
	// --------------------------------------------------------------------------------------------------
	
	public static NumberFormat getNumberFormat(int decimalPlace, boolean thousandSeparator) {
		NumberFormat nf = NumberFormat.getInstance();
		
		nf.setMinimumFractionDigits(decimalPlace);
		nf.setMaximumFractionDigits(decimalPlace);
		nf.setGroupingUsed(thousandSeparator);
		
		return nf;		
	}
	
	public static NumberFormat getNumberFormat(int minDecimalPlace, int maxDecimalPlace, boolean thousandSeparator) {
		NumberFormat nf = NumberFormat.getInstance();
		
		nf.setMinimumFractionDigits(minDecimalPlace);
		nf.setMaximumFractionDigits(maxDecimalPlace);
		nf.setGroupingUsed(thousandSeparator);
		
		return nf;		
	}
	
	// --------------------------------------------------------------------------------------------------
	
	/**
	 * Return true for the following cases: 
	 * 1) condition is null.
	 * 2) text is not null and contains condition
	 */
	public static boolean containsIgnoreCase(String text, String condition) {
		boolean result;
		
		if (condition == null) {
			result = true;
		} else {
			if (text == null) {
				result = false;
			} else {
				result = text.toUpperCase().contains(condition.toUpperCase());
			}
		}
		
		return result;
	}
	
	/**
	 * Return true for the following cases: 
	 * 1) condition is null.
	 * 2) Either one non-null value in textList contains condition
	 */
	public static boolean containsIgnoreCaseForEitherOne(List<String> textList, String condition) {
		boolean result;
		
		if (condition == null) {
			result = true;
		} else {
			result = false;
			
			for (String text : textList) {
				if ((text != null) && (text.toUpperCase().contains(condition.toUpperCase()))) {
					result = true;
					break;
				}
			}
		}
		
		return result;
	}
	
	// --------------------------------------------------------------------------------------------------
	
	public static String generateHashCode() {
		return generateHashCode(130, 32);
	}

	public static String generateHashCode(int numBits, int radix) {
		return new BigInteger(numBits, secureRandom).toString(radix);
	}
	
	// ---------------------------------------------------------------------------------------------------
	
	public static String escapeHTML(String s) {
		if (notNullAndNotEmpty(s) == false) {
			return s;
		}
	    StringBuilder out = new StringBuilder(Math.max(16, s.length()));
	    for (int i = 0; i < s.length(); i++) {
	        char c = s.charAt(i);
	        if (c > 127 || c == '\'' ||c == '"' || c == '<' || c == '>' || c == '&') {
	            out.append("&#");
	            out.append((int) c);
	            out.append(';');
	        } else {
	            out.append(c);
	        }
	    }
	    return out.toString();
	}

	// ---------------------------------------------------------------------------------------------------
	
	public static String md5(String input) {
        try { 
        	MessageDigest md = MessageDigest.getInstance("MD5");
        	
        	byte[] inputBytes = input.getBytes();
            byte[] messageDigest = md.digest(inputBytes);
            
            BigInteger number = new BigInteger(1, messageDigest);
            String result = number.toString(16);
            
            // Now we need to zero pad it if you actually want the full 32 chars.
            while (result.length() < 32) {
            	result = "0" + result;
            }
            
            return result;
            
        } catch (Exception ex) {
            ex.printStackTrace(); 
            return null; 
        } 
	}
	
	// ---------------------------------------------------------------------------------------------------
	
	protected static int getLevenshteinDistance(String s, String t) {
		return StringUtils.getLevenshteinDistance(s, t);
	}
	
	protected static int computeSimilarityScore(String s, String t) {
		int matchScore = 0;

		int distance = getLevenshteinDistance(s, t);

		if (distance < 0) {
			return matchScore;
		}

		if (distance == 0) {
			matchScore = 100;
		} else {

			String longStr = (s.length() >= t.length()) ? s : t;
			Double score = ((double) (longStr.length() - distance) / longStr.length()) * 100;
			matchScore = score.intValue();
		}

		return matchScore;
	}
	
	/**
	 * @return [0-100] similarity scores. 100 means very similar.
	 */
	public static int computeSimilarityScore(String s1, String s2, boolean ignoreCase, boolean ignoreSymbols) {
		String s;
		String t;
		
		if (s1 != null) {
			s = s1;
		} else {
			s = "";
		}
		if (s2 != null) {
			t = s2;
		} else {
			t = "";
		}
		
		if (ignoreCase == true) {
			if (ignoreSymbols == true) {
				return StringUtility.computeSimilarityScore(
						s.replaceAll("(?<=[ (])@|@(?=[.,;?:) !])", "").toUpperCase(),
						t.replaceAll("(?<=[ (])@|@(?=[.,;?:) !])", "").toUpperCase()
						);
			} else {
				return StringUtility.computeSimilarityScore(
						s.toUpperCase(),
						t.toUpperCase()
						);
			}
		} else {
			if (ignoreSymbols == true) {
				return StringUtility.computeSimilarityScore(
						s.replaceAll("(?<=[ (])@|@(?=[.,;?:) !])", ""),
						t.replaceAll("(?<=[ (])@|@(?=[.,;?:) !])", "")
						);
			} else {
				return StringUtility.computeSimilarityScore(
						s,
						t
						);
			}
		}
	}
	
	
	/**
	 * Get current random
	 */
	public static ThreadLocalRandom getRandom() {
		return ThreadLocalRandom.current();
	}
	
	/**
	 * Generate random string  eg： randomString("0123456789", 6) = "011345"
	 */
	public static String randomString(String baseString, int length) {
		if (StringUtils.isEmpty(baseString)) {
			return StringUtils.EMPTY;
		}
		final StringBuilder sb = new StringBuilder(length);

		if (length < 1) {
			length = 1;
		}
		int baseLength = baseString.length();
		for (int i = 0; i < length; i++) {
			int number = getRandom().nextInt(baseLength);
			sb.append(baseString.charAt(number));
		}
		return sb.toString();
	}
	
	// ---------------------------------------------------------------------------------------------------
	
	public static String truncate(String text, int length) {
		return StringUtils.left(text, length);
	}
	
	// ---------------------------------------------------------------------------------------------------
	
	/**
	 * Do not throw any exception 
	 */
	public static String silentFormat(String format, Object... args) {
		String result;
		
		try {
			result = String.format(format, args);
		} catch (Exception ex) {
			ex.printStackTrace();
			result = format;
		}
		
		return result;
	}
	
	// ---------------------------------------------------------------------------------------------------
	
//	public static void main(String[] args) {
//		System.out.println("Score: " + computeSimilarityScore("<EMAIL>", "<EMAIL>", true, true));
//	}
	
}