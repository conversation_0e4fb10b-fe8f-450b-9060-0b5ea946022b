package com.fangcang.grfp.core.vo;

import java.util.ArrayList;
import java.util.List;

public class GeneratePoiStatVO {

    // 五星级 19
    private List<Long> fiveStarHotelIds;
    // 豪华型 (29)
    private List<Long> quisaFiveStarHotelIds;
    //  四星级/高档型百分比 (39,49)
    private List<Long> fourAndQuisaFourStarHotelIds;
    //  三星/舒适型百分比 (59,64)
    private List<Long> threeAndQuisaThreeStarHotelIds;
    //  经济型、(69)
    private List<Long> quisaTwoStarHotelIds;
    //  二星及以下/公寓、(66,79)
    private List<Long> twoAndDownStarHotelIds;

    // 五星级 19
    int fiveStarHotelCount = 0;
    // 豪华型 (29)
    int quisaFiveStarHotelCount = 0;
    //  四星级/高档型百分比 (39,49)
    int fourAndQuisaFourStarHotelCount = 0;
    //  三星/舒适型百分比 (59,64)
    int threeAndQuisaThreeStarHotelCount = 0;
    //  经济型、(69)
    int quisaTwoStarHotelCount = 0;
    //  二星及以下/公寓、(66,79)
    int twoAndDownStarHotelCount = 0;
    // 五星级 19
    int fiveStarHotelRoomNightCount = 0;
    // 豪华型 (29)
    int quisaFiveStarHotelRoomNightCount = 0;
    //  四星级/高档型百分比 (39,49)
    int fourAndQuisaFourStarHotelRoomNightCount = 0;
    //  三星/舒适型百分比 (59,64)
    int threeAndQuisaThreeStarHotelRoomNightCount = 0;
    //  经济型、(69)
    int quisaTwoStarHotelRoomNightCount = 0;
    //  二星及以下/公寓、(66,79)
    int twoAndDownStarHotelRoomNightCount = 0;

    // 五星级 19
    private int fiveStarHotelMaxRoomNightCount= 0;
    // 豪华型 (29)
    private int quisaFiveStarHotelMaxRoomNightCount= 0;
    //  四星级/高档型百分比 (39,49)
    private int fourAndQuisaFourStarHotelMaxRoomNightCount= 0;
    //  三星/舒适型百分比 (59,64)
    private int threeAndQuisaThreeStarHotelMaxRoomNightCount= 0;
    //  经济型、(69)
    private int quisaTwoStarHotelMaxRoomNightCount= 0;
    //  二星及以下/公寓、(66,79)
    private int twoAndDownStarHotelMaxRoomNightCount= 0;

    // POI附近统计数据
    private RecommendNearPoiStatInfo recommendNearPoiStat;


    public GeneratePoiStatVO(){
        // 五星级 19
        List<Long> fiveStarHotelIds = new ArrayList<>();
        // 豪华型 (29)
        List<Long> quisaFiveStarHotelIds = new ArrayList<>();
        //  四星级/高档型百分比 (39,49)
        List<Long> fourAndQuisaFourStarHotelIds = new ArrayList<>();
        //  三星/舒适型百分比 (59,64)
        List<Long> threeAndQuisaThreeStarHotelIds = new ArrayList<>();
        //  经济型、(69)
        List<Long> quisaTwoStarHotelIds = new ArrayList<>();
        //  二星及以下/公寓、(66,79)
        List<Long> twoAndDownStarHotelIds = new ArrayList<>();
        // 五星级 19
        int fiveStarHotelMaxRoomNightCount= 0;
        // 豪华型 (29)
        int quisaFiveStarHotelMaxRoomNightCount= 0;
        //  四星级/高档型百分比 (39,49)
        int fourAndQuisaFourStarHotelMaxRoomNightCount= 0;
        //  三星/舒适型百分比 (59,64)
        int threeAndQuisaThreeStarHotelMaxRoomNightCount= 0;
        //  经济型、(69)
        int quisaTwoStarHotelMaxRoomNightCount= 0;
        //  二星及以下/公寓、(66,79)
        int twoAndDownStarHotelMaxRoomNightCount= 0;

    }

    public List<Long> getFiveStarHotelIds() {
        return fiveStarHotelIds;
    }

    public void setFiveStarHotelIds(List<Long> fiveStarHotelIds) {
        this.fiveStarHotelIds = fiveStarHotelIds;
    }

    public List<Long> getQuisaFiveStarHotelIds() {
        return quisaFiveStarHotelIds;
    }

    public void setQuisaFiveStarHotelIds(List<Long> quisaFiveStarHotelIds) {
        this.quisaFiveStarHotelIds = quisaFiveStarHotelIds;
    }

    public List<Long> getFourAndQuisaFourStarHotelIds() {
        return fourAndQuisaFourStarHotelIds;
    }

    public void setFourAndQuisaFourStarHotelIds(List<Long> fourAndQuisaFourStarHotelIds) {
        this.fourAndQuisaFourStarHotelIds = fourAndQuisaFourStarHotelIds;
    }

    public int getFiveStarHotelCount() {
        return fiveStarHotelCount;
    }

    public void setFiveStarHotelCount(int fiveStarHotelCount) {
        this.fiveStarHotelCount = fiveStarHotelCount;
    }

    public int getQuisaFiveStarHotelCount() {
        return quisaFiveStarHotelCount;
    }

    public void setQuisaFiveStarHotelCount(int quisaFiveStarHotelCount) {
        this.quisaFiveStarHotelCount = quisaFiveStarHotelCount;
    }

    public int getFourAndQuisaFourStarHotelCount() {
        return fourAndQuisaFourStarHotelCount;
    }

    public void setFourAndQuisaFourStarHotelCount(int fourAndQuisaFourStarHotelCount) {
        this.fourAndQuisaFourStarHotelCount = fourAndQuisaFourStarHotelCount;
    }

    public int getThreeAndQuisaThreeStarHotelCount() {
        return threeAndQuisaThreeStarHotelCount;
    }

    public void setThreeAndQuisaThreeStarHotelCount(int threeAndQuisaThreeStarHotelCount) {
        this.threeAndQuisaThreeStarHotelCount = threeAndQuisaThreeStarHotelCount;
    }

    public int getQuisaTwoStarHotelCount() {
        return quisaTwoStarHotelCount;
    }

    public void setQuisaTwoStarHotelCount(int quisaTwoStarHotelCount) {
        this.quisaTwoStarHotelCount = quisaTwoStarHotelCount;
    }

    public int getTwoAndDownStarHotelCount() {
        return twoAndDownStarHotelCount;
    }

    public void setTwoAndDownStarHotelCount(int twoAndDownStarHotelCount) {
        this.twoAndDownStarHotelCount = twoAndDownStarHotelCount;
    }

    public int getFiveStarHotelRoomNightCount() {
        return fiveStarHotelRoomNightCount;
    }

    public void setFiveStarHotelRoomNightCount(int fiveStarHotelRoomNightCount) {
        this.fiveStarHotelRoomNightCount = fiveStarHotelRoomNightCount;
    }

    public int getQuisaFiveStarHotelRoomNightCount() {
        return quisaFiveStarHotelRoomNightCount;
    }

    public void setQuisaFiveStarHotelRoomNightCount(int quisaFiveStarHotelRoomNightCount) {
        this.quisaFiveStarHotelRoomNightCount = quisaFiveStarHotelRoomNightCount;
    }

    public int getFourAndQuisaFourStarHotelRoomNightCount() {
        return fourAndQuisaFourStarHotelRoomNightCount;
    }

    public void setFourAndQuisaFourStarHotelRoomNightCount(int fourAndQuisaFourStarHotelRoomNightCount) {
        this.fourAndQuisaFourStarHotelRoomNightCount = fourAndQuisaFourStarHotelRoomNightCount;
    }

    public int getThreeAndQuisaThreeStarHotelRoomNightCount() {
        return threeAndQuisaThreeStarHotelRoomNightCount;
    }

    public void setThreeAndQuisaThreeStarHotelRoomNightCount(int threeAndQuisaThreeStarHotelRoomNightCount) {
        this.threeAndQuisaThreeStarHotelRoomNightCount = threeAndQuisaThreeStarHotelRoomNightCount;
    }

    public int getQuisaTwoStarHotelRoomNightCount() {
        return quisaTwoStarHotelRoomNightCount;
    }

    public void setQuisaTwoStarHotelRoomNightCount(int quisaTwoStarHotelRoomNightCount) {
        this.quisaTwoStarHotelRoomNightCount = quisaTwoStarHotelRoomNightCount;
    }

    public int getTwoAndDownStarHotelRoomNightCount() {
        return twoAndDownStarHotelRoomNightCount;
    }

    public void setTwoAndDownStarHotelRoomNightCount(int twoAndDownStarHotelRoomNightCount) {
        this.twoAndDownStarHotelRoomNightCount = twoAndDownStarHotelRoomNightCount;
    }

    public List<Long> getThreeAndQuisaThreeStarHotelIds() {
        return threeAndQuisaThreeStarHotelIds;
    }

    public void setThreeAndQuisaThreeStarHotelIds(List<Long> threeAndQuisaThreeStarHotelIds) {
        this.threeAndQuisaThreeStarHotelIds = threeAndQuisaThreeStarHotelIds;
    }

    public List<Long> getQuisaTwoStarHotelIds() {
        return quisaTwoStarHotelIds;
    }

    public void setQuisaTwoStarHotelIds(List<Long> quisaTwoStarHotelIds) {
        this.quisaTwoStarHotelIds = quisaTwoStarHotelIds;
    }

    public List<Long> getTwoAndDownStarHotelIds() {
        return twoAndDownStarHotelIds;
    }

    public void setTwoAndDownStarHotelIds(List<Long> twoAndDownStarHotelIds) {
        this.twoAndDownStarHotelIds = twoAndDownStarHotelIds;
    }

    public int getFiveStarHotelMaxRoomNightCount() {
        return fiveStarHotelMaxRoomNightCount;
    }

    public void setFiveStarHotelMaxRoomNightCount(int fiveStarHotelMaxRoomNightCount) {
        this.fiveStarHotelMaxRoomNightCount = fiveStarHotelMaxRoomNightCount;
    }

    public int getQuisaFiveStarHotelMaxRoomNightCount() {
        return quisaFiveStarHotelMaxRoomNightCount;
    }

    public void setQuisaFiveStarHotelMaxRoomNightCount(int quisaFiveStarHotelMaxRoomNightCount) {
        this.quisaFiveStarHotelMaxRoomNightCount = quisaFiveStarHotelMaxRoomNightCount;
    }

    public int getFourAndQuisaFourStarHotelMaxRoomNightCount() {
        return fourAndQuisaFourStarHotelMaxRoomNightCount;
    }

    public void setFourAndQuisaFourStarHotelMaxRoomNightCount(int fourAndQuisaFourStarHotelMaxRoomNightCount) {
        this.fourAndQuisaFourStarHotelMaxRoomNightCount = fourAndQuisaFourStarHotelMaxRoomNightCount;
    }

    public int getThreeAndQuisaThreeStarHotelMaxRoomNightCount() {
        return threeAndQuisaThreeStarHotelMaxRoomNightCount;
    }

    public void setThreeAndQuisaThreeStarHotelMaxRoomNightCount(int threeAndQuisaThreeStarHotelMaxRoomNightCount) {
        this.threeAndQuisaThreeStarHotelMaxRoomNightCount = threeAndQuisaThreeStarHotelMaxRoomNightCount;
    }

    public int getQuisaTwoStarHotelMaxRoomNightCount() {
        return quisaTwoStarHotelMaxRoomNightCount;
    }

    public void setQuisaTwoStarHotelMaxRoomNightCount(int quisaTwoStarHotelMaxRoomNightCount) {
        this.quisaTwoStarHotelMaxRoomNightCount = quisaTwoStarHotelMaxRoomNightCount;
    }

    public int getTwoAndDownStarHotelMaxRoomNightCount() {
        return twoAndDownStarHotelMaxRoomNightCount;
    }

    public void setTwoAndDownStarHotelMaxRoomNightCount(int twoAndDownStarHotelMaxRoomNightCount) {
        this.twoAndDownStarHotelMaxRoomNightCount = twoAndDownStarHotelMaxRoomNightCount;
    }

    public RecommendNearPoiStatInfo getRecommendNearPoiStat() {
        return recommendNearPoiStat;
    }

    public void setRecommendNearPoiStat(RecommendNearPoiStatInfo recommendNearPoiStat) {
        this.recommendNearPoiStat = recommendNearPoiStat;
    }
}
