package com.fangcang.grfp.core.vo.response.hotel;

import com.fangcang.grfp.core.base.BaseVO;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 订单监控返回对象
 */
@Getter
@Setter
public class DisHotelDailyOrderResponse extends BaseVO {

    private Long hotelId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 间夜数量
     */
    private Integer roomNightCount;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 节省金额
     */
    private BigDecimal savedAmount;

    /**
     * 开始时间
     */
    private String startTime;


    /**
     * 有OTA价格的总预订金额
     */
    private BigDecimal hasOtaPriceOrderAmount;

    /**
     * OTA的总价格
     **/
    private BigDecimal totalOtaPrice;

    /**
     * 有节省对比数据的订单间夜数
     **/
    private Integer savedRoomNightCount;

    /**
     * 节省率  （散客价格-预订价格）/散客价格
     **/
    private BigDecimal savedAmountRate;

    /**
     * 抽样率  =实际监控节省数据间夜总量/所选条件间夜总量
     **/
    private BigDecimal savedRoomNightRate;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 分销商编码
     */
    private String distributorCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     **/
    private String orgName;

    /**
     * 是否关注酒店 (1:关注)
     */
    private Integer isFollowHotel;

    /**
     * 省份
     */
    private String province;

    /**
     * 省份名称
     */
    private String provinceName;

}
