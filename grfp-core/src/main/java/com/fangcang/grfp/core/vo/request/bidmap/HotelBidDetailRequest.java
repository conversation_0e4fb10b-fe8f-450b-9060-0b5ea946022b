package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("酒店报价详情请求")
@Getter
@Setter
public class HotelBidDetailRequest extends BaseVO {

    @ApiModelProperty("项目意向酒店ID")
    private Integer projectIntentHotelId;

    @ApiModelProperty("项目ID")
    private Integer projectId;

    @ApiModelProperty("酒店ID")
    private Long hotelId;

}
