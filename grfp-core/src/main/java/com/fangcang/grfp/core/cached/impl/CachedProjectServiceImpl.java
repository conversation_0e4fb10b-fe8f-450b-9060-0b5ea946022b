package com.fangcang.grfp.core.cached.impl;

import cn.hutool.core.io.FileUtil;
import com.fangcang.grfp.core.cached.CachedProjectService;
import com.fangcang.grfp.core.entity.AttachmentFileEntity;
import com.fangcang.grfp.core.entity.ProjectEntity;
import com.fangcang.grfp.core.enums.FileTypeAndPathEnum;
import com.fangcang.grfp.core.mapper.AttachmentFileMapper;
import com.fangcang.grfp.core.mapper.ProjectHotelWhiteMapper;
import com.fangcang.grfp.core.mapper.ProjectMapper;
import com.fangcang.grfp.core.oss.OssManager;
import com.fangcang.grfp.core.util.StringUtil;
import com.fangcang.grfp.core.vo.AttachmentFileVO;
import com.fangcang.grfp.core.vo.request.common.QueryAttachmentInfoRequest;
import com.fangcang.grfp.core.vo.request.project.QueryProjectHotelWhiteRequest;
import com.fangcang.grfp.core.vo.response.common.AttachmentInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CachedProjectServiceImpl implements CachedProjectService {
    // ---------------------------------------------------------------------------------------------------- Private Member Variables

    @Autowired
    private ProjectMapper projectMapper;
    @Autowired
    private ProjectHotelWhiteMapper projectHotelWhiteMapper;
    @Autowired
    private AttachmentFileMapper attachmentFileMapper;
    @Autowired
    private OssManager ossManager;

    @Override
    @Cacheable(value="cachedProjectService.getById", keyGenerator = "keyGenerator", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public ProjectEntity getById(Integer projectId) {
        return projectMapper.selectById(projectId);
    }

    @Override
    @Cacheable(value="cachedProjectService.queryProjectWhiteHotelIdList", keyGenerator = "keyGenerator", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public List<Long> queryProjectWhiteHotelIdList(Integer projectId, Integer hotelWhiteTypeId) {
        QueryProjectHotelWhiteRequest queryProjectHotelWhiteRequest = new QueryProjectHotelWhiteRequest();
        queryProjectHotelWhiteRequest.setProjectId(projectId);
        queryProjectHotelWhiteRequest.setHotelWhiteType(hotelWhiteTypeId);
        return projectHotelWhiteMapper.queryProjectWhiteHotelIdList(queryProjectHotelWhiteRequest);
    }

    @Override
    @Cacheable(value="cachedProjectService.queryProjectEmailAttachmentList", keyGenerator = "keyGenerator", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public List<AttachmentFileVO> queryProjectEmailAttachmentList(Integer projectId)  {
        // 查询项目电邮附件
        QueryAttachmentInfoRequest queryAttachmentInfoRequest = new QueryAttachmentInfoRequest();
        queryAttachmentInfoRequest.setBusinessType(FileTypeAndPathEnum.PROJECT_EMAIL_ATTACHMENT.businessType);
        queryAttachmentInfoRequest.setExternalId(Long.valueOf(projectId));
        List<AttachmentFileEntity> projectEmailAttachments = attachmentFileMapper.queryAttachment(queryAttachmentInfoRequest);

        List<AttachmentFileVO> attachmentInfoList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(projectEmailAttachments)){
            for (AttachmentFileEntity projectEmailAttachment : projectEmailAttachments) {
                ByteArrayOutputStream swapStream = null;
                try {
                    InputStream fileInputStream = ossManager.getObjectPublic(projectEmailAttachment.getFileKey());
                    swapStream = new ByteArrayOutputStream();
                    byte[] buff = new byte[1024];
                    int rc = 0;
                    while ((rc = fileInputStream.read(buff, 0, 1024)) > 0) {
                        swapStream.write(buff, 0, rc);
                    }
                    AttachmentFileVO attachmentFileVO = new AttachmentFileVO();
                    attachmentFileVO.setFileName(projectEmailAttachment.getFileOriginalName());
                    attachmentFileVO.setData(swapStream.toByteArray());
                    attachmentInfoList.add(attachmentFileVO);
                } catch (Exception e) {
                    log.error("获取附件文件失败,附件id：{}", projectEmailAttachment.getAttachmentFileId(), e);
                }
            }
        }

        return attachmentInfoList;
    }


}
