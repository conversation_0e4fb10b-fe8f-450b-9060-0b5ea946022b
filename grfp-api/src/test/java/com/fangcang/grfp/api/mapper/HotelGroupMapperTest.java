package com.fangcang.grfp.api.mapper;

import com.fangcang.grfp.api.BaseTest;
import com.fangcang.grfp.core.entity.HotelGroupDefaultUnapplicableDayEntity;
import com.fangcang.grfp.core.mapper.HotelGroupDefaultUnapplicableDayMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.mail.MessagingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class HotelGroupMapperTest extends BaseTest {

    @Autowired
    private HotelGroupDefaultUnapplicableDayMapper hotelGroupDefaultUnapplicableDayMapper;

    @Test
    void testBatchInsert() throws MessagingException {
        List<HotelGroupDefaultUnapplicableDayEntity> hotelGroupDefaultUnapplicableDayList = new ArrayList<>();
        HotelGroupDefaultUnapplicableDayEntity entity = new HotelGroupDefaultUnapplicableDayEntity();
        entity.setProjectIntentHotelGroupId(1);
        entity.setProjectId(2);
        entity.setStartDate(new Date());
        entity.setEndDate(new Date());
        entity.setCreator("gang");
        entity.setModifier("gang");
        hotelGroupDefaultUnapplicableDayList.add(entity);
        HotelGroupDefaultUnapplicableDayEntity entity2 = new HotelGroupDefaultUnapplicableDayEntity();
        entity2.setProjectIntentHotelGroupId(1);
        entity2.setProjectId(2);
        entity2.setStartDate(new Date());
        entity2.setEndDate(new Date());
        entity2.setCreator("gang");
        entity2.setModifier("gang");
        hotelGroupDefaultUnapplicableDayList.add(entity2);
        hotelGroupDefaultUnapplicableDayMapper.batchInsert(hotelGroupDefaultUnapplicableDayList);
    }
}
