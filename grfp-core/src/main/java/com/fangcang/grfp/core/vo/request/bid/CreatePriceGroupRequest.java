package com.fangcang.grfp.core.vo.request.bid;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CreatePriceGroupRequest {


    /**
     * lra承诺：1-是，0-否
     */
    private Integer lra;

    /**
     * 是否包含早餐
     */
    private Integer isIncludeBreakfast;

    /**
     * 备注，最多500汉字
     */
    private String remark;

    /**
     * 价格列表
     */
    private List<CreatePriceRequest> priceList;


}
