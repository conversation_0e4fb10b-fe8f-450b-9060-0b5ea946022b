package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.ProjectHotelPriceLevelEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 项目酒店价格档次 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface ProjectHotelPriceLevelMapper extends BaseMapper<ProjectHotelPriceLevelEntity> {

    default List<ProjectHotelPriceLevelEntity> queryByProjectIntentHotelId(int projectIntentHotelId){
        LambdaQueryWrapper<ProjectHotelPriceLevelEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectHotelPriceLevelEntity::getProjectIntentHotelId, projectIntentHotelId);
        lambdaQueryWrapper.orderByAsc(ProjectHotelPriceLevelEntity::getRoomLevelNo);
        return selectList(lambdaQueryWrapper);
    }

    default ProjectHotelPriceLevelEntity queryByHotelPriceLevelId(int hotelPriceLevelId){
        LambdaQueryWrapper<ProjectHotelPriceLevelEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectHotelPriceLevelEntity::getHotelPriceLevelId, hotelPriceLevelId);
        return selectOne(lambdaQueryWrapper);
    }

    default void deleteByProjectIdAndHotelId(Integer projectId, Long hotelId) {
        LambdaQueryWrapper<ProjectHotelPriceLevelEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHotelPriceLevelEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectHotelPriceLevelEntity::getHotelId, hotelId);
        delete(queryWrapper);
    }

    default int selectLevelCount(Integer projectIntentHotelId) {
        LambdaQueryWrapper<ProjectHotelPriceLevelEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHotelPriceLevelEntity::getProjectIntentHotelId, projectIntentHotelId);
        return selectCount(queryWrapper);
    }

    default List<ProjectHotelPriceLevelEntity> queryByProjectIdAndHotelId(Integer projectId, Long hotelId) {
        LambdaQueryWrapper<ProjectHotelPriceLevelEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHotelPriceLevelEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectHotelPriceLevelEntity::getHotelId, hotelId);
        return selectList(queryWrapper);
    }

    void insertBatch(@Param("list") List<ProjectHotelPriceLevelEntity> entities);

    int updateRoomCount(ProjectHotelPriceLevelEntity projectHotelPriceLevel);
}
