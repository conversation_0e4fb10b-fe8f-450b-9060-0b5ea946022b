package com.fangcang.grfp.api.controller.project.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Getter
@Setter
@ApiModel(description = "自定义采购策略选项权重请求")
public class UpdateCustomStrategyOptionWeightRequest {

    @ApiModelProperty(value = "自定义采购策略 ID", required = true)
    @NotNull
    private Long optionId;

    @ApiModelProperty(value = "权重分值", required = true)
    private BigDecimal weightScore = BigDecimal.ZERO;

}
