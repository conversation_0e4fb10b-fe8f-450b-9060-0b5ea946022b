<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.CurrencyExchangeRateLogMapper">

    <select id="selectLatestByCurrencyCodes" resultType="com.fangcang.grfp.core.entity.CurrencyExchangeRateLogEntity">
        SELECT t1.*
        FROM t_currency_exchange_rate_log t1
        JOIN (
        SELECT currency_code, MAX(currency_exchange_rate_log_id) AS max_id
        FROM t_currency_exchange_rate_log
        WHERE currency_code IN
        <foreach collection="currencyCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY currency_code
        ) t2 ON t1.currency_code = t2.currency_code AND t1.currency_exchange_rate_log_id = t2.max_id
    </select>


    <insert id="batchInsert">
        insert into t_currency_exchange_rate_log(currency_code, before_kv_json, after_kv_json, creator)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.currencyCode}, #{item.beforeKvJson}, #{item.afterKvJson}, #{item.creator})
        </foreach>
    </insert>

    <select id="queryPageList" resultType="com.fangcang.grfp.core.entity.CurrencyExchangeRateLogEntity">
        SELECT *
        FROM
        t_currency_exchange_rate_log
        <where>
            <if test="currencyCode != null and currencyCode != ''">
                AND currency_code = #{currencyCode}
            </if>
        </where>
        ORDER BY `create_time` DESC
    </select>
</mapper>
