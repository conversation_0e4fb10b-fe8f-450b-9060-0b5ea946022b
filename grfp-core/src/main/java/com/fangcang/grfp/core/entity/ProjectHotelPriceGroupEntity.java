package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 项目酒店价格组
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_hotel_price_group")
public class ProjectHotelPriceGroupEntity extends BaseVO {


    /**
     * 项目酒店价格组ID
     */
    @TableId(value = "hotel_price_group_id", type = IdType.AUTO)
    private Integer hotelPriceGroupId;

    /**
     * 项目酒店意向ID
     */
    @TableField("project_intent_hotel_id")
    private Integer projectIntentHotelId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 项目酒店价格档次ID
     */
    @TableField("hotel_price_level_id")
    private Integer hotelPriceLevelId;

    /**
     * 适用星期，按国际规范，星期天是第1天
     */
    @TableField("applicable_weeks")
    private String applicableWeeks;

    /**
     * lra承诺：1-是，0-否
     */
    @TableField("lra")
    private Integer lra;

    /**
     * 是否包含早餐
     */
    @TableField("is_include_breakfast")
    private Integer isIncludeBreakfast;

    /**
     * 备注，最多500汉字
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否被锁定
     */
    @TableField("is_locked")
    private Integer isLocked;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 数据库不存在字段, 方便业务处理设置
     */
    @TableField(value = "hotelPriceEntities", exist = false)
    private List<ProjectHotelPriceEntity> hotelPriceEntities;


}
