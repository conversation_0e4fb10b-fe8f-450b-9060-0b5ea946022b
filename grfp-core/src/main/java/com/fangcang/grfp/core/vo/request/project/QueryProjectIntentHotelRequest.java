package com.fangcang.grfp.core.vo.request.project;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel("查询项目意向酒店请求")
public class QueryProjectIntentHotelRequest extends PageQuery {
    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull
    private Long projectId;

    @ApiModelProperty("酒店ID")
    private Long hotelId;

    @ApiModelProperty("酒店名称")
    private String hotelName;

    @ApiModelProperty("城市编码")
    private String cityCode;
} 