package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 权限管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_user_permission")
public class UserPermissionEntity extends BaseVO {


    /**
     * 用户权限ID
     */
    @TableId(value = "user_permission_id", type = IdType.AUTO)
    private Integer userPermissionId;

    /**
     * 机构类型 1：平台，2：酒店，3：企业，4酒店集团
     */
    @TableField("org_type")
    private Integer orgType;

    /**
     * 角色编码类型 ADMIN：管理员，EMPLOYEE：员工，HEAD_ORGANIZATION：机构负责人，CHANNEL_ADMIN:渠道合作管理员
     */
    @TableField("role_code")
    private String roleCode;

    /**
     * 权限
     */
    @TableField("permission")
    private String permission;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
