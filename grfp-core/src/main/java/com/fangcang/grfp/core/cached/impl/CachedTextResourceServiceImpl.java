package com.fangcang.grfp.core.cached.impl;


import cn.hutool.core.date.DateUtil;
import com.fangcang.grfp.core.cached.CachedTextResourceService;
import com.fangcang.grfp.core.constant.TextResourceType;
import com.fangcang.grfp.core.entity.TextResourceEntity;
import com.fangcang.grfp.core.mapper.TextResourceMapper;
import com.fangcang.grfp.core.util.StringUtil;
import com.fangcang.grfp.core.vo.TextResourceVO;
import com.fangcang.grfp.core.vo.TextResourcesVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CachedTextResourceServiceImpl implements CachedTextResourceService {

	// ---------------------------------------------------------------------------------------------------- Private Member Variables
	
	@Autowired
	private TextResourceMapper textResourceMapper;

	// ---------------------------------------------------------------------------------------------------- Public Methods
	
	@Override
	@Cacheable(value="cachedTextResourceService.getWebValue", keyGenerator = "keyGenerator", unless = "#result == null", cacheManager = "ehCacheCacheManager")
	public String getWebValue(int languageId, String textResourceCode) {
		// Query from DB
		TextResourceEntity textResourceEntity = textResourceMapper.getByCode(TextResourceType.WEB, textResourceCode);
	
		// Logging if necessary
		if ((textResourceEntity == null)) {
			log.info("missTextResource web: {}", textResourceCode);
		}
		
		// Prepare the result
		String result;
		if (textResourceEntity == null) {
			result = "[" + textResourceCode + "]";
		} else {
			TextResourceVO textResourceVO = new TextResourceVO(textResourceEntity);
			result = textResourceVO.getTextResourceValue(languageId);
		}
		
		return result;
	}

	@Override
	@Cacheable(value="cachedTextResourceService.getMsgValue", keyGenerator = "keyGenerator", unless = "#result == null", cacheManager = "ehCacheCacheManager")
	public String getMsgValue(int languageId, String textResourceCode) {
		// Query from DB
		TextResourceEntity textResourceEntity = textResourceMapper.getByCode(TextResourceType.MESSAGE, textResourceCode);

		// Logging if necessary
		if ((textResourceEntity == null)) {
			log.info("missTextResource msg: {}", textResourceCode);
		}

		// Prepare the result
		String result;
		if (textResourceEntity == null) {
			result = "[" + textResourceCode + "]";
		} else {
			TextResourceVO textResourceVO = new TextResourceVO(textResourceEntity);
			result = textResourceVO.getTextResourceValue(languageId);
		}

		return result;
	}

	@Override
	@Cacheable(value="cachedTextResourceService.getLatestUpdatedTime", cacheManager = "ehCacheCacheManager")
	public String getLatestUpdatedTime(Integer textResourceType) {
		// Core logic
        return DateUtil.formatDateTime(textResourceMapper.getLastUpdatedTime(textResourceType));
	}

	@Override
	@CacheEvict(value="cachedTextResourceService.getTextResourcesVO", keyGenerator = "keyGenerator", cacheManager = "ehCacheCacheManager")
	public void clearTextResourcesVO(int languageId, Integer textResourceType, String lastUpdatedTime) {
		log.info("clearTextResourcesVO {}, {}, {}", languageId, textResourceType, lastUpdatedTime);

	}

	@Override
	@Cacheable(value="cachedTextResourceService.getTextResourcesVO", keyGenerator = "keyGenerator", unless = "#result == null", cacheManager = "ehCacheCacheManager")
	public TextResourcesVO getTextResourcesVO(int languageId, Integer textResourceType, String lastUpdatedTime) {
		// Core logic
		Date lastLocaleUpdatedTime = StringUtil.isValidString(lastUpdatedTime) ? DateUtil.parseDateTime(lastUpdatedTime) : null;
		
		List<TextResourceEntity> textResourceList = textResourceMapper.getListByUpdatedTime(lastLocaleUpdatedTime, null, textResourceType, null);
		Date tempLastUpdatedTime = lastLocaleUpdatedTime;
		Map<String, String> textResourceMap = new HashMap<String, String>();
		for(TextResourceEntity textResource : textResourceList) {
			if(tempLastUpdatedTime == null || tempLastUpdatedTime.compareTo(textResource.getModifyTime()) < 0) {
				tempLastUpdatedTime = textResource.getModifyTime();
			}
			TextResourceVO textResourceVO = new TextResourceVO(textResource);
			textResourceMap.put(textResource.getTextResourceCode(), textResourceVO.getTextResourceValue(languageId));
		}
		
		// Prepare the result
		TextResourcesVO textResourcesVO = new TextResourcesVO();
		
		// Set value
		textResourcesVO.setTextResources(textResourceMap);
		textResourcesVO.setLastUpdatedTime(DateUtil.formatDateTime(tempLastUpdatedTime));
		
		return textResourcesVO;
	}


}
