package com.fangcang.grfp.core.vo.request.hotelgroup;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;

@ApiModel("查询被邀请单体酒店请求")
@Getter
@Setter
public class QueryInvitedSingleHotelRequest extends PageQuery {

    @ApiModelProperty("城市代码")
    private String cityCode;

    @ApiModelProperty("酒店ID")
    private Long hotelId;

    @ApiModelProperty("项目名称")
    private String projectName;

    // 以下字段用于内部业务逻辑，不在API文档中暴露
    @ApiModelProperty(hidden = true)
    private Collection<Long> hotelGroupBrandIdList;

    @ApiModelProperty(hidden = true)
    private Integer orgId;
} 