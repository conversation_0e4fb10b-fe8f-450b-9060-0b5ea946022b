package com.fangcang.grfp.core.vo.response.common;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.jackson.OssPublicJsonSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.File;


@ApiModel("附件信息响应")
@Getter
@Setter
public class AttachmentInfoResponse extends BaseVO {

    //附件id
    @ApiModelProperty("附件id")
    private Long attachmentFileId;

    //文件原名称
    @ApiModelProperty("文件原名称")
    private String fileOriginalName;

    @ApiModelProperty("文件Url")
    private String fileUrl;

    @ApiModelProperty("文件Key")
    private String fileKey;

    //类型 1 机构logo 2.签约主体
    @ApiModelProperty("业务类型")
    private Integer businessType;

    //0无效 1有效
    @ApiModelProperty("是否有效")
    private Integer isActive;

    //外部业务id
    @ApiModelProperty("外部业务id")
    private Long externalId;

    //base64字符串
    @ApiModelProperty("base64字符串")
    private String fileBase64String;

    //文件后缀
    @ApiModelProperty("文件大小")
    private Long fileSize;


}
