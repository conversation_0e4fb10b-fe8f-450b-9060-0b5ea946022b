package generator;

import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;

/**
 * 代码生成器配置
 *
 */
@Data
public class CodeGeneratorConfig {

    /**
     * 代码生成的目录. 默认 /tmp/mybatis-plus-generator
     */
    private String output = "/tmp/mybatis-plus-generator";

    /**
     * jdbc url
     */
    private String dbUrl;

    /**
     * jdbc user
     */
    private String dbUser;

    /**
     * jdbc password
     */
    private String dbPassword;

    /**
     * 代码包. entity 和 mapper 的 java 父包
     */
    private String basePackage;

    /**
     * jdbc 驱动名. 默认 mysql
     */
    private String dbDriverName = "com.mysql.cj.jdbc.Driver";

    /**
     * 默认的 id 生成类型. 默认 UUID
     */
    private IdType idType = IdType.ASSIGN_ID;

    /**
     * entity 的基类. 默认 的 BaseVO
     */
    private String superEntityClass = "com.fangcang.grfp.core.base.BaseVO";

}
