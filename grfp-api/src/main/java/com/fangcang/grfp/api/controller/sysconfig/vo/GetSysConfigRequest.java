package com.fangcang.grfp.api.controller.sysconfig.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@ApiModel("获取系统配置请求")
@Getter
@Setter
public class GetSysConfigRequest extends BaseVO {

    @ApiModelProperty(value = "系统配置编号", required = true)
    @NotBlank
    private String sysConfigCode;
}
