package com.fangcang.grfp.api.controller.project.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@ApiModel(description = "更新自定义采购策略权重请求")
public class UpdateCustomTendStrategyWeightRequest {

    @ApiModelProperty(value = "自定义采购策略 ID", required = true)
    @NotNull
    private Long customTendStrategyId;

    @ApiModelProperty(value = "是否支持策略: 1-是 0-否", required = true)
    @NotNull
    private Integer whtStrategyNameState;

    @ApiModelProperty(value = "权重分值", required = true)
    private BigDecimal whtStrategyName = BigDecimal.ZERO;

    @ApiModelProperty(value = "策略选项, 策略类型为选项(单选或多选)时必填")
    @Valid
    private List<UpdateCustomStrategyOptionWeightRequest> options;

}
