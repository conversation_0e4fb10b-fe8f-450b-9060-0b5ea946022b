<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.OrgPoiMapper">
    <insert id="upsert">
        insert into t_org_poi (poi_name, poi_address, org_id, city_code, province_code, country_code, map_poi_id, lng_google, lat_google, creator, modifier, state)
            value
            (#{orgPoiEntity.poiName}, #{orgPoiEntity.poiAddress}, #{orgPoiEntity.orgId}, #{orgPoiEntity.cityCode}, #{orgPoiEntity.provinceCode}, #{orgPoiEntity.countryCode},
             #{orgPoiEntity.mapPoiId}, #{orgPoiEntity.lngGoogle}, #{orgPoiEntity.latGoogle}, #{orgPoiEntity.creator}, #{orgPoiEntity.modifier}, #{orgPoiEntity.state})
        on duplicate key update poi_name      = values(poi_name),
                                poi_address   = values(poi_address),
                                org_id        = values(org_id),
                                city_code     = values(city_code),
                                province_code = values(province_code),
                                country_code  = values(country_code),
                                map_poi_id    = values(map_poi_id),
                                lng_google    = values(lng_google),
                                lat_google    = values(lat_google),
                                modifier      = values(modifier),
                                state         = values(state)
    </insert>

    <insert id="batchInsert">
        insert into t_org_poi (poi_name, poi_address, org_id, city_code, province_code, country_code, map_poi_id, lng_google, lat_google, creator)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.poiName}, #{item.poiAddress}, #{item.orgId}, #{item.cityCode}, #{item.provinceCode}, #{item.countryCode}, #{item.mapPoiId}, #{item.lngGoogle},
            #{item.latGoogle}, #{item.creator})
        </foreach>
    </insert>

    <select id="selectByCondition" resultType="com.fangcang.grfp.core.vo.response.poi.ListPoiVO">
        select t1.poi_id,
               t1.poi_name,
               t1.poi_address,
               t1.org_id,
               t2.org_name,
               t1.city_code,
               t1.map_poi_id,
               t1.lng_google,
               t1.lat_google,
               t1.state,
               t1.creator,
               t1.create_time,
               t1.modifier,
               t1.modify_time
        from t_org_poi t1
                 inner join t_org t2 on t1.org_id = t2.org_id and t2.state = 1
        <where>
            t1.STATE = 1
            <if test="request.poiName != null and request.poiName != ''">
                and t1.poi_name like concat('%', #{request.poiName}, '%')
            </if>
            <if test="request.orgName != null and request.orgName != ''">
                and t2.org_name like concat('%', #{request.orgName}, '%')
            </if>
            <if test="request.countryCode != null and request.countryCode!= ''">
                and t1.country_code = #{request.countryCode}
            </if>
            <if test="request.provinceCode != null and request.provinceCode!= ''">
                and t1.province_code = #{request.provinceCode}
            </if>
            <if test="request.cityCode != null and request.cityCode != ''">
                and t1.city_code = #{request.cityCode}
            </if>
            <if test="request.orgId != null">
                and t1.org_id = #{request.orgId}
            </if>
        </where>
        order by t1.poi_id desc
    </select>
</mapper>
