package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class ProjectHotelPriceAndGroupInfoVO extends BaseVO {

    /**
     * 项目酒店价格ID
     */

    private Integer hotelPriceId;

    /**
     * 项目酒店意向ID
     */

    private Integer projectIntentHotelId;

    /**
     * 项目ID
     */

    private Integer projectId;

    /**
     * 酒店ID
     */

    private Long hotelId;

    /**
     * 项目酒店价格档次ID
     */

    private Integer hotelPriceLevelId;

    /**
     * 项目酒店价格组ID
     */

    private Integer hotelPriceGroupId;

    /**
     * 价格类型 1:协议价,2:Season1价,3:Season2价
     */

    private Integer priceType;

    /**
     * 单人价格
     */

    private BigDecimal onePersonPrice;

    /**
     * 双人价格
     */

    private BigDecimal twoPersonPrice;

    /**
     * 单人价格 (最近议价价格)
     */

    private BigDecimal lastOnePersonPrice;

    /**
     * 双人价格 (最近议价价格)
     */
    private BigDecimal lastTwoPersonPrice;

    /**
     * lra承诺：1-是，0-否
     */
    private Integer lra;

    /**
     * 是否包含早餐
     */
    private Integer isIncludeBreakfast;


}
