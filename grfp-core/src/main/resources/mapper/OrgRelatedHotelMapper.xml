<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.OrgRelatedHotelMapper">

    <select id="queryPageList" resultType="com.fangcang.grfp.core.vo.OrgRelatedHotelVO">
        SELECT
            t1.org_related_hotel_id AS orgRelatedHotelId,
        <if test="languageId != null and languageId == 1">
            th.name_en_us AS hotelName,
        </if>
        <if test="languageId != null and languageId == 2">
            th.name_zh_cn AS hotelName,
        </if>
            th.hotel_id AS hotelId,
            th.city_code AS cityCode,
            th.hotel_brand_id AS hotelBrandId,
            th.hotel_group_id AS hotelGroupId,
            t1.org_id AS orgId
        FROM t_org_related_hotel t1
        LEFT JOIN t_hotel th ON t1.hotel_id = th.hotel_id
        WHERE t1.org_id = #{orgId}

    </select>

    <select id="queryOrgRelatedHotelList" resultType="com.fangcang.grfp.core.entity.OrgRelatedHotelEntity">
        SELECT
            t.ORG_RELATED_HOTEL_ID AS orgRelatedHotelId,
            t.ORG_ID AS orgId,
            t.HOTEL_ID AS hotelId
        FROM t_org_related_hotel t
        WHERE t.ORG_ID = #{orgId,jdbcType=BIGINT}
        ORDER BY orgRelatedHotelId ASC

    </select>
</mapper>
