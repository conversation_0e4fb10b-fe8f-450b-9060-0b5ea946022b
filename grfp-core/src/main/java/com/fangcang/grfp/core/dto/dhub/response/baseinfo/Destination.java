package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("目的地信息")
public class Destination extends BaseVO {

    /**
     * 目的地ID
     */
    @ApiModelProperty("目的地ID")
    private String destinationId;

    /**
     * 目的地类型：1酒店，2城市，3行政区，4商业区，5旅游区，6城市及周边，7比城市小的区，8地点/地铁站目的地名称
     */
    @ApiModelProperty("目的地类型：1酒店，2城市，3行政区，4商业区，5旅游区，6城市及周边，7比城市小的区，8地点/地铁站目的地名称")
    private int dataType;

    /**
     * 目的地数据
     */
    @ApiModelProperty("目的地数据")
    private List<DestinationName> destinationName;

}
