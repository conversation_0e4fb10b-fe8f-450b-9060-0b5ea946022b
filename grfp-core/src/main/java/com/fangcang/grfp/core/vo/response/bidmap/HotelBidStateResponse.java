package com.fangcang.grfp.core.vo.response.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("酒店报价状态统计response")
@Getter
@Setter
public class HotelBidStateResponse extends BaseVO {

    // 报价状态
    @ApiModelProperty("酒店报价状态 报价状态 0:未报价，1:新标，2：议价中，3：议价中签，4：已否定，5，放弃报价，6：修订报价，7：拒绝报价")
    private Integer hotelBidState;

    // 数量
    @ApiModelProperty("数量")
    private Integer count;

}
