package com.fangcang.grfp.core.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 项目酒店报价税费设定
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@ApiModel("酒店税费设定信息")
@Getter
@Setter
public class BidHotelTaxSettingsVO extends BaseVO {

    /**
     * 提前入住税计算方式 1:百分比，2:固定值
     */
    @ApiModelProperty("前入住税计算方式 1:百分比，2:固定值")
    private Integer earlyckFeeType;

    /**
     * 提前入住税
     */
    @ApiModelProperty("提前入住税")
    private BigDecimal earlyckFeeValue;


    /**
     * 是否包含提前入住税
     */
    @ApiModelProperty("是否包含提前入住税")
    private Integer earlyckFeeIsInclude;

    /**
     * 入住税计算方式 1:百分比，2:固定值
     */
    @ApiModelProperty("入住税计算方式 1:百分比，2:固定值")
    private Integer lodgtxFeeType;

    /**
     * 入住税
     */
    @ApiModelProperty("入住税")
    private BigDecimal lodgtxFeeValue;

    /**
     * 是否包含入住税
     */
    @ApiModelProperty("是否包含入住税")
    private Integer lodgtxFeeIsInclude;

    /**
     * 国家税 计算方式 1:百分比，2:固定值
     */
    @ApiModelProperty("国家税 计算方式 1:百分比，2:固定值")
    private Integer statetxFeeType;

    /**
     * 国家税 
     */
    @ApiModelProperty("国家税")
    private BigDecimal statetxFeeValue;

    @ApiModelProperty("是否包含国家税")
    private Integer statetxFeeIsInclude;

    /**
     * 城市税 计算方式 1:百分比，2:固定值
     */
    @ApiModelProperty("城市税 计算方式 1:百分比，2:固定值")
    private Integer citytxFeeType;

    /**
     * 城市税 
     */
    @ApiModelProperty("城市税")
    private BigDecimal citytxFeeValue;

    @ApiModelProperty("是否包含城市税")
    private Integer citytxFeeIsInclude;

    /**
     * 客房增值税 计算方式 1:百分比，2:固定值
     */
    @ApiModelProperty("客房增值税 计算方式 1:百分比，2:固定值")
    private Integer vatgstrmFeeType;

    /**
     * 客房增值税 
     */
    @ApiModelProperty("客房增值税")
    private BigDecimal vatgstrmFeeValue;

    /**
     * 是否包含客房增值税
     */
    @ApiModelProperty("是否包含客房增值税")
    private Integer vatgstrmFeeIsInclude;

    /**
     * 餐饮增值税 计算方式 1:百分比，2:固定值
     */
    @ApiModelProperty("餐饮增值税 计算方式 1:百分比，2:固定值")
    private Integer vatgstfbFeeType;

    /**
     * 餐饮增值税 
     */
    @ApiModelProperty("餐饮增值税")
    private BigDecimal vatgstfbFeeValue;

    /**
     * 是否包含餐饮增值税
     */
    @ApiModelProperty("是否包含餐饮增值税")
    private Integer vatgstfbFeeIsInclude;

    /**
     * 服务费 计算方式 1:百分比，2:固定值
     */
    @ApiModelProperty("服务费 计算方式 1:百分比，2:固定值")
    private Integer serviceFeeType;

    /**
     * 服务费 
     */
    @ApiModelProperty("服务费")
    private BigDecimal serviceFeeValue;

    /**
     * 是否包含餐饮增值税
     */
    @ApiModelProperty("是否包含服务费")
    private Integer serviceFeeIsInclude;

    /**
     * 占用 计算方式 1:百分比，2:固定值
     */
    @ApiModelProperty("占用 计算方式 1:百分比，2:固定值")
    private Integer occFeeType;

    /**
     * 占用 
     */
    @ApiModelProperty("占用")
    private BigDecimal occFeeValue;

    /**
     * 占用
     */
    @ApiModelProperty("是否包含占用")
    private Integer occFeeIsInclude;

    /**
     * 其他税费1 计算方式 1:百分比，2:固定值
     */
    @ApiModelProperty("其他税费1 计算方式 1:百分比，2:固定值")
    private Integer othertx1FeeType;

    /**
     * 其他税费1描述 
     */
    @ApiModelProperty("其他税费1描述")
    private String othertx1FeeDesc;

    /**
     * 其他税费1 
     */
    @ApiModelProperty("其他税费1")
    private BigDecimal othertx1FeeValue;

    /**
     * 其他税费1描述
     */
    @ApiModelProperty("是否包含其他税费1")
    private Integer othertx1FeeIsInclude;

    /**
     * 其他税费2 计算方式 1:百分比，2:固定值
     */
    @ApiModelProperty("其他税费2 计算方式 1:百分比，2:固定值")
    private Integer othertx2FeeType;

    /**
     * 其他税费2描述 
     */
    @ApiModelProperty("其他税费2描述")
    private String othertx2FeeDesc;

    /**
     * 其他税费2
     */
    @ApiModelProperty("其他税费2")
    private BigDecimal othertx2FeeValue;

    /**
     * 是否包含其他税费2
     */
    @ApiModelProperty("是否包含其他税费2")
    private Integer othertx2FeeIsInclude;


    /**
     * 其他税费3 计算方式 1:百分比，2:固定值
     */
    @ApiModelProperty("其他税费3 计算方式 1:百分比，2:固定值")
    private Integer othertx3FeeType;

    /**
     * 其他税费3描述 
     */
    @ApiModelProperty("其他税费3描述")
    private String othertx3FeeDesc;

    /**
     * 其他税费3
     */
    @ApiModelProperty("其他税费3")
    private BigDecimal othertx3FeeValue;

    /**
     * 是否包含其他税费3
     */
    @ApiModelProperty("是否包含其他税费3")
    private Integer othertx3FeeIsInclude;


}
