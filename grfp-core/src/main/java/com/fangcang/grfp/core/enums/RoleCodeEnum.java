package com.fangcang.grfp.core.enums;

import com.fangcang.grfp.core.util.GenericAppUtility;

/**
 * <AUTHOR>
 * @ClassName RoleCodeEnum
 * @Description 角色编码类型
 * @createTime 2022-08-29 12:10:32
 * @Param 
 * @return 
 */
public enum RoleCodeEnum {

    ADMIN("ADMIN", "管理员"), EMPLOYEE("EMPLOYEE", "员工"),HEAD_ORGANIZATION("HEAD_ORGANIZATION","机构负责人"),
    CHANNEL_ADMIN("CHANNEL_ADMIN", "渠道合作管理员");

    public String key;
    public String value;

    RoleCodeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getKeyByValue(String value) {
        String key = null;
        for(RoleCodeEnum roleCodeEnum : RoleCodeEnum.values()) {
            if(roleCodeEnum.value .equals(value)) {
                key = roleCodeEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(String key) {
        String value = null;
        for(RoleCodeEnum roleCodeEnum : RoleCodeEnum.values()) {
            if(roleCodeEnum.key.equals(key)) {
                value = roleCodeEnum.value;
                break;
            }
        }
        return value;
    }

    public static RoleCodeEnum getEnumByKey(String key){
        RoleCodeEnum roleCodeEnum = null;
        for(RoleCodeEnum roleCode : RoleCodeEnum.values()) {
            if(roleCode.key.equals(key)) {
                roleCodeEnum = roleCode;
                break;
            }
        }
        return roleCodeEnum;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getRoleName(int languageId,  String roleCode){
        return GenericAppUtility.getText(languageId, "ROLE_" + roleCode);
    }
}
