package com.fangcang.grfp.core.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public enum HotelStarGroupEnum {

    FIVESTAR(1, Collections.singletonList("19"),"五星"),
    QUISAFIVESTAR(2, Collections.singletonList("29"),"豪华型"),
    FOURSTAR_QUISAFOURSTAR(3, Arrays.asList("39","49"),"四星/高档型"),
    THREESTAR_QUISATWOSTAR(4, Arrays.asList("59","64"),"三星/舒适型"),
    QUISATWOSTAR(5, Collections.singletonList("69"),"经济型"),
    TWOSTAR_AND_BELOWTWOSTAR(6, Arrays.asList("66","79"),"二星及以下/公寓");


    public int key;

    public List<String> values;

    public String message;



    HotelStarGroupEnum(int key, List<String> values, String message) {
        this.key = key;
        this.values = values;
        this.message = message;
    }

    public static HotelStarGroupEnum getEnumByKey(Integer key) {
        if (key == null) {
            return null;
        }
        for (HotelStarGroupEnum hotelStarGroupEnum : HotelStarGroupEnum.values()) {
            if (hotelStarGroupEnum.key == key.intValue()) {
                return hotelStarGroupEnum;
            }
        }
        return null;
    }

    public static HotelStarGroupEnum getEnumByName(String name) {
        if (name == null) {
            return null;
        }
        for (HotelStarGroupEnum hotelStarGroupEnum : HotelStarGroupEnum.values()) {
            if (hotelStarGroupEnum.name().equals(name)) {
                return hotelStarGroupEnum;
            }
        }
        return null;
    }

    public static HotelStarGroupEnum getEnumByHotelStar(String hotelStar){
        if (StringUtils.isEmpty(hotelStar)) {
            return null;
        }
        for (HotelStarGroupEnum hotelStarGroupEnum : HotelStarGroupEnum.values()) {
            if (hotelStarGroupEnum.values.contains(hotelStar)) {
                return hotelStarGroupEnum;
            }
        }
        return null;

    }
}
