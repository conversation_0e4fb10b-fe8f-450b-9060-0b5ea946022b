package com.fangcang.grfp.core.manager;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fangcang.grfp.core.base.ImportErrorVO;
import com.fangcang.grfp.core.base.ImportRowErrorVO;
import com.fangcang.grfp.core.base.Response;
import com.fangcang.grfp.core.config.ThreadPoolAutoConfiguration;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.entity.*;
import com.fangcang.grfp.core.enums.*;
import com.fangcang.grfp.core.mapper.*;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.*;
import com.fangcang.grfp.core.util.DateUtil;
import com.fangcang.grfp.core.vo.CustomStrategyBidOptionVO;
import com.fangcang.grfp.core.vo.LanyonImportColumnVO;
import com.fangcang.grfp.core.vo.LanyonImportDataVO;
import com.fangcang.grfp.core.vo.request.bid.*;
import com.fangcang.grfp.core.vo.response.project.CustomStrategyOptionVO;
import com.fangcang.grfp.core.vo.response.project.ProjectBidCustomTendStrategyVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Range;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *  Lanyon Import
 */
@Component
@Slf4j
public class LanyonImportManager {

    @Resource
    private ProjectIntentHotelMapper projectIntentHotelMapper;

    @Resource
    private AmadeusMapingMapper amadeusMapingMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private LanyonImportColumnMapper lanyonImportColumnMapper;
    @Resource
    private ProjectManager projectManager;
    @Resource
    private SysImportRecordMapper sysImportRecordMapper;
    @Resource
    private BidManager bidManager;
    @Resource
    private LanyonImportDataMapper lanyonImportDataMapper;

    @Async(ThreadPoolAutoConfiguration.EXECUTOR_NAME)
    public void asyncImportData(Long sysImportRecordId, MultipartFile uploadFile, Integer projectId, UserSession userSession) throws Exception {
        // 定义转换数据
        Map<Long, CreateBidRequest> importHotelDataMap = new HashMap<>();

        // 校验excel数据
        Map<Long, List<Map<String, String>>> lanyonHotelDataMap = new HashMap<>();
        Response validateResponse = this.validateUploadLanyonBidData(uploadFile,  projectId, importHotelDataMap, lanyonHotelDataMap, userSession);
        // 检查不通过 返回
        if(!Objects.equals(validateResponse.getResult(), ReturnResultEnum.SUCCESS.errorNo)){
            SysImportRecordEntity updateEntity = new SysImportRecordEntity();
            updateEntity.setSysImportRecordId(sysImportRecordId);
            updateEntity.setStatus(ImportStatusEnum.FAIL.getKey());
            ImportErrorVO importErrorVO = new ImportErrorVO();
            importErrorVO.setType(ImportErrorTypeEnum.UNKNOWN.getType());
            importErrorVO.setErrorCode(validateResponse.getMsg());
            if(validateResponse.getData() != null){
                List<ImportRowErrorVO> importRowErrorVOList = (List<ImportRowErrorVO>)validateResponse.getData();
                importErrorVO.setRowErrors(importRowErrorVOList);
            }
            updateEntity.setFailRemark(JsonUtil.objectToJson(importErrorVO));
            sysImportRecordMapper.updateById(updateEntity);
            return;
        }

        // 创建报价
        if (!importHotelDataMap.isEmpty()) {
            // 创建报价
            int succeededProceedCount = 0;
            try {
                succeededProceedCount = bidManager.batchCreateBid(new ArrayList<>(importHotelDataMap.values()), lanyonHotelDataMap, userSession);
            } catch (Exception ex){
                log.error("create bid error", ex);
                log.error(ExceptionUtility.getDetailedExceptionString(ex));
            } finally {
                int status = succeededProceedCount == importHotelDataMap.size() ? ImportStatusEnum.SUCCESS.getKey() : ImportStatusEnum.FAIL.getKey();
                SysImportRecordEntity updateEntity = new SysImportRecordEntity();
                updateEntity.setSysImportRecordId(sysImportRecordId);
                updateEntity.setStatus(status);
                if(succeededProceedCount != importHotelDataMap.size()){
                    ImportErrorVO importErrorVO = new ImportErrorVO();
                    importErrorVO.setType(succeededProceedCount == 0 ? ImportErrorTypeEnum.UNKNOWN.getType() :  ImportErrorTypeEnum.PARTIAL_FAIL.getType());
                    updateEntity.setFailRemark(JsonUtil.objectToJson(importErrorVO));
                }
                sysImportRecordMapper.updateById(updateEntity);
            }
        }
    }

    public Response validateUploadLanyonBidData(MultipartFile uploadFile,
                                                Integer projectId, Map<Long, CreateBidRequest> importHotelDataMap,
                                                Map<Long, List<Map<String, String>>> lanyonImportDataMap, UserSession userSession) throws Exception {
        Response response = new Response();
        ProjectEntity project = projectMapper.selectById(projectId);
        if(project == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg(ErrorCode.PROJECT_NOT_EXIST);
            return response;
        }

        List<ImportRowErrorVO> importRowErrorList = new ArrayList<>();
        Workbook wb = WorkbookFactory.create(uploadFile.getInputStream());

        // 获得有多少行数据 不能多于10000行
        Sheet sheet = wb.getSheetAt(0);
        int rowSize = sheet.getPhysicalNumberOfRows();
        log.info("checkFormat rowSize=" + rowSize);
        if(rowSize<=1){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg(ErrorCode.IMPORT_DATA_CANNOT_BE_EMPTY);
            return response;
        }

        if (rowSize > 10001) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg(ErrorCode.IMPORT_DATA_EXCEED_LIMIT);
            return response;
        }
        //校验标题是否符合规范
        Row titleRow = sheet.getRow(0);
        if (titleRow == null) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg(ErrorCode.IMPORT_RECORD_NOT_EXIST);
            return response;
        }

        // （1）	校验模板是否是LANYON模板
        List<LanyonImportColumnVO> lanyonImportColumns = lanyonImportColumnMapper.queryAll();
        Map<Integer, String> lanyonImportIndexColumnMap = lanyonImportColumns.stream().collect(Collectors.toMap(LanyonImportColumnVO::getColumnIndex, o->o.getColumnCode()));
        int k=0;
        List<String> unkonwnHeaderNameList = new ArrayList<>();
        Map<Integer, String> headerMap = new HashMap<>();
        for(LanyonImportColumnVO lanyonImportColumn : lanyonImportColumns){
            Cell cell = titleRow.getCell(k);
            if(cell == null){
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                log.info("导入模板中，"+ lanyonImportColumn.getColumnCode()+", 列未包含在已支持LANYON导入列表内，请新增后重新导入");
                response.setMsg(lanyonImportColumn.getColumnCode() + "Header Not Exist");
                return response;
            }
            String headName = cell.getStringCellValue();
            if (!lanyonImportColumn.getColumnCode().equals(headName)) {
                unkonwnHeaderNameList.add(headName);
            }
            headerMap.put(k, lanyonImportColumn.getColumnCode());
            k++;
        }
        if(CollectionUtils.isNotEmpty(unkonwnHeaderNameList)){
            log.info("未知headNames： " + JsonUtil.objectToJson(unkonwnHeaderNameList));
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("导入模板中，"+ JsonUtil.objectToJson(unkonwnHeaderNameList) +", 未包含在已支持LANYON导入列表内，请删除后重新导入");
            return response;
        }

        // 查找项目自定义策略
        List<ProjectBidCustomTendStrategyVO> customTendStrategyResponseList = projectManager.queryProjectCustomTendStrategyInfo(projectId);
        // 查找已经报价酒店
        List<ProjectIntentHotelEntity> projectIntentHotelList = projectIntentHotelMapper.selectByProjectId(projectId);
        Map<Long, ProjectIntentHotelEntity> projectIntentHotelMap = projectIntentHotelList.stream().collect(Collectors.toMap(ProjectIntentHotelEntity::getHotelId, Function.identity()));

        List<ImportRowErrorVO> invalidMsgList = new ArrayList<>();
        Map<Long, List<PriceApplicableDayEntity>> hotelPriceAppleDayMap = new HashMap<>();
        Map<Long, List<CreateBidRequest>> importHotelDataListMap = new HashMap<>();
        Set<String> hotelBreakfastSet = new HashSet<>();
        for (int i = 1; i < rowSize; i++) {
            int columnIndex = 0;
            Row dataRow = sheet.getRow(i);
            try {
                if (dataRow == null) {
                    continue;
                }
                // 酒店编码
                String propCode = CommonUtil.getCellValue(dataRow.getCell(0));
                if(!StringUtil.isValidString(propCode)){
                    // importRowErrorVO.setErrorMsg("导入模板，第"+ (i+1) + "行少关键PROPCODE项目信息，请补充后重新导入");
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.setErrorCode(ErrorCode.IMPORT_ROW_COLUMN_DATA_CANNOT_BE_EMPTY);
                    importRowErrorVO.setErrorMsg("PROPCODE");
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                String internalHotelCode = CommonUtil.getCellValue(dataRow.getCell(1));
                if(!StringUtil.isValidString(internalHotelCode)){
                    //invalidMsgList.add("导入模板，第"+ (i+1) + "行少关键INTERNALHOTELCODE项目信息，请补充后重新导入");
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.setErrorCode(ErrorCode.IMPORT_ROW_COLUMN_DATA_CANNOT_BE_EMPTY);
                    importRowErrorVO.setErrorMsg("INTERNALHOTELCODE");
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }

                // currencyCode
                String currencyCode = CommonUtil.getCellValue(dataRow.getCell(63));
                if(!StringUtil.isValidString(currencyCode)){
                    //invalidMsgList.add("导入模板，第"+ (i+1) + "行少关键AMADEUS_CHAINCODE项目信息，请补充后重新导入");
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.setErrorCode(ErrorCode.IMPORT_ROW_COLUMN_DATA_CANNOT_BE_EMPTY);
                    importRowErrorVO.setErrorMsg("RATE_CURR");
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }

                // Amadeus集团代码
                String amadeusChaincode = CommonUtil.getCellValue(dataRow.getCell(52));
                String amadeusPropcode = CommonUtil.getCellValue(dataRow.getCell(53));
                if(!StringUtil.isValidString(amadeusChaincode)){
                    //invalidMsgList.add("导入模板，第"+ (i+1) + "行少关键AMADEUS_CHAINCODE项目信息，请补充后重新导入");
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.setErrorCode(ErrorCode.IMPORT_ROW_COLUMN_DATA_CANNOT_BE_EMPTY);
                    importRowErrorVO.setErrorMsg("AMADEUS_CHAINCODE");
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                // Amadeus酒店代码
                if(!StringUtil.isValidString(amadeusPropcode)){
                    //invalidMsgList.add("导入模板，第"+ (i+1) + "行少关键AMADEUS_PROPCODE项目信息，请补充后重新导入");
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.setErrorCode(ErrorCode.IMPORT_ROW_COLUMN_DATA_CANNOT_BE_EMPTY);
                    importRowErrorVO.setErrorMsg("AMADEUS_PROPCODE");
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                String amadeusCode = amadeusChaincode+amadeusPropcode;
                Long hotelId = amadeusMapingMapper.queryHotelIdBySpHotelId(amadeusCode);
                if(hotelId == null){
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.setErrorCode(ErrorCode.HOTEL_NOT_EXIST);
                    importRowErrorVO.setErrorMsg(amadeusCode);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }

                // （3）	校验关键信息是否为空
                List<String> nullCodeList = new ArrayList<>();
                String rfpName =  CommonUtil.getCellValue(dataRow.getCell(236));
                if(!StringUtil.isValidString(rfpName)){
                    nullCodeList.add("RFP_NAME");
                }
                String rfpPhone =  CommonUtil.getCellValue(dataRow.getCell(240));
                if(!StringUtil.isValidString(rfpPhone)){
                    nullCodeList.add("RFP_PHONE");
                }
                String rfpEmail =  CommonUtil.getCellValue(dataRow.getCell(241));
                if(!StringUtil.isValidString(rfpEmail)){
                    nullCodeList.add("RFP_EMAIL");
                }
                String roomType1Define = CommonUtil.getCellValue(dataRow.getCell(66));
                if(!StringUtil.isValidString(roomType1Define)){
                    nullCodeList.add("ROOMTYPE1DEFINE");
                }
                //Date season1Start
                if(dataRow.getCell(72) == null){
                    nullCodeList.add("SEASON1START");
                }
                //  Date season1End
                if(dataRow.getCell(73) == null){
                    nullCodeList.add("SEASON1END");
                }
                String cancPol = getCellCancPol(dataRow.getCell(179));
                if(!StringUtil.isValidString(cancPol)){
                    nullCodeList.add("CANC_POL");
                }
                String breakInclude = CommonUtil.getCellValue(dataRow.getCell(211));
                if(!StringUtil.isValidString(breakInclude)){
                    nullCodeList.add("BREAK_INCLUDE");
                }
                if(CollectionUtils.isNotEmpty(nullCodeList)){
                    String codes = "";
                    for(String code : nullCodeList){
                        codes = codes + code + ",";
                    }
                    codes = codes.substring(0,codes.length()-1);
                    //invalidMsgList.add("第" + (i + 1) + "行缺少关键信息" + codes + "，请补充关键信息后，重新导入。");
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_1, codes);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                // LRA_S1_RT1_SGL LRA_时段1_房型1_单人入住
                String lraS1Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(74));
                // LRA_S1_RT1_DBL LRA_时段1_房型1_双人入住
                String lraS1Rt1Dbl = CommonUtil.getCellValue(dataRow.getCell(75));
                // NLRA_S1_RT1_SGL NLRA_时段1_房型1_单人入住
                String nlraS1Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(80));
                // NLRA_S1_RT1_DBL NLRA_时段1_房型1_双人入住
                String nlraS1Rt1Dbl = CommonUtil.getCellValue(dataRow.getCell(81));
                if(StringUtil.isEmpty(lraS1Rt1Sgl) && StringUtil.isEmpty(lraS1Rt1Dbl) &&
                        StringUtil.isEmpty(nlraS1Rt1Sgl) && StringUtil.isEmpty(nlraS1Rt1Dbl)){
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(74+1), "SEASON1  RT1");
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }

                // 检查酒店是否存在其他机构已经报价， 如果存在并且是中签，不允许导入
                ProjectIntentHotelEntity intentHotel = projectIntentHotelMap.get(hotelId);

                if(intentHotel != null && (
                        Objects.equals(intentHotel.getBidState(), HotelBidStateEnum.NEW_BID.bidState) ||
                                Objects.equals(intentHotel.getBidState(), HotelBidStateEnum.BID_WINNING.bidState) ||
                                Objects.equals(intentHotel.getBidState(), HotelBidStateEnum.UPDATED_BID.bidState) ||
                                Objects.equals(intentHotel.getBidState(), HotelBidStateEnum.REJECTED.bidState) || (
                                Objects.equals(intentHotel.getBidState(), HotelBidStateEnum.UNDER_NEGOTIATION.bidState) &&
                                        (intentHotel.getBidUploadSource() == null || !Objects.equals(intentHotel.getBidUploadSource(), BidUploadSourceEnum.LANYON.key))
                        )
                )) {
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.setErrorCode(ErrorCode.CANNOT_BID_DUE_TO_ALREADY_SUBMIT_BID);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                    //invalidMsgList.add("第" + (i + 1) + "行酒店在当前项目已有" + HotelBidStateEnum.getHotelBidStateEnum(intentHotel.getBidState()) + "状态，请与酒店联系，或删除对应酒店导入信息后，重新导入");
                }

                // CreateBidStrategyRequest
                CreateBidRequest createBidRequest = new CreateBidRequest();
                createBidRequest.setHotelId(hotelId);
                createBidRequest.setProjectId(project.getProjectId());
                createBidRequest.setHotelBidContactName(rfpName);
                createBidRequest.setHotelBidContactMobile(rfpPhone);
                createBidRequest.setHotelBidContactEmail(rfpEmail);

                createBidRequest.setBidUploadSource(BidUploadSourceEnum.LANYON.key);
                createBidRequest.setIsUpload(RfpConstant.constant_1);
                createBidRequest.setHotelSalesContactName(rfpName);
                createBidRequest.setHotelSalesContactEmail(rfpEmail);
                createBidRequest.setHotelSalesContactMobile(rfpPhone);
                // 酒店集团LANYON导入报价，酒店集团报价人信息自动带入导入报价人信息
                createBidRequest.setBidOrgId(userSession.getUserOrg().getOrgId());
                createBidRequest.setBidOrgType(userSession.getUserOrg().getOrgType());
                createBidRequest.setHotelGroupBidContactName(userSession.getUsername());
                createBidRequest.setHotelGroupBidContactMobile(userSession.getMobileAreaCode() + userSession.getMobile());
                createBidRequest.setHotelGroupBidContactEmail(userSession.getEmail());

                // 币种  cell 63
                createBidRequest.setCurrencyCode(currencyCode);

                // 房型描述
                createBidRequest.setRoomCountMap(new HashMap<>());
                roomType1Define = roomType1Define.replaceAll("/",",");
                CreatePriceLevelRequest createPriceLevelRequest1 = new CreatePriceLevelRequest();
                createPriceLevelRequest1.setLanyonRoomDesc(roomType1Define);
                createBidRequest.getRoomCountMap().put(RoomLevelEnum.ONE.key, createPriceLevelRequest1);
                // ROOMTYPE2DEFINE
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(68)))){
                    String roomType2Define = CommonUtil.getCellValue(dataRow.getCell(68)).replaceAll("/",",");
                    CreatePriceLevelRequest createPriceLevelRequest2 = new CreatePriceLevelRequest();
                    createPriceLevelRequest2.setLanyonRoomDesc(roomType2Define);
                    createBidRequest.getRoomCountMap().put(RoomLevelEnum.TWO.key, createPriceLevelRequest2);
                }
                // ROOMTYPE3DEFINE
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(70)))){
                    String roomType3Define = CommonUtil.getCellValue(dataRow.getCell(70)).replaceAll("/",",");
                    CreatePriceLevelRequest createPriceLevelRequest3 = new CreatePriceLevelRequest();
                    createPriceLevelRequest3.setLanyonRoomDesc(roomType3Define);
                    createBidRequest.getRoomCountMap().put(RoomLevelEnum.THREE.key, createPriceLevelRequest3);
                }
                // ROOMTYPE1NUMBER
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(67)))) {
                    CreatePriceLevelRequest levelRequest = createBidRequest.getRoomCountMap().get(RoomLevelEnum.ONE.key);
                    if(levelRequest != null){
                        levelRequest.setTotalRoomCount(Integer.valueOf(CommonUtil.getCellValue(dataRow.getCell(67))));
                    }
                }
                // ROOMTYPE2NUMBER
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(69)))) {
                    CreatePriceLevelRequest levelRequest = createBidRequest.getRoomCountMap().get(RoomLevelEnum.TWO.key);
                    if(levelRequest != null){
                        levelRequest.setTotalRoomCount(Integer.valueOf(CommonUtil.getCellValue(dataRow.getCell(69))));
                    }
                }
                // ROOMTYPE3NUMBER
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(71)))) {
                    CreatePriceLevelRequest levelRequest = createBidRequest.getRoomCountMap().get(RoomLevelEnum.THREE.key);
                    if(levelRequest != null){
                        levelRequest.setTotalRoomCount(Integer.valueOf(CommonUtil.getCellValue(dataRow.getCell(71))));
                    }
                }

                // 协议价日期
                // 基础协议价日期 SEASON1START
                //hotelLeveBaseDayMap.put(hotelId, baseDay);
                Date season1Start = convertCellToDate(dataRow.getCell(72));
                Date season1End = convertCellToDate(dataRow.getCell(73));
                createBidRequest.setBidStartTime(season1Start);
                createBidRequest.setBidEndTime(season1End);

                // Season1 SEASON2START 日期
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(92))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(93)))) {
                    Range<Date> season1RangeDate = Range.between(convertCellToDate(dataRow.getCell(92)), convertCellToDate(dataRow.getCell(93)));
                    createBidRequest.setSeason1Dates(Arrays.asList(season1RangeDate));
                }
                // Season2 SEASON3START 日期
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(112))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(113)))) {
                    Range<Date> season1RangeDate = Range.between(convertCellToDate(dataRow.getCell(112)), convertCellToDate(dataRow.getCell(113)));
                    createBidRequest.setSeason1Dates(Arrays.asList(season1RangeDate));
                }

                // 客户定制内容 243(客户定制内容1) -> 272(客户定制内容30)
                List<CreateCustomBidStrategyRequest> projectCustomBidStrategyList = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(customTendStrategyResponseList)){
                    int index=0;
                    int colIndexBegin = 243;
                    int colIndexTo = 272;
                    for(ProjectBidCustomTendStrategyVO customTendStrategyResponse : customTendStrategyResponseList){
                        int colIndexFrom = colIndexBegin + index;
                        String supportStrategyValue = CommonUtil.getCellValue(dataRow.getCell(colIndexFrom));
                        if(StringUtil.isValidString(supportStrategyValue)) {
                            CreateCustomBidStrategyRequest projectCustomBidStrategy = new CreateCustomBidStrategyRequest();
                            projectCustomBidStrategy.setCustomTendStrategyId(customTendStrategyResponse.getId().intValue());
                            projectCustomBidStrategy.setStrategyName(customTendStrategyResponse.getStrategyName());
                            projectCustomBidStrategy.setSupportStrategyName(customTendStrategyResponse.getSupportStrategyName());
                            projectCustomBidStrategy.setStrategyType(customTendStrategyResponse.getStrategyType());
                            if(customTendStrategyResponse.getStrategyType() == CustomStrategyTypeEnum.YSE_OR_NO.key &&
                                    (supportStrategyValue.equals("Y") || supportStrategyValue.equals("N"))){
                                projectCustomBidStrategy.setSupportStrategyName(supportStrategyValue.equals("Y") ? RfpConstant.constant_1 : RfpConstant.constant_0);
                                projectCustomBidStrategyList.add(projectCustomBidStrategy);
                            } else if(customTendStrategyResponse.getStrategyType() == CustomStrategyTypeEnum.TEXT.key){
                                projectCustomBidStrategy.setSupportStrategyText(supportStrategyValue);
                                projectCustomBidStrategyList.add(projectCustomBidStrategy);
                            } else if(customTendStrategyResponse.getStrategyType() == CustomStrategyTypeEnum.RADIO.key || customTendStrategyResponse.getStrategyType() == CustomStrategyTypeEnum.CHECKBOX.key){
                               List<CustomStrategyBidOptionVO> optionVOList = new ArrayList<>();
                               for(CustomStrategyOptionVO customStrategyOptionVO : customTendStrategyResponse.getOptions()) {
                                   CustomStrategyBidOptionVO optionVO = new CustomStrategyBidOptionVO();
                                   optionVO.setOptionName(customStrategyOptionVO.getOptionName());
                                   optionVO.setOptionId(customStrategyOptionVO.getId());
                                   optionVO.setIsSupport(customStrategyOptionVO.getOptionName().equals(supportStrategyValue) ? RfpConstant.constant_1 : RfpConstant.constant_0);
                                   optionVOList.add(optionVO);
                               }
                                projectCustomBidStrategy.setOptions(optionVOList);
                            } else {
                                log.info("Lanyon导入无效自定义策略: {}, {}", projectCustomBidStrategy, customTendStrategyResponse);
                            }
                        }
                        index++;
                        if(colIndexFrom >  colIndexTo){
                            break;
                        }
                    }
                    createBidRequest.setCustomBidStrategies(projectCustomBidStrategyList);
                }

                // 不适应日期
                List<Range<Date>> unApplicableDayRangeList = new ArrayList<>();
                // BD1_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(325))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(326)))) {
                    unApplicableDayRangeList.add(Range.between(convertCellToDate(dataRow.getCell(325)),convertCellToDate(dataRow.getCell(326))));
                }
                // BD2_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(334))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(335)))) {
                    unApplicableDayRangeList.add(Range.between(convertCellToDate(dataRow.getCell(334)),convertCellToDate(dataRow.getCell(335))));
                }
                // BD3_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(343))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(344)))) {
                    unApplicableDayRangeList.add(Range.between(convertCellToDate(dataRow.getCell(343)),convertCellToDate(dataRow.getCell(344))));
                }
                // BD4_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(352))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(353)))) {
                    unApplicableDayRangeList.add(Range.between(convertCellToDate(dataRow.getCell(352)),convertCellToDate(dataRow.getCell(353))));
                }
                // BD5_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(361))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(362)))) {
                    unApplicableDayRangeList.add(Range.between(convertCellToDate(dataRow.getCell(361)),convertCellToDate(dataRow.getCell(362))));
                }
                // BD6_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(370))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(371)))) {
                    unApplicableDayRangeList.add(Range.between(convertCellToDate(dataRow.getCell(370)),convertCellToDate(dataRow.getCell(371))));
                }
                // BD7_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(379))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(380)))) {
                    unApplicableDayRangeList.add(Range.between(convertCellToDate(dataRow.getCell(379)),convertCellToDate(dataRow.getCell(380))));
                }
                // BD8_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(388))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(389)))) {
                    unApplicableDayRangeList.add(Range.between(convertCellToDate(dataRow.getCell(388)),convertCellToDate(dataRow.getCell(389))));
                }
                // BD9_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(397))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(398)))) {
                    unApplicableDayRangeList.add(Range.between(convertCellToDate(dataRow.getCell(397)),convertCellToDate(dataRow.getCell(398))));
                }
                // BD10_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(406))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(407)))) {
                    unApplicableDayRangeList.add(Range.between(convertCellToDate(dataRow.getCell(406)),convertCellToDate(dataRow.getCell(407))));
                }
                createBidRequest.setUnapplicableDates(unApplicableDayRangeList);

                // 解析房档价格信息
                Map<Integer, List<CreatePriceGroupRequest>> lavelPriceGroupMap = new HashMap<>();
                createBidRequest.setPriceGroupMap(lavelPriceGroupMap);

                // 检查早餐报价是否重复
                if(hotelBreakfastSet.contains(hotelId + breakInclude)){
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_HAS_DUPLICATE_HOTEL_BREAKFAST_DATA, breakInclude);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                    //invalidMsgList.add("第" + (i + 1) + "酒店报价重复：存在相同酒店是否包含早餐 (" +breakInclude+ ")，请修改导入信息后，重新导入" );
                }
                hotelBreakfastSet.add(hotelId + breakInclude);

                // 是否包含早餐
                boolean isIncludeBreakfast = "Y".equalsIgnoreCase(breakInclude);
                createBidRequest.setIsIncludeBreakfast(isIncludeBreakfast ? RfpConstant.constant_1 : RfpConstant.constant_0);

                // 税费
                // Price 1 增值税
                boolean isIncludePrice1 = false;
                BigDecimal price1Fee = null;
                // 增值税 VATGSTRM_INCLUDE 含客房增值税 VATGSTRM_FEE
                String price1Include = CommonUtil.getCellValue(dataRow.getCell(194));
                if(!validateIsInclude(price1Include)){
                    // invalidMsgList.add("第" + (i + 1) + "是否含客房增值税：" + price1Include +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(194+1), price1Include);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }

                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String price1Uom = CommonUtil.getCellValue(dataRow.getCell(193));
                if(!validateUom(price1Uom)){
                    //invalidMsgList.add("第" + (i + 1) + "客房增值税计税方案：" + price1Uom +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(193+1),price1Uom);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                // 税费加收数值（百分比或固定值)
                String price1FeeStr =  CommonUtil.getCellValue(dataRow.getCell(192));
                if(StringUtil.isValidString(price1FeeStr) && StringUtil.isValidString(price1Uom) && StringUtil.isValidString(price1FeeStr)) {
                    isIncludePrice1 = "Y".equalsIgnoreCase(price1Include);
                    price1Fee = new BigDecimal(price1FeeStr);
                }

                // 含餐饮增值税
                boolean isIncludePrice2 = false;
                BigDecimal price2Fee = null;
                String price2Include = CommonUtil.getCellValue(dataRow.getCell(197));
                if(!validateIsInclude(price2Include)){
                    //invalidMsgList.add("第" + (i + 1) + "含餐饮增值税：" + price2Include +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(197+1),price1Uom);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String price2Uom = CommonUtil.getCellValue(dataRow.getCell(196));
                if(!validateUom(price2Uom)){
                    //invalidMsgList.add("第" + (i + 1) + "餐饮增值税计税方案：" + price2Uom +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(196+1),price1Uom);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                // 税费加收数值（百分比或固定值)
                String price2FeeStr = CommonUtil.getCellValue(dataRow.getCell(195));
                if(StringUtil.isValidString(price2Include) && StringUtil.isValidString(price2Uom) && StringUtil.isValidString(price2FeeStr)) {
                    isIncludePrice2 = "Y".equalsIgnoreCase(price2Include);
                    price2Fee = new BigDecimal(price2FeeStr);
                }

                // 服务费 SERVICE_INCLUDE
                boolean isIncludePrice3 = false;
                BigDecimal price3Fee = null;
                String price3Include = CommonUtil.getCellValue(dataRow.getCell(200));
                if(!validateIsInclude(price3Include)){
                    //invalidMsgList.add("第" + (i + 1) + "否含服务费：" + price3Include +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(200+1),price3Include);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String price3Uom = CommonUtil.getCellValue(dataRow.getCell(199));
                if(!validateUom(price3Uom)){
                    //invalidMsgList.add("第" + (i + 1) + "服务费计税方案：" + price3Uom +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(199+1),price3Uom);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                // 税费加收数值（百分比或固定值)
                String price3FeeStr = CommonUtil.getCellValue(dataRow.getCell(198));
                if(StringUtil.isValidString(price3Include) && StringUtil.isValidString(price3Uom) && StringUtil.isValidString(price3FeeStr)) {
                    isIncludePrice3 = "Y".equalsIgnoreCase(price3Include);
                    price3Fee = new BigDecimal(price3FeeStr);
                }

                // 含占费用 OCC_INCLUDE
                boolean isIncludePrice4 = false;
                BigDecimal price4Fee = null;
                String price4Include = CommonUtil.getCellValue(dataRow.getCell(203));
                if(!validateIsInclude(price4Include)){
                    //invalidMsgList.add("第" + (i + 1) + "含占用费：" + price4Include +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(203+1),price4Include);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String price4Uom = CommonUtil.getCellValue(dataRow.getCell(202));
                if(!validateUom(price4Uom)){
                    //invalidMsgList.add("第" + (i + 1) + "含占用费计税方案：" + price4Uom +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(202+1),price4Uom);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }

                // 税费加收数值（百分比或固定值)
                String price4FeeStr = CommonUtil.getCellValue(dataRow.getCell(201));
                if(StringUtil.isValidString(price4Include) && StringUtil.isValidString(price4Uom) && StringUtil.isValidString(price4FeeStr)) {
                    isIncludePrice4 = "Y".equalsIgnoreCase(price4Include);
                    price4Fee = new BigDecimal(price4FeeStr);
                }

                // 含占费用 OTHERTX_FEE_INCL
                boolean isIncludePrice5 = false;
                BigDecimal price5Fee = null;
                String price5Include = CommonUtil.getCellValue(dataRow.getCell(207));
                if(!validateIsInclude(price5Include)){
                    //invalidMsgList.add("第" + (i + 1) + "含其他税费：" + price5Include +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(207+1),price5Include);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }

                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String price5Uom = CommonUtil.getCellValue(dataRow.getCell(205));
                if(!validateUom(price5Uom)){
                    //invalidMsgList.add("第" + (i + 1) + "其他税费计税方案：" + price5Uom +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(205+1),price5Uom);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                // 税费加收数值（百分比或固定值)
                String price5FeeStr = CommonUtil.getCellValue(dataRow.getCell(204));
                String price5Desc = "";
                if(StringUtil.isValidString(price5Include) && StringUtil.isValidString(price5Uom) && StringUtil.isValidString(price5FeeStr)) {
                    isIncludePrice5 = "Y".equalsIgnoreCase(price5Include);
                    price5Fee = new BigDecimal(price5FeeStr);
                    price5Desc = CommonUtil.getCellValue(dataRow.getCell(206));
                }

                // EARLYCK_FEE：提前入住税  cell 180
                //EARLYCK_UOM：F 固定值  P 百分比  181
                //EARLYCK_INCLUDE：Y 包含  N不包含 182
                boolean isIncludePriceEarlyck = false;
                BigDecimal priceEarlyckFee = null;
                String priceEarlyckInclude = CommonUtil.getCellValue(dataRow.getCell(182));
                if(!validateIsInclude(priceEarlyckInclude)){
                    //invalidMsgList.add("第" + (i + 1) + "含占用费：" + price4Include +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(182+1),priceEarlyckInclude);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String priceEarlyckUom = CommonUtil.getCellValue(dataRow.getCell(181));
                if(!validateUom(priceEarlyckUom)){
                    //invalidMsgList.add("第" + (i + 1) + "含占用费计税方案：" + price4Uom +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(181+1), priceEarlyckUom);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }

                // 税费加收数值（百分比或固定值)
                String priceEarlyckFeeStr = CommonUtil.getCellValue(dataRow.getCell(180));
                if(StringUtil.isValidString(priceEarlyckInclude) && StringUtil.isValidString(priceEarlyckUom) && StringUtil.isValidString(priceEarlyckFeeStr)) {
                    isIncludePriceEarlyck = "Y".equalsIgnoreCase(priceEarlyckInclude);
                    priceEarlyckFee = new BigDecimal(priceEarlyckFeeStr);
                }

                // LODGTX_FEE    入住税 183
                //LODGTX_UOM    F 固定值  P 百分比 184
                //LODGTX_INCLUDE    Y 包含  N不包含 185
                boolean isIncludePriceLodgtxFee = false;
                BigDecimal priceLodgtxFee = null;
                String priceLodgtxFeeInclude = CommonUtil.getCellValue(dataRow.getCell(185));
                if(!validateIsInclude(priceLodgtxFeeInclude)){
                    //invalidMsgList.add("第" + (i + 1) + "含占用费：" + price4Include +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(185+1),priceLodgtxFeeInclude);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String priceLodgtxUom = CommonUtil.getCellValue(dataRow.getCell(184));
                if(!validateUom(priceLodgtxUom)){
                    //invalidMsgList.add("第" + (i + 1) + "含占用费计税方案：" + price4Uom +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(184+1), priceLodgtxUom);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }

                // 税费加收数值（百分比或固定值)
                String priceLodgtxFeeStr = CommonUtil.getCellValue(dataRow.getCell(183));
                if(StringUtil.isValidString(priceLodgtxFeeStr) && StringUtil.isValidString(priceLodgtxUom) && StringUtil.isValidString(priceLodgtxFeeInclude)) {
                    isIncludePriceLodgtxFee = "Y".equalsIgnoreCase(priceLodgtxFeeInclude);
                    priceLodgtxFee = new BigDecimal(priceLodgtxFeeStr);
                }

                // STATETX_FEE    国家税  186
                //STATETX_UOM    F 固定值  P 百分比  187
                //STATETX_INCLUDE    Y 包含  N不包含  188
                boolean isIncludePriceStatetxFee = false;
                BigDecimal priceStatetxFee = null;
                String priceStatetxFeeInclude = CommonUtil.getCellValue(dataRow.getCell(188));
                if(!validateIsInclude(priceStatetxFeeInclude)){
                    //invalidMsgList.add("第" + (i + 1) + "含占用费：" + price4Include +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(188+1),priceStatetxFeeInclude);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String priceStatetxUom = CommonUtil.getCellValue(dataRow.getCell(187));
                if(!validateUom(priceStatetxUom)){
                    //invalidMsgList.add("第" + (i + 1) + "含占用费计税方案：" + price4Uom +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(187+1), priceStatetxUom);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }

                // 税费加收数值（百分比或固定值)
                String priceStatetxFeeStr = CommonUtil.getCellValue(dataRow.getCell(186));
                if(StringUtil.isValidString(priceStatetxFeeStr) && StringUtil.isValidString(priceStatetxUom) && StringUtil.isValidString(priceStatetxFeeInclude)) {
                    isIncludePriceStatetxFee = "Y".equalsIgnoreCase(priceStatetxFeeInclude);
                    priceStatetxFee = new BigDecimal(priceStatetxFeeStr);
                }

                // CITYTX_FEE    城市税  189
                //CITYTX_UOM    F 固定值  P 百分比 190
                //CITYTX_INCLUDE    Y 包含  N不包含  191
                boolean isIncludePriceCitytxFee = false;
                BigDecimal priceCitytxFee = null;
                String priceCitytxFeeInclude = CommonUtil.getCellValue(dataRow.getCell(191));
                if(!validateIsInclude(priceCitytxFeeInclude)){
                    //invalidMsgList.add("第" + (i + 1) + "含占用费：" + price4Include +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(191+1),priceCitytxFeeInclude);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }
                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String priceCitytxUom = CommonUtil.getCellValue(dataRow.getCell(190));
                if(!validateUom(priceCitytxUom)){
                    //invalidMsgList.add("第" + (i + 1) + "含占用费计税方案：" + price4Uom +"不合法，请修改导入信息后，重新导入" );
                    ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                    importRowErrorVO.addError(ErrorCode.IMPORT_ROW_DATA_INVALIDATED_2, lanyonImportIndexColumnMap.get(190+1), priceCitytxUom);
                    invalidMsgList.add(importRowErrorVO);
                    continue;
                }

                // 税费加收数值（百分比或固定值)
                String priceCitytxFeeStr = CommonUtil.getCellValue(dataRow.getCell(189));
                if(StringUtil.isValidString(priceCitytxFeeStr) && StringUtil.isValidString(priceCitytxUom) && StringUtil.isValidString(priceCitytxFeeInclude)) {
                    isIncludePriceCitytxFee = "Y".equalsIgnoreCase(priceCitytxFeeInclude);
                    priceCitytxFee = new BigDecimal(priceCitytxFeeStr);
                }

                // 税费
                CreateTaxSettingRequest createTaxSettingRequest = new CreateTaxSettingRequest();

                // 提前入住税
                if(priceEarlyckFee != null) {
                    createTaxSettingRequest.setEarlyckFeeValue(priceEarlyckFee);
                    createTaxSettingRequest.setEarlyckFeeType(FeeRateTypeEnum.getKeyByLanyonUom(priceEarlyckUom));
                    createTaxSettingRequest.setEarlyckFeeIsInclude(isIncludePriceEarlyck ? RfpConstant.constant_1 : RfpConstant.constant_0);
                }

                // 入住税
                if(priceLodgtxFee != null) {
                    createTaxSettingRequest.setLodgtxFeeValue(priceLodgtxFee);
                    createTaxSettingRequest.setLodgtxFeeType(FeeRateTypeEnum.getKeyByLanyonUom(priceLodgtxUom));
                    createTaxSettingRequest.setLodgtxFeeIsInclude(isIncludePriceLodgtxFee ? RfpConstant.constant_1 : RfpConstant.constant_0);
                }

                // 国家税
                if(priceStatetxFee != null) {
                    createTaxSettingRequest.setStatetxFeeValue(priceStatetxFee);
                    createTaxSettingRequest.setStatetxFeeType(FeeRateTypeEnum.getKeyByLanyonUom(priceStatetxUom));
                    createTaxSettingRequest.setStatetxFeeIsInclude(isIncludePriceStatetxFee ? RfpConstant.constant_1 : RfpConstant.constant_0);
                }
                // 城市税
                if(priceCitytxFee != null) {
                    createTaxSettingRequest.setCitytxFeeValue(priceCitytxFee);
                    createTaxSettingRequest.setCitytxFeeType(FeeRateTypeEnum.getKeyByLanyonUom(priceCitytxUom));
                    createTaxSettingRequest.setCitytxFeeIsInclude(isIncludePriceCitytxFee ? RfpConstant.constant_1 : RfpConstant.constant_0);
                }

                if(price1Fee != null) {
                    createTaxSettingRequest.setVatgstrmFeeIsInclude(isIncludePrice1 ? RfpConstant.constant_1 : RfpConstant.constant_0);
                    createTaxSettingRequest.setVatgstrmFeeType(FeeRateTypeEnum.getKeyByLanyonUom(price1Uom));
                    createTaxSettingRequest.setVatgstrmFeeValue(price1Fee);
                }

                if(price2Fee != null) {
                    createTaxSettingRequest.setVatgstfbFeeIsInclude(isIncludePrice2 ? RfpConstant.constant_1 : RfpConstant.constant_0);
                    createTaxSettingRequest.setVatgstfbFeeType(FeeRateTypeEnum.getKeyByLanyonUom(price2Uom));
                    createTaxSettingRequest.setVatgstfbFeeValue(price2Fee);
                }

                if(price3Fee != null) {
                    createTaxSettingRequest.setServiceFeeIsInclude(isIncludePrice3 ? RfpConstant.constant_1 : RfpConstant.constant_0);
                    createTaxSettingRequest.setServiceFeeType(FeeRateTypeEnum.getKeyByLanyonUom(price3Uom));
                    createTaxSettingRequest.setServiceFeeValue(price3Fee);
                }

                if(price4Fee != null) {
                    createTaxSettingRequest.setOccFeeIsInclude(isIncludePrice4 ? RfpConstant.constant_1 : RfpConstant.constant_0);
                    createTaxSettingRequest.setOccFeeType(FeeRateTypeEnum.getKeyByLanyonUom(price4Uom));
                    createTaxSettingRequest.setOccFeeValue(price4Fee);
                }

                if(price5Fee != null) {
                    createTaxSettingRequest.setOthertx1FeeIsInclude(isIncludePrice5 ? RfpConstant.constant_1 : RfpConstant.constant_0);
                    createTaxSettingRequest.setOthertx1FeeType(FeeRateTypeEnum.getKeyByLanyonUom(price5Uom));
                    createTaxSettingRequest.setOthertx1FeeValue(price5Fee);
                    createTaxSettingRequest.setOthertx1FeeDesc(price5Desc);
                }

                createBidRequest.setTaxSetting(createTaxSettingRequest);

                // 取消政策 CANC_POL
                String cancelRestrictTime = "";
                if(cancPol.contains("AM")){
                    cancelRestrictTime = cancPol.substring(0, cancPol.length() - 2);
                    if(cancelRestrictTime.length() == 1) {
                        cancelRestrictTime = "0" + cancelRestrictTime;
                    }
                    cancelRestrictTime = cancelRestrictTime + ":00";
                } else if(cancPol.contains("PM")){
                    cancelRestrictTime = cancPol.substring(0, cancPol.length() - 2);
                    cancelRestrictTime = String.valueOf(Integer.valueOf(cancelRestrictTime) + 12);
                    cancelRestrictTime = cancelRestrictTime + ":00";
                } else if (cancPol.length() > 6){ //兼容18:00:00 格式
                    cancelRestrictTime = cancPol.substring(0,5);
                }

                // WIFI  223
                String wirelessStr = CommonUtil.getCellValue(dataRow.getCell(223));

                CreateBidStrategyRequest createBidStrategyRequest = new CreateBidStrategyRequest();
                createBidStrategyRequest.setSupportCancelDay(0);
                createBidStrategyRequest.setSupportCancelTime(cancelRestrictTime);
                createBidStrategyRequest.setSupportWifi(Objects.equals("Y", wirelessStr) ? RfpConstant.constant_1 : RfpConstant.constant_0);
                createBidRequest.setBidStrategy(createBidStrategyRequest);

                // LRA_S1_RT1_SGL LRA_时段1_房型1_单人入住
                if(StringUtil.isValidString(lraS1Rt1Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.ONE.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(lraS1Rt1Sgl), isIncludeBreakfast,
                            1
                    );
                }

                // 房档1 Season1价格 LRA_S2_RT1_SGL LRA_时段2_房型1_单人入住
                String lraS2Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(94));
                if(StringUtil.isValidString(lraS2Rt1Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.ONE.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(lraS2Rt1Sgl), isIncludeBreakfast,
                            1
                    );
                }
                // LRA_S3_RT1_SGL 时段3_房型1_单人入住
                String lraS3Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(114));
                if(StringUtil.isValidString(lraS3Rt1Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.ONE.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(lraS3Rt1Sgl), isIncludeBreakfast,
                            1
                    );
                }

                // LRA_S1_RT1_DBL Leve1 LRA_时段1_房型1_双人入住
                if(StringUtil.isValidString(lraS1Rt1Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.ONE.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(lraS1Rt1Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // LRA_S2_RT1_DBL Leve1 LRA_时段2_房型1_双人入住
                String lraS2Rt1Dbl = CommonUtil.getCellValue(dataRow.getCell(95));
                if(StringUtil.isValidString(lraS2Rt1Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.ONE.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(lraS2Rt1Dbl), isIncludeBreakfast,
                            2
                    );

                }
                // LRA_S3_RT1_DBL Leve1 LRA_时段3_房型1_双人入住
                String lraS3Rt1Dbl = CommonUtil.getCellValue(dataRow.getCell(115));
                if(StringUtil.isValidString(lraS3Rt1Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.ONE.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(lraS3Rt1Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // NLRA_S1_RT1_SGL 房档1+NLRA+单人入住
                // String nlraS1Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(80));
                if(StringUtil.isValidString(nlraS1Rt1Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.ONE.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(nlraS1Rt1Sgl), isIncludeBreakfast,
                            1
                    );
                }

                // NLRA_S2_RT1_SGL 房档1+NLRA+单人入住
                String nlraS2Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(100));
                if(StringUtil.isValidString(nlraS2Rt1Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.ONE.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(nlraS2Rt1Sgl), isIncludeBreakfast,
                            1
                    );
                }
                // NLRA_S3_RT1_SGL 房档1+NLRA+单人入住
                String nlraS3Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(120));
                if(StringUtil.isValidString(nlraS3Rt1Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.ONE.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(nlraS3Rt1Sgl), isIncludeBreakfast,
                            1
                    );
                }
                // NLRA_S1_RT1_DBL 房档1+NLRA+双人入住
                if(StringUtil.isValidString(nlraS1Rt1Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.ONE.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(nlraS1Rt1Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // NLRA_S2_RT1_DBL 房档1+NLRA+双人入住
                String nlraS2Rt1Dbl = CommonUtil.getCellValue(dataRow.getCell(100));
                if(StringUtil.isValidString(nlraS2Rt1Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.ONE.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(nlraS2Rt1Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // NLRA_S3_RT1_DBL 房档1+NLRA+双人入住
                String nlraS3Rt1Dbl = CommonUtil.getCellValue(dataRow.getCell(121));
                if(StringUtil.isValidString(nlraS3Rt1Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.ONE.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(nlraS3Rt1Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // 房档2
                // LRA_S1_RT2_SGL LRA_时段1_房型2_单人入住
                String lraS1Rt2Sgl = CommonUtil.getCellValue(dataRow.getCell(76));
                if(StringUtil.isValidString(lraS1Rt2Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.TWO.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(lraS1Rt2Sgl), isIncludeBreakfast,
                            1
                    );
                }
                // LRA_S2_RT2_SGL LRA_时段3_房型2_单人入住
                String lraS2Rt2Sgl = CommonUtil.getCellValue(dataRow.getCell(96));
                if(StringUtil.isValidString(lraS2Rt2Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.TWO.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(lraS2Rt2Sgl), isIncludeBreakfast,
                            1
                    );
                }
                // LRA_S3_RT2_SGL LRA_时段3_房型2_单人入住
                String lraS3Rt2Sgl = CommonUtil.getCellValue(dataRow.getCell(116));
                if(StringUtil.isValidString(lraS3Rt2Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.TWO.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(lraS3Rt2Sgl), isIncludeBreakfast,
                            1
                    );
                }
                // LRA_S1_RT2_DBL // LRA_时段1_房型2_双人入住
                String lraS1Rt2Dbl = CommonUtil.getCellValue(dataRow.getCell(77));
                if(StringUtil.isValidString(lraS1Rt2Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.TWO.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(lraS1Rt2Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // LRA_S2_RT2_DBL // LRA_时段1_房型2_双人入住
                String lraS2Rt2Dbl = CommonUtil.getCellValue(dataRow.getCell(97));
                if(StringUtil.isValidString(lraS2Rt2Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.TWO.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(lraS2Rt2Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // LRA_S3_RT2_DBL // LRA_时段1_房型2_双人入住
                String lraS3Rt2Dbl = CommonUtil.getCellValue(dataRow.getCell(117));
                if(StringUtil.isValidString(lraS3Rt2Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.TWO.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(lraS3Rt2Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // 第三批次：房档2+NLRA+单人入住
                // NLRA_S1_RT2_SGL  NLRA_时段1_房型2_单人入住
                String nlraS1Rt2Sgl = CommonUtil.getCellValue(dataRow.getCell(82));
                if(StringUtil.isValidString(nlraS1Rt2Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.TWO.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(nlraS1Rt2Sgl), isIncludeBreakfast,
                            1
                    );
                }
                // NLRA_S2_RT2_SGL  NLRA_时段2_房型2_单人入住
                String nlraS2Rt2Sgl = CommonUtil.getCellValue(dataRow.getCell(102));
                if(StringUtil.isValidString(nlraS2Rt2Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.TWO.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(nlraS2Rt2Sgl), isIncludeBreakfast,
                            1
                    );
                }
                // NLRA_S3_RT2_SGL  NLRA_时段3_房型2_单人入住
                String nlraS3Rt2Sgl = CommonUtil.getCellValue(dataRow.getCell(122));
                if(StringUtil.isValidString(nlraS3Rt2Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.TWO.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(nlraS3Rt2Sgl), isIncludeBreakfast,
                            1
                    );
                }
                //第四批次：房档2+NLRA+双人入住
                //NLRA_S1_RT2_DBL，NLRA_S2_RT2_DBL，NLRA_S3_RT2_DBL
                String nlraS1Rt2Dbl = CommonUtil.getCellValue(dataRow.getCell(83));
                if(StringUtil.isValidString(nlraS1Rt2Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.TWO.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(nlraS1Rt2Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // NLRA_S2_RT2_DBL
                String nlraS2Rt2Dbl = CommonUtil.getCellValue(dataRow.getCell(103));
                if(StringUtil.isValidString(nlraS2Rt2Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.TWO.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(nlraS2Rt2Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // NLRA_S3_RT2_DBL
                String nlraS3Rt2Dbl = CommonUtil.getCellValue(dataRow.getCell(123));
                if(StringUtil.isValidString(nlraS3Rt2Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.TWO.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(nlraS3Rt2Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // 房档三
                // 第一批次：房档3+LRA+单人入住
                //LRA_S1_RT3_SGL，LRA_S2_RT3_SGL，LRA_S3_RT3_SGL
                String lraS1Rt3Sgl = CommonUtil.getCellValue(dataRow.getCell(78));
                if(StringUtil.isValidString(lraS1Rt3Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.THREE.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(lraS1Rt3Sgl), isIncludeBreakfast,
                            1
                    );
                }
                // LRA_S2_RT3_SGL
                String lraS2Rt3Sgl = CommonUtil.getCellValue(dataRow.getCell(98));
                if(StringUtil.isValidString(lraS2Rt3Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.THREE.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(lraS2Rt3Sgl), isIncludeBreakfast,
                            1
                    );
                }
                // LRA_S3_RT3_SGL
                String lraS3Rt3Sgl = CommonUtil.getCellValue(dataRow.getCell(118));
                if(StringUtil.isValidString(lraS3Rt3Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.THREE.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(lraS3Rt3Sgl), isIncludeBreakfast,
                            1
                    );
                }
                // 第二批次：房档3+LRA+双人入住
                //LRA_S1_RT3_DBL，LRA_S2_RT3_DBL，LRA_S3_RT3_DBL
                String lraS1Rt3Dbl = CommonUtil.getCellValue(dataRow.getCell(79));
                if(StringUtil.isValidString(lraS1Rt3Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.THREE.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(lraS1Rt3Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // LRA_S2_RT3_DBL
                String lraS2Rt3Dbl = CommonUtil.getCellValue(dataRow.getCell(99));
                if(StringUtil.isValidString(lraS2Rt3Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.THREE.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(lraS2Rt3Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // LRA_S3_RT3_DBL
                String lraS3Rt3Dbl = CommonUtil.getCellValue(dataRow.getCell(119));
                if(StringUtil.isValidString(lraS3Rt3Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.THREE.key,
                            RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(lraS3Rt3Dbl), isIncludeBreakfast,
                            2
                    );
                }
                //第三批次：房档3+NLRA+单人入住
                //NLRA_S1_RT3_SGL，NLRA_S2_RT3_SGL，NLRA_S3_RT3_SGL
                String nlraS1Rt3Sgl = CommonUtil.getCellValue(dataRow.getCell(84));
                if(StringUtil.isValidString(nlraS1Rt3Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.THREE.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(nlraS1Rt3Sgl), isIncludeBreakfast,
                            1
                    );
                }
                // NLRA_S2_RT3_SGL
                String nlraS2Rt3Sgl = CommonUtil.getCellValue(dataRow.getCell(104));
                if(StringUtil.isValidString(nlraS2Rt3Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.THREE.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(nlraS2Rt3Sgl), isIncludeBreakfast,
                            1
                    );
                }
                // NLRA_S3_RT3_SGL
                String nlraS3Rt3Sgl = CommonUtil.getCellValue(dataRow.getCell(124));
                if(StringUtil.isValidString(nlraS3Rt3Sgl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.THREE.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(nlraS3Rt3Sgl), isIncludeBreakfast,
                            1
                    );

                }
                // 第四批次：房档3+NLRA+双人入住
                //NLRA_S1_RT3_DBL，NLRA_S2_RT3_DBL，NLRA_S3_RT3_DBL
                String nlraS1Rt3Dbl = CommonUtil.getCellValue(dataRow.getCell(85));
                if(StringUtil.isValidString(nlraS1Rt3Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.THREE.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(nlraS1Rt3Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // NLRA_S2_RT3_DBL
                String nlraS2Rt3Dbl = CommonUtil.getCellValue(dataRow.getCell(105));
                if(StringUtil.isValidString(nlraS2Rt3Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.THREE.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(nlraS2Rt3Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // NLRA_S3_RT3_DBL
                String nlraS3Rt3Dbl = CommonUtil.getCellValue(dataRow.getCell(125));
                if(StringUtil.isValidString(nlraS3Rt3Dbl)) {
                    addLevelPrice(createBidRequest,
                            RoomLevelEnum.THREE.key,
                            RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(nlraS3Rt3Dbl), isIncludeBreakfast,
                            2
                    );
                }
                // 增加酒店报价
                if(!importHotelDataListMap.containsKey(hotelId)) {
                    importHotelDataListMap.put(hotelId, new ArrayList<>());
                }
                importHotelDataListMap.get(hotelId).add(createBidRequest);
                Map<String, String> hotelLanyonDataMap = new HashMap<>();
                // 增加lanyon 原始数据
                for(Integer index : headerMap.keySet()){
                    String code = headerMap.get(index);
                    String value = CommonUtil.getCellValue2(dataRow.getCell(index));
                    if(code.equals("CANC_POL")){
                        value = getCellCancPol(dataRow.getCell(index));
                    }
                    hotelLanyonDataMap.put(code, value);
                }
                if(!lanyonImportDataMap.containsKey(hotelId)){
                    lanyonImportDataMap.put(hotelId, new ArrayList<>());
                }
                lanyonImportDataMap.get(hotelId).add(hotelLanyonDataMap);
            } catch (Exception e) {
                log.error("导入酒店集团报价异常： {}" ,(i+1),e);
                ImportRowErrorVO importRowErrorVO = new ImportRowErrorVO(i+1);
                importRowErrorVO.setErrorCode(ErrorCode.IMPORT_ROW_EXCEPTION);
                invalidMsgList.add(importRowErrorVO);
                //invalidMsgList.add("第"+ (i+1) + "行导入异常");
            }
            // 多余200错误数据，不检查
            if(invalidMsgList.size() > 200){
                break;
            }
        }

        // 合并酒店包含无早和含早的报价数据
        if(invalidMsgList.size() == 0) {
            for (Long hotelId : importHotelDataListMap.keySet()) {
                List<CreateBidRequest> importHotelDataList = importHotelDataListMap.get(hotelId);
                int hotelIndex = 0;
                try {
                    for (CreateBidRequest importHotelData : importHotelDataList) {
                        if (hotelIndex == 0) {
                            importHotelDataMap.put(hotelId, importHotelData);
                        } else if (hotelIndex == 1) {
                            // 合并报价
                            Map<Integer, List<CreatePriceGroupRequest>> newLevelPriceGroupMap = new HashMap<>();
                            CreateBidRequest firstImportLanyonBidDto = importHotelDataMap.get(hotelId);
                            if (!Objects.equals(firstImportLanyonBidDto.getIsIncludeBreakfast(), importHotelData.getIsIncludeBreakfast())) {
                                Map<Integer, List<CreatePriceGroupRequest>> levelPriceGroupMap1 = firstImportLanyonBidDto.getPriceGroupMap();
                                Map<Integer, List<CreatePriceGroupRequest>> levelPriceGroupMap2 = importHotelData.getPriceGroupMap();
                                Set<Integer> roomLevelNoSet = new HashSet<>();
                                roomLevelNoSet.addAll(levelPriceGroupMap1.keySet());
                                roomLevelNoSet.addAll(levelPriceGroupMap2.keySet());
                                roomLevelNoSet = roomLevelNoSet.stream().sorted().collect(Collectors.toCollection(LinkedHashSet::new));
                                for (Integer roomLevelNo : roomLevelNoSet) {
                                    List<CreatePriceGroupRequest> levelPriceGroupList1 = levelPriceGroupMap1.get(roomLevelNo);
                                    List<CreatePriceGroupRequest> levelPriceGroupList2 = levelPriceGroupMap2.get(roomLevelNo);
                                    newLevelPriceGroupMap.put(roomLevelNo, new ArrayList<>());
                                    if(Objects.equals(firstImportLanyonBidDto.getIsIncludeBreakfast(),RfpConstant.constant_0)){
                                        newLevelPriceGroupMap.get(roomLevelNo).addAll(levelPriceGroupList1);
                                        newLevelPriceGroupMap.get(roomLevelNo).addAll(levelPriceGroupList2);
                                    } else {
                                        newLevelPriceGroupMap.get(roomLevelNo).addAll(levelPriceGroupList2);
                                        newLevelPriceGroupMap.get(roomLevelNo).addAll(levelPriceGroupList1);
                                    }
                                }
                                // 设置合并后的报价
                                importHotelDataMap.get(hotelId).setPriceGroupMap(newLevelPriceGroupMap);
                            } else {
                                log.error("Lanyon导入数据错误，存在相同酒店相同含早或者无早数据1 {}", hotelId);
                                response.setResult(ReturnResultEnum.FAILED.errorNo);
                                response.setMsg(ErrorCode.IMPORT_ROW_HAS_DUPLICATE_HOTEL_BREAKFAST_DATA );
                                return response;

                            }
                            // 最多允许两条
                        } else {

                            //invalidMsgList.add("存在相同酒店相同含早或者无早数据 " + hotelId);
                            log.error("Lanyon导入数据错误，存在相同酒店相同含早或者无早数据2 {}", hotelId);
                            response.setResult(ReturnResultEnum.FAILED.errorNo);
                            response.setMsg(ErrorCode.IMPORT_ROW_HAS_DUPLICATE_HOTEL_BREAKFAST_DATA );
                            return response;
                        }
                        hotelIndex++;
                    }
                } catch (Exception ex) {
                    //invalidMsgList.add("Lanyon导入数据错误，合并相同酒店早餐异常 " + hotelId);
                    log.info("Lanyon导入数据错误，合并相同酒店早餐异常 "+ ExceptionUtility.getDetailedExceptionString(ex));
                    log.error("Lanyon导入数据错误，合并相同酒店早餐异常", ex);
                    response.setResult(ReturnResultEnum.FAILED.errorNo);
                    response.setMsg(ErrorCode.IMPORT_DATA_EXCEPTION );
                    return response;
                }
            }
        }

        if(!invalidMsgList.isEmpty()){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setData(invalidMsgList);
            log.error("检查失败信息：{}", invalidMsgList);
            return response;
        }


        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(invalidMsgList);
        return response;
    }

    private String getCellCancPol(Cell cancPolCell){
        String cancPol = "";
        if(isDateCell(cancPolCell)){
            Date cancPolDateTime = cancPolCell.getDateCellValue();
            cancPol = DateUtil.dateToString(cancPolDateTime, "HH:mm:ss");
        } else {
            cancPol = String.valueOf(cancPolCell.getStringCellValue());
        }
        return cancPol;
    }

    private boolean isDateCell(Cell cancPolCell){
        boolean isDateCell = false;
        try {
            isDateCell = org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cancPolCell);
        } catch (Exception ex){
            log.info(ex.getMessage());
        }
        return isDateCell;
    }

    /**
     * 转换日期格式兼容 Lanyon 和 MYR RFP
     * @param cell
     * @return
     */
    private Date convertCellToDate(Cell cell){
        Date result = null;
        try {
            try {
                result = cell.getDateCellValue();
            } catch (Exception ex) {
                log.debug("convertCellToDate exception ", ex);
                String rawDateString = cell.getStringCellValue();
                String dateString = rawDateString;
                // 格式化 2024/1/1
                if(StringUtils.isNotEmpty(rawDateString) && rawDateString.contains("/")){
                    String[] dataStringArray = rawDateString.split("/");
                    int month = Integer.valueOf(dataStringArray[1]);
                    int day = Integer.valueOf(dataStringArray[2]);
                    dateString = dataStringArray[0] + "-" + String.format("%02d", month) + "-" + String.format("%02d", day);
                }
                result = DateUtil.stringToDate(dateString);
            }
        } catch (Exception ex){
            log.error("convertCellToDate service exception {} ", cell.getColumnIndex(), ex);

        }
        return result;
    }

    /**
     * 检查 Uom值是否合法
     * @param uom
     * @return
     */
    private boolean validateUom(String uom){
        if("P".equalsIgnoreCase(uom) || "F".equalsIgnoreCase(uom) || "N".equalsIgnoreCase(uom) || StringUtil.isEmpty(uom)){
            return true;
        }
        return false;
    }
    private boolean validateIsInclude(String isInclude){
        if("Y".equalsIgnoreCase(isInclude) || "N".equalsIgnoreCase(isInclude) || StringUtil.isEmpty(isInclude)){
            return true;
        }
        return false;
    }

    private void addLevelPrice(CreateBidRequest createBidRequest, Integer levelNo,
                               Integer lra,
                               Integer priceType, BigDecimal price, boolean isIncludeBreakfastBoolean, // 单人入住,双人入住
                               int personCount
    ){
        Integer isIncludeBreakfast = isIncludeBreakfastBoolean ? RfpConstant.constant_1 : RfpConstant.constant_0;
        // 新增房当
        if(!createBidRequest.getPriceGroupMap().containsKey(levelNo)){
            createBidRequest.getPriceGroupMap().put(levelNo, new LinkedList<>());
        }

        // 查询是否存在相同报价组
        CreatePriceGroupRequest createPriceGroupRequest = null;
        List<CreatePriceGroupRequest> importLanyonHotelPriceGroupBidDtoList = createBidRequest.getPriceGroupMap().get(levelNo);
        for(CreatePriceGroupRequest importLanyonHotelPriceGroupBidDto : importLanyonHotelPriceGroupBidDtoList){
            if(Objects.equals(importLanyonHotelPriceGroupBidDto.getLra(), lra) && Objects.equals(importLanyonHotelPriceGroupBidDto.getIsIncludeBreakfast(), isIncludeBreakfast)){
                createPriceGroupRequest = importLanyonHotelPriceGroupBidDto;
            }
        }
        // 不存在相同报价组，初始一个
        if(createPriceGroupRequest == null) {
            createPriceGroupRequest = new CreatePriceGroupRequest();

            // 设置LRA
            createPriceGroupRequest.setLra(lra);
            createPriceGroupRequest.setIsIncludeBreakfast(isIncludeBreakfast);
            // 退订信息
            /**
             importHotelGroupBidDto.setCancelRestrictType(CancelRestrictTypeEnum.PAY_FIRST_NIGHT.key);
             importHotelGroupBidDto.setCancelRestrictTime(cancelRestrictTime);
             importHotelGroupBidDto.setCancelRestrictDay(0);
             **/

            // 初始化价格
            List<CreatePriceRequest> hotelPriceBidDtoList = new LinkedList<>();
            createPriceGroupRequest.setPriceList(hotelPriceBidDtoList);
            createPriceGroupRequest.setRemark("");
            createBidRequest.getPriceGroupMap().get(levelNo).add(createPriceGroupRequest);

        }
        // 设置价格
        boolean hasTheSamePriceType = false;
        for(CreatePriceRequest createPriceRequest : createPriceGroupRequest.getPriceList()){
            if(Objects.equals(createPriceRequest.getPriceType(), priceType)){
                if(personCount == 1) {
                    createPriceRequest.setOnePersonPrice(price);
                } else if(personCount == 2) {
                    createPriceRequest.setTwoPersonPrice(price);
                }
                hasTheSamePriceType = true;
            }
        }
        if(!hasTheSamePriceType){
            CreatePriceRequest importLanyonHotelPriceBidDto = new CreatePriceRequest();
            importLanyonHotelPriceBidDto.setPriceType(priceType);
            if(personCount == 1) {
                importLanyonHotelPriceBidDto.setOnePersonPrice(price);
            } else if(personCount == 2) {
                importLanyonHotelPriceBidDto.setTwoPersonPrice(price);
            }
            createPriceGroupRequest.getPriceList().add(importLanyonHotelPriceBidDto);
        }

    }


    public LanyonImportDataVO queryLanyonImportDataVO(Integer projectId, Long hotelId, int dataType){
        LanyonImportDataEntity lanyonImportData = lanyonImportDataMapper.getLanyonImportData(projectId, hotelId, dataType);
        if(lanyonImportData == null){
            return null;
        }
        LanyonImportDataVO vo = new LanyonImportDataVO();
        vo.setLanyonImportDataId(lanyonImportData.getLanyonImportDataId());
        vo.setProjectId(lanyonImportData.getProjectId());
        vo.setHotelId(lanyonImportData.getHotelId());
        vo.setDataType(lanyonImportData.getDataType());
        vo.setJsonData(lanyonImportData.getJsonData());
        vo.setCreator(lanyonImportData.getCreator());
        vo.setModifyTime(lanyonImportData.getModifyTime());
        vo.setModifier(lanyonImportData.getModifier());
        vo.setModifyTime(lanyonImportData.getModifyTime());
        return vo;

    }



}
