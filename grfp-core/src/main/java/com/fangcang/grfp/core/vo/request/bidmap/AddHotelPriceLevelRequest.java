package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.BidHotelPriceLevelInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("新增或者修改价格房档")
@Getter
@Setter
public class AddHotelPriceLevelRequest extends BaseVO {

    @ApiModelProperty("项目意向酒店ID")
    @NotNull
    private Integer projectIntentHotelId;

    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;

    @ApiModelProperty("酒店ID")
    @NotNull
    private Long hotelId;

    @ApiModelProperty("价格房档信息")
    @NotNull
    private BidHotelPriceLevelInfoVO priceLevelInfo;
}
