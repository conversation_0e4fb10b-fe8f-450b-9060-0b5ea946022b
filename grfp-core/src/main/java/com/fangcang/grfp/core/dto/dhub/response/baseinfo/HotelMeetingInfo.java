package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import lombok.Data;

import java.util.List;

@Data
public class HotelMeetingInfo {

    /**
     * 酒店id
     */
    private Integer hotelId;

    /**
     * 会议室id
     */
    private Integer meetingTypeId;

    /**
     * 会议室名称
     */
    private String meetingTypeName;

    /**
     * 会议室长度
     */
    private Double meetingL;

    /**
     * 会议室宽度
     */
    private Double meetingW;

    /**
     * 会议室高度
     */
    private Double meetingH;

    /**
     * 最小容纳人数
     */
    private Integer minPeoples;

    /**
     * 最大容纳人数
     */
    private Integer maxPeoples;

    /**
     * 会议室楼层
     */
    private String layer;

    /**
     * 会议室面积
     */
    private String area;

    /**
     * 有无立柱
     */
    private Integer frameIn;

    /**
     * 上午段
     */
    private String morningSection;

    /**
     * 下午段
     */
    private String afternoonSection;

    /**
     * 晚上段
     */
    private String nightSection;

    private String notes;

    /**
     * 全体段
     */
    private String allDaySection;

    /**
     * 摆放类型数组
     */
    private String putTypes;

    /**
     * 会议室图片列表
     */
    private List<MeetingImg> meetingImgDTOList;

}
