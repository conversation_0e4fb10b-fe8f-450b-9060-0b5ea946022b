package com.fangcang.grfp.core.vo.request.bid;

import cn.hutool.core.date.DateUtil;
import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Objects;

@Getter
@Setter
@ApiModel(description = "查询导入报价任务请求")
public class ImportBidTaskListRequest extends PageQuery {

    @ApiModelProperty(value = "请求编号, 导出的时候才需要填")
    private Long requestNo;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "酒店 ID")
    private Long hotelId;

    @ApiModelProperty(value = "导入酒店关键字")
    private String propName;

    @ApiModelProperty(value = "创建时间, 开始范围")
    private Date createDateStart;

    @ApiModelProperty(value = "创建时间, 结束范围")
    private Date createDateEnd;

    @ApiModelProperty(value = "是否校验错误: 1-是 0-否")
    private Integer isValidateError;

    @ApiModelProperty(value = "报价生成状态: 0-待生成 1-已生成")
    private Integer generateBidStatus;

    public Date getCreateDateEnd() {
        return Objects.nonNull(createDateEnd) ? DateUtil.endOfDay(createDateEnd) : null;
    }
}
