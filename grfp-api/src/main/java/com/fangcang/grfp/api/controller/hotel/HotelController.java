package com.fangcang.grfp.api.controller.hotel;

import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.hotel.service.HotelService;
import com.fangcang.grfp.api.controller.hotel.vo.GetBidTemplateRequest;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.IdRequest;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.manager.BidMapManager;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.vo.BidHotelPriceGroupVO;
import com.fangcang.grfp.core.vo.BidHotelPriceLevelInfoVO;
import com.fangcang.grfp.core.vo.request.IntentHotelRequest;
import com.fangcang.grfp.core.vo.request.bidmap.*;
import com.fangcang.grfp.core.vo.request.hotel.ProjectIntentHotelRequest;
import com.fangcang.grfp.core.vo.response.bidmap.GoToHotelBidResponse;
import com.fangcang.grfp.core.vo.response.bidmap.HotelBidDetailResponse;
import com.fangcang.grfp.core.vo.response.hotel.HotelRoomTypeVO;
import com.fangcang.grfp.core.vo.response.hotel.ProjectHotelTentResponse;
import com.fangcang.grfp.core.vo.response.hotelgroup.QueryBidStatCountVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@Api(tags="酒店")
@RestController
@RequestMapping("/Api/Hotel")
public class HotelController extends BaseController {

    @Autowired
    private HotelService hotelService;
    @Autowired
    private BidMapManager bidMapManager;

    @ApiOperation("报价项目列表")
    @PostMapping("/SelectHotelTentPage")
    @ResponseBody
    public Result<PageVO<ProjectHotelTentResponse>> selectHotelTentPage(HttpServletRequest request, @RequestBody ProjectIntentHotelRequest projectIntentHotelRequest) {
        return Result.ok(hotelService.selectProjectHotelTentList(request, projectIntentHotelRequest));
    }

    @ApiOperation("酒店查询报价统计数据")
    @PostMapping("/QueryBidStatCount")
    @ResponseBody
    public Result<QueryBidStatCountVO> queryBidStatCount(HttpServletRequest request) {
        return Result.ok(hotelService.queryBidStatCount(request));
    }

    @ApiOperation("地图酒店去报价")
    @PostMapping("/GoToHotelBid")
    public Result<GoToHotelBidResponse> goToHotelBid(HttpServletRequest request, @Valid @RequestBody GoToHotelBidRequest req) {
        return Result.ok(bidMapManager.goToHotelBid(request, req));
    }

    @ApiOperation("检查报价项目信息")
    @PostMapping("/IsHotelSatisfyProjectBaseInfo")
    public Result<Void> isHotelSatisfyProjectBaseInfo(HttpServletRequest request, @Valid @RequestBody IntentHotelRequest req) {
        bidMapManager.isHotelSatisfyProjectBaseInfo(req);
        return Result.ok();
    }


    @ApiOperation("新增酒店集团房档 返回房档ID")
    @PostMapping("/AddHotelPriceLevel")
    @UserAuditLog("酒店-新增酒店集团房档")
    public Result<Integer> addHotelPriceLevel(HttpServletRequest request, @Valid @RequestBody AddHotelPriceLevelRequest req) {
        return Result.ok(bidMapManager.addHotelPriceLevel(request, req));
    }

    @ApiOperation("查询酒店集团报价房档列表 返回房档")
    @PostMapping("/QueryBidHotelPriceLevelList")
    public Result<List<BidHotelPriceLevelInfoVO>> queryHotelPriceLevelList(HttpServletRequest request, @Valid @RequestBody QueryHotelPriceLevelListRequest req) {
        return Result.ok(bidMapManager.querBidHotelPriceLevelInfoList(UserSession.get(), AppUtility.getRequestHeaderLanguage(request), req.getProjectIntentHotelId(), null, req.getHotelId(), null));
    }


    @ApiOperation("新增活修改酒店报价房档房型 返回房档ID")
    @PostMapping("/AddOrUpdateHotelPriceLevelRoomInfo")
    @UserAuditLog("酒店-新增活修改酒店报价房档房型")
    public Result<Integer> addOrUpdateHotelPriceLevelRoomInfo(HttpServletRequest request, @Valid @RequestBody AddHotelPriceLevelRoomRequest req) {
        return Result.ok(bidMapManager.addOrUpdateHotelPriceLevelRoomInfo(request, req));
    }

    @ApiOperation("新增或者修改酒店报价价格组 返回价格组ID")
    @PostMapping("/AddOrUpdateHotelPriceGroup")
    @UserAuditLog("酒店-新增或者修改酒店报价价格组")
    public Result<Integer> addOrUpdateHotelPriceGroup(HttpServletRequest request, @Valid @RequestBody BidHotelPriceGroupVO req) {
        return Result.ok(bidMapManager.addOrUpdateHotelPriceGroup(request, req));
    }

    @ApiOperation("删除酒店报价价格组 传hotelPriceGroupId")
    @PostMapping("/DeleteHotelPriceGroup")
    @UserAuditLog("酒店-删除酒店报价价格组")
    public Result<Void> deleteHotelPriceGroup(HttpServletRequest request, @Valid @RequestBody IdRequest<Integer> req) {
        bidMapManager.deleteHotelPriceGroup(request, req);
        return Result.ok();
    }

    @ApiOperation("提交酒店报价")
    @PostMapping("/SubmitHotelBid")
    @UserAuditLog("酒店-提交酒店报价")
    public Result<Integer> submitHotelBid(HttpServletRequest request, @Valid @RequestBody SubmitHotelBidRequest req) {
        return Result.ok(bidMapManager.submitHotelBid(request, req));
    }

    @ApiOperation("保存酒店报价模板")
    @PostMapping("/SaveBidTemplate")
    @UserAuditLog("酒店-保存酒店报价模板")
    public Result<Void> saveBidTemplate(HttpServletRequest request, @RequestBody SubmitHotelBidRequest req) {
        bidMapManager.saveBidTemplate(request, req);
        return Result.ok();
    }

    @ApiOperation("获取酒店报价模板 ID:projectIntentHotelId")
    @PostMapping("/GetBidTemplate")
    public Result<SubmitHotelBidRequest> getBidTemplate(HttpServletRequest request, @Valid @RequestBody GetBidTemplateRequest req) {
        return Result.ok(bidMapManager.getBidTemplate(req.getProjectIntentHotelId()));
    }

    @ApiOperation("酒店查看报价详情")
    @PostMapping("/QueryHotelBidDetail")
    public Result<HotelBidDetailResponse> queryHotelBidDetail(HttpServletRequest request, @Valid @RequestBody HotelBidDetailRequest req) {
        return Result.ok(bidMapManager.queryHotelBidDetail(request, req));
    }

    @ApiOperation("修改酒店销售人员")
    @PostMapping("/UpdateBidHotelSales")
    @UserAuditLog("酒店-修改酒店销售人员")
    public Result<Void> updateBidHotelSales(HttpServletRequest request, @Valid @RequestBody UpdateBidHotelSalesUserRequest req) {
        bidMapManager.updateBidHotelSales(req);
        return Result.ok();
    }

    @ApiOperation("酒店拒绝议价")
    @PostMapping("/RejectNegotiation")
    @UserAuditLog("酒店-酒店拒绝议价")
    public Result<Void> rejectNegotiation(HttpServletRequest request, @Valid @RequestBody RejectNegotiationRequest req) {
        bidMapManager.rejectNegotiation(req);
        return Result.ok();
    }

    @ApiOperation("查询酒店房型信息")
    @PostMapping("/QueryHotelRoomTypeInfo")
    public Result<HotelRoomTypeVO> queryHotelRoomTypeInfo(HttpServletRequest request, @Valid @RequestBody IdRequest<Long> req) {
        return Result.ok(hotelService.queryHotelRoomTypeInfo(request, req.getId()));
    }

}
