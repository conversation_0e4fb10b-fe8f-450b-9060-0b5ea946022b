package com.fangcang.grfp.core.mapper;

import com.fangcang.grfp.core.entity.HotelGroupDefaultUnapplicableDayEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.vo.BidUnApplicableDayVO;
import com.fangcang.grfp.core.vo.HotelGroupDefaultApplicableDayVO;
import com.fangcang.grfp.core.vo.HotelGroupDefaultUnApplicableDayVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 项目意向酒店集团默认不适用日期 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface HotelGroupDefaultUnapplicableDayMapper extends BaseMapper<HotelGroupDefaultUnapplicableDayEntity> {

    List<HotelGroupDefaultUnApplicableDayVO> queryDefaultUnApplicableDayInfo(Integer projectIntentHotelGroupId);

    int deleteByProjectIntentHotelGroupId(int projectIntentHotelGroupId);

    int batchInsert(@Param("list") List<HotelGroupDefaultUnapplicableDayEntity> list);

    List<BidUnApplicableDayVO> queryDefaultUnApplicableDayVOList(Integer projectIntentHotelGroupId);
}
