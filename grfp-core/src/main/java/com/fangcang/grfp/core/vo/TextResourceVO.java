package com.fangcang.grfp.core.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.entity.TextResourceEntity;
import com.fangcang.grfp.core.enums.LanguageEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

@ApiModel("文字资源")
@Getter
@Setter
public class TextResourceVO extends BaseVO {
	// ---------------------------------------------------------------------------------------------------- Private Member Variables


	@ApiModelProperty("文字资源编号")
    private String textResourceCode;
	/**
	 * 文字资源 文字资源类型 1:网页文本，2：提示信息
	 */
	@ApiModelProperty("文字资源 文字资源类型 1:网页文本，2：提示信息")
	private Integer textResourceType;
	/**
	 * 英文文字翻译
	 */
	@ApiModelProperty("英文")
	private String valueEnUs;

	/**
	 * 中文文字翻译
	 */
	@ApiModelProperty("中文")
	private String valueZhCn;


	// ---------------------------------------------------------------------------------------------------- Constructor
	
	public TextResourceVO() {
		super();

	}
	
	public TextResourceVO(TextResourceEntity textResourceEntity) {
		super();

		BeanUtils.copyProperties(textResourceEntity, this);
	}
	// ---------------------------------------------------------------------------------------------------- Getters/Setters
	


	// ---------------------------------------------------------------------------------------------------- Public Methods

	public String getTextResourceValue(int languageId) {
		if(languageId == LanguageEnum.EN_US.key){
			return valueEnUs;
		} else if(languageId == LanguageEnum.ZH_CN.key){
			return valueZhCn;
		} else {
			return valueEnUs;
		}
	}

}
