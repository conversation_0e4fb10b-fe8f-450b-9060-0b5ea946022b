package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.core.entity.ProjectPoiEntity;
import com.fangcang.grfp.core.vo.response.bidmap.ProjectPoiInfoResponse;
import com.fangcang.grfp.core.vo.response.project.ProjectPoiVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 项目 POI 表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
public interface ProjectPoiMapper extends BaseMapper<ProjectPoiEntity> {

    /**
     * 批量插入
     */
    void batchInsert(@Param("list") List<ProjectPoiEntity> list);

    /**
     * 根据项目 ID 和 POI ID 删除
     */
    default void deleteByProjectIdAndPoiId(Integer projectId, Long poiId) {
        LambdaQueryWrapper<ProjectPoiEntity> queryWrapper = Wrappers.lambdaQuery(ProjectPoiEntity.class);
        queryWrapper.eq(ProjectPoiEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectPoiEntity::getPoiId, poiId);
        delete(queryWrapper);
    }

    /**
     * 根据项目 ID 查询 POI 信息
     */
    List<ProjectPoiVO> selectPoiInfoByProjectId(@Param("projectId")Integer projectId, @Param("cityCode") String cityCode);

    /**
     * 查询项目地图poi信息
     */
    List<ProjectPoiInfoResponse> selectMapProjectPoiInfo(@Param("projectId")Integer projectId, @Param("poiId") Integer poiId, @Param("poiName") String poiName, @Param("cityCode") String cityCode, @Param("distance") Integer distance);

    /**
     * 查询项目地图poi信息
     */
    Page<ProjectPoiInfoResponse> selectMapProjectPoiInfoPage(IPage<?> page, @Param("projectId")Integer projectId, @Param("poiName") String poiName, @Param("cityCode") String cityCode, @Param("distance") Integer distance);

    /**
     * 根据项目 ID 查询 POI 信息
     */
    List<ProjectPoiInfoResponse> selectProjectPoiInfo(@Param("projectId") Integer projectId);

    /**
     * 清空城市 POI 统计信息
     */
    void clearCityPoiStat(@Param("projectId") Integer projectId);

    /**
     * 更新项目 POI 统计信息
     */
    void updateProjectPoiStatInfo(@Param("req") ProjectPoiEntity projectPoi);

    void updateProjectPoi3KmStatInfo(@Param("projectPoi") ProjectPoiEntity projectPoi);
}
