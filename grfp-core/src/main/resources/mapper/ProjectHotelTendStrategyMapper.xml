<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectHotelTendStrategyMapper">
    <insert id="upsert">
        INSERT INTO t_project_hotel_tend_strategy (project_id,
                                                   support_vcc_pay,
                                                   support_pay_at_hotel,
                                                   support_checkin_info,
                                                   support_no_guarantee,
                                                   support_pay_early_checkout,
                                                   support_include_tax_service,
                                                   support_wifi,
                                                   support_price_limit,
                                                   limit_min_price,
                                                   limit_max_price,
                                                   limit_price_currency_code,
                                                   support_cancel,
                                                   support_cancel_day,
                                                   support_cancel_time,
                                                   support_lra,
                                                   support_max_not_applicable_day,
                                                   max_not_applicable_day,
                                                   support_max_room_type_count,
                                                   max_room_type_count,
                                                   support_season_day_limit,
                                                   max_season_day,
                                                   is_require_no_breakfast_single,
                                                   is_require_no_breakfast_double,
                                                   is_require_with_breakfast_single,
                                                   is_require_with_breakfast_double,
                                                   is_recommend_no_breakfast_single,
                                                   is_recommend_no_breakfast_double,
                                                   is_recommend_with_breakfast_single,
                                                   is_recommend_with_breakfast_double,
                                                   is_include_all_weekly_day,
                                                   is_require_tax_details,
                                                   is_require_include_all_taxes,
                                                   is_recommend_include_all_taxes,
                                                   creator)
        VALUES (#{entity.projectId},
                #{entity.supportVccPay},
                #{entity.supportPayAtHotel},
                #{entity.supportCheckinInfo},
                #{entity.supportNoGuarantee},
                #{entity.supportPayEarlyCheckout},
                #{entity.supportIncludeTaxService},
                #{entity.supportWifi},
                #{entity.supportPriceLimit},
                #{entity.limitMinPrice},
                #{entity.limitMaxPrice},
                #{entity.limitPriceCurrencyCode},
                #{entity.supportCancel},
                #{entity.supportCancelDay},
                #{entity.supportCancelTime},
                #{entity.supportLra},
                #{entity.supportMaxNotApplicableDay},
                #{entity.maxNotApplicableDay},
                #{entity.supportMaxRoomTypeCount},
                #{entity.maxRoomTypeCount},
                #{entity.supportSeasonDayLimit},
                #{entity.maxSeasonDay},
                #{entity.isRequireNoBreakfastSingle},
                #{entity.isRequireNoBreakfastDouble},
                #{entity.isRequireWithBreakfastSingle},
                #{entity.isRequireWithBreakfastDouble},
                #{entity.isRecommendNoBreakfastSingle},
                #{entity.isRecommendNoBreakfastDouble},
                #{entity.isRecommendWithBreakfastSingle},
                #{entity.isRecommendWithBreakfastDouble},
                #{entity.isIncludeAllWeeklyDay},
                #{entity.isRequireTaxDetails},
                #{entity.isRequireIncludeAllTaxes},
                #{entity.isRecommendIncludeAllTaxes},
                #{entity.creator})
        ON DUPLICATE KEY UPDATE support_vcc_pay                    = VALUES(support_vcc_pay),
                                support_pay_at_hotel               = VALUES(support_pay_at_hotel),
                                support_checkin_info               = VALUES(support_checkin_info),
                                support_no_guarantee               = VALUES(support_no_guarantee),
                                support_pay_early_checkout         = VALUES(support_pay_early_checkout),
                                support_include_tax_service        = VALUES(support_include_tax_service),
                                support_wifi                       = VALUES(support_wifi),
                                support_price_limit                = VALUES(support_price_limit),
                                limit_min_price                    = VALUES(limit_min_price),
                                limit_max_price                    = VALUES(limit_max_price),
                                limit_price_currency_code          = VALUES(limit_price_currency_code),
                                support_cancel                     = VALUES(support_cancel),
                                support_cancel_day                 = VALUES(support_cancel_day),
                                support_cancel_time                = VALUES(support_cancel_time),
                                support_lra                        = VALUES(support_lra),
                                support_max_not_applicable_day     = VALUES(support_max_not_applicable_day),
                                max_not_applicable_day             = VALUES(max_not_applicable_day),
                                support_max_room_type_count        = VALUES(support_max_room_type_count),
                                max_room_type_count                = VALUES(max_room_type_count),
                                support_season_day_limit           = VALUES(support_season_day_limit),
                                max_season_day                     = VALUES(max_season_day),
                                is_require_no_breakfast_single     = VALUES(is_require_no_breakfast_single),
                                is_require_no_breakfast_double     = VALUES(is_require_no_breakfast_double),
                                is_require_with_breakfast_single   = VALUES(is_require_with_breakfast_single),
                                is_require_with_breakfast_double   = VALUES(is_require_with_breakfast_double),
                                is_recommend_no_breakfast_single   = VALUES(is_recommend_no_breakfast_single),
                                is_recommend_no_breakfast_double   = VALUES(is_recommend_no_breakfast_double),
                                is_recommend_with_breakfast_single = VALUES(is_recommend_with_breakfast_single),
                                is_recommend_with_breakfast_double = VALUES(is_recommend_with_breakfast_double),
                                is_include_all_weekly_day          = VALUES(is_include_all_weekly_day),
                                is_require_tax_details             = VALUES(is_require_tax_details),
                                is_require_include_all_taxes       = VALUES(is_require_include_all_taxes),
                                is_recommend_include_all_taxes     = VALUES(is_recommend_include_all_taxes),
                                modifier                           = #{entity.modifier}
    </insert>

    <select id="selectByProjectId" resultType="com.fangcang.grfp.core.vo.response.project.ProjectHotelTendStrategyVO">
        select * from t_project_hotel_tend_strategy where project_id = #{projectId}
    </select>
</mapper>
