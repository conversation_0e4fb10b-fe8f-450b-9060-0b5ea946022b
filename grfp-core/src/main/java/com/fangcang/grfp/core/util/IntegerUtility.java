package com.fangcang.grfp.core.util;


public class IntegerUtility {
	
	// ---------------------------------------------------------------------------------------------------- Public Static Method
	
	public static int parseNullOrEmptyString(String text, int valueForNullAndEmpty) {
		int result = valueForNullAndEmpty;
		
		if (text != null) {
			if (!text.equals("")) {
				try {
					result = Integer.parseInt(text);
				} catch (NumberFormatException ex) {
					result = valueForNullAndEmpty;
				}
			}
		}
		
		return result;		
	}

	public static int parseNullOrEmptyString(String text) {
		return parseNullOrEmptyString(text, -1);
	}
	
	public static boolean validateString(String text) {
		boolean result = true;
		
		try {
			Integer.parseInt(text);
		} catch (NumberFormatException ex) {
			result = false;
		} catch (Exception ex2) {
			result = false;
		}
		
		return result;		
	}
}
