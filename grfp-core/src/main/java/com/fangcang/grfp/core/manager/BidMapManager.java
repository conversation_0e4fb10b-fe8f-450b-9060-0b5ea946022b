package com.fangcang.grfp.core.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.core.base.IdRequest;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Response;
import com.fangcang.grfp.core.bo.RedisService;
import com.fangcang.grfp.core.cached.*;
import com.fangcang.grfp.core.config.ThreadPoolAutoConfiguration;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.constant.RedisConstant;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.HotelImage;
import com.fangcang.grfp.core.entity.*;
import com.fangcang.grfp.core.enums.ApplicableWeeksTypeEnum;
import com.fangcang.grfp.core.enums.*;
import com.fangcang.grfp.core.mapper.*;
import com.fangcang.grfp.core.oss.OssManager;
import com.fangcang.grfp.core.thread.ProjectHotelLastYearBidInfoQueryThread;
import com.fangcang.grfp.core.thread.ProjectHotelPriceRoomTypeQueryThread;
import com.fangcang.grfp.core.thread.ProjectRecommendHotelQueryThread;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.*;
import com.fangcang.grfp.core.vo.*;
import com.fangcang.grfp.core.vo.request.IntentHotelRequest;
import com.fangcang.grfp.core.vo.request.QueryBidMapHotelInfoRequest;
import com.fangcang.grfp.core.vo.request.QueryHotelGroupBidMapHotelListRequest;
import com.fangcang.grfp.core.vo.request.bidmap.*;
import com.fangcang.grfp.core.vo.response.HexagonsStat;
import com.fangcang.grfp.core.vo.response.HotelLowestPricesResponse;
import com.fangcang.grfp.core.vo.response.HotelResponse;
import com.fangcang.grfp.core.vo.response.bidmap.*;
import com.fangcang.grfp.core.vo.response.bidprice.PriceApplicableRoomInfoResponse;
import com.fangcang.grfp.core.vo.response.hotel.RoomNameInfoVO;
import com.fangcang.grfp.core.vo.response.hotelprice.HotelMinPriceResponse;
import com.fangcang.grfp.core.vo.response.project.*;
import com.fangcang.grfp.core.vo.response.recommendhotel.BidRecommendHotelInfoQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 报价地图公共方法
 */
@Component
@Slf4j
public class BidMapManager {

    @Resource
    private ProjectIntentHotelMapper projectIntentHotelMapper;
    @Resource
    private RecommendHotelMapper recommendHotelMapper;
    @Resource
    private OrgMapper orgMapper;
    @Resource
    private HotelHexagonLngLatManager hotelHexagonLngLatManager;
    @Resource
    private CachedHotelGroupService cachedHotelGroupService;
    @Resource
    private CachedProjectService cachedProjectService;
    @Resource
    private CachedHotelService cachedHotelService;
    @Resource
    private ProjectLastYearCityStatMapper projectLastYearCityStatMapper;
    @Resource
    private ProjectHotelHistoryDataMapper projectHotelHistoryDataMapper;
    @Resource
    private ProjectPoiMapper projectPoiMapper;
    @Resource
    private ProjectInviteHotelMapper projectInviteHotelMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectIntentHotelGroupMapper projectIntentHotelGroupMapper;
    @Resource
    private ProjectCustomTendStrategyMapper projectCustomTendStrategyMapper;
    @Resource
    private ProjectHotelTendStrategyMapper projectHotelTendStrategyMapper;
    @Resource
    private HGroupDefaultCusStrategyMapper hGroupDefaultCusStrategyMapper;
    @Resource
    private HotelGroupDefaultApplicableDayMapper hotelGroupDefaultApplicableDayMapper;
    @Resource
    private HotelGroupDefaultUnapplicableDayMapper hotelGroupDefaultUnapplicableDayMapper;
    @Resource
    private ProjectCustomBidStrategyMapper projectCustomBidStrategyMapper;
    @Resource
    private ProjectHotelBidStrategyMapper projectHotelBidStrategyMapper;
    @Resource
    private PriceApplicableDayMapper priceApplicableDayMapper;
    @Resource
    private PriceUnapplicableDayMapper priceUnapplicableDayMapper;
    @Resource
    private PriceApplicableRoomMapper priceApplicableRoomMapper;
    @Resource
    private ProjectHotelTaxSettingsMapper projectHotelTaxSettingsMapper;
    @Resource
    private ProjectHotelPriceLevelMapper projectHotelPriceLevelMapper;
    @Resource
    private ProjectHotelPriceGroupMapper projectHotelPriceGroupMapper;
    @Resource
    private ProjectHotelPriceMapper projectHotelPriceMapper;
    @Resource
    private HotelManager hotelManager;
    @Resource
    private HotelMapper hotelMapper;
    @Resource
    private ProjectHotelTendWeightMapper projectHotelTendWeightMapper;
    @Resource
    private BidOperateLogMapper bidOperateLogMapper;
    @Resource
    private ProjectHotelBidTempInfoMapper projectHotelBidTempInfoMapper;
    @Resource
    private CachedProjectIntentHotelService cachedProjectIntentHotelService;
    @Resource(name = ThreadPoolAutoConfiguration.QUERY_BID_HOTEL_INFO_EXECUTOR_NAME)
    private Executor queryBidHotelInfoExecutor;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ProjectHotelRemarkMapper projectHotelRemarkMapper;
    @Resource
    private OssManager ossManager;
    @Resource
    private OrgRelatedHotelBrandMapper orgRelatedHotelBrandMapper;
    @Resource
    private LanyonImportDataMapper lanyonImportDataMapper;
    @Resource
    private ProjectManager projectManager;
    @Resource
    private CustomBidStrategyOptionMapper customBidStrategyOptionMapper;
    @Resource
    private ProjectCustomStrategyOptionMapper projectCustomStrategyOptionMapper;
    @Resource
    private HGroupDefaultCusStrategyOptionMapper hGroupDefaultCusStrategyOptionMapper;
    @Resource
    private MailManager mailManager;
    @Autowired
    private CachedCurrencyService cachedCurrencyService;
    @Resource
    private BidManager bidManager;


    /**
     * 通知酒店报价
     */
    public Integer notifyHotelBid(QueryBidMapHotelInfoRequest req) {
        UserSession userSession = UserSession.get();
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.queryByProjectAndHotelId(req.getProjectId(), req.getHotelId());
        // 不存在建立邀约关系
        if (projectIntentHotel == null) {
            projectIntentHotel = new ProjectIntentHotelEntity();
            projectIntentHotel.setProjectId(req.getProjectId());
            projectIntentHotel.setHotelId(req.getHotelId());
            projectIntentHotel.setInviteStatus(RfpConstant.constant_1);
            projectIntentHotel.setLastInviteTime(new Date());
            projectIntentHotel.setBidState(HotelBidStateEnum.NO_BID.bidState);
            projectIntentHotel.setSendMailStatus(RfpConstant.constant_0);
            projectIntentHotel.setIsUpload(RfpConstant.constant_0);
            projectIntentHotel.setHotelServicePoints(new BigDecimal(100));
            projectIntentHotel.setCreator(userSession.getUsername());
            List<RecommendHotelEntity> recommendHotels = recommendHotelMapper.selectRecommendHotelByHotelIds(Arrays.asList(req.getHotelId()));
            if (CollectionUtils.isNotEmpty(recommendHotels)) {
                for (RecommendHotelEntity recommendHotel : recommendHotels) {
                    projectIntentHotel.setHotelSalesContactName(recommendHotel.getContactName());
                    projectIntentHotel.setHotelSalesContactMobile(recommendHotel.getContactMobile());
                    projectIntentHotel.setHotelSalesContactEmail(recommendHotel.getContactEmail());
                }
            } else {
                OrgEntity org = orgMapper.getOrgByHotelId(req.getHotelId());
                if (org != null) {
                    projectIntentHotel.setHotelSalesContactName(org.getContactName());
                    projectIntentHotel.setHotelSalesContactMobile(org.getContactMobile());
                    projectIntentHotel.setHotelSalesContactEmail(org.getContactEmail());
                }
            }
            projectIntentHotelMapper.insert(projectIntentHotel);
        }
        return projectIntentHotel.getProjectIntentHotelId();
    }

    /**
     * 查询地图推荐酒店
     */
    public PageVO<BidRecommendHotelInfoQueryResponse> queryBidMapRecommendHotelList(HttpServletRequest request, QueryHotelGroupBidMapHotelListRequest req){
        // 获取酒店集团用户信息
        UserSession userSession = UserSession.get();
        int languageId = GenericAppUtility.getRequestHeaderLanguage(request);

        // 同步酒店信息
        HotelEntity queryHotel = null;
        if(req.getHotelId() != null){
            queryHotel = hotelMapper.selectById(req.getHotelId());
            if(queryHotel == null) {
                List<HotelEntity> hotelList = hotelManager.queryHotelBasicInfo(Collections.singletonList(req.getHotelId()), true);
                if(CollectionUtils.isNotEmpty(hotelList)){
                    queryHotel = hotelList.get(0);
                }
            }
        }
        // 酒店集团设置相关查询条件
        if(Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.HOTELGROUP.key)) {
            HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfoVO = cachedHotelGroupService.getHotelGroupUserRelatedInfoVO(userSession.getUserOrg().getOrgId(), UserUtility.getEmployUserId(userSession));
            req.setOrgId(hotelGroupUserRelatedInfoVO.getHotelGroupOrgId());
            req.setHotelGroupBrandIdList(hotelGroupUserRelatedInfoVO.getHotelBrandIdSet());
        }

        // 查询推荐酒单
        Page<BidRecommendHotelInfoQueryResponse> page = new Page<>(req.getPageIndex(), req.getPageSize());
        projectIntentHotelMapper.queryHotelGroupRecommendHotelInfo(page, req);

        handlerData(languageId, req, page, queryHotel);

        return new PageVO<>(page.getTotal(), page.getPages(), page.getRecords());
    }

    private void handlerData(int languageId, QueryHotelGroupBidMapHotelListRequest req, Page<BidRecommendHotelInfoQueryResponse> page, HotelEntity queryHotel){
        // 若精确选中酒店不在酒店推荐列表，则表示酒店集团要替非推荐酒店报价，在酒店推荐页签显示所选酒店，推荐等级为空，地图显示所选酒店为我的酒店位置
        if(req.getHotelId() != null && CollectionUtils.isEmpty(page.getRecords()) && queryHotel != null){
            BidRecommendHotelInfoQueryResponse bidRecommendHotelInfoQueryResponse = new BidRecommendHotelInfoQueryResponse();
            bidRecommendHotelInfoQueryResponse.setProjectId(req.getProjectId());
           ;
            BeanUtils.copyProperties(queryHotel, bidRecommendHotelInfoQueryResponse);
            bidRecommendHotelInfoQueryResponse.setHotelName(HotelUtility.getHotelName(languageId, queryHotel));
            List<BidRecommendHotelInfoQueryResponse> records = new ArrayList<>();
            records.add(bidRecommendHotelInfoQueryResponse);
            page.setRecords(records);
            page.setTotal(1);
            page.setPages(1);

        }
        if(CollectionUtils.isNotEmpty(page.getRecords())){
            page.getRecords().forEach(item -> {
                item.setHotelStarName(HotelStarEnum.getTextByKey(item.getHotelStar(), languageId));
                item.setHotelName(GenericAppUtility.getName(languageId, item.getHotelId(), item.getNameEnUs(), item.getNameZhCn()));
                if(item.getLngGoogle() == null || item.getLatGoogle() == null){
                    double[]  lngLatGoogleArrays = CoordinateUtils.bd09ToGcj02(item.getLngBaidu().doubleValue(), item.getLatBaidu().doubleValue());
                    item.setLngGoogle(BigDecimal.valueOf(lngLatGoogleArrays[0]));
                    item.setLatGoogle(BigDecimal.valueOf(lngLatGoogleArrays[1]));
                }
            });
        }
    }

    /**
     * 查询邀请酒店
     */
    public PageVO<BidRecommendHotelInfoQueryResponse> queryBidMapInvitedHotelList(HttpServletRequest request, QueryHotelGroupBidMapHotelListRequest req){
        // 获取酒店集团用户信息
        UserSession userSession = UserSession.get();
        int languageId = GenericAppUtility.getRequestHeaderLanguage(request);

        // 同步酒店信息
        HotelEntity queryHotel = null;
        if(req.getHotelId() != null){
            queryHotel = hotelMapper.selectById(req.getHotelId());
            if(queryHotel == null) {
                List<HotelEntity> hotelList = hotelManager.queryHotelBasicInfo(LanguageEnum.getEnumByKey(languageId), Collections.singletonList(req.getHotelId()), true);
                if(CollectionUtils.isNotEmpty(hotelList)){
                    queryHotel = hotelList.get(0);
                }
            }
        }
        if(Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.HOTELGROUP.key)) {
            HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfoVO = GenericAppUtility.getHotelGroupUserRelatedInfoVO(userSession);
            req.setOrgId(hotelGroupUserRelatedInfoVO.getHotelGroupOrgId());
            req.setHotelGroupBrandIdList(hotelGroupUserRelatedInfoVO.getHotelBrandIdSet());
        }

        // 查询推荐酒单
        Page<BidRecommendHotelInfoQueryResponse> page = new Page<>(req.getPageIndex(), req.getPageSize());
        projectIntentHotelMapper.queryHotelGroupBidMapInvitedHotelList(page, req);

        handlerData(languageId, req, page, queryHotel);

        return new PageVO<>(page.getTotal(), page.getPages(), page.getRecords());
    }

    public MapPoiBidHotelInfoResponse queryMapHotelPoiBidHotelInfo(HttpServletRequest request, QueryMapPoiBidHotelInfoRequest req) throws ExecutionException, InterruptedException {
        UserSession userSession = UserSession.get();
        int languageId = GenericAppUtility.getRequestHeaderLanguage(request);
        Integer projectId = req.getProjectId();

        String cityCode = "";
        ProjectPoiInfoResponse basePoiResponse = null;
        HotelResponse hotelResponse = null;
        Long queryHotelId = req.getHotelId() != null ? req.getHotelId() : req.getRecommendHotelId();
        // 查询poi坐标信息
        if(req.getPoiId() != null){
           List<ProjectPoiInfoResponse> basePoiResponseList = projectPoiMapper.selectMapProjectPoiInfo(projectId, req.getPoiId(), null, null, req.getDistance());
            if(CollectionUtils.isEmpty(basePoiResponseList)){
                GenericAppUtility.serviceError(languageId, ErrorCode.PROJECT_POI_NOT_EXIST);
            }
            basePoiResponse = basePoiResponseList.get(0);
            cityCode = basePoiResponse.getCityCode();
            // 查询地图酒店信息
        } else if(queryHotelId != null){
            HotelEntity hotel = cachedHotelService.getById(req.getHotelId());
            cityCode = hotel.getCityCode();
            hotelResponse = new HotelResponse();
            HotelUtility.calculateNullGoogleLngLat(hotel);
            BeanUtils.copyProperties(hotel, hotelResponse);
            hotelResponse.setHotelName(GenericAppUtility.getName(languageId, hotel.getHotelId(), hotel.getNameEnUs(),hotel.getNameZhCn()));
            hotelResponse.setHotelStarName(HotelStarEnum.getTextByKey(hotel.getHotelStar(), languageId));
        }

        // 查询项目所有酒店报价信息
        List<BidHotelInfoQueryResponse> allBidHotelInfoQueryResponses = cachedProjectIntentHotelService.queryProjectBidHotelInfo(req.getProjectId(), cityCode);
        allBidHotelInfoQueryResponses.forEach(item -> {
            item.setHotelName(GenericAppUtility.getName(languageId, item.getHotelId(), item.getHotelNameEnUs(), item.getHotelNameZhCn()));
            item.setHotelAddress(GenericAppUtility.getName(languageId, item.getHotelId(), item.getHotelAddressEnUs(), item.getHotelAddressZhCn()));
            item.setHotelStarName(HotelStarEnum.getTextByKey(item.getHotelStar(), languageId));
        });

        // 查询距离范围的酒店或者poi信息
        // 根据经纬度过滤符合条件的酒店报价
        List<BidHotelInfoQueryResponse> hotelBidHotelInfoQueryResponses = allBidHotelInfoQueryResponses.stream().filter(
                o -> LocationUtil.isInLngLatInDistanceRange(req.getDistance(), o.getLatGoogle(), o.getLngGoogle(), req.getLatGoogle(), req.getLngGoogle())
        ).collect(Collectors.toList());

        // 查询项目范围内POI
        List<ProjectPoiInfoResponse> allProjectPoiInfoResponseList = projectPoiMapper.selectMapProjectPoiInfo(projectId, null, null, cityCode, req.getDistance());
        List<ProjectPoiInfoResponse> hotelProjectPoiInfoResponseList = allProjectPoiInfoResponseList.stream().filter(
                o -> LocationUtil.isInLngLatInDistanceRange(req.getDistance(), o.getLatGoogle(), o.getLngGoogle(), req.getLatGoogle(), req.getLngGoogle())
        ).collect(Collectors.toList());

        Map<Long, BidHotelInfoQueryResponse> hotelIdBidHotelInfoQueryResponseMap = hotelBidHotelInfoQueryResponses.stream().collect(Collectors.toMap(BidHotelInfoQueryResponse::getHotelId, Function.identity()));
        Map<Long, BidHotelInfoQueryResponse> lastYearBidHotelInfoQueryResponseMap = null;
        Map<Long, BidHotelInfoQueryResponse> lastYearCommendBidHotelInfoQueryResponseMap = null;


        // 查询公里范围推荐酒店列表
        FutureTask<Map<Long, BidHotelInfoQueryResponse>> bidCommentHotelInfoQueryResponseMapTask = null;
        if(req.getRecommendHotelId() != null){
            bidCommentHotelInfoQueryResponseMapTask = new FutureTask<>(
                    new ProjectRecommendHotelQueryThread(projectId, req.getRecommendHotelId(),
                            req.getDistance(),
                            req.getLngGoogle(),
                            req.getLatGoogle(),
                            cityCode,
                            projectHotelHistoryDataMapper, projectIntentHotelMapper));
            queryBidHotelInfoExecutor.execute(bidCommentHotelInfoQueryResponseMapTask);
        }

        // 查询早餐和最低价格
        if (CollectionUtils.isNotEmpty(hotelBidHotelInfoQueryResponses)) {
            int countDownLatchCount = 2;
            CountDownLatch countDownLatch = new CountDownLatch(countDownLatchCount);
            //项目意向酒店id
            List<Integer> projectIntentHotelIds = hotelBidHotelInfoQueryResponses.stream().map(BidHotelInfoQueryResponse::getProjectIntentHotelId).collect(Collectors.toList());

            //最低价和早餐，房型
            FutureTask<List<HotelMinPriceResponse>> hotelMinPriceResponseListTask = new FutureTask<>(new ProjectHotelPriceRoomTypeQueryThread(countDownLatch, projectIntentHotelIds, this, hotelManager, priceApplicableRoomMapper, languageId));
            queryBidHotelInfoExecutor.execute(hotelMinPriceResponseListTask);

            // 查询去年报价信息
            Long baseCenterHotelId = req.getHotelId();
            if(req.getRecommendHotelId() != null){
                baseCenterHotelId = req.getRecommendHotelId();
            }
            List<Long> queryLastYearBidInfoHotelIdList = new ArrayList<>(hotelIdBidHotelInfoQueryResponseMap.keySet());
            FutureTask<Map<Long, BidHotelInfoQueryResponse>> bidHotelInfoQueryResponseMapTask = new FutureTask<>(
                    new ProjectHotelLastYearBidInfoQueryThread(countDownLatch, projectId, baseCenterHotelId,
                            queryLastYearBidInfoHotelIdList, projectHotelHistoryDataMapper));
            queryBidHotelInfoExecutor.execute(bidHotelInfoQueryResponseMapTask);

            boolean noTimeout = true;
            try {
                //超时返回false
                noTimeout = countDownLatch.await(60000, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                log.error("酒店报价POI坐标信息查询失败，请求参数：" + JsonUtil.objectToJson(baseCenterHotelId), e);
            }
            if (!noTimeout) {
                log.error("bidHotelInfoQueryResponseMapTask 执行超时 ");
                GenericAppUtility.serviceError(ErrorCode.QUERY_TIME_OUT);
            }

            //报价最低价查询
            List<HotelMinPriceResponse> hotelMinPriceResponseList = hotelMinPriceResponseListTask == null ? null : hotelMinPriceResponseListTask.get();

            //hotel去年报价信息
            lastYearBidHotelInfoQueryResponseMap = bidHotelInfoQueryResponseMapTask == null ? null : bidHotelInfoQueryResponseMapTask.get();

            //最低价和早餐
            Map<Long, HotelMinPriceResponse> hotelMinPriceResponseMap = hotelMinPriceResponseList.stream().collect(Collectors.toMap(HotelMinPriceResponse::getHotelId, Function.identity()));
            for(BidHotelInfoQueryResponse bidHotelInfoQueryResponse : hotelBidHotelInfoQueryResponses){
                HotelMinPriceResponse hotelMinPriceResponse = hotelMinPriceResponseMap.get(bidHotelInfoQueryResponse.getHotelId());
                if(hotelMinPriceResponse == null){
                    log.info("酒店没有找到最低报价 {}", JsonUtil.objectToJson(bidHotelInfoQueryResponse));
                    continue;
                }
                bidHotelInfoQueryResponse.setRoomResponseList(hotelMinPriceResponse.getRoomResponseList());
                bidHotelInfoQueryResponse.setMinPrice(hotelMinPriceResponse.getMinPrice());
                bidHotelInfoQueryResponse.setIsIncludeBreakfast(hotelMinPriceResponse.getIsIncludeBreakfast());
                bidHotelInfoQueryResponse.setRoomTypeDesc(hotelMinPriceResponse.getRoomTypeDesc());

                BidHotelInfoQueryResponse lastYearBidHotelInfoQueryResponse = lastYearBidHotelInfoQueryResponseMap.get(bidHotelInfoQueryResponse.getHotelId());
                if(lastYearBidHotelInfoQueryResponse != null) {
                    bidHotelInfoQueryResponse.setRecommendLevel(lastYearBidHotelInfoQueryResponse.getRecommendLevel());
                    bidHotelInfoQueryResponse.setLastYearAvgPrice(lastYearBidHotelInfoQueryResponse.getLastYearAvgPrice());
                    bidHotelInfoQueryResponse.setLastYearCityOrder(lastYearBidHotelInfoQueryResponse.getLastYearCityOrder());
                    bidHotelInfoQueryResponse.setLatestYearRoomNight(lastYearBidHotelInfoQueryResponse.getLatestYearRoomNight());
                    bidHotelInfoQueryResponse.setTheSameLevelPrice(lastYearBidHotelInfoQueryResponse.isTheSameLevelPrice());
                    bidHotelInfoQueryResponse.setLastYearAvgPrice(lastYearBidHotelInfoQueryResponse.getLastYearAvgPrice());
                } else{
                    bidHotelInfoQueryResponse.setLastYearServicePoints(BigDecimal.ZERO);
                    bidHotelInfoQueryResponse.setLastYearCityOrder(0);
                    bidHotelInfoQueryResponse.setLatestYearRoomNight(0);
                    bidHotelInfoQueryResponse.setTheSameLevelPrice(false);
                    bidHotelInfoQueryResponse.setLastYearAvgPrice(BigDecimal.ZERO);
                }
            }
        }


        //推荐酒店hotel去年报价信息
        lastYearCommendBidHotelInfoQueryResponseMap = bidCommentHotelInfoQueryResponseMapTask == null ? null : bidCommentHotelInfoQueryResponseMapTask.get();

        // 准备返回值
        // 报价酒店坐标信息
        List<BidHotelInfoQueryResponse> bidHotelInfoQueryResponseList = new LinkedList<>();
        if(req.getHotelId() != null){
            BidHotelInfoQueryResponse baseBidHotelInfoQueryResponse = hotelIdBidHotelInfoQueryResponseMap.get(req.getHotelId());
            bidHotelInfoQueryResponseList.add(baseBidHotelInfoQueryResponse);
        }
        for(Long hotelId : hotelIdBidHotelInfoQueryResponseMap.keySet()){
            if(req.getHotelId() != null && Objects.equals(hotelId, req.getHotelId())){
                continue;
            }
            bidHotelInfoQueryResponseList.add(hotelIdBidHotelInfoQueryResponseMap.get(hotelId));
        }
        // POI排序
        List<ProjectPoiInfoResponse> poiInfoResponseList = new LinkedList<>();
        if(basePoiResponse != null){
            poiInfoResponseList.add(basePoiResponse);
            for(ProjectPoiInfoResponse poiInfoResponse : hotelProjectPoiInfoResponseList){
                if(Objects.equals(basePoiResponse.getPoiId(), poiInfoResponse.getPoiId())){
                    continue;
                }
                poiInfoResponseList.add(poiInfoResponse);
            }
        } else {
            poiInfoResponseList = hotelProjectPoiInfoResponseList;
        }

        //推荐酒店坐标信息
        List<BidHotelInfoQueryResponse> commendHotelInfoQueryResponseList = new LinkedList<>();
        if(lastYearCommendBidHotelInfoQueryResponseMap != null && !lastYearCommendBidHotelInfoQueryResponseMap.isEmpty()){
            commendHotelInfoQueryResponseList = new LinkedList<>(new ArrayList<>(lastYearCommendBidHotelInfoQueryResponseMap.values()));
            // 设置推荐价格
            List<Long> recommendHotelIdList =  commendHotelInfoQueryResponseList.stream().map(o -> o.getHotelId()).collect(Collectors.toList());
            Map<Long, RecommendHotelEntity> recommendHotelMap = recommendHotelMapper.selectRecommendHotelByHotelIds(recommendHotelIdList).stream().collect(Collectors.toMap(RecommendHotelEntity::getHotelId, Function.identity()));
            commendHotelInfoQueryResponseList.forEach(item -> {
                if(recommendHotelMap.containsKey(item.getHotelId())) {
                    item.setReferencePrice(recommendHotelMap.get(item.getHotelId()).getReferencePrice());
                }
            });
        }


        // 返回值
        MapPoiBidHotelInfoResponse mapPoiBidHotelInfoResponse = new MapPoiBidHotelInfoResponse();
        mapPoiBidHotelInfoResponse.setProjectId(req.getProjectId());
        mapPoiBidHotelInfoResponse.setCommendHotelInfoQueryResponseList(commendHotelInfoQueryResponseList);
        mapPoiBidHotelInfoResponse.setBidHotelInfoQueryResponseList(bidHotelInfoQueryResponseList);
        mapPoiBidHotelInfoResponse.setHotelProjectPoiInfoResponseList(poiInfoResponseList);


        return mapPoiBidHotelInfoResponse;
    }
    /**
     * 查询地图酒店详情信息
     */
    public QueryBidMapHotelInfoResponse queryBidMapHotelInfo(HttpServletRequest request, QueryHotelGroupBidMapHotelListRequest req){
        UserSession userSession = UserSession.get();
        boolean isHotelOrHotelGroupUser = userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.HOTEL.key) || userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.HOTELGROUP.key);
        int languageId = GenericAppUtility.getRequestHeaderLanguage(request);

        if(Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.HOTELGROUP.key)){
            HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfoVO = GenericAppUtility.getHotelGroupUserRelatedInfoVO(userSession);
            req.setHotelGroupBrandIdList(hotelGroupUserRelatedInfoVO.getHotelBrandIdSet());
        }

        // 定义返回值
        QueryBidMapHotelInfoResponse queryBidMapHotelInfoResponse = new QueryBidMapHotelInfoResponse();

        HotelEntity hotel = cachedHotelService.getById(req.getHotelId());
        HotelUtility.calculateNullGoogleLngLat(hotel);
        queryBidMapHotelInfoResponse.setHotelId(req.getHotelId());
        queryBidMapHotelInfoResponse.setHotelName(GenericAppUtility.getName(languageId, hotel.getHotelId(), hotel.getNameEnUs(), hotel.getNameZhCn()));
        queryBidMapHotelInfoResponse.setHotelStar(hotel.getHotelStar());
        queryBidMapHotelInfoResponse.setHotelStarName(HotelStarEnum.getTextByKey(hotel.getHotelStar(), languageId));
        queryBidMapHotelInfoResponse.setLatGoogle(hotel.getLatGoogle());
        queryBidMapHotelInfoResponse.setLngGoogle(hotel.getLngGoogle());
        queryBidMapHotelInfoResponse.setCityCode(hotel.getCityCode());
        queryBidMapHotelInfoResponse.setBidWeight(BigDecimal.ZERO);
        queryBidMapHotelInfoResponse.setRating(hotel.getRating());
        queryBidMapHotelInfoResponse.setMainPicUrl(hotel.getMainPicUrl());
        queryBidMapHotelInfoResponse.setCityName(GenericAppUtility.getCityName(languageId, hotel.getCityCode()));

        // 查询酒店最近POI名称
        List<ProjectPoiVO> cityPoiList = projectPoiMapper.selectPoiInfoByProjectId(req.getProjectId(), hotel.getCityCode());
        if(CollectionUtils.isNotEmpty(cityPoiList)){
            // BigDecimal lngGoogle, BigDecimal latGoogle, List<ProjectPoiVO> cityPoiList){
            MinDistancePoiInfo minDistancePoiInfo = PoiUtility.filteMinDistancePoiInfo(hotel.getLngGoogle(), hotel.getLatGoogle(), cityPoiList);
            if(minDistancePoiInfo.getMinDistance() != null){
                queryBidMapHotelInfoResponse.setPoiName(minDistancePoiInfo.getPoiName());
                queryBidMapHotelInfoResponse.setPoiDistance(minDistancePoiInfo.getMinDistanceKmFormat());
            }
        }

        // 查询酒店图片
        /**
        List<HotelImage> hotelImageList = hotelManager.queryHotelImages(Lists.newArrayList(hotel.getHotelId()));
        if(CollectionUtils.isNotEmpty(hotelImageList)){
            HotelImage hotelImage = hotelImageList.get(0);
            queryBidMapHotelInfoResponse.setHotelImageList(hotelImage.getImages());
        }
         **/

        // 查询项目
        ProjectEntity project = cachedProjectService.getById(req.getProjectId());
        if(project == null){
            return queryBidMapHotelInfoResponse;
        }
        queryBidMapHotelInfoResponse.setRelatedProjectId(project.getRelatedProjectId());
        // 总权重分
        ProjectHotelTendWeightEntity projectHotelTendWeight = projectHotelTendWeightMapper.selectEntityByProjectId(project.getProjectId());
        if(projectHotelTendWeight != null) {
            queryBidMapHotelInfoResponse.setProjectWeight(projectHotelTendWeight.getWhtTotalWeight());
        }

        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.queryByProjectAndHotelId(project.getProjectId(), hotel.getHotelId());
        if(projectIntentHotel != null) {
            queryBidMapHotelInfoResponse.setCurrencyCode(projectIntentHotel.getCurrencyCode());
            queryBidMapHotelInfoResponse.setViewCurrencyCode(projectIntentHotel.getCurrencyCode());
            queryBidMapHotelInfoResponse.setViewCurrencyExchangeRate(new BigDecimal("1"));
            // 获取展示币种
            CurrencyExchangeRateEntity viewCurrency = cachedCurrencyService.getCurrencyRateInfo(GenericAppUtility.getRequestHeaderCurrency());
            CurrencyExchangeRateEntity bidCurrency = cachedCurrencyService.getCurrencyRateInfo(queryBidMapHotelInfoResponse.getCurrencyCode());
            if (Objects.nonNull(viewCurrency) && Objects.nonNull(bidCurrency)) {
                queryBidMapHotelInfoResponse.setViewCurrencyCode(viewCurrency.getCurrencyCode());
                queryBidMapHotelInfoResponse.setViewCurrencyExchangeRate(NumberUtil.div(viewCurrency.getExchangeRate(), bidCurrency.getExchangeRate(), 10));
            }
        }

        // 平台或者企业用户返回数据
        if(!isHotelOrHotelGroupUser) {
            if(projectIntentHotel != null && !projectIntentHotel.getBidState().equals(HotelBidStateEnum.NO_BID.bidState)) {
                queryBidMapHotelInfoResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                queryBidMapHotelInfoResponse.setBidState(projectIntentHotel.getBidState());
                // 查询今年报价
                // -----------------------------------------------  设置可用日期
                List<BidApplicableDayVO> priceApplicableDayList = priceApplicableDayMapper.selectVOListByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                if (CollectionUtils.isNotEmpty(priceApplicableDayList)) {
                    queryBidMapHotelInfoResponse.setBidApplicableDayList(priceApplicableDayList);
                }
                queryBidMapHotelInfoResponse.setBidApplicableDayList(priceApplicableDayList);

                // --------------------------------------------  设置不可以日期
                List<BidUnApplicableDayVO> priceUnApplicableDayList = priceUnapplicableDayMapper.selectVOListByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                if (CollectionUtils.isNotEmpty(priceUnApplicableDayList)) {
                    queryBidMapHotelInfoResponse.setBidUnApplicableDayList(priceUnApplicableDayList);
                }

                // 查询报价税费
                List<BidHotelPriceLevelInfoVO> bidHotelPriceLevelInfoVOList = this.querBidHotelPriceLevelInfoList(UserSession.get(), languageId, projectIntentHotel.getProjectIntentHotelId(), projectIntentHotel, projectIntentHotel.getHotelId(), null);
                queryBidMapHotelInfoResponse.setHotelPriceLevelInfoVOList(bidHotelPriceLevelInfoVOList);

                // 设置最低加个和房型信息
                if (CollectionUtils.isNotEmpty(bidHotelPriceLevelInfoVOList)) {
                    Map<Integer, BidHotelPriceLevelInfoVO> roomLevelBidHotelPriceLevelInfoVOMap = bidHotelPriceLevelInfoVOList.stream().collect(Collectors.toMap(BidHotelPriceLevelInfoVO::getRoomLevelNo, Function.identity()));
                    BidHotelPriceLevelInfoVO level1BidHotelPriceLevelInfoVO = roomLevelBidHotelPriceLevelInfoVOMap.get(1);
                    String roomTypeDesc = "";
                    if (CollectionUtils.isNotEmpty(level1BidHotelPriceLevelInfoVO.getRoomList())) {
                        roomTypeDesc = level1BidHotelPriceLevelInfoVO.getRoomList().get(0).getRoomName();
                    }
                    List<HotelMinPriceResponse> hotelMinPriceResponses =  this.queryHotelMinPrice(languageId, Collections.singletonList(projectIntentHotel.getProjectIntentHotelId()), true);
                    if(CollectionUtils.isNotEmpty(hotelMinPriceResponses)){
                        HotelMinPriceResponse hotelMinPriceResponse = hotelMinPriceResponses.get(0);
                        queryBidMapHotelInfoResponse.setBreakfastNum(BreakfastNumEnum.getTextByKey(hotelMinPriceResponse.getBreakfastNum(), languageId));
                        queryBidMapHotelInfoResponse.setMinPrice(hotelMinPriceResponse.getMinPrice());
                        queryBidMapHotelInfoResponse.setRoomTypeDesc(hotelMinPriceResponse.getRoomTypeDesc());
                    }
                }
            }
            // 查询去年报价
            if(project.getRelatedProjectId() != null){
                ProjectIntentHotelEntity lastYearProjectIntentHotel = projectIntentHotelMapper.queryByProjectAndHotelId(project.getRelatedProjectId(), hotel.getHotelId());
                if(lastYearProjectIntentHotel != null && !lastYearProjectIntentHotel.getBidState().equals(HotelBidStateEnum.NO_BID.bidState)) {
                    List<BidHotelPriceLevelInfoVO> lastYearBidHotelPriceLevelInfoVOList = this.querBidHotelPriceLevelInfoList(UserSession.get(),languageId, lastYearProjectIntentHotel.getProjectIntentHotelId(), lastYearProjectIntentHotel, lastYearProjectIntentHotel.getHotelId(), null);
                    queryBidMapHotelInfoResponse.setLastYearHotelPriceLevelInfoVOList(lastYearBidHotelPriceLevelInfoVOList);
                }
            }
        }

        // 设置统计值
        ProjectLastYearCityStatEntity projectLastYearCityStat = projectLastYearCityStatMapper.selectByProjectIdAndCityCode(project.getProjectId(), hotel.getCityCode());
        if(projectLastYearCityStat != null && projectLastYearCityStat.getTotalRoomNight() > 0) {
            // 获取总间夜数
            BigDecimal totalRoomNight = BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight());

            // 计算比率
            BigDecimal totalRoomNight500Rate = NumberUtil.div(BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight500()), totalRoomNight, 4);
            BigDecimal totalRoomNight400To500Rate = NumberUtil.div(BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight400To500()), totalRoomNight, 4);
            BigDecimal totalRoomNight300To400Rate = NumberUtil.div(BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight300To400()), totalRoomNight, 4);
            BigDecimal totalRoomNight200To300Rate = NumberUtil.div(BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight200To300()), totalRoomNight, 4);
            BigDecimal totalRoomNight100To200Rate = NumberUtil.div(BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight100To200()), totalRoomNight, 4);

            if(Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.DISTRIBUTOR.key) || Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.PLATFORM.key)) {
                queryBidMapHotelInfoResponse.setLastYearTotalRoomNight(NumberUtil.decimalFormat(",###", projectLastYearCityStat.getTotalRoomNight()));
                queryBidMapHotelInfoResponse.setLastYearTotalSalesAmount(NumberUtil.decimalFormat(",###", projectLastYearCityStat.getTotalSalesAmount()));
            } else {
                queryBidMapHotelInfoResponse.setLastYearTotalRoomNight(formatStatData(NumberUtil.decimalFormat(",###", projectLastYearCityStat.getTotalRoomNight())));
                queryBidMapHotelInfoResponse.setLastYearTotalSalesAmount(formatStatData(NumberUtil.decimalFormat(",###", projectLastYearCityStat.getTotalSalesAmount())));
            }

            // 格式化前端展示数据
            BigDecimal decimal100 = new BigDecimal("100");
            queryBidMapHotelInfoResponse.setTotalRoomNight500(String.valueOf(totalRoomNight500Rate.multiply(decimal100).intValue()));
            queryBidMapHotelInfoResponse.setTotalRoomNight400To500(String.valueOf(totalRoomNight400To500Rate.multiply(decimal100).intValue()));
            queryBidMapHotelInfoResponse.setTotalRoomNight300To400(String.valueOf(totalRoomNight300To400Rate.multiply(decimal100).intValue()));
            queryBidMapHotelInfoResponse.setTotalRoomNight200To300(String.valueOf(totalRoomNight200To300Rate.multiply(decimal100).intValue()));
            queryBidMapHotelInfoResponse.setTotalRoomNight100To200(String.valueOf(totalRoomNight100To200Rate.multiply(decimal100).intValue()));
            int totalRoomNight100 = 100 - Integer.parseInt(queryBidMapHotelInfoResponse.getTotalRoomNight500()) - Integer.parseInt(queryBidMapHotelInfoResponse.getTotalRoomNight400To500())
                    - Integer.parseInt(queryBidMapHotelInfoResponse.getTotalRoomNight300To400()) - Integer.parseInt(queryBidMapHotelInfoResponse.getTotalRoomNight200To300())
                - Integer.parseInt(queryBidMapHotelInfoResponse.getTotalRoomNight100To200());
            queryBidMapHotelInfoResponse.setTotalRoomNight100(String.valueOf(totalRoomNight100));
        } else {
            queryBidMapHotelInfoResponse.setLastYearTotalRoomNight("--");
            queryBidMapHotelInfoResponse.setLastYearTotalSalesAmount("--");
        }

        // 设置酒店去年校交易间夜数
        QueryHistoryProjectInfoResponse baseQueryHistoryProjectInfoResponse = null;
        int hotelAvgPrice = 0;
        List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoResponseList = projectHotelHistoryDataMapper.queryHistoryProjectHotelList(project.getProjectId(), null, null, hotel.getCityCode(), null);
        for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : queryHistoryProjectInfoResponseList){
            if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), hotel.getHotelId())){
                baseQueryHistoryProjectInfoResponse = queryHistoryProjectInfoResponse;
                queryBidMapHotelInfoResponse.setLatestYearRoomNight(queryHistoryProjectInfoResponse.getRoomNightCount());
                queryBidMapHotelInfoResponse.setLatestYearOrder(queryHistoryProjectInfoResponse.getCityOrder());
                if(queryHistoryProjectInfoResponse.getTotalAmount().compareTo(BigDecimal.ZERO) > 0 &&
                        queryHistoryProjectInfoResponse.getRoomNightCount() > 0
                ) {
                    hotelAvgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()),0 ,RoundingMode.HALF_UP).intValue();
                }
                break;
            }
        }
        List<Long> theSameLevelHotelIdList;
        if(hotelAvgPrice > 0){
            int finalHotelAvgPrice1 = hotelAvgPrice;
            theSameLevelHotelIdList = queryHistoryProjectInfoResponseList.stream().filter(o -> isTheSameLevel(finalHotelAvgPrice1, o)).map(QueryHistoryProjectInfoResponse::getHotelId).collect(Collectors.toList());
        } else {
            theSameLevelHotelIdList = new ArrayList<>();
        }

        // 查询项目所有酒店报价信息
        List<BidHotelInfoQueryResponse> cityBidHotelInfoQueryResponses = projectIntentHotelMapper.queryProjectBidHotelInfo(req.getProjectId(), hotel.getCityCode());
        cityBidHotelInfoQueryResponses.forEach(item -> {
            LngLatGoogleVO lngLatGoogleVO = CoordinateUtils.calculateNullGoogleLngLat(item.getLngGoogle(), item.getLatGoogle(), item.getLngBaiDu(), item.getLatBaiDu());
            item.setLngGoogle(lngLatGoogleVO.getLngGoogle());
            item.setLatGoogle(lngLatGoogleVO.getLatGoogle());
            if(Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.PLATFORM.key) ||
                    Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.DISTRIBUTOR.key)){
                item.setHotelName(GenericAppUtility.getName(languageId, item.getHotelId(), item.getHotelNameEnUs(), item.getHotelNameZhCn()));
            } else {
                item.setHotelName("******" + GenericAppUtility.getText(languageId, "HOTEL"));
            }
        });

        // 根据经纬度过滤符合条件的3公里范围酒店报价信息
        List<BidHotelInfoQueryResponse> threeHotelBidHotelInfoQueryResponses = cityBidHotelInfoQueryResponses.stream().filter(
                o -> LocationUtil.isInLngLatInDistanceRange(3, o.getLatGoogle(), o.getLngGoogle(), hotel.getLatGoogle(), hotel.getLngGoogle())
        ).collect(Collectors.toList());
        List<HotelBidMapHotelInfoResponse> bidMapHotelInfoResponses = new ArrayList<>();
        threeHotelBidHotelInfoQueryResponses.forEach(item -> {
            if(req.getHotelId() != null && Objects.equals(item.getHotelId(), req.getHotelId())){
                return;
            }
            HotelBidMapHotelInfoResponse hotelInfoResponse = new HotelBidMapHotelInfoResponse();
            hotelInfoResponse.setHotelName(item.getHotelName());
            hotelInfoResponse.setLatBaiDu(item.getLatBaiDu());
            hotelInfoResponse.setLngBaiDu(item.getLngBaiDu());
            hotelInfoResponse.setLatGoogle(item.getLatGoogle());
            hotelInfoResponse.setLngGoogle(item.getLngGoogle());
            hotelInfoResponse.setBidState(item.getBidState());
            bidMapHotelInfoResponses.add(hotelInfoResponse);
        });
        queryBidMapHotelInfoResponse.setBidHotelInfoQueryResponseList(bidMapHotelInfoResponses);

        // 查询项目3公里范围内POI
        List<ProjectPoiInfoResponse> allProjectPoiInfoResponseList = projectPoiMapper.selectMapProjectPoiInfo(project.getProjectId(), null, null, hotel.getCityCode(), 3);
        List<ProjectPoiInfoResponse> hotelProjectPoiInfoResponseList = allProjectPoiInfoResponseList.stream().filter(
                o -> LocationUtil.isInLngLatInDistanceRange(3, o.getLatGoogle(), o.getLngGoogle(), hotel.getLatGoogle(), hotel.getLngGoogle())
        ).collect(Collectors.toList());
        queryBidMapHotelInfoResponse.setHotelProjectPoiInfoResponseList(hotelProjectPoiInfoResponseList);

        QueryProjectHotelBidStatResponse threeQueryProjectHotelBidStatResponse = new QueryProjectHotelBidStatResponse();
        int finalHotelAvgPrice = hotelAvgPrice;

        // 过滤3同档公里历史数据
        List<QueryHistoryProjectInfoResponse> threeHistoryProjectInfoResponseList = queryHistoryProjectInfoResponseList.stream().filter(
                o -> theSameLevelHotelIdList.contains(o.getHotelId()) && LocationUtil.isInLngLatInDistanceRange(3, hotel.getLatGoogle(), hotel.getLngGoogle(), o.getLatGoogle(), o.getLngGoogle())
        ).collect(Collectors.toList());
        // 3公里同档结算间夜数排名
        QueryProjectHotelBidStatResponse calculateThreeHotelNightRoomOrder = HotelHistoryDataStatUtil.calculateHotelNightRoomOrder(hotel.getHotelId(), threeHistoryProjectInfoResponseList);
        queryBidMapHotelInfoResponse.setTheSameLevelOrder(calculateThreeHotelNightRoomOrder.getLastYearRoomNightOrder());
        threeQueryProjectHotelBidStatResponse.setLastYearRoomNightCount(calculateThreeHotelNightRoomOrder.getLastYearRoomNightCount());
        threeQueryProjectHotelBidStatResponse.setLastYearRoomNightOrder(calculateThreeHotelNightRoomOrder.getLastYearRoomNightOrder());

        // 3公里同档结算间夜数金额
        QueryProjectHotelBidStatResponse calculateThreeHotelSalesAmountOrder = HotelHistoryDataStatUtil.calculateSalesAmountOrder(hotel.getHotelId(), threeHistoryProjectInfoResponseList);
        threeQueryProjectHotelBidStatResponse.setLastYearAmount(calculateThreeHotelSalesAmountOrder.getLastYearAmount());
        threeQueryProjectHotelBidStatResponse.setLastYearAmountOrder(calculateThreeHotelSalesAmountOrder.getLastYearAmountOrder());
        List<BidHotelInfoQueryResponse> lastYearCityBidHotelInfoQueryResponses = new ArrayList<>();
        if(Objects.nonNull(project.getRelatedProjectId())) {
            lastYearCityBidHotelInfoQueryResponses = projectIntentHotelMapper.queryProjectBidHotelInfo(project.getRelatedProjectId(), hotel.getCityCode());
        }

        // 查询去年3公里同档酒店服务分排名
        if(CollectionUtils.isNotEmpty(lastYearCityBidHotelInfoQueryResponses)) {
            List<BidHotelInfoQueryResponse> threeLastYearBidHotelInfoQueryResponses = lastYearCityBidHotelInfoQueryResponses.stream().filter(
                    o -> theSameLevelHotelIdList.contains(o.getHotelId()) && LocationUtil.isInLngLatInDistanceRange(3, o.getLatGoogle(), o.getLngGoogle(), hotel.getLatGoogle(), hotel.getLngGoogle())
            ).collect(Collectors.toList());
            QueryProjectHotelBidStatResponse queryProjectHotelBidStatResponse = HotelHistoryDataStatUtil.calculateHotelServicePointOrder(hotel.getHotelId(), threeLastYearBidHotelInfoQueryResponses);
            threeQueryProjectHotelBidStatResponse.setLastYearServicePoint(queryProjectHotelBidStatResponse.getLastYearServicePoint());
            threeQueryProjectHotelBidStatResponse.setLastYearServicePointOrder(queryProjectHotelBidStatResponse.getLastYearServicePointOrder());
        }

        // 查询OTA排名 (3公里同档范围)
        List<BidHotelInfoQueryResponse> ratingHotelBidHotelInfoQueryResponses = threeHotelBidHotelInfoQueryResponses.stream().filter(o -> theSameLevelHotelIdList.contains(o.getHotelId())).collect(Collectors.toList());
        int rattingOrder = HotelHistoryDataStatUtil.calculateOTAOrder(req.getHotelId(),ratingHotelBidHotelInfoQueryResponses);
        threeQueryProjectHotelBidStatResponse.setRatingOrder(rattingOrder);
        threeQueryProjectHotelBidStatResponse.setRating(hotel.getRating());
        // 设置3公里范围同档
        queryBidMapHotelInfoResponse.setThreeQueryProjectHotelBidStatResponse(threeQueryProjectHotelBidStatResponse);

        // --------------------------------------------------------------------------------------------------------------
        // 城市同档
        QueryProjectHotelBidStatResponse cityLevelQueryProjectHotelBidStatResponse = new QueryProjectHotelBidStatResponse();
        List<QueryHistoryProjectInfoResponse> cityLevelHistoryProjectInfoResponseList = queryHistoryProjectInfoResponseList.stream().filter(
                o -> isTheSameLevel(finalHotelAvgPrice, o)).collect(Collectors.toList());
        QueryProjectHotelBidStatResponse calculateCityLevelHotelNightRoomOrder = HotelHistoryDataStatUtil.calculateHotelNightRoomOrder(hotel.getHotelId(), cityLevelHistoryProjectInfoResponseList);
        cityLevelQueryProjectHotelBidStatResponse.setLastYearRoomNightCount(calculateCityLevelHotelNightRoomOrder.getLastYearRoomNightCount());
        cityLevelQueryProjectHotelBidStatResponse.setLastYearRoomNightOrder(calculateCityLevelHotelNightRoomOrder.getLastYearRoomNightOrder());

        // 城市同档成交间夜数金额
        QueryProjectHotelBidStatResponse calculateCityLevelHotelSalesAmountOrder = HotelHistoryDataStatUtil.calculateSalesAmountOrder(hotel.getHotelId(), cityLevelHistoryProjectInfoResponseList);
        cityLevelQueryProjectHotelBidStatResponse.setLastYearAmount(calculateCityLevelHotelSalesAmountOrder.getLastYearAmount());
        cityLevelQueryProjectHotelBidStatResponse.setLastYearAmountOrder(calculateCityLevelHotelSalesAmountOrder.getLastYearAmountOrder());

        // 城市同档查询去年服务分排名
        if(CollectionUtils.isNotEmpty(lastYearCityBidHotelInfoQueryResponses)) {
            List<BidHotelInfoQueryResponse> cityLevelLastYearBidHotelInfoQueryResponses = lastYearCityBidHotelInfoQueryResponses.stream().filter(
                    o -> theSameLevelHotelIdList.contains(o.getHotelId())
            ).collect(Collectors.toList());
            QueryProjectHotelBidStatResponse queryProjectHotelBidStatResponse = HotelHistoryDataStatUtil.calculateHotelServicePointOrder(hotel.getHotelId(), cityLevelLastYearBidHotelInfoQueryResponses);
            cityLevelQueryProjectHotelBidStatResponse.setLastYearServicePoint(queryProjectHotelBidStatResponse.getLastYearServicePoint());
            cityLevelQueryProjectHotelBidStatResponse.setLastYearServicePointOrder(queryProjectHotelBidStatResponse.getLastYearServicePointOrder());
        }

        // 城市同档 OTA
        List<BidHotelInfoQueryResponse> cityLevelRatingHotelBidHotelInfoQueryResponses = cityBidHotelInfoQueryResponses.stream().filter(o -> theSameLevelHotelIdList.contains(o.getHotelId())).collect(Collectors.toList());
        int cityLevelRattingOrder = HotelHistoryDataStatUtil.calculateOTAOrder(hotel.getHotelId(), cityLevelRatingHotelBidHotelInfoQueryResponses);
        cityLevelQueryProjectHotelBidStatResponse.setRatingOrder(cityLevelRattingOrder);
        cityLevelQueryProjectHotelBidStatResponse.setRating(hotel.getRating());
        queryBidMapHotelInfoResponse.setCityTheSameLevelQueryProjectHotelBidStatResponse(cityLevelQueryProjectHotelBidStatResponse);

        //-------------------------------------------------------------------------------------------------------

        //城市排名
        QueryProjectHotelBidStatResponse cityQueryProjectHotelBidStatResponse = new QueryProjectHotelBidStatResponse();
        if(baseQueryHistoryProjectInfoResponse != null) { // 酒店去年历史数据不存在，不需要计算城市排名
            //城市结算间夜数排名
            QueryProjectHotelBidStatResponse calculateCityHotelNightRoomOrder = HotelHistoryDataStatUtil.calculateHotelNightRoomOrder(hotel.getHotelId(), queryHistoryProjectInfoResponseList);
            cityQueryProjectHotelBidStatResponse.setLastYearRoomNightCount(calculateCityHotelNightRoomOrder.getLastYearRoomNightCount());
            cityQueryProjectHotelBidStatResponse.setLastYearRoomNightOrder(calculateCityHotelNightRoomOrder.getLastYearRoomNightOrder());

            // 城市成交间夜数金额
            QueryProjectHotelBidStatResponse calculateCityHotelSalesAmountOrder = HotelHistoryDataStatUtil.calculateSalesAmountOrder(hotel.getHotelId(), queryHistoryProjectInfoResponseList);
            cityQueryProjectHotelBidStatResponse.setLastYearAmount(calculateCityHotelSalesAmountOrder.getLastYearAmount());
            cityQueryProjectHotelBidStatResponse.setLastYearAmountOrder(calculateCityHotelSalesAmountOrder.getLastYearAmountOrder());

            // 城市查询去年服务分排名
            if (CollectionUtils.isNotEmpty(lastYearCityBidHotelInfoQueryResponses)) {
                QueryProjectHotelBidStatResponse queryProjectHotelBidStatResponse = HotelHistoryDataStatUtil.calculateHotelServicePointOrder(hotel.getHotelId(), lastYearCityBidHotelInfoQueryResponses);
                cityQueryProjectHotelBidStatResponse.setLastYearServicePoint(queryProjectHotelBidStatResponse.getLastYearServicePoint());
                cityQueryProjectHotelBidStatResponse.setLastYearServicePointOrder(queryProjectHotelBidStatResponse.getLastYearServicePointOrder());
            }
        }

        // 城市 OTA
        cityQueryProjectHotelBidStatResponse.setRating(hotel.getRating());
        int cityRattingOrder = HotelHistoryDataStatUtil.calculateOTAOrder(hotel.getHotelId(), cityBidHotelInfoQueryResponses);
        cityQueryProjectHotelBidStatResponse.setRatingOrder(cityRattingOrder);
        cityQueryProjectHotelBidStatResponse.setRating(hotel.getRating());

        // 设置城市统计
        queryBidMapHotelInfoResponse.setCityQueryProjectHotelBidStatResponse(cityQueryProjectHotelBidStatResponse);


        // 是否为推荐酒店
        /**
        RecommendHotelEntity recommendHotel = recommendHotelMapper.selectRecommendHotelByHotelId(hotel.getHotelId(), RfpConstant.constant_1);
        queryBidMapHotelInfoResponse.setIsRecommendHotel(recommendHotel != null ? RfpConstant.constant_1 : RfpConstant.constant_0);
        **/

        // 是否意向酒店
        ProjectInviteHotelEntity projectInviteHotel = projectInviteHotelMapper.queryByProjectHotelId(project.getProjectId(), hotel.getHotelId());
        queryBidMapHotelInfoResponse.setIsInvitedHotel(projectInviteHotel != null ? RfpConstant.constant_1 : RfpConstant.constant_0);

        // 查询备注信息
        List<ProjectHotelRemarkEntity> projectHotelRemarkList = projectHotelRemarkMapper.selectByProjectId(project.getProjectId(), hotel.getHotelId());
        List<ProjectHotelRemarkResponse> projectHotelRemarkResponseList = projectHotelRemarkList.stream().map(projectHotelRemark -> {
            ProjectHotelRemarkResponse projectHotelRemarkResponse = new ProjectHotelRemarkResponse();
            BeanUtils.copyProperties(projectHotelRemark, projectHotelRemarkResponse);
            projectHotelRemarkResponse.setRemark(GenericAppUtility.getText(languageId, projectHotelRemark.getRemark()));
            return projectHotelRemarkResponse;
        }).collect(Collectors.toList());
        queryBidMapHotelInfoResponse.setProjectHotelRemarkResponseList(projectHotelRemarkResponseList);

        return queryBidMapHotelInfoResponse;
    }



    public void updateBidHotelSales(UpdateBidHotelSalesUserRequest updateBidHotelSalesUserRequest){
        UserSession userSession = UserSession.get();
        updateBidHotelSalesUserRequest.setModifier(userSession.getUsername());
        projectIntentHotelMapper.updateHotelSalesContactInfo(updateBidHotelSalesUserRequest);


    }

    public HotelBidDetailResponse queryHotelBidDetail(HttpServletRequest request, HotelBidDetailRequest req){
        UserSession userSession = UserSession.get();
        int languageId = request != null ? GenericAppUtility.getRequestHeaderLanguage(request) : LanguageEnum.EN_US.key;

        // 查询项目意向酒店
        ProjectIntentHotelEntity projectIntentHotel = null;
        if(req.getProjectIntentHotelId() != null){
            projectIntentHotel = projectIntentHotelMapper.selectById(req.getProjectIntentHotelId());
        } else if(req.getProjectId() != null && req.getHotelId() != null){
            projectIntentHotel = projectIntentHotelMapper.queryByProjectAndHotelId(req.getProjectId(), req.getHotelId());
        }
        if(Objects.isNull(projectIntentHotel)){
            GenericAppUtility.serviceError(ErrorCode.BID_NOT_EXIST);
        }

        // 定义返回值
        HotelBidDetailResponse hotelBidDetailResponse = new HotelBidDetailResponse();

        ProjectEntity project = cachedProjectService.getById(projectIntentHotel.getProjectId());
        hotelBidDetailResponse.setProjectId(project.getProjectId());
        hotelBidDetailResponse.setHotelId(projectIntentHotel.getHotelId());
        hotelBidDetailResponse.setCurrencyCode(projectIntentHotel.getCurrencyCode());
        hotelBidDetailResponse.setViewCurrencyCode(hotelBidDetailResponse.getCurrencyCode());
        hotelBidDetailResponse.setViewCurrencyExchangeRate(new BigDecimal("1"));
        // 设置显示币种和显示汇率
        CurrencyExchangeRateEntity viewCurrency = cachedCurrencyService.getCurrencyRateInfo(GenericAppUtility.getRequestHeaderCurrency());
        CurrencyExchangeRateEntity bidCurrency = cachedCurrencyService.getCurrencyRateInfo(projectIntentHotel.getCurrencyCode());
        if (Objects.nonNull(viewCurrency) && Objects.nonNull(bidCurrency)) {
            hotelBidDetailResponse.setViewCurrencyCode(viewCurrency.getCurrencyCode());
            hotelBidDetailResponse.setViewCurrencyExchangeRate(NumberUtil.div(viewCurrency.getExchangeRate(), bidCurrency.getExchangeRate(), 10));
        }

        // 报价信息
        ProjectIntentHotelBidVO projectIntentHotelBid = new ProjectIntentHotelBidVO();
        BeanUtils.copyProperties(projectIntentHotel, projectIntentHotelBid);
        // 处理员工权益url
        projectIntentHotelBid.setEmployRight(projectIntentHotel.getEmployeeRight());
        if(StringUtil.isValidString(projectIntentHotel.getEmployeeRightFileUrl())) {
            projectIntentHotelBid.setEmployRightFileUrl(JsonUtil.jsonToList(projectIntentHotel.getEmployeeRightFileUrl(), UploadFileVO.class));
        }
        hotelBidDetailResponse.setProjectIntentHotelBid(projectIntentHotelBid);

        // 查询用户是否有权限查看
        HotelEntity hotel = cachedHotelService.getById(projectIntentHotel.getHotelId());
        if(userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.HOTELGROUP.key)){
            if(Objects.isNull(hotel.getHotelBrandId()) && !Objects.equals(projectIntentHotel.getBidOrgId(), userSession.getUserOrg().getOrgId())){
                GenericAppUtility.serviceError(ErrorCode.HOTEL_GROUP_NO_PERMISSION);
            }
        } else if(userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.DISTRIBUTOR.key)){
            if(!project.getTenderOrgId().equals(userSession.getUserOrg().getOrgId())){
                GenericAppUtility.serviceError(ErrorCode.DISTRIBUTOR_NO_PERMISSION);
            }
        } else if(userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.HOTEL.key)){
            HotelOrgRelatedInfoVO hotelOrgRelatedInfoVO = GenericAppUtility.getHotelOrgRelatedInfoVO(userSession);
            if(!hotelOrgRelatedInfoVO.getHotelIdList().contains(hotel.getHotelId())){
                GenericAppUtility.serviceError(ErrorCode.HOTEL_NO_PERMISSION);
            }
        }
        hotelBidDetailResponse.setMainPicUrl(hotel.getMainPicUrl());

        // 查询POI信息
        if(Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.PLATFORM.key) ||
                Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.DISTRIBUTOR.key)
        ){
            List<ProjectPoiVO> cityPoiList = projectPoiMapper.selectPoiInfoByProjectId(project.getProjectId(), hotel.getCityCode());
            if(CollectionUtils.isNotEmpty(cityPoiList)){
                // BigDecimal lngGoogle, BigDecimal latGoogle, List<ProjectPoiVO> cityPoiList){
                MinDistancePoiInfo minDistancePoiInfo = PoiUtility.filteMinDistancePoiInfo(hotel.getLngGoogle(), hotel.getLatGoogle(), cityPoiList);
                if(minDistancePoiInfo.getMinDistance() != null){
                    hotelBidDetailResponse.setPoiName(minDistancePoiInfo.getPoiName());
                    hotelBidDetailResponse.setPoiDistance(minDistancePoiInfo.getMinDistanceKmFormat());
                }
            }
        }

        // 查询项目
        hotelBidDetailResponse.setCurrencyCode(projectIntentHotel.getCurrencyCode());

        // --------------------------------------------------------------- 报价策略
        // 项目项目报价策略
        ProjectHotelTendStrategyVO projectHotelTendStrategyVO = projectHotelTendStrategyMapper.selectByProjectId(projectIntentHotel.getProjectId());
        hotelBidDetailResponse.setProjectHotelTendStrategy(projectHotelTendStrategyVO);

        // 设置酒店报价策略
        BidProjectStrategyVO bidProjectStrategyVO = prepareBidProjectStrategyVO(projectIntentHotel.getProjectIntentHotelId());
        hotelBidDetailResponse.setBidProjectStrategy(bidProjectStrategyVO);

        // 查询Lanyon导入数量
        if(Objects.equals(projectIntentHotel.getIsUpload(), RfpConstant.constant_1) && Objects.equals(projectIntentHotel.getBidUploadSource(), BidUploadSourceEnum.LANYON.key)){
            hotelBidDetailResponse.setIsLanyonImport(RfpConstant.constant_1);
            int lanyonImportDataCount = lanyonImportDataMapper.getLanyonImportDataCount(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
            hotelBidDetailResponse.setLanyonImportDataCount(lanyonImportDataCount);
        }

        // --------------------------------------------------------------- 酒店信息

        // 查询酒店基本信息
        HotelInfoVO hotelInfoVO = prepareHotelInfoVO(languageId, hotel);
        hotelBidDetailResponse.setHotelInfo(hotelInfoVO);

        List<HotelImage> hotelImages =  hotelManager.queryHotelImages(Collections.singletonList(hotelInfoVO.getHotelId()));
        if(CollectionUtils.isNotEmpty(hotelImages)){
            hotelBidDetailResponse.setHotelImageList(hotelImages.get(0).getImages());
        }

        // ------------------------------------------------------- 自定义策略
        // 查询项目自定义策略
        List<ProjectBidCustomTendStrategyVO> projectCustomTendStrategyList = projectManager.queryProjectCustomTendStrategyInfo(project.getProjectId());
        hotelBidDetailResponse.setProjectCustomTendStrategyList(projectCustomTendStrategyList);

        // 设置酒店自定义报价承诺
        List<BidCustomStrategyVO> bidCusomStrategyList = queryBidCustomStrategy(projectIntentHotel.getProjectIntentHotelId(), projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        hotelBidDetailResponse.setBidCustomStrategyList(bidCusomStrategyList);

        // -----------------------------------------------  设置可用日期
        List<BidApplicableDayVO> priceApplicableDayList = priceApplicableDayMapper.selectVOListByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        if(CollectionUtils.isNotEmpty(priceApplicableDayList)){
            hotelBidDetailResponse.setBidApplicableDayList(priceApplicableDayList);
        }
        hotelBidDetailResponse.setBidApplicableDayList(priceApplicableDayList);

        // --------------------------------------------  设置不可以日期
        List<BidUnApplicableDayVO> priceUnApplicableDayList = priceUnapplicableDayMapper.selectVOListByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        if(CollectionUtils.isNotEmpty(priceUnApplicableDayList)){
            hotelBidDetailResponse.setBidUnApplicableDayList(priceUnApplicableDayList);
        }
        // --------------------------------------------  设置项目报价信息
        // 税费设置
        BidHotelTaxSettingsVO bidHotelTaxSettings = projectHotelTaxSettingsMapper.queryBidHotelTaxSettingsVO(projectIntentHotel.getProjectIntentHotelId());
        hotelBidDetailResponse.setBidHotelTaxSettings(bidHotelTaxSettings);

        List<RoomNameInfoVO> roomNameInfoList = hotelManager.queryRoomNameListByHotelId(languageId, hotel.getHotelId());
        hotelBidDetailResponse.setRoomNameList(roomNameInfoList);

        // 查询操作日期
       if(userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.PLATFORM.key)) {
           List<BidOperateLogEntity> bidOperateLogList = bidOperateLogMapper.queryByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
           List<BidOperateLogVO> bidOperateLogVOList = bidOperateLogList.stream().map(bidOperateLog -> {
               BidOperateLogVO bidOperateLogVO = new BidOperateLogVO();
               BeanUtils.copyProperties(bidOperateLog, bidOperateLogVO);
               bidOperateLogVO.setOperator(bidOperateLog.getCreator());
               bidOperateLogVO.setOperateTime(bidOperateLog.getCreateTime());
               return bidOperateLogVO;
           }).collect(Collectors.toList());
           hotelBidDetailResponse.setBidOperateLogList(bidOperateLogVOList);
       }

        // 查询酒店最近POI名称
        List<ProjectPoiVO> cityPoiList = projectPoiMapper.selectPoiInfoByProjectId(req.getProjectId(), hotel.getCityCode());
        if(CollectionUtils.isNotEmpty(cityPoiList)){
            // BigDecimal lngGoogle, BigDecimal latGoogle, List<ProjectPoiVO> cityPoiList){
            MinDistancePoiInfo minDistancePoiInfo = PoiUtility.filteMinDistancePoiInfo(hotel.getLngGoogle(), hotel.getLatGoogle(), cityPoiList);
            if(minDistancePoiInfo.getMinDistance() != null){
                hotelBidDetailResponse.setPoiName(minDistancePoiInfo.getPoiName());
                hotelBidDetailResponse.setPoiDistance(minDistancePoiInfo.getMinDistanceKmFormat());
            }
        }

        // 查询备注信息
        List<ProjectHotelRemarkEntity> projectHotelRemarkList = projectHotelRemarkMapper.selectByProjectId(project.getProjectId(), hotel.getHotelId());
        List<ProjectHotelRemarkResponse> projectHotelRemarkResponseList = projectHotelRemarkList.stream().map(projectHotelRemark -> {
            ProjectHotelRemarkResponse projectHotelRemarkResponse = new ProjectHotelRemarkResponse();
            BeanUtils.copyProperties(projectHotelRemark, projectHotelRemarkResponse);
            projectHotelRemarkResponse.setRemark(GenericAppUtility.getText(languageId, projectHotelRemark.getRemark()));
            return projectHotelRemarkResponse;
        }).collect(Collectors.toList());
        hotelBidDetailResponse.setProjectHotelRemarkResponseList(projectHotelRemarkResponseList);

        return hotelBidDetailResponse;

    }

    public List<BidHotelPriceLevelInfoVO> querBidHotelPriceLevelInfoList(UserSession userSession, int languageId,  int projectIntentHotelId, ProjectIntentHotelEntity projectIntentHotel, Long hotelId, List<RoomNameInfoVO> roomNameInfoList){
        // 平台/企业用户
        int userOrgTypeId = userSession.getUserOrg().getOrgType();
        boolean isViewTaxInfoUser = userOrgTypeId == OrgTypeEnum.PLATFORM.key || userOrgTypeId == OrgTypeEnum.DISTRIBUTOR.key;

        // 查询酒店房型列表
        if(roomNameInfoList == null) {
            roomNameInfoList = hotelManager.queryRoomNameListByHotelId(languageId, hotelId);
        }
        if(projectIntentHotel == null){
            projectIntentHotel = projectIntentHotelMapper.selectById(projectIntentHotelId);
        }

        Map<Long, RoomNameInfoVO> roomNameInfoMap = roomNameInfoList.stream().collect(Collectors.toMap(RoomNameInfoVO::getRoomId, Function.identity()));
        List<BidHotelPriceLevelInfoVO> bidHotelPriceLevelInfoVOList = new ArrayList<>();
        List<ProjectHotelPriceLevelEntity> hotelPriceLevelList = projectHotelPriceLevelMapper.queryByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        if (CollectionUtils.isNotEmpty(hotelPriceLevelList)) {
            // 税费信息
            BidHotelTaxSettingsVO bidHotelTaxSettings = null;
            BidHotelTaxSettingsVO lastYearBidHotelTaxSettings = null;
            if(isViewTaxInfoUser) {
                // 查询当前税费信息
                bidHotelTaxSettings = projectHotelTaxSettingsMapper.queryBidHotelTaxSettingsVO(projectIntentHotel.getProjectIntentHotelId());
                // 查询去年税费信息
                ProjectEntity project = cachedProjectService.getById(projectIntentHotel.getProjectId());
                if (project.getRelatedProjectId() != null) {
                    lastYearBidHotelTaxSettings = projectHotelTaxSettingsMapper.queryBidHotelTaxSettingsVOByProjectHotelId(project.getRelatedProjectId(), hotelId);
                }
            }

            // 查询报价信息获取币种
            String currencyCode = projectIntentHotel.getCurrencyCode();
            BigDecimal exchangeRate = null;
            String viewCurrencyCode = currencyCode;
            BigDecimal viewCurrencyExchangeRate = new BigDecimal("1");
            if(StringUtil.isValidString(currencyCode)) {
                CurrencyExchangeRateEntity currencyExchangeRateEntity = cachedCurrencyService.getCurrencyRateInfo(currencyCode);
                exchangeRate = currencyExchangeRateEntity.getInverseExchangeRate();

                // 设置显示汇率和显示币种
                CurrencyExchangeRateEntity viewCurrency = cachedCurrencyService.getCurrencyRateInfo(GenericAppUtility.getRequestHeaderCurrency());
                if (Objects.nonNull(viewCurrency)) {
                    viewCurrencyCode = viewCurrency.getCurrencyCode();
                    viewCurrencyExchangeRate = NumberUtil.div(viewCurrency.getExchangeRate(), currencyExchangeRateEntity.getExchangeRate(), 10);
                }
            }

            // 查询可用房型列表
            List<BidApplicableRoomVO> applicableRoomList = buildBidApplicableRoomList(projectIntentHotel.getProjectIntentHotelId(), roomNameInfoMap);

            Map<Integer, List<BidApplicableRoomVO>> priceLevelApplicableRoomMap = applicableRoomList.stream().collect(Collectors.groupingBy(BidApplicableRoomVO::getHotelPriceLevelId));
            // 查询价格组
            List<ProjectHotelPriceGroupEntity> hotelPriceGroupList = projectHotelPriceGroupMapper.selectByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            Map<Integer, List<ProjectHotelPriceGroupEntity>> priceLevelHotelPriceGroupMap = hotelPriceGroupList.stream().collect(Collectors.groupingBy(ProjectHotelPriceGroupEntity::getHotelPriceLevelId));

            // 查询价格
            List<ProjectHotelPriceEntity> hotelPriceList = projectHotelPriceMapper.selectByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            Map<Integer, List<ProjectHotelPriceEntity>> hotelGroupPriceMap = hotelPriceList.stream().collect(Collectors.groupingBy(ProjectHotelPriceEntity::getHotelPriceGroupId));
            for(ProjectHotelPriceLevelEntity projectHotelPriceLevel: hotelPriceLevelList){
                // 设置房档信息
                BidHotelPriceLevelInfoVO bidHotelPriceLevelInfo = new BidHotelPriceLevelInfoVO();
                BeanUtils.copyProperties(projectHotelPriceLevel, bidHotelPriceLevelInfo);

                // 设置币种汇率
                bidHotelPriceLevelInfo.setCurrencyCode(currencyCode);
                bidHotelPriceLevelInfo.setExchangeRate(exchangeRate);
                bidHotelPriceLevelInfo.setViewCurrencyCode(viewCurrencyCode);
                bidHotelPriceLevelInfo.setViewCurrencyExchangeRate(viewCurrencyExchangeRate);

                StringBuilder roomNameDesc = new StringBuilder();
                // 设置房档房型
                if(priceLevelApplicableRoomMap.containsKey(projectHotelPriceLevel.getHotelPriceLevelId())){
                    bidHotelPriceLevelInfo.setRoomList(priceLevelApplicableRoomMap.get(projectHotelPriceLevel.getHotelPriceLevelId()));
                    for(BidApplicableRoomVO bidApplicableRoomVO : bidHotelPriceLevelInfo.getRoomList()){
                        roomNameDesc.append(bidApplicableRoomVO.getRoomName()).append(",");
                    }
                }
                if(StringUtil.isValidString(roomNameDesc.toString())){
                    bidHotelPriceLevelInfo.setRoomNameDesc(roomNameDesc.substring(0, roomNameDesc.length()-1));
                } else if (StringUtil.isValidString(projectHotelPriceLevel.getLanyonRoomDesc())){
                    // 默认显示lanyon room desc
                    bidHotelPriceLevelInfo.setRoomNameDesc(projectHotelPriceLevel.getLanyonRoomDesc());
                    String[] roomDesArray = projectHotelPriceLevel.getLanyonRoomDesc().split(",");
                    List<BidApplicableRoomVO> roomVOList = new ArrayList<>();
                    for(String roomDes: roomDesArray){
                        BidApplicableRoomVO bidApplicableRoomVO = new BidApplicableRoomVO();
                        bidApplicableRoomVO.setRoomName(roomDes);
                        roomVOList.add(bidApplicableRoomVO);
                    }
                    bidHotelPriceLevelInfo.setRoomList(roomVOList);
                }
                // 设置房当价格组
                if(priceLevelHotelPriceGroupMap.containsKey(projectHotelPriceLevel.getHotelPriceLevelId())){
                    List<ProjectHotelPriceGroupEntity> priceGroupList = priceLevelHotelPriceGroupMap.get(projectHotelPriceLevel.getHotelPriceLevelId());
                    List<BidHotelPriceGroupVO> bidHotelPriceGroupList = new ArrayList<>();
                    for(ProjectHotelPriceGroupEntity priceGroup: priceGroupList){
                        BidHotelPriceGroupVO bidHotelPriceGroup = new BidHotelPriceGroupVO();
                        BeanUtils.copyProperties(priceGroup, bidHotelPriceGroup);
                        if(hotelGroupPriceMap.containsKey(priceGroup.getHotelPriceGroupId())){
                            // 设置价格组价格
                            List<ProjectHotelPriceEntity> priceList = hotelGroupPriceMap.get(priceGroup.getHotelPriceGroupId());
                            List<BidHotelPriceVO> bidHotelPriceList = new ArrayList<>();
                            for(ProjectHotelPriceEntity price: priceList){
                                BidHotelPriceVO bidHotelPrice = new BidHotelPriceVO();
                                BeanUtils.copyProperties(price, bidHotelPrice);
                                // 初始化包含税费价格 （方便前端统一显示包含税费价格）
                                bidHotelPrice.setOnePersonIncludeTaxPrice(bidHotelPrice.getOnePersonPrice());
                                bidHotelPrice.setTwoPersonIncludeTaxPrice(bidHotelPrice.getTwoPersonPrice());
                                bidHotelPrice.setLastOnePersonIncludeTaxPrice(bidHotelPrice.getLastOnePersonPrice());
                                bidHotelPrice.setLastTwoPersonIncludeTaxPrice(bidHotelPrice.getLastTwoPersonPrice());

                                // 企业或者平台需要计算税费
                                if (isViewTaxInfoUser) {
                                    // 根据税费调整税费价格
                                    if(bidHotelPrice.getOnePersonPrice() != null && bidHotelTaxSettings != null && bidHotelPrice.getOnePersonPrice().compareTo(BigDecimal.ZERO) > 0) {
                                        bidHotelPrice.setOnePersonPriceTaxInfo(BidUtil.calculatePriceTax(bidHotelPrice.getOnePersonPrice(), bidHotelTaxSettings));
                                        bidHotelPrice.setOnePersonIncludeTaxPrice(bidHotelPrice.getOnePersonPriceTaxInfo().getTotalCostAmount());
                                    }
                                    if(bidHotelPrice.getTwoPersonPrice() != null && bidHotelTaxSettings != null && bidHotelPrice.getTwoPersonPrice().compareTo(BigDecimal.ZERO) > 0) {
                                        bidHotelPrice.setTwoPersonPriceTaxInfo(BidUtil.calculatePriceTax(bidHotelPrice.getTwoPersonPrice(), bidHotelTaxSettings));
                                        bidHotelPrice.setTwoPersonIncludeTaxPrice(bidHotelPrice.getTwoPersonPriceTaxInfo().getTotalCostAmount());
                                    }

                                    // 计算去年包含税费价格
                                    if(bidHotelPrice.getLastOnePersonPrice() != null && lastYearBidHotelTaxSettings != null && bidHotelPrice.getLastOnePersonPrice().compareTo(BigDecimal.ZERO) > 0) {
                                        HotelPriceTaxVO lastOnePersonPriceTaxInfo = BidUtil.calculatePriceTax(bidHotelPrice.getLastOnePersonPrice(), lastYearBidHotelTaxSettings);
                                        bidHotelPrice.setLastOnePersonIncludeTaxPrice(lastOnePersonPriceTaxInfo.getTotalCostAmount());
                                        bidHotelPrice.setLastOnePersonPriceTaxInfo(lastOnePersonPriceTaxInfo);
                                    }
                                    if(bidHotelPrice.getLastTwoPersonPrice() != null && lastYearBidHotelTaxSettings != null && bidHotelPrice.getLastTwoPersonPrice().compareTo(BigDecimal.ZERO) > 0) {
                                        HotelPriceTaxVO lastTwoPersonPriceTaxInfo = BidUtil.calculatePriceTax(bidHotelPrice.getLastTwoPersonPrice(), lastYearBidHotelTaxSettings);
                                        bidHotelPrice.setLastTwoPersonIncludeTaxPrice(lastTwoPersonPriceTaxInfo.getTotalCostAmount());
                                        bidHotelPrice.setLastTwoPersonPriceTaxInfo(lastTwoPersonPriceTaxInfo);
                                    }
                                }
                                bidHotelPriceList.add(bidHotelPrice);
                            }
                            bidHotelPriceGroup.setBidHotelPriceList(bidHotelPriceList);
                        }
                        bidHotelPriceGroupList.add(bidHotelPriceGroup);
                    }
                    bidHotelPriceLevelInfo.setBidHotelPriceGroupList(bidHotelPriceGroupList);
                }
                bidHotelPriceLevelInfoVOList.add(bidHotelPriceLevelInfo);
            }
        }
        return bidHotelPriceLevelInfoVOList;
    }

    /**
     * 构建报价可用房型列表(房型 id + 自定义房型名称)
     */
    private List<BidApplicableRoomVO> buildBidApplicableRoomList(int projectIntentHotelId, Map<Long, RoomNameInfoVO> roomNameInfoMap) {
        //查询可用房型
        List<BidApplicableRoomVO> applicableRoomList = priceApplicableRoomMapper.selectBidApplicableRoom(projectIntentHotelId);
        if (CollUtil.isEmpty(applicableRoomList)) {
            return applicableRoomList;
        }

        // 设置房型名称房型
        applicableRoomList.forEach(item -> {
            if (Objects.nonNull(item.getRoomTypeId())) {
                item.setRoomName(Optional.ofNullable(roomNameInfoMap.get(item.getRoomTypeId())).map(RoomNameInfoVO::getRoomName).orElse(null));
            } else if (StringUtils.isNotBlank(item.getCustomRoomTypeName())) {
                item.setRoomName(item.getCustomRoomTypeName());
            }
        });
        return applicableRoomList;
    }

    private HotelInfoVO prepareHotelInfoVO(int languageId, HotelEntity hotel){
        HotelInfoVO hotelInfoVO = new HotelInfoVO();
        HotelUtility.calculateNullGoogleLngLat(hotel);
        BeanUtils.copyProperties(hotel, hotelInfoVO);
        hotelInfoVO.setHotelName(HotelUtility.getHotelName(languageId, hotel));
        hotelInfoVO.setHotelAddress(HotelUtility.getHotelAddress(languageId, hotel));
        hotelInfoVO.setHotelStarName(HotelStarEnum.getTextByKey(hotel.getHotelStar(), languageId));
        if(hotel.getHotelBrandId() != null) {
            hotelInfoVO.setHotelBrandName(GenericAppUtility.getHotelBrandName(languageId, hotel.getHotelBrandId()));
        }
        hotelInfoVO.setRoomCount(hotel.getRoomNum());
        return hotelInfoVO;
    }

    private BidProjectStrategyVO prepareBidProjectStrategyVO(Integer projectIntentHotelId){
        BidProjectStrategyVO bidProjectStrategyVO = new BidProjectStrategyVO();
        ProjectHotelBidStrategyEntity projectHotelBidStrategy = projectHotelBidStrategyMapper.selectById(projectIntentHotelId);
        BeanUtils.copyProperties(projectHotelBidStrategy, bidProjectStrategyVO);
        return bidProjectStrategyVO;
    }


    public GoToHotelBidResponse goToHotelBid(HttpServletRequest request, GoToHotelBidRequest req){
        UserSession userSession = UserSession.get();
        int languageId = GenericAppUtility.getRequestHeaderLanguage(request);

        // 定义返回值
        GoToHotelBidResponse goToHotelBidResponse = new GoToHotelBidResponse();
        // 查询项目
        ProjectEntity project = projectMapper.selectById(req.getProjectId());
        if(Objects.isNull(project)){
            GenericAppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }
        Date enrollEndTime = DateUtil.endOfDay(project.getPriceMonitorEndDate());
        Date now = new Date();
        if (now.compareTo(enrollEndTime) > 0) {
            GenericAppUtility.serviceError(ErrorCode.CANNOT_BID_DUE_TO_AFTER_BID_END_DATE_TIME);
        }
        goToHotelBidResponse.setProjectId(req.getProjectId());
        goToHotelBidResponse.setHotelId(req.getHotelId());

        HotelEntity hotel = cachedHotelService.getById(req.getHotelId());
        if(hotel == null){
            GenericAppUtility.serviceError(ErrorCode.HOTEL_NOT_EXIST);
        }
        ProjectIntentHotelGroupEntity projectIntentHotelGroup = null;

        // 查询项目意向酒店
        ProjectIntentHotelEntity projectIntentHotel = prepareProjectIntentHotel(userSession, req.getProjectId(), hotel, projectIntentHotelGroup);
        if(projectIntentHotel.getBidState() > HotelBidStateEnum.NO_BID.bidState && !projectIntentHotel.getBidState().equals(HotelBidStateEnum.UNDER_NEGOTIATION.bidState)){
            GenericAppUtility.serviceError(ErrorCode.CANNOT_BID_DUE_TO_ALREADY_SUBMIT_BID);
        }

        // 设置报价默认币种
        goToHotelBidResponse.setCurrencyCode(projectIntentHotel.getCurrencyCode());

        // 检查报价权限
        if (userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.HOTELGROUP.key)) {
            // 查询酒店集团默认报价
            projectIntentHotelGroup = projectIntentHotelGroupMapper.queryHotelGroupProjectIntentGroup(req.getProjectId(), userSession.getUserOrg().getOrgId());
            if (Objects.isNull(projectIntentHotelGroup)) {
                GenericAppUtility.serviceError(ErrorCode.PROJECT_NOT_INVITE_HOTEL_GROUP);
            }
            HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfo = GenericAppUtility.getHotelGroupUserRelatedInfoVO(userSession);
            if (projectIntentHotelGroup.getIsBrandLimit() == RfpConstant.constant_1 && !hotelGroupUserRelatedInfo.getHotelBrandIdSet().contains(hotel.getHotelBrandId())) {
                GenericAppUtility.serviceError(ErrorCode.HOTEL_NOT_BELONG_HOTEL_GROUP);
            }

        } else if (userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.HOTEL.key)) {
            HotelOrgRelatedInfoVO hotelOrgRelatedInfo = GenericAppUtility.getHotelOrgRelatedInfoVO(userSession);
            if (!hotelOrgRelatedInfo.getHotelIdList().contains(req.getHotelId())) {
                GenericAppUtility.serviceError(ErrorCode.HOTEL_NO_PERMISSION);
            }
        } else {
            GenericAppUtility.serviceError(ErrorCode.INVALID_USER);
        }

        ProjectIntentHotelBidVO projectIntentHotelBidVO = new ProjectIntentHotelBidVO();
        BeanUtils.copyProperties(projectIntentHotel, projectIntentHotelBidVO);
        // 设置员工权限
        projectIntentHotelBidVO.setEmployRight(projectIntentHotel.getEmployeeRight());
        if(StringUtil.isValidString(projectIntentHotel.getEmployeeRightFileUrl())) {
            projectIntentHotelBidVO.setEmployRightFileUrl(JsonUtil.jsonToList(projectIntentHotel.getEmployeeRightFileUrl(), UploadFileVO.class));
        }
        goToHotelBidResponse.setProjectIntentHotelBid(projectIntentHotelBidVO);

        // --------------------------------------------------------------- 报价策略
        // 项目项目报价策略
        ProjectHotelTendStrategyVO projectHotelTendStrategyVO = projectHotelTendStrategyMapper.selectByProjectId(project.getProjectId());
        goToHotelBidResponse.setProjectHotelTendStrategy(projectHotelTendStrategyVO);

        // 设置酒店报价策略
        BidProjectStrategyVO bidProjectStrategyVO = new BidProjectStrategyVO();
        ProjectHotelBidStrategyEntity projectHotelBidStrategy = projectHotelBidStrategyMapper.selectById(projectIntentHotel.getProjectIntentHotelId());
        if(projectHotelBidStrategy != null){
            BeanUtils.copyProperties(projectHotelBidStrategy, bidProjectStrategyVO);
        } else if(!Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.UNDER_NEGOTIATION.bidState) && projectIntentHotelGroup != null){
            // 取酒店集团默认报价策略
            BeanUtils.copyProperties(projectIntentHotelGroup, bidProjectStrategyVO);
        }
        goToHotelBidResponse.setBidProjectStrategy(bidProjectStrategyVO);

        // --------------------------------------------------------------- 酒店信息

        // 查询酒店基本信息
        HotelInfoVO hotelInfoVO = new HotelInfoVO();
        HotelUtility.calculateNullGoogleLngLat(hotel);
        BeanUtils.copyProperties(hotel, hotelInfoVO);
        hotelInfoVO.setHotelName(HotelUtility.getHotelName(languageId, hotel));
        hotelInfoVO.setHotelAddress(HotelUtility.getHotelAddress(languageId, hotel));
        hotelInfoVO.setHotelStarName(HotelStarEnum.getTextByKey(hotel.getHotelStar(), languageId));
        goToHotelBidResponse.setHotelInfo(hotelInfoVO);

        // ------------------------------------------------------- 自定义策略
        // 查询项目自定义策略
        List<ProjectBidCustomTendStrategyVO> projectCustomTendStrategyList = projectManager.queryProjectCustomTendStrategyInfo(project.getProjectId());
        goToHotelBidResponse.setProjectCustomTendStrategyList(projectCustomTendStrategyList);

        // 设置酒店自定义报价承诺
        List<BidCustomStrategyVO> bidCustomStrategyList = queryBidCustomStrategy(projectIntentHotel.getProjectIntentHotelId(), projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        // 如果没有自定义报价策略, 则查酒店集团默认自定义报价策略
        if (CollectionUtils.isEmpty(bidCustomStrategyList) && projectIntentHotelGroup != null) {
            List<HGroupDefaultCusStrategyVO> hotelGroupDefaultCusStrategyList = queryHotelGroupDefaultCustomStrategy(projectIntentHotelGroup.getProjectIntentHotelGroupId());
            if (CollectionUtils.isNotEmpty(hotelGroupDefaultCusStrategyList)) {
                bidCustomStrategyList = hotelGroupDefaultCusStrategyList.stream().map(this::convertToBidCusomStrategyVOFromHGroup).collect(Collectors.toList());
            }
        }
        goToHotelBidResponse.setBidCustomStrategyList(bidCustomStrategyList);

        // -----------------------------------------------  设置可用日期
        List<BidApplicableDayVO> priceApplicableDayList = priceApplicableDayMapper.selectVOListByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        if(CollectionUtils.isNotEmpty(priceApplicableDayList)){
            goToHotelBidResponse.setBidApplicableDayList(priceApplicableDayList);
        } else if(projectIntentHotelGroup != null){
            // 查询适用日期
            priceApplicableDayList = hotelGroupDefaultApplicableDayMapper.queryDefaultApplicableDayInfo(projectIntentHotelGroup.getProjectIntentHotelGroupId());
        }
        goToHotelBidResponse.setBidApplicableDayList(priceApplicableDayList);
        addBasePriceApplicableDay(priceApplicableDayList, project);

        // --------------------------------------------  设置不可以日期
        List<BidUnApplicableDayVO> priceUnApplicableDayList = priceUnapplicableDayMapper.selectVOListByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        if(CollectionUtils.isNotEmpty(priceUnApplicableDayList)){
            goToHotelBidResponse.setBidUnApplicableDayList(priceUnApplicableDayList);
        } else if(projectIntentHotelGroup != null){
            // 查询适用日期
            priceUnApplicableDayList = hotelGroupDefaultUnapplicableDayMapper.queryDefaultUnApplicableDayVOList(projectIntentHotelGroup.getProjectIntentHotelGroupId());
        }
        goToHotelBidResponse.setBidUnApplicableDayList(priceUnApplicableDayList);

        // --------------------------------------------  设置项目报价信息
        ProjectBidInfoVO projectBidInfoVO = new ProjectBidInfoVO();
        projectBidInfoVO.setIsBidLastYear(0);
        if(project.getRelatedProjectId() != null){
            ProjectIntentHotelEntity lastYearProjectIntentHotel = projectIntentHotelMapper.queryByProjectAndHotelId(project.getRelatedProjectId(), req.getHotelId());
            if(lastYearProjectIntentHotel != null){
                projectBidInfoVO.setIsBidLastYear(1);
                projectBidInfoVO.setLastYearProjectId(project.getRelatedProjectId());
            }
        }
        goToHotelBidResponse.setProjectBidInfo(projectBidInfoVO);

        // 查询酒店房型列表
        List<RoomNameInfoVO> roomNameInfoList = hotelManager.queryRoomNameListByHotelId(languageId, hotel.getHotelId());
        goToHotelBidResponse.setRoomNameList(roomNameInfoList);

        // 税费设置
        BidHotelTaxSettingsVO bidHotelTaxSettings = projectHotelTaxSettingsMapper.queryBidHotelTaxSettingsVO(projectIntentHotel.getProjectIntentHotelId());
        if(Objects.isNull(bidHotelTaxSettings)){
            bidHotelTaxSettings = new BidHotelTaxSettingsVO();
        }
        goToHotelBidResponse.setBidHotelTaxSettings(bidHotelTaxSettings);

        // 查询备注信息
        List<ProjectHotelRemarkEntity> projectHotelRemarkList = projectHotelRemarkMapper.selectByProjectId(project.getProjectId(), hotel.getHotelId());
        if(CollectionUtils.isNotEmpty(projectHotelRemarkList)) {
            List<ProjectHotelRemarkResponse> projectHotelRemarkResponseList = projectHotelRemarkList.stream().map(projectHotelRemark -> {
                ProjectHotelRemarkResponse projectHotelRemarkResponse = new ProjectHotelRemarkResponse();
                BeanUtils.copyProperties(projectHotelRemark, projectHotelRemarkResponse);
                projectHotelRemarkResponse.setRemark(GenericAppUtility.getText(languageId, projectHotelRemark.getRemark()));
                return projectHotelRemarkResponse;
            }).collect(Collectors.toList());
            goToHotelBidResponse.setProjectHotelRemarkResponseList(projectHotelRemarkResponseList);
        }

        return goToHotelBidResponse;

    }

    /**
     * 查询自定义报价策略
     */
    public List<BidCustomStrategyVO> queryBidCustomStrategy(int projectIntentHotelId, int projectId, long hotelId) {
        // 查询报价策略
        List<ProjectCustomBidStrategyEntity> projectCustomBidStrategyList = projectCustomBidStrategyMapper.queryByProjectIntentHotelId(projectIntentHotelId);
        if (CollUtil.isEmpty(projectCustomBidStrategyList)) {
            return Collections.emptyList();
        }

        // 查找选项类型的 option
        Set<Long> optionTypeStrategyIds = projectCustomBidStrategyList.stream()
            .filter(e -> CustomStrategyTypeEnum.isOptionType(e.getStrategyType()))
            .map(e -> Long.valueOf(e.getCustomTendStrategyId())).collect(Collectors.toSet());
        List<CustomBidStrategyOptionEntity> options = customBidStrategyOptionMapper.selectByStrategyIdAndProjectIdAndHotelId(optionTypeStrategyIds, projectId, hotelId);
        Map<Long, List<CustomBidStrategyOptionEntity>> optionsMap = options.stream().collect(Collectors.groupingBy(CustomBidStrategyOptionEntity::getStrategyId));

        // 构建报价策略列表
        return projectCustomBidStrategyList.stream()
            .map(e -> this.convertToBidCustomStrategyVO(e, optionsMap.get(Long.valueOf(e.getCustomTendStrategyId()))))
            .collect(Collectors.toList());
    }

    /**
     * 查询酒店集团自定义报价策略
     */
    public List<HGroupDefaultCusStrategyVO> queryHotelGroupDefaultCustomStrategy(int projectIntentHotelGroupId) {
        // 查询酒店集团其他承诺
        List<HGroupDefaultCusStrategyVO> hotelGroupDefaultCusStrategyList = hGroupDefaultCusStrategyMapper.queryHGroupDefaultCusStrategy(projectIntentHotelGroupId);
        if (CollectionUtils.isEmpty(hotelGroupDefaultCusStrategyList)) {
            return Collections.emptyList();
        }

        // 查询选项, 只有选项类型的需要查
        Set<Long> optionTypeStrategyIds = hotelGroupDefaultCusStrategyList.stream()
            .filter(e -> CustomStrategyTypeEnum.isOptionType(e.getStrategyType()))
            .map(e -> Long.valueOf(e.getCustomTendStrategyId())).collect(Collectors.toSet());
        if (CollUtil.isEmpty(optionTypeStrategyIds)) {
            return hotelGroupDefaultCusStrategyList;
        }

        // 转换 option
        List<HGroupDefaultCusStrategyOptionEntity> options =
            hGroupDefaultCusStrategyOptionMapper.selectByProjectIntentHotelGroupIdAndStrategyIds(projectIntentHotelGroupId, optionTypeStrategyIds);
        Map<Long, List<HGroupDefaultCusStrategyOptionEntity>> optionsMap = options.stream().collect(Collectors.groupingBy(HGroupDefaultCusStrategyOptionEntity::getStrategyId));
        hotelGroupDefaultCusStrategyList.forEach(e -> {
            List<HGroupDefaultCusStrategyOptionEntity> optionList = optionsMap.get(Long.valueOf(e.getCustomTendStrategyId()));
            if (CollectionUtils.isNotEmpty(optionList)) {
                e.setOptions(optionList.stream().map(option -> {
                    HGroupDefaultCusStrategyOptionVO hGroupDefaultCusStrategyOptionVO = new HGroupDefaultCusStrategyOptionVO();
                    BeanUtils.copyProperties(option, hGroupDefaultCusStrategyOptionVO);
                    return hGroupDefaultCusStrategyOptionVO;
                }).collect(Collectors.toList()));
            }
        });
        return hotelGroupDefaultCusStrategyList;
    }

    public BidHotelPriceLevelInfoVO queryHotelPriceLevel(HttpServletRequest request, QueryHotelPriceLevelRequest req){
        int languageId = GenericAppUtility.getRequestHeaderLanguage(request);

        ProjectHotelPriceLevelEntity hotelPriceLevel = projectHotelPriceLevelMapper.selectById(req.getHotelPriceLevelId());

        // 查询酒店房型列表
        List<RoomNameInfoVO> roomNameInfoList = hotelManager.queryRoomNameListByHotelId(languageId, hotelPriceLevel.getHotelId());

        Map<Long, RoomNameInfoVO> roomNameInfoMap = roomNameInfoList.stream().collect(Collectors.toMap(RoomNameInfoVO::getRoomId, Function.identity()));

        //查询可用房型
        List<PriceApplicableRoomEntity> applicableRoomList = priceApplicableRoomMapper.selectPriceApplicableRoomList(req.getHotelPriceLevelId());
        List<BidApplicableRoomVO> applicableRoomVOList = applicableRoomList.stream().map(item -> {
            BidApplicableRoomVO applicableRoomVO = new BidApplicableRoomVO();
            BeanUtils.copyProperties(item, applicableRoomVO);
            RoomNameInfoVO roomNameInfoVO = roomNameInfoMap.get(item.getRoomTypeId());
            applicableRoomVO.setRoomName(Optional.ofNullable(roomNameInfoVO).map(RoomNameInfoVO::getRoomName).orElse(null));
            return applicableRoomVO;
        }).collect(Collectors.toList());

        // 查询价格组
        List<ProjectHotelPriceGroupEntity> priceGroupList = projectHotelPriceGroupMapper.selectByHotelPriceLevelId(req.getHotelPriceLevelId());

        // 查询价格
        List<ProjectHotelPriceEntity> hotelPriceList = projectHotelPriceMapper.selectByProjectPriceLevelId(req.getHotelPriceLevelId());
        Map<Integer, List<ProjectHotelPriceEntity>> hotelGroupPriceMap = hotelPriceList.stream().collect(Collectors.groupingBy(ProjectHotelPriceEntity::getHotelPriceGroupId));

        // 设置房档信息
        BidHotelPriceLevelInfoVO bidHotelPriceLevelInfo = new BidHotelPriceLevelInfoVO();
        BeanUtils.copyProperties(hotelPriceLevel, bidHotelPriceLevelInfo);

        // 设置房档房型
        bidHotelPriceLevelInfo.setRoomList(applicableRoomVOList);

        // 设置房当价格组
        List<BidHotelPriceGroupVO> bidHotelPriceGroupList = new ArrayList<>();
        for(ProjectHotelPriceGroupEntity priceGroup: priceGroupList){
            BidHotelPriceGroupVO bidHotelPriceGroup = new BidHotelPriceGroupVO();
            BeanUtils.copyProperties(priceGroup, bidHotelPriceGroup);
            if(hotelGroupPriceMap.containsKey(priceGroup.getHotelPriceGroupId())){
                // 设置价格组价格
                List<ProjectHotelPriceEntity> priceList = hotelGroupPriceMap.get(priceGroup.getHotelPriceGroupId());
                List<BidHotelPriceVO> bidHotelPriceList = new ArrayList<>();
                for(ProjectHotelPriceEntity price: priceList){
                    BidHotelPriceVO bidHotelPrice = new BidHotelPriceVO();
                    BeanUtils.copyProperties(price, bidHotelPrice);
                    bidHotelPriceList.add(bidHotelPrice);
                }
                bidHotelPriceGroup.setBidHotelPriceList(bidHotelPriceList);
            }
            bidHotelPriceGroupList.add(bidHotelPriceGroup);
        }
        bidHotelPriceLevelInfo.setBidHotelPriceGroupList(bidHotelPriceGroupList);
        return bidHotelPriceLevelInfo;

    }

    /**
     * 新增或者修改房档房型
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer addOrUpdateHotelPriceLevelRoomInfo(HttpServletRequest httpServletRequest, AddHotelPriceLevelRoomRequest req){
        UserSession userSession = UserSession.get();
        // 查询房档信息
        ProjectHotelPriceLevelEntity projectHotelPriceLevel = projectHotelPriceLevelMapper.selectById(req.getHotelPriceLevelId());

        // 查询报价房档信息
        List<BidApplicableRoomVO> dbPriceApplicableRoomList = priceApplicableRoomMapper.selectBidApplicableRoom(projectHotelPriceLevel.getHotelPriceLevelId());

        // 校验房型
        validateDuplicateRoomType(req.getHotelPriceLevelId(), req.getRoomList(), dbPriceApplicableRoomList);

        // 更新数量
        int insertOrUpdateResult = 0;
        projectHotelPriceLevel.setBigBedRoomCount(req.getBigBedRoomCount());
        projectHotelPriceLevel.setDoubleBedRoomCount(req.getDoubleBedRoomCount());
        projectHotelPriceLevel.setTotalRoomCount(req.getTotalRoomCount());
        projectHotelPriceLevel.setModifier(userSession.getUsername());
        insertOrUpdateResult = projectHotelPriceLevelMapper.updateById(projectHotelPriceLevel);
        if(insertOrUpdateResult == 0){
            GenericAppUtility.serviceError(ErrorCode.ADD_OR_UPDATE_PRICE_APPLICABLE_ROOM_FAILED);
        }

        // 新增或者修改房档案房型
        Set<Integer> dbPriceApplicableRoomIdSet = dbPriceApplicableRoomList.stream().map(BidApplicableRoomVO::getPriceApplicableRoomId).collect(Collectors.toSet());
        Set<Integer> priceApplicableRoomIdSet = req.getRoomList().stream().map(BidApplicableRoomVO::getPriceApplicableRoomId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Integer> deletePriceApplicableRoomIdSet = dbPriceApplicableRoomIdSet.stream().filter(x -> !priceApplicableRoomIdSet.contains(x)).collect(Collectors.toSet());
        if(CollectionUtils.isNotEmpty(deletePriceApplicableRoomIdSet)){
            priceApplicableRoomMapper.deleteByPriceApplicableRoomIds(deletePriceApplicableRoomIdSet);
        }
        List<PriceApplicableRoomEntity> priceApplicableRoomList = new ArrayList<>();
        for (BidApplicableRoomVO room : req.getRoomList()) {
            PriceApplicableRoomEntity priceApplicableRoom = new PriceApplicableRoomEntity();
            priceApplicableRoom.setPriceApplicableRoomId(room.getPriceApplicableRoomId());
            priceApplicableRoom.setProjectIntentHotelId(projectHotelPriceLevel.getProjectIntentHotelId());
            priceApplicableRoom.setProjectId(projectHotelPriceLevel.getProjectId());
            priceApplicableRoom.setHotelId(projectHotelPriceLevel.getHotelId());
            priceApplicableRoom.setHotelPriceLevelId(projectHotelPriceLevel.getHotelPriceLevelId());
            priceApplicableRoom.setDisplayOrder(room.getDisplayOrder());
            priceApplicableRoom.setRoomTypeId(room.getRoomTypeId());
            if (Objects.isNull(room.getRoomTypeId())) {
                priceApplicableRoom.setCustomRoomTypeName(room.getCustomRoomTypeName());
            }
            priceApplicableRoom.setModifier(userSession.getUsername());
            priceApplicableRoom.setCreator(userSession.getUsername());
            priceApplicableRoomList.add(priceApplicableRoom);
        }
        insertOrUpdateResult = priceApplicableRoomMapper.batchInsertOrUpdate(priceApplicableRoomList);
        if(insertOrUpdateResult == 0){
            GenericAppUtility.serviceError(ErrorCode.ADD_OR_UPDATE_PRICE_APPLICABLE_ROOM_FAILED);
        }
        return projectHotelPriceLevel.getHotelPriceLevelId();
    }

    /**
     * 校验重复房型
     * @param hotelPriceLevelId 当前房档等级 id
     * @param roomList 房型列表
     * @param dbPriceApplicableRoomList 已存在的全部房型列表
     */
    private static void validateDuplicateRoomType(Integer hotelPriceLevelId, List<BidApplicableRoomVO> roomList, List<BidApplicableRoomVO> dbPriceApplicableRoomList) {
        // 检查房型是否在其他房档存在
        List<BidApplicableRoomVO> otherLevelApplicableRoomList = dbPriceApplicableRoomList.stream().filter(x -> !x.getHotelPriceLevelId().equals(hotelPriceLevelId)).collect(Collectors.toList());
        // 校验房型 id
        Set<Long> otherLevelApplicableRoomTypeIdList = otherLevelApplicableRoomList.stream().map(BidApplicableRoomVO::getRoomTypeId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> priceApplicableRoomTypeIdSet = roomList.stream().map(BidApplicableRoomVO::getRoomTypeId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.containsAny(otherLevelApplicableRoomTypeIdList, priceApplicableRoomTypeIdSet)) {
            log.error("房型 id : {} 包含在其他房档 : {}", priceApplicableRoomTypeIdSet, otherLevelApplicableRoomTypeIdList);
            GenericAppUtility.serviceError(ErrorCode.ROOM_TYPE_EXIST_IN_OTHER_PRICE_LEVEL);
        }
        // 校验房型名称
        Set<String> otherLevelApplicableRoomNameList = otherLevelApplicableRoomList.stream().map(BidApplicableRoomVO::getCustomRoomTypeName).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<String> priceApplicableRoomNameSet = roomList.stream().map(BidApplicableRoomVO::getCustomRoomTypeName).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.containsAny(otherLevelApplicableRoomNameList, priceApplicableRoomNameSet)) {
            log.error("房型名称 : {} 包含在其他房档 : {}", priceApplicableRoomNameSet, otherLevelApplicableRoomNameList);
            GenericAppUtility.serviceError(ErrorCode.ROOM_TYPE_EXIST_IN_OTHER_PRICE_LEVEL);
        }
    }

    /**
     * 新增或价格房档
     */
    @Transactional(rollbackFor = Throwable.class)
    public Integer addHotelPriceLevel(HttpServletRequest request, AddHotelPriceLevelRequest req){
        UserSession userSession = UserSession.get();

        // 检查用户是否有全新修改
        HotelEntity hotel = cachedHotelService.getById(req.getHotelId());

        validateUserPermission(userSession, req.getProjectId(), hotel);

        // 新增或者修改价格房档信息
        ProjectHotelPriceLevelEntity projectHotelPriceLevel = new ProjectHotelPriceLevelEntity();
        projectHotelPriceLevel.setProjectIntentHotelId(req.getProjectIntentHotelId());
        projectHotelPriceLevel.setProjectId(req.getProjectId());
        projectHotelPriceLevel.setHotelId(req.getHotelId());
        projectHotelPriceLevel.setRoomLevelNo(req.getPriceLevelInfo().getRoomLevelNo());
        projectHotelPriceLevel.setBigBedRoomCount(req.getPriceLevelInfo().getBigBedRoomCount());
        projectHotelPriceLevel.setDoubleBedRoomCount(req.getPriceLevelInfo().getDoubleBedRoomCount());
        projectHotelPriceLevel.setTotalRoomCount(req.getPriceLevelInfo().getTotalRoomCount());
        projectHotelPriceLevel.setModifier(userSession.getUsername());
        projectHotelPriceLevel.setCreator(userSession.getUsername());
        int insertOrUpdateResult = projectHotelPriceLevelMapper.insert(projectHotelPriceLevel);
        if (insertOrUpdateResult == 0) {
            GenericAppUtility.serviceError(ErrorCode.ADD_HOTEL_PRICE_LEVEL_FAILED);
        }

        // 查询报价房档信息
        List<BidApplicableRoomVO> dbPriceApplicableRoomList = priceApplicableRoomMapper.selectBidApplicableRoom(projectHotelPriceLevel.getProjectIntentHotelId());

        // 校验重复房型
        validateDuplicateRoomType(projectHotelPriceLevel.getHotelPriceLevelId(), req.getPriceLevelInfo().getRoomList(), dbPriceApplicableRoomList);

        // 检查是否超过最大房档数
        ProjectHotelTendStrategyVO projectHotelTendStrategyVO = projectHotelTendStrategyMapper.selectByProjectId(req.getProjectId());
        if(projectHotelTendStrategyVO != null && projectHotelTendStrategyVO.getMaxRoomTypeCount() != null){
            int levelCount = projectHotelPriceLevelMapper.selectLevelCount(req.getProjectIntentHotelId());
            if(projectHotelTendStrategyVO.getSupportMaxRoomTypeCount() == RfpConstant.constant_1 && levelCount > projectHotelTendStrategyVO.getMaxRoomTypeCount()){
                GenericAppUtility.serviceError(ErrorCode.ADD_HOTEL_PRICE_LEVEL_FAILED_DUE_TO_MAX_LEVEL_COUNT_LIMIT);
            }
        }

        // 新增或修改可用房型
        for (BidApplicableRoomVO room : req.getPriceLevelInfo().getRoomList()) {
            PriceApplicableRoomEntity priceApplicableRoom = new PriceApplicableRoomEntity();
            priceApplicableRoom.setPriceApplicableRoomId(room.getPriceApplicableRoomId());
            priceApplicableRoom.setProjectIntentHotelId(projectHotelPriceLevel.getProjectIntentHotelId());
            priceApplicableRoom.setProjectId(projectHotelPriceLevel.getProjectId());
            priceApplicableRoom.setHotelId(projectHotelPriceLevel.getHotelId());
            priceApplicableRoom.setHotelPriceLevelId(projectHotelPriceLevel.getHotelPriceLevelId());
            priceApplicableRoom.setRoomTypeId(room.getRoomTypeId());
            if (Objects.isNull(room.getRoomTypeId())) {
                priceApplicableRoom.setCustomRoomTypeName(StringUtils.defaultIfBlank(room.getCustomRoomTypeName(), null));
            }
            priceApplicableRoom.setDisplayOrder(room.getDisplayOrder());
            priceApplicableRoom.setModifier(userSession.getUsername());
            if (room.getPriceApplicableRoomId() != null) {
                insertOrUpdateResult = priceApplicableRoomMapper.updateById(priceApplicableRoom);
            } else {
                priceApplicableRoom.setCreator(userSession.getUsername());
                insertOrUpdateResult = priceApplicableRoomMapper.insert(priceApplicableRoom);
            }
            if (insertOrUpdateResult == 0) {
                GenericAppUtility.serviceError(ErrorCode.ADD_PRICE_APPLICABLE_ROOM_FAILED);
            }
        }

        // 新增或者修改价格组
        for (BidHotelPriceGroupVO bidHotelPriceGroupVO : req.getPriceLevelInfo().getBidHotelPriceGroupList()) {
            ProjectHotelPriceGroupEntity projectHotelPriceGroup = new ProjectHotelPriceGroupEntity();
            projectHotelPriceGroup.setProjectIntentHotelId(req.getProjectIntentHotelId());
            projectHotelPriceGroup.setProjectId(req.getProjectId());
            projectHotelPriceGroup.setHotelId(req.getHotelId());
            projectHotelPriceGroup.setHotelPriceLevelId(projectHotelPriceLevel.getHotelPriceLevelId());
            projectHotelPriceGroup.setApplicableWeeks(bidHotelPriceGroupVO.getApplicableWeeks());
            projectHotelPriceGroup.setLra(bidHotelPriceGroupVO.getLra());
            projectHotelPriceGroup.setIsIncludeBreakfast(bidHotelPriceGroupVO.getIsIncludeBreakfast());
            projectHotelPriceGroup.setRemark(bidHotelPriceGroupVO.getRemark());
            projectHotelPriceGroup.setModifier(userSession.getUsername());
            if (bidHotelPriceGroupVO.getHotelPriceGroupId() != null) {
                projectHotelPriceGroup.setHotelPriceGroupId(bidHotelPriceGroupVO.getHotelPriceGroupId());
                insertOrUpdateResult = projectHotelPriceGroupMapper.updateById(projectHotelPriceGroup);
            } else {
                projectHotelPriceGroup.setCreator(userSession.getUsername());
                insertOrUpdateResult = projectHotelPriceGroupMapper.insert(projectHotelPriceGroup);
            }
            if (insertOrUpdateResult == 0) {
                log.error("ADD_OR_UPDATE_HOTEL_PRICE_GROUP_FAILED failed {}", projectHotelPriceGroup);
                GenericAppUtility.serviceError(ErrorCode.ADD_HOTEL_PRICE_GROUP_FAILED);
            }

            //新增价格组价格
            // 查询数据库已经存在的价格
            List<ProjectHotelPriceEntity> projectHotelPriceList = projectHotelPriceMapper.selectByProjectPriceGroupId(projectHotelPriceGroup.getHotelPriceGroupId());
            Set<Integer> dbHotelPriceIdSet = projectHotelPriceList.stream().map(ProjectHotelPriceEntity::getHotelPriceId).collect(Collectors.toSet());
            Set<Integer> hotelPriceIdSet = bidHotelPriceGroupVO.getBidHotelPriceList().stream().map(BidHotelPriceVO::getHotelPriceId).filter(Objects::nonNull).collect(Collectors.toSet());
            Set<Integer> deleteHotelPriceIdSet = dbHotelPriceIdSet.stream().filter(x -> !hotelPriceIdSet.contains(x)).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(deleteHotelPriceIdSet)){
                projectHotelPriceMapper.deleteBatchIds(deleteHotelPriceIdSet);
            }
            if(CollectionUtils.isEmpty(bidHotelPriceGroupVO.getBidHotelPriceList())){
                GenericAppUtility.serviceError(ErrorCode.ADD_HOTEL_PRICE_GROUP_FAILED_DUE_TO_NULL_HOTEL_PRICE);
            }
            for (BidHotelPriceVO bidHotelPriceVO : bidHotelPriceGroupVO.getBidHotelPriceList()) {
                ProjectHotelPriceEntity projectHotelPrice = new ProjectHotelPriceEntity();
                projectHotelPrice.setProjectIntentHotelId(req.getProjectIntentHotelId());
                projectHotelPrice.setProjectId(req.getProjectId());
                projectHotelPrice.setHotelId(req.getHotelId());
                projectHotelPrice.setHotelPriceGroupId(projectHotelPriceGroup.getHotelPriceGroupId());
                projectHotelPrice.setHotelPriceLevelId(projectHotelPriceLevel.getHotelPriceLevelId());
                projectHotelPrice.setPriceType(bidHotelPriceVO.getPriceType());
                projectHotelPrice.setOnePersonPrice(bidHotelPriceVO.getOnePersonPrice());
                projectHotelPrice.setTwoPersonPrice(bidHotelPriceVO.getTwoPersonPrice());
                projectHotelPrice.setModifier(userSession.getUsername());
                if(bidHotelPriceVO.getHotelPriceId() == null) {
                    projectHotelPrice.setCreator(userSession.getUsername());
                    insertOrUpdateResult = projectHotelPriceMapper.insert(projectHotelPrice);
                } else {
                    projectHotelPrice.setHotelPriceId(bidHotelPriceVO.getHotelPriceId());
                    insertOrUpdateResult = projectHotelPriceMapper.updateById(projectHotelPrice);
                }
                if (insertOrUpdateResult == 0) {
                    log.error("ADD_OR_UPDATE_HOTEL_PRICE_FAILED failed {}", projectHotelPrice);
                    GenericAppUtility.serviceError(ErrorCode.ADD_HOTEL_PRICE_FAILED);
                }
            }
        }
        return projectHotelPriceLevel.getHotelPriceLevelId();
    }

    private void validateUserPermission(UserSession userSession, int projectId, HotelEntity hotel){
        if(userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.HOTELGROUP.key)){
            HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfoVO = GenericAppUtility.getHotelGroupUserRelatedInfoVO(userSession);
            ProjectIntentHotelGroupEntity projectIntentHotelGroup = projectIntentHotelGroupMapper.queryHotelGroupProjectIntentGroup(projectId, hotelGroupUserRelatedInfoVO.getHotelGroupOrgId());
            if(projectIntentHotelGroup == null){
                GenericAppUtility.serviceError(ErrorCode.PROJECT_NOT_INVITE_HOTEL_GROUP);
            }
            if(projectIntentHotelGroup.getIsBrandLimit() == RfpConstant.constant_1 && !hotelGroupUserRelatedInfoVO.getHotelBrandIdSet().contains(hotel.getHotelBrandId())){
                log.error("HOTEL_NOT_BELONG_HOTEL_GROUP {}", userSession.getUserOrg().getOrgId());
                GenericAppUtility.serviceError(ErrorCode.HOTEL_NOT_BELONG_HOTEL_GROUP);
            }
        } else if(userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.HOTEL.key)){
            HotelOrgRelatedInfoVO hotelOrgRelatedInfoVO = GenericAppUtility.getHotelOrgRelatedInfoVO(userSession);
            if(!hotelOrgRelatedInfoVO.getHotelIdList().contains(hotel.getHotelId())){
                log.error("HOTEL_NO_PERMISSION {}", userSession.getUserOrg().getOrgId());
                GenericAppUtility.serviceError(ErrorCode.HOTEL_NO_PERMISSION);
            }
        }
    }


    /**
     * 新增或价格房档
     */
    @Transactional(rollbackFor = Throwable.class)
    public Integer addOrUpdateHotelPriceGroup(HttpServletRequest request, BidHotelPriceGroupVO req){
        UserSession userSession = UserSession.get();

        // 查询房档信息
        ProjectHotelPriceLevelEntity projectHotelPriceLevel = projectHotelPriceLevelMapper.selectById(req.getHotelPriceLevelId());

        // 检查房当价格组是否重复
        List<ProjectHotelPriceGroupEntity> projectHotelPriceGroupList = projectHotelPriceGroupMapper.selectByHotelPriceLevelId(req.getHotelPriceLevelId());
        for(ProjectHotelPriceGroupEntity projectHotelPriceGroup : projectHotelPriceGroupList){
            if(req.getHotelPriceGroupId() != null && projectHotelPriceGroup.getHotelPriceGroupId().equals(req.getHotelPriceGroupId())){
                continue;
            }
            Integer lra = projectHotelPriceGroup.getLra();
            Integer isIncludeBreakfast = projectHotelPriceGroup.getIsIncludeBreakfast();
            if(Objects.equals(lra, req.getLra()) && Objects.equals(isIncludeBreakfast, req.getIsIncludeBreakfast())){
                List<Integer> hotelPriceGroupWeekDayList = Arrays.stream(projectHotelPriceGroup.getApplicableWeeks().split(",")).map(Integer::parseInt).collect(Collectors.toList());
                List<Integer> reqWeekDayList = Arrays.stream(req.getApplicableWeeks().split(",")).map(Integer::parseInt).collect(Collectors.toList());
                for(Integer weekDay : reqWeekDayList){
                    if(hotelPriceGroupWeekDayList.contains(weekDay)){
                        log.error("HOTEL_PRICE_GROUP_WEEK_AND_LRA_EXIST {}, week {}", projectHotelPriceGroup, weekDay);
                        GenericAppUtility.serviceError(ErrorCode.HOTEL_PRICE_GROUP_WEEK_DAY_REPEAT);
                    }
                }
            }
        }

        // 新增或者修改价格组
        ProjectHotelPriceGroupEntity projectHotelPriceGroup = new ProjectHotelPriceGroupEntity();
        projectHotelPriceGroup.setProjectIntentHotelId(projectHotelPriceLevel.getProjectIntentHotelId());
        projectHotelPriceGroup.setProjectId(projectHotelPriceLevel.getProjectId());
        projectHotelPriceGroup.setHotelId(projectHotelPriceLevel.getHotelId());
        projectHotelPriceGroup.setHotelPriceLevelId(req.getHotelPriceLevelId());
        projectHotelPriceGroup.setApplicableWeeks(req.getApplicableWeeks());
        projectHotelPriceGroup.setIsIncludeBreakfast(req.getIsIncludeBreakfast());
        projectHotelPriceGroup.setLra(req.getLra());
        projectHotelPriceGroup.setRemark(req.getRemark());
        projectHotelPriceGroup.setModifier(userSession.getUsername());
        int insertOrUpdateResult = 0;
        if (req.getHotelPriceGroupId() != null) {
            projectHotelPriceGroup.setHotelPriceGroupId(req.getHotelPriceGroupId());
            insertOrUpdateResult = projectHotelPriceGroupMapper.updateById(projectHotelPriceGroup);
        } else {
            projectHotelPriceGroup.setCreator(userSession.getUsername());
            insertOrUpdateResult = projectHotelPriceGroupMapper.insert(projectHotelPriceGroup);
        }
        if (insertOrUpdateResult == 0) {
            log.error("ADD_OR_UPDATE_HOTEL_PRICE_GROUP_FAILED failed {}", projectHotelPriceGroup);
            GenericAppUtility.serviceError(ErrorCode.ADD_OR_UPDATE_HOTEL_PRICE_GROUP_FAILED);
        }

        //新增价格组价格
        // 查询数据库已经存在的价格
        List<ProjectHotelPriceEntity> projectHotelPriceList = projectHotelPriceMapper.selectByProjectPriceGroupId(projectHotelPriceGroup.getHotelPriceGroupId());
        Set<Integer> dbHotelPriceIdSet = projectHotelPriceList.stream().map(ProjectHotelPriceEntity::getHotelPriceId).collect(Collectors.toSet());
        Set<Integer> hotelPriceIdSet = req.getBidHotelPriceList().stream().map(BidHotelPriceVO::getHotelPriceId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Integer> deleteHotelPriceIdSet = dbHotelPriceIdSet.stream().filter(x -> !hotelPriceIdSet.contains(x)).collect(Collectors.toSet());
        if(CollectionUtils.isNotEmpty(deleteHotelPriceIdSet)){
            projectHotelPriceMapper.deleteBatchIds(deleteHotelPriceIdSet);
        }
        for (BidHotelPriceVO bidHotelPriceVO : req.getBidHotelPriceList()) {
            ProjectHotelPriceEntity projectHotelPrice = new ProjectHotelPriceEntity();
            projectHotelPrice.setProjectIntentHotelId(projectHotelPriceLevel.getProjectIntentHotelId());
            projectHotelPrice.setProjectId(projectHotelPriceLevel.getProjectId());
            projectHotelPrice.setHotelId(projectHotelPriceLevel.getHotelId());
            projectHotelPrice.setHotelPriceGroupId(projectHotelPriceGroup.getHotelPriceGroupId());
            projectHotelPrice.setHotelPriceLevelId(req.getHotelPriceLevelId());
            projectHotelPrice.setPriceType(bidHotelPriceVO.getPriceType());
            projectHotelPrice.setOnePersonPrice(bidHotelPriceVO.getOnePersonPrice());
            projectHotelPrice.setTwoPersonPrice(bidHotelPriceVO.getTwoPersonPrice());
            projectHotelPrice.setModifier(userSession.getUsername());
            if(bidHotelPriceVO.getHotelPriceId() == null) {
                projectHotelPrice.setCreator(userSession.getUsername());
                insertOrUpdateResult = projectHotelPriceMapper.insert(projectHotelPrice);
            } else {
                projectHotelPrice.setHotelPriceId(bidHotelPriceVO.getHotelPriceId());
                insertOrUpdateResult = projectHotelPriceMapper.updateById(projectHotelPrice);
            }
            if (insertOrUpdateResult == 0) {
                log.error("ADD_OR_UPDATE_HOTEL_PRICE_FAILED failed {}", projectHotelPrice);
                GenericAppUtility.serviceError(ErrorCode.ADD_OR_UPDATE_HOTEL_PRICE_FAILED);
            }
        }

        return projectHotelPriceGroup.getHotelPriceGroupId();
    }


    /**
     * 新增或价格房档
     */
    @Transactional(rollbackFor = Throwable.class)
    public void deleteHotelPriceGroup(HttpServletRequest request, IdRequest<Integer> req){
        UserSession userSession = UserSession.get();
        ProjectHotelPriceGroupEntity projectHotelPriceGroup = projectHotelPriceGroupMapper.selectById(req.getId());
        if(projectHotelPriceGroup != null) {
            // 删除价格
            projectHotelPriceMapper.deleteByProjectPriceGroupId(req.getId());
            projectHotelPriceGroupMapper.deleteById(req.getId());

            // 查询价格房档是否还存在价格组
            List<ProjectHotelPriceGroupEntity> projectHotelPriceGroupList = projectHotelPriceGroupMapper.selectByHotelPriceLevelId(projectHotelPriceGroup.getHotelPriceLevelId());
            // 如果价格房档不存在价格组
            if(CollectionUtils.isEmpty(projectHotelPriceGroupList)){
                List<ProjectHotelPriceLevelEntity> projectHotelPriceLevelList = projectHotelPriceLevelMapper.queryByProjectIntentHotelId(projectHotelPriceGroup.getProjectIntentHotelId());
                boolean isDelete = false;
                for (ProjectHotelPriceLevelEntity projectHotelPriceLevelEntity : projectHotelPriceLevelList) {
                    if(projectHotelPriceLevelEntity.getHotelPriceLevelId().equals(projectHotelPriceGroup.getHotelPriceLevelId())) {
                        projectHotelPriceLevelMapper.deleteById(projectHotelPriceLevelEntity.getHotelPriceLevelId());
                        isDelete = true;
                        priceApplicableRoomMapper.deleteByHotelPriceLevelId(projectHotelPriceLevelEntity.getHotelPriceLevelId());
                    }
                    if(isDelete){
                        // 修改其他房档号
                        projectHotelPriceLevelEntity.setRoomLevelNo(projectHotelPriceLevelEntity.getRoomLevelNo() - 1);
                        projectHotelPriceLevelEntity.setModifier(userSession.getUsername());
                        projectHotelPriceLevelMapper.updateById(projectHotelPriceLevelEntity);
                    }
                }
                projectHotelPriceLevelMapper.deleteById(projectHotelPriceGroup.getHotelPriceLevelId());

            }
        }
    }

    public Integer submitHotelBid(HttpServletRequest request, SubmitHotelBidRequest req){
        UserSession userSession = UserSession.get();
        // 查询项目信息
        ProjectEntity project = projectMapper.selectById(req.getProjectId());
        if(project == null){
            GenericAppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        if(new Date().after(DateUtil.endOfDay(project.getPriceMonitorEndDate()))){
            GenericAppUtility.serviceError(ErrorCode.CANNOT_BID_DUE_TO_AFTER_BID_END_DATE_TIME);
        }

        // 查询项目意向酒店
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.selectById(req.getProjectIntentHotelId());
        if(projectIntentHotel == null){
            GenericAppUtility.serviceError(ErrorCode.CANNOT_FOUND_PROJECT_INTENT_HOTEL);
        }
        if(projectIntentHotel.getBidState().equals(HotelBidStateEnum.BID_WINNING.bidState)){
            GenericAppUtility.serviceError(ErrorCode.CANNOT_SUBMIT_BID_DUE_TO_BID_WINNING);
        }
        if(projectIntentHotel.getBidState().equals(HotelBidStateEnum.REJECTED.bidState)){
            GenericAppUtility.serviceError(ErrorCode.CANNOT_SUBMIT_BID_DUE_TO_BID_REJECTED);
        }
        if(projectIntentHotel.getBidState().equals(HotelBidStateEnum.REJECT_NEGOTIATION.bidState)){
            GenericAppUtility.serviceError(ErrorCode.CANNOT_SUBMIT_BID_DUE_TO_REJECT_NEGOTIATION);
        }
        if(Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.HOTEL.key) && (projectIntentHotel.getHotelGroupApproveStatus() != null && projectIntentHotel.getHotelGroupApproveStatus().equals(HotelGroupApproveStatusEnum.WAITING.key))){
            GenericAppUtility.serviceError(ErrorCode.CANNOT_SUBMIT_BID_DUE_TO_HOTEL_GROUP_APPROVE_SATUS_WAITING);
        }

        // 检查有没有报价信息
        List<ProjectHotelPriceLevelEntity> hotelPriceLevelPriceList = projectHotelPriceLevelMapper.queryByProjectIdAndHotelId(req.getProjectId(), req.getHotelId());
        if(CollectionUtils.isEmpty(hotelPriceLevelPriceList)){
            log.error("CANNOT_SUBMIT_BID_DUE_TO_HOTEL_PRICE_GROUP_IS_EMPTY {}", projectIntentHotel.getProjectIntentHotelId());
            GenericAppUtility.serviceError(ErrorCode.CANNOT_SUBMIT_BID_DUE_TO_HOTEL_PRICE_LEVEL_IS_EMPTY);
        }
        List<ProjectHotelPriceGroupEntity> hotelPriceGroupList = projectHotelPriceGroupMapper.selectByProjectIdAndHotelId(req.getProjectId(), req.getHotelId());
        Map<Integer, List<ProjectHotelPriceGroupEntity>> hotelPriceGroupMap = hotelPriceGroupList.stream().collect(Collectors.groupingBy(ProjectHotelPriceGroupEntity::getHotelPriceLevelId));
        for(ProjectHotelPriceLevelEntity projectHotelPriceLevel : hotelPriceLevelPriceList){
            List<ProjectHotelPriceGroupEntity> priceLevelGroupList = hotelPriceGroupMap.get(projectHotelPriceLevel.getHotelPriceLevelId());
            if(CollectionUtils.isEmpty(priceLevelGroupList)){
                log.error("CANNOT_SUBMIT_BID_DUE_TO_HOTEL_PRICE_GROUP_IS_EMPTY {}", projectHotelPriceLevel);
                GenericAppUtility.serviceError(ErrorCode.CANNOT_SUBMIT_BID_DUE_TO_HOTEL_PRICE_GROUP_IS_EMPTY);
            }
        }
        // 检查season类型
        Set<Integer> priceTypeSet = new HashSet<>();
        for(BidApplicableDayVO bidApplicableDayVO : req.getBidApplicableDayList()){
            priceTypeSet.add(bidApplicableDayVO.getPriceType());
        }
        List<ProjectHotelPriceEntity> hotelPriceList = projectHotelPriceMapper.selectByProjectIdAndHotelId(req.getProjectId(), req.getHotelId());
        Map<Integer, List<ProjectHotelPriceEntity>> hotelPriceMap = hotelPriceList.stream().collect(Collectors.groupingBy(ProjectHotelPriceEntity::getHotelPriceGroupId));
        for(ProjectHotelPriceGroupEntity projectHotelPriceGroup : hotelPriceGroupList){
            List<ProjectHotelPriceEntity> hotelPriceEntityList = hotelPriceMap.get(projectHotelPriceGroup.getHotelPriceGroupId());
            if(CollectionUtils.isEmpty(hotelPriceEntityList)){
                log.error("CANNOT_SUBMIT_BID_DUE_TO_HOTEL_PRICE_IS_EMPTY {}", projectHotelPriceGroup.getHotelPriceGroupId());
                GenericAppUtility.serviceError(ErrorCode.CANNOT_SUBMIT_BID_DUE_TO_HOTEL_PRICE_IS_EMPTY);
            }
            // 检查season类型
            for(ProjectHotelPriceEntity projectHotelPrice : hotelPriceEntityList){
                if(!priceTypeSet.contains(projectHotelPrice.getPriceType())){
                    GenericAppUtility.serviceError(ErrorCode.CANNOT_SUBMIT_BID_DUE_TO_EXIST_NO_SEASON_DATE_PRICE);
                }
            }
        }

        // 检查用户是否有权限访问酒店
        HotelEntity hotel = cachedHotelService.getById(req.getHotelId());
        validateUserPermission(userSession, req.getProjectId(), hotel);
        HotelUtility.calculateNullGoogleLngLat(hotel);


        ProjectHotelBidStrategyEntity projectHotelBidStrategy = projectHotelBidStrategyMapper.queryProjectHotelBidStrategy(req.getProjectId(), req.getHotelId());
        // 清除旧脏数据
        if(projectHotelBidStrategy != null && !projectHotelBidStrategy.getProjectIntentHotelId().equals(req.getProjectIntentHotelId())){
            projectHotelBidStrategyMapper.deleteByProjectAndHotelId(req.getProjectId(), req.getHotelId());
            projectHotelBidStrategy = null;
        }

        // 判断投标策略有没有数据，有就更新，没有就新增
        if(projectHotelBidStrategy != null){
            // 更新
            BeanUtils.copyProperties(projectHotelBidStrategy, req.getBidProjectStrategy());
            projectHotelBidStrategy.setModifier(userSession.getUsername());
            projectHotelBidStrategyMapper.updateById(projectHotelBidStrategy);
        } else {
            projectHotelBidStrategy = new ProjectHotelBidStrategyEntity();
            BeanUtils.copyProperties(req.getBidProjectStrategy(), projectHotelBidStrategy);
            projectHotelBidStrategy.setProjectIntentHotelId(req.getProjectIntentHotelId());
            projectHotelBidStrategy.setProjectId(req.getProjectId());
            projectHotelBidStrategy.setHotelId(req.getHotelId());
            projectHotelBidStrategy.setCreator(userSession.getUsername());
            projectHotelBidStrategy.setModifier(userSession.getUsername());
            projectHotelBidStrategyMapper.insert(projectHotelBidStrategy);
        }

        boolean isNeedValidateBid = GenericAppUtility.isNeedValidateBid(projectIntentHotel);
        //新增或修改自定义投标策略
        List<ProjectCustomBidStrategyEntity> projectCustomBidStrategyList = new ArrayList<>();
        List<CustomBidStrategyOptionEntity> strategyOptionList = new ArrayList<>();
        for (BidCustomStrategyVO bidCustomStrategy : req.getBidCustomStrategyList()) {
            ProjectCustomBidStrategyEntity projectCustomBidStrategy = new ProjectCustomBidStrategyEntity();
            projectCustomBidStrategy.setCustomTendStrategyId(bidCustomStrategy.getCustomTendStrategyId());
            projectCustomBidStrategy.setProjectIntentHotelId(req.getProjectIntentHotelId());
            projectCustomBidStrategy.setCreator(userSession.getUsername());
            projectCustomBidStrategy.setModifier(userSession.getUsername());
            projectCustomBidStrategy.setHotelId(req.getHotelId());
            projectCustomBidStrategy.setProjectId(req.getProjectId());
            projectCustomBidStrategy.setStrategyType(bidCustomStrategy.getStrategyType());
            projectCustomBidStrategy.setStrategyName(bidCustomStrategy.getStrategyName());
            projectCustomBidStrategy.setSupportStrategyName(bidCustomStrategy.getSupportStrategyName());
            projectCustomBidStrategy.setSupportStrategyText(bidCustomStrategy.getSupportStrategyText());
            projectCustomBidStrategyList.add(projectCustomBidStrategy);

            // 设置策略选项
            if(CustomStrategyTypeEnum.isOptionType(projectCustomBidStrategy.getStrategyType()) && CollectionUtils.isEmpty(bidCustomStrategy.getOptions())){
                GenericAppUtility.serviceError(ErrorCode.CANNOT_SUBMIT_BID_DUE_TO_CUSTOM_STRATEGY_IS_EMPTY);
            }
            if (CollectionUtils.isNotEmpty(bidCustomStrategy.getOptions())) {
                bidCustomStrategy.getOptions().forEach(option -> {
                    CustomBidStrategyOptionEntity customBidStrategyOptionEntity = new CustomBidStrategyOptionEntity();
                    customBidStrategyOptionEntity.setOptionId(option.getOptionId());
                    customBidStrategyOptionEntity.setStrategyId(Long.valueOf(bidCustomStrategy.getCustomTendStrategyId()));
                    customBidStrategyOptionEntity.setProjectId(projectCustomBidStrategy.getProjectId());
                    customBidStrategyOptionEntity.setHotelId(projectCustomBidStrategy.getHotelId());
                    customBidStrategyOptionEntity.setProjectIntentHotelId(projectCustomBidStrategy.getProjectIntentHotelId());
                    customBidStrategyOptionEntity.setIsSupport(option.getIsSupport());
                    customBidStrategyOptionEntity.setOptionName(option.getOptionName());
                    customBidStrategyOptionEntity.setCreator(userSession.getUsername());
                    customBidStrategyOptionEntity.setModifier(userSession.getUsername());
                    strategyOptionList.add(customBidStrategyOptionEntity);
                });
            }
        }
        if(CollectionUtils.isNotEmpty(projectCustomBidStrategyList)){
            projectCustomBidStrategyMapper.batchMergeProjectCustomBidStrategy(projectCustomBidStrategyList);
        }
        if (CollectionUtils.isNotEmpty(strategyOptionList)) {
            customBidStrategyOptionMapper.batchUpsert(strategyOptionList);
        }

        // 计算权重分
        BigDecimal bigWeight = isNeedValidateBid ? calculateWeight(req, projectIntentHotel, hotel) : null;

        boolean isNeedHotelGroupApprove = isNeedHotelGroupApprove(project.getProjectId(), hotel, userSession);
        // 需要酒店集团审核
        if(isNeedHotelGroupApprove){
            projectIntentHotel.setHotelGroupApproveStatus(HotelGroupApproveStatusEnum.WAITING.key);
            projectIntentHotel.setBidState(HotelBidStateEnum.NO_BID.bidState);
        } else if(Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.UNDER_NEGOTIATION.bidState)){
            // 议价中报价并且不需要酒店集团审核提交状态为修改报价
            projectIntentHotel.setBidState(HotelBidStateEnum.UPDATED_BID.bidState);
        } else {
            projectIntentHotel.setHotelGroupApproveStatus(HotelGroupApproveStatusEnum.APPROVED.key);
            projectIntentHotel.setBidState(HotelBidStateEnum.NEW_BID.bidState);
        }

        saveOperateLog(projectIntentHotel, userSession,   "BID_STATUS_UPDATE_TO_" + HotelBidStateEnum.getHotelBidStateEnum(projectIntentHotel.getBidState()));

        // 保存税费
        ProjectHotelTaxSettingsEntity projectHotelTaxSettings = projectHotelTaxSettingsMapper.selectByProjectIdAndHotelId(req.getProjectId(), req.getHotelId());
        // 清除脏数据
        if(projectHotelTaxSettings != null && !projectHotelTaxSettings.getProjectIntentHotelId().equals(req.getProjectIntentHotelId())){
            projectHotelTaxSettingsMapper.deleteByProjectIdAndHotelId(req.getProjectId(), req.getHotelId());
            projectHotelTaxSettings = null;
        }

        // 新增或者更新
        if(projectHotelTaxSettings != null) {
            BeanUtils.copyProperties(req.getBidHotelTaxSettings(), projectHotelTaxSettings);
            projectHotelTaxSettings.setProjectIntentHotelId(req.getProjectIntentHotelId());
            projectHotelTaxSettings.setHotelId(req.getHotelId());
            projectHotelTaxSettings.setProjectId(req.getProjectId());
            projectHotelTaxSettings.setCreator(userSession.getUsername());
            projectHotelTaxSettings.setModifier(userSession.getUsername());
            projectHotelTaxSettingsMapper.updateById(projectHotelTaxSettings);
        } else {
            projectHotelTaxSettings = new ProjectHotelTaxSettingsEntity();
            projectHotelTaxSettings.setProjectIntentHotelId(req.getProjectIntentHotelId());
            projectHotelTaxSettings.setHotelId(req.getHotelId());
            projectHotelTaxSettings.setProjectId(req.getProjectId());
            projectHotelTaxSettings.setCreator(userSession.getUsername());
            projectHotelTaxSettings.setModifier(userSession.getUsername());
            BeanUtils.copyProperties(req.getBidHotelTaxSettings(), projectHotelTaxSettings);
            projectHotelTaxSettingsMapper.insert(projectHotelTaxSettings);
        }

        // 更新项目意向酒店
        BeanUtils.copyProperties(req, projectIntentHotel);
        projectIntentHotel.setBidWeight(bigWeight);
        projectIntentHotel.setBidOrgId(userSession.getUserOrg().getOrgId());
        projectIntentHotel.setBidOrgType(userSession.getUserOrg().getOrgType());

        projectIntentHotel.setHotelBidContactName(req.getHotelBidContactName());
        projectIntentHotel.setModifier(userSession.getUsername());

        // 设置员工权益图片
        List<UploadFileVO> employeeRightFileUrls = convertEmployeeRightUrl(req.getEmployeeRightFileUrl());
        projectIntentHotel.setEmployeeRightFileUrl(JsonUtil.objectToJson(employeeRightFileUrls));
        projectIntentHotelMapper.updateBidContactInfo(projectIntentHotel);

        ProjectHotelTendStrategyVO projectHotelTendStrategy = projectHotelTendStrategyMapper.selectByProjectId(req.getProjectId());
        // 保存报价日期
        // 检查season天数
        if(projectHotelTendStrategy != null && Objects.equals(projectHotelTendStrategy.getSupportSeasonDayLimit(), RfpConstant.constant_1)){
            int totalDays = 0;
            for(BidApplicableDayVO bidApplicableDayVO : req.getBidApplicableDayList()){
                // 排查base price
                if(bidApplicableDayVO.getPriceType() != HotelPriceTypeEnum.BASE_PRICE.key) {
                    totalDays = totalDays + (int) DateUtil.betweenDay(bidApplicableDayVO.getStartDate(), bidApplicableDayVO.getEndDate(), true) + 1;
                }
            }
            if(totalDays > projectHotelTendStrategy.getMaxSeasonDay()){
                GenericAppUtility.serviceError(ErrorCode.CANNOT_SUBMIT_BID_DUE_TO_SEASON_DAYS_LIMIT);
            }
        }
        insertOrUpdateProjectHotelPriceApplicableDay(false, req.getProjectIntentHotelId(), req.getProjectId(),
                req.getHotelId(), req.getBidApplicableDayList(), userSession.getUsername());

        // 保存不可用日期
        // 检查不适应天总数
        if(projectHotelTendStrategy != null && CollectionUtils.isNotEmpty(req.getBidUnApplicableDayList())) {
            int totalDays = 0;
            for(BidUnApplicableDayVO bidUnApplicableDayVO : req.getBidUnApplicableDayList()){
                totalDays = totalDays + (int)DateUtil.betweenDay(bidUnApplicableDayVO.getStartDate(), bidUnApplicableDayVO.getEndDate(), true) + 1;
            }
            if(projectHotelTendStrategy.getSupportMaxNotApplicableDay() == RfpConstant.constant_1 && totalDays > projectHotelTendStrategy.getMaxNotApplicableDay()){
                GenericAppUtility.serviceError(ErrorCode.CANNOT_SUBMIT_BID_DUE_TO_UNAPPLICABLE_DAYS_LIMIT);
            }
            insertPriceUnapplicableDay(false, req.getProjectIntentHotelId(), req.getProjectId(),
                    req.getHotelId(), req.getBidUnApplicableDayList(), userSession.getUsername());
        }

        // 删除临时保存
        projectHotelBidTempInfoMapper.deleteById(req.getProjectIntentHotelId());

        return projectIntentHotel.getProjectIntentHotelId();

    }

    public Integer insertOrUpdateProjectHotelPriceApplicableDay(Boolean isInsert, Integer projectIntentHotelId, Integer projectId, Long hotelId,
                                                                List<BidApplicableDayVO> priceApplicableDayList, String operator) {

        // 批量全部更新，先删除旧数据
        if(!isInsert){
            priceApplicableDayMapper.deleteByProjectAndHotelId(projectId, hotelId);
        }
        List<PriceApplicableDayEntity> priceApplicableDayEntities = new ArrayList<>();
        for(BidApplicableDayVO bidApplicableDayVO : priceApplicableDayList){
            PriceApplicableDayEntity priceApplicableDayEntity = new PriceApplicableDayEntity();
            priceApplicableDayEntity.setProjectIntentHotelId(projectIntentHotelId);
            priceApplicableDayEntity.setProjectId(projectId);
            priceApplicableDayEntity.setHotelId(hotelId);
            priceApplicableDayEntity.setPriceType(bidApplicableDayVO.getPriceType());
            priceApplicableDayEntity.setStartDate(bidApplicableDayVO.getStartDate());
            priceApplicableDayEntity.setEndDate(bidApplicableDayVO.getEndDate());
            priceApplicableDayEntity.setCreator(operator);
            priceApplicableDayEntities.add(priceApplicableDayEntity);
        }
        int result = priceApplicableDayMapper.insertBatch(priceApplicableDayEntities);
        if(result == 0){
            log.error("设置报价日期异常 {}", priceApplicableDayList);
            throw  new RuntimeException("设置报价日期异常");
        }
        return result;
    }

    public void insertPriceUnapplicableDay(Boolean isInsert, Integer projectIntentHotelId, Integer projectId, Long hotelId, List<BidUnApplicableDayVO> priceUnApplicableDayList,
                                               String operator) {

        // 批量全部更新，先删除旧数据
        if(!isInsert){
            priceUnapplicableDayMapper.deleteByProjectIdAndHotelId(projectId, hotelId);
        }

        List<PriceUnapplicableDayEntity> priceUnapplicableDayEntities = new ArrayList<>();

        for(BidUnApplicableDayVO bidUnApplicableDayVO : priceUnApplicableDayList){
            PriceUnapplicableDayEntity priceUnapplicableDay = new PriceUnapplicableDayEntity();
            priceUnapplicableDay.setProjectIntentHotelId(projectIntentHotelId);
            priceUnapplicableDay.setHotelId(hotelId);
            priceUnapplicableDay.setProjectId(projectId);
            priceUnapplicableDay.setStartDate(bidUnApplicableDayVO.getStartDate());
            priceUnapplicableDay.setEndDate(bidUnApplicableDayVO.getEndDate());
            priceUnapplicableDay.setCreator(operator);
            priceUnapplicableDayEntities.add(priceUnapplicableDay);
        }
        if(CollectionUtils.isNotEmpty(priceUnapplicableDayEntities)) {
            priceUnapplicableDayMapper.batchInsert(priceUnapplicableDayEntities);
        }
    }

    private BidCustomStrategyVO convertToBidCustomStrategyVO(ProjectCustomBidStrategyEntity projectCustomBidStrategyEntity, List<CustomBidStrategyOptionEntity> options) {
        BidCustomStrategyVO vo = new BidCustomStrategyVO();
        BeanUtils.copyProperties(projectCustomBidStrategyEntity, vo);

        // 转换策略选项
        if (CollectionUtils.isNotEmpty(options)) {
            vo.setOptions(options.stream().map(option -> {
                BidCustomStrategyVO.CustomStrategyBidOption customStrategyBidOption = new BidCustomStrategyVO.CustomStrategyBidOption();
                BeanUtils.copyProperties(option, customStrategyBidOption);
                return customStrategyBidOption;
            }).collect(Collectors.toList()));
        }
        return vo;
    }

    private BidCustomStrategyVO convertToBidCusomStrategyVOFromHGroup(HGroupDefaultCusStrategyVO hGroupDefaultCusStrategyVO){
        BidCustomStrategyVO vo = new BidCustomStrategyVO();
        BeanUtils.copyProperties(hGroupDefaultCusStrategyVO, vo);
        if (CollectionUtils.isNotEmpty(hGroupDefaultCusStrategyVO.getOptions())) {
            vo.setOptions(hGroupDefaultCusStrategyVO.getOptions().stream().map(option -> {
                BidCustomStrategyVO.CustomStrategyBidOption customStrategyBidOption = new BidCustomStrategyVO.CustomStrategyBidOption();
                BeanUtils.copyProperties(option, customStrategyBidOption);
                return customStrategyBidOption;
            }).collect(Collectors.toList()));
        }
        return vo;
    }

    private ProjectIntentHotelEntity prepareProjectIntentHotel(UserSession userSession, int projectId, HotelEntity hotel, ProjectIntentHotelGroupEntity projectIntentHotelGroup){
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.queryByProjectAndHotelId(projectId, hotel.getHotelId());
        Integer supplyOrgId = userSession.getUserOrg().getOrgId();
        if(projectIntentHotel == null){
            // 新增一条项目意向酒店记录
            projectIntentHotel = new ProjectIntentHotelEntity();
            projectIntentHotel.setProjectId(projectId);
            projectIntentHotel.setHotelId(hotel.getHotelId());
            projectIntentHotel.setBidState(HotelBidStateEnum.NO_BID.bidState);
            projectIntentHotel.setSendMailStatus(0);
            projectIntentHotel.setIsUpload(RfpConstant.constant_0);
            projectIntentHotel.setHotelServicePoints(new BigDecimal("100"));
            projectIntentHotel.setBidUploadSource(RfpConstant.constant_0);
            projectIntentHotel.setCreator(userSession.getUsername());
            projectIntentHotel.setModifier(userSession.getUsername());
            projectIntentHotel.setInviteStatus(RfpConstant.constant_0);
            projectIntentHotel.setBidOrgId(supplyOrgId);
            projectIntentHotel.setCurrencyCode(hotel.getCurrencyCode());
            projectIntentHotel.setBidOrgType(userSession.getUserOrg().getOrgType());
            setHotelGroupBidContentInfo(projectIntentHotel, projectIntentHotelGroup, userSession);
            projectIntentHotelMapper.insert(projectIntentHotel);
        } else {
            Integer hotelOrgId = projectIntentHotel.getBidOrgId();
            if((userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTEL.key ||
                    (userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTELGROUP.key)
            )){
                if(hotelOrgId != null &&  hotelOrgId.intValue() != supplyOrgId.intValue() && !projectIntentHotel.getBidState().equals(HotelBidStateEnum.NO_BID.bidState)){
                    GenericAppUtility.serviceError(ErrorCode.HOTEL_ALREADY_BID_CANNOT_REPEAT_BID);
                }
            } else{
                GenericAppUtility.serviceError(ErrorCode.ADD_FAILED);
            }
            this.setHotelGroupBidContentInfo(projectIntentHotel, projectIntentHotelGroup, userSession);
        }
        if(StringUtil.isEmpty(projectIntentHotel.getCurrencyCode())){
            projectIntentHotel.setCurrencyCode(hotel.getCurrencyCode());
        }
        return projectIntentHotel;
    }

    public void setHotelGroupBidContentInfo(ProjectIntentHotelEntity projectIntentHotel, ProjectIntentHotelGroupEntity projectIntentHotelGroup, UserSession userSession) {
        if(projectIntentHotel.getBidState() > HotelBidStateEnum.NO_BID.bidState){
            return;
        }
        if(Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.HOTELGROUP.key)){
            // 查询酒店集团是否存报价模板联系方式
             projectIntentHotel.setHotelGroupBidContactName(userSession.getUsername());
            projectIntentHotel.setHotelGroupBidContactEmail(userSession.getEmail());
            projectIntentHotel.setHotelGroupBidContactMobile(userSession.getMobileAreaCode() + userSession.getMobile());

            // 查询酒店集团是否存报价模板联系方式
            if(projectIntentHotelGroup != null && StringUtils.isNotEmpty(projectIntentHotelGroup.getHotelGroupBidContactName()) &&
                    StringUtils.isNotEmpty(projectIntentHotelGroup.getHotelGroupBidContactMobile()) &&
                    StringUtils.isNotEmpty(projectIntentHotelGroup.getHotelGroupBidContactEmail())
            ){
                projectIntentHotel.setHotelGroupBidContactName(projectIntentHotelGroup.getHotelGroupBidContactName());
                projectIntentHotel.setHotelGroupBidContactMobile(projectIntentHotelGroup.getHotelGroupBidContactMobile());
                projectIntentHotel.setHotelGroupBidContactEmail(projectIntentHotelGroup.getHotelGroupBidContactEmail());
            }
        }
    }

    // 格式话**
    private String formatStatData(String statData){
        if(statData.length() <= 2){
            return "**";
        }
        StringBuffer result = new StringBuffer();
        result.append(statData.substring(0,1));
        String subString = statData.substring(1, statData.length());
        for(int i=0; i<10; i++){
            subString = subString.replaceAll(String.valueOf(i), "*");
        }
        result.append(subString);
        return result.toString();

    }

    // 是否为同档报价
    private boolean isTheSameLevel(int baseAvgPrice, QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse){
        if(baseAvgPrice == 0){
            return false;
        }
        if(queryHistoryProjectInfoResponse.getRoomNightCount() > 0 && queryHistoryProjectInfoResponse.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
            int avgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()), 0, RoundingMode.HALF_UP).intValue();
            if (baseAvgPrice > 0 && Math.abs(baseAvgPrice - avgPrice) <= 50) {
                return true;
            }
        }
        return false;
    }

    public void addBasePriceApplicableDay(List<BidApplicableDayVO> applicableDayList, ProjectEntity project){
        boolean hasBasePrice = false;
        for(BidApplicableDayVO applicableDayVO : applicableDayList){
            if(applicableDayVO.getPriceType() == HotelPriceTypeEnum.BASE_PRICE.key){
                hasBasePrice = true;
            }
        }
        // 设置基础协议日期
        if(!hasBasePrice){
            BidApplicableDayVO bidApplicableDay = new BidApplicableDayVO();
            bidApplicableDay.setPriceType(HotelPriceTypeEnum.BASE_PRICE.key);
            bidApplicableDay.setStartDate(project.getPriceMonitorStartDate());
            bidApplicableDay.setEndDate(project.getPriceMonitorEndDate());
            applicableDayList.add(bidApplicableDay);

        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateProjectIntentHotelBidState(HttpServletRequest request, UpdateHotelBidStateRequest req){
        UserSession userSession = UserSession.get();
        ProjectEntity project = projectMapper.selectById(req.getProjectId());
        if (project == null) {
            GenericAppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }
        List<ProjectIntentHotelEntity> projectIntentHotelList = projectIntentHotelMapper.queryProjectIntentByHotelIdList(req.getProjectId(), req.getHotelIds());
        if(!CollectionUtils.isNotEmpty(projectIntentHotelList)){
            GenericAppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }

        // 更新报价状态
        req.setModifier(userSession.getUsername());
        int i = projectIntentHotelMapper.updateBidState(req);
        if(i != req.getHotelIds().size()){
            GenericAppUtility.serviceError(ErrorCode.UPDATE_FAILED);
        }

        List<Integer> projectIntentHotelIds = projectIntentHotelList.stream().map(ProjectIntentHotelEntity::getProjectIntentHotelId).collect(Collectors.toList());
        // 议价中需要记录议价前的价格
        if(req.getBidState().equals(HotelBidStateEnum.UNDER_NEGOTIATION.bidState)){
            projectHotelPriceMapper.recordLastPrice(projectIntentHotelIds);
        }
        // 议价中，已中签，已否决通知状态要设置为“待通知”
        if (Objects.equals(req.getBidState(), HotelBidStateEnum.UNDER_NEGOTIATION.bidState) ||
                Objects.equals(req.getBidState(), HotelBidStateEnum.BID_WINNING.bidState) ||
                Objects.equals(req.getBidState(), HotelBidStateEnum.REJECTED.bidState)
        ) {
            projectIntentHotelMapper.updateToWaitNotify(req.getProjectId(), req.getHotelIds(), userSession.getUsername());
            if(Objects.equals(project.getBidStateUpdatedNotifyMode() ,RfpConstant.constant_1)){
                String key = RedisConstant.NOTIFY_BIDDER;
                for(ProjectIntentHotelEntity projectIntentHotel : projectIntentHotelList) {
                    //加力代录的批量导入已中签的酒店功能，无需执行自动发送报价状态变更通知短信和邮件
                    if(Objects.equals(projectIntentHotel.getIsUpload(), RfpConstant.constant_1) && Objects.equals(projectIntentHotel.getBidUploadSource(), BidUploadSourceEnum.JIALI.key)){
                        continue;
                    }
                    try {
                        redisService.sadd(key, projectIntentHotel.getProjectId() + "_" + projectIntentHotel.getProjectIntentHotelId() + "_" + userSession.getUsername());
                    } catch (Exception e) {
                        log.error("自动通知加入通知投标人队列失败,项目ID：" + projectIntentHotel.getProjectId() + "邀请酒店ID：" + projectIntentHotel.getProjectIntentHotelId(), e);
                    }
                }
            }

            if(!StringUtils.isEmpty(req.getRemark())){
                for(ProjectIntentHotelEntity projectIntentHotel : projectIntentHotelList) {
                    ProjectHotelRemarkEntity projectHotelRemark = new ProjectHotelRemarkEntity();
                    projectHotelRemark.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                    projectHotelRemark.setProjectId(projectIntentHotel.getProjectId());
                    projectHotelRemark.setHotelId(projectIntentHotel.getHotelId());
                    projectHotelRemark.setRemarkType(Objects.requireNonNull(HotelBidStateEnum.getHotelBidStateEnum(req.getBidState())).name());
                    projectHotelRemark.setRemark(req.getRemark());
                    projectHotelRemark.setCreator(userSession.getUsername());
                    projectHotelRemarkMapper.insert(projectHotelRemark);
                }
            }
            // 记录操作日志
            String operateContent = "BID_STATE_UPDATE_TO_" + Objects.requireNonNull(HotelBidStateEnum.getHotelBidStateEnum(req.getBidState())).name();
            if(StringUtil.isValidString(req.getUploadRemark())){
                operateContent = "Bid State Update To " + Objects.requireNonNull(HotelBidStateEnum.getHotelBidStateEnum(req.getBidState())).name() + ", Remark：" +  req.getUploadRemark();
            }
            for(ProjectIntentHotelEntity projectIntentHotel : projectIntentHotelList){
                saveOperateLog(projectIntentHotel, userSession, operateContent);
            }

        }
    }

    // 计算权重方法
    public BigDecimal calculateWeight(SubmitHotelBidRequest req, ProjectIntentHotelEntity projectIntentHotel, HotelEntity hotel) {
        BigDecimal sumWeight = new BigDecimal(0);

        // 项目权重
        ProjectHotelTendWeightEntity projectHotelTendWeight = projectHotelTendWeightMapper.selectEntityByProjectId(req.getProjectId());
        // 意向酒店
        if (projectHotelTendWeight != null) {
            /** 间夜量 */
            if (projectHotelTendWeight.getWhtRoomNightState().equals(StateEnum.Effective.key)) {
                if (projectIntentHotel.getLastYearRoomNight() != null && projectIntentHotel.getLastYearRoomNight().longValue() >= 120) {
                    sumWeight = sumWeight.add(projectHotelTendWeight.getWhtRoomNight());
                }
            }

            /** 间夜量(额外加分项) */
            if (projectHotelTendWeight.getWhtRoomNightExState().equals(StateEnum.Effective.key)) {
                if (projectIntentHotel.getLastYearRoomNight() != null &&  projectIntentHotel.getLastYearRoomNight().longValue() >= 600) {
                    sumWeight = sumWeight.add(projectHotelTendWeight.getWhtRoomNightEx());
                }
            }

            // 查询最低报价
            BigDecimal minPrice = projectHotelPriceMapper.selectMinPriceByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());

            // 查询酒店7天起价
            BigDecimal minLowerPrice = null;
            com.fangcang.grfp.core.base.Response hotelMinPriceResponse = hotelManager.queryHotelLowestPrice(Collections.singletonList(hotel.getHotelId()), new Date(), cn.hutool.core.date.DateUtil.offsetDay(new Date(), 7));
            if(hotelMinPriceResponse != null && hotelMinPriceResponse.getResult().equals(ReturnResultEnum.SUCCESS.errorNo)){
                List<HotelLowestPricesResponse> hotelMinPriceResponses = (List<HotelLowestPricesResponse>)hotelMinPriceResponse.getData();
                if(CollectionUtils.isNotEmpty(hotelMinPriceResponses)){
                    HotelLowestPricesResponse hotelLowestPricesResponse = hotelMinPriceResponses.get(0);
                    minLowerPrice = hotelLowestPricesResponse.getLowestPrice();
                }
            }

            /** 价格优势 */
            if(minPrice != null && minLowerPrice != null) {
                if (projectHotelTendWeight.getWhtPriceAdvantageState().equals(StateEnum.Effective.key)) {
                    if (((minLowerPrice.subtract(minPrice)).divide(minLowerPrice, 2, BigDecimal.ROUND_HALF_UP)).compareTo(new BigDecimal(0.15)) > 0) {
                        sumWeight = sumWeight.add(projectHotelTendWeight.getWhtPriceAdvantage());
                    }
                }
                /** 价格优势(额外加分项) */
                if (projectHotelTendWeight.getWhtPriceAdvantageExState().equals(StateEnum.Effective.key)) {
                    if (((minLowerPrice.subtract(minPrice)).divide(minLowerPrice, 2, BigDecimal.ROUND_HALF_UP)).compareTo(new BigDecimal(0.25)) > 0) {
                        sumWeight = sumWeight.add(projectHotelTendWeight.getWhtPriceAdvantageEx());
                    }
                }
            }

            // 企业项目POI信息
            List<ProjectPoiVO> projectPoiList = projectPoiMapper.selectPoiInfoByProjectId(req.getProjectId(), null);
            projectPoiList = projectPoiList.stream().filter(projectPoiVO -> projectPoiVO.getCityCode().equals(hotel.getCityCode())).collect(Collectors.toList());

            /** 计算位置 */
            if (projectHotelTendWeight.getWhtLocationState().equals(StateEnum.Effective.key)) {
                // 酒店基础信息
                if (!CollectionUtils.isEmpty(projectPoiList)) {
                    // 需要关联城市，在相同城市才计算距离
                    for (ProjectPoiVO projectPoiVO : projectPoiList) {
                        if (projectPoiVO.getLngGoogle() != null && projectPoiVO.getLatGoogle() !=null) {
                            BigDecimal distance = LocationUtil.getDistance(projectPoiVO.getLatGoogle(), projectPoiVO.getLngGoogle(),
                                    hotel.getLatGoogle(), hotel.getLngGoogle());
                            if (new BigDecimal(2).compareTo(distance) >= 0) {
                                sumWeight = sumWeight.add(projectHotelTendWeight.getWhtLocation());
                                break;
                            }
                        }
                    }
                }
            }

            /** 计算城市 城市权重，酒店在项目POI所在城市 */
            if (projectHotelTendWeight.getWhtCityState().equals(StateEnum.Effective.key)) {
                if(CollectionUtils.isNotEmpty(projectPoiList)) {
                    sumWeight = sumWeight.add(projectHotelTendWeight.getWhtCity());
                }
            }

            /** 查询报价信息 */
            List<ProjectHotelPriceGroupEntity> hotelPriceGroupList = projectHotelPriceGroupMapper.selectByProjectIntentHotelId(req.getProjectIntentHotelId());

            /** 计算差旅标准 */

            /** OTA评分 平均分 */
            if (projectHotelTendWeight.getWhtOtaScoreState().equals(StateEnum.Effective.key)) {
                if (hotel.getRating() != null){
                    BigDecimal averageScore = new BigDecimal(hotel.getRating());
                    if (averageScore.compareTo(new BigDecimal(4.5)) > 0) {
                        sumWeight = sumWeight.add(projectHotelTendWeight.getWhtOtaScore());
                    }
                }
            }

            /** 公司统一支付 */
            if (projectHotelTendWeight.getWhtCoPayState().equals(StateEnum.Effective.key) &&
                    Objects.equals(req.getBidProjectStrategy().getSupportVccPay(),StateEnum.Effective.key)) {
                sumWeight = sumWeight.add(projectHotelTendWeight.getWhtCoPay());
            }

            /** 早餐 */
            if (projectHotelTendWeight.getWhtBreakfastState().equals(StateEnum.Effective.key)) {
                if(Objects.equals(req.getBidProjectStrategy().getIsIncludeBreakfast(), RfpConstant.constant_1) ){
                    sumWeight = sumWeight.add(projectHotelTendWeight.getWhtBreakfast());
                }
            }

            /** LRA */
            if (projectHotelTendWeight.getWhtLraState().equals(StateEnum.Effective.key)) {
                for(ProjectHotelPriceGroupEntity hotelPriceGroup : hotelPriceGroupList){
                    if (hotelPriceGroup.getLra().equals(StateEnum.Effective.key)) {
                        sumWeight = sumWeight.add(projectHotelTendWeight.getWhtLra());
                        break;
                    }
                }
            }

        }

        //自定义采购策略权重
        List<BidCustomStrategyVO> projectCustomBidStrategies = req.getBidCustomStrategyList();
        List<ProjectCustomTendStrategyEntity> queryCustomTendStrategyList = projectCustomTendStrategyMapper.selectByProjectId(req.getProjectId());
        // 查询选项
        Map<Long, List<CustomStrategyOptionVO>> optionsMap = queryStrategyOptionMap(queryCustomTendStrategyList);

        if(CollectionUtils.isNotEmpty(queryCustomTendStrategyList) && CollectionUtils.isNotEmpty(queryCustomTendStrategyList)){
            Map<Long, ProjectCustomTendStrategyEntity> queryCustomTendStrategyResponseMap = new HashMap<>();
            for (ProjectCustomTendStrategyEntity queryCustomTendStrategyResponse : queryCustomTendStrategyList) {
                queryCustomTendStrategyResponseMap.put(queryCustomTendStrategyResponse.getId(),queryCustomTendStrategyResponse);
            }
            for (BidCustomStrategyVO projectCustomBidStrategy : projectCustomBidStrategies) {
                // 文本录入不记录权重分
                if(projectCustomBidStrategy.getStrategyType() == CustomStrategyTypeEnum.TEXT.key){
                    continue;
                }
                Long customTendStrategyId = Long.valueOf(projectCustomBidStrategy.getCustomTendStrategyId());
                if(queryCustomTendStrategyResponseMap.containsKey(customTendStrategyId)){
                    ProjectCustomTendStrategyEntity queryCustomTendStrategyResponse = queryCustomTendStrategyResponseMap.get(customTendStrategyId);
                    // 权重开关没开或者权重值小于等于 0 不处理
                    if (queryCustomTendStrategyResponse.getWhtStrategyNameState() == 0 || NumberUtil.isLessOrEqual(queryCustomTendStrategyResponse.getWhtStrategyName(), BigDecimal.ZERO)) {
                        continue;
                    }

                    // 处理是否类型策略
                    if (projectCustomBidStrategy.getStrategyType() == CustomStrategyTypeEnum.YSE_OR_NO.key) {
                        sumWeight = sumWeight.add(queryCustomTendStrategyResponse.getWhtStrategyName());
                    }

                    // 处理选项类型
                    if (CustomStrategyTypeEnum.isOptionType(projectCustomBidStrategy.getStrategyType())) {
                        List<CustomStrategyOptionVO> optionList = optionsMap.get(Long.valueOf(projectCustomBidStrategy.getCustomTendStrategyId()));
                        Map<Long, CustomStrategyOptionVO> optionMap = optionList.stream().collect(Collectors.toMap(CustomStrategyOptionVO::getId, Function.identity()));
                        for (BidCustomStrategyVO.CustomStrategyBidOption option : projectCustomBidStrategy.getOptions()) {
                            if (option.getIsSupport() == 0) {
                                continue;
                            }
                            CustomStrategyOptionVO o = optionMap.get(option.getOptionId());
                            if (o != null) {
                                sumWeight = sumWeight.add(o.getWeightScore());
                            }
                        }
                    }
                }
            }
        }
        return sumWeight;
    }

    private Map<Long, List<CustomStrategyOptionVO>> queryStrategyOptionMap(List<ProjectCustomTendStrategyEntity> queryCustomTendStrategyList) {
        Set<Long> optionTypeStrategyIds = queryCustomTendStrategyList.stream()
            .filter(e -> CustomStrategyTypeEnum.isOptionType(e.getStrategyType()))
            .map(ProjectCustomTendStrategyEntity::getId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(optionTypeStrategyIds)) {
            return Collections.emptyMap();
        }
        List<CustomStrategyOptionVO> options = projectCustomStrategyOptionMapper.selectByStrategyIds(optionTypeStrategyIds);
        // 按策略 id 分组
        return options.stream().collect(Collectors.groupingBy(CustomStrategyOptionVO::getStrategyId));
    }


    public void saveOperateLog(ProjectIntentHotelEntity projectIntentHotel, UserSession userSession, String operateContent) {
        BidOperateLogEntity bidOperateLog = new BidOperateLogEntity();
        bidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        bidOperateLog.setProjectId(projectIntentHotel.getProjectId());
        bidOperateLog.setHotelId(projectIntentHotel.getHotelId());
        bidOperateLog.setCreator(userSession.getUsername());
        bidOperateLog.setOperateContent(operateContent);
        bidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
        bidOperateLogMapper.insert(bidOperateLog);
    }

    /**
     * 保存报价模板
     */
    public void saveBidTemplate(HttpServletRequest request, SubmitHotelBidRequest req){
        UserSession userSession = UserSession.get();
        // url转换
        List<UploadFileVO> employeeRightFileUrls = convertEmployeeRightUrl(req.getEmployeeRightFileUrl());
        req.setEmployeeRightFileUrl(employeeRightFileUrls);

        ProjectHotelBidTempInfoEntity projectHotelBidTempInfo = projectHotelBidTempInfoMapper.selectById(req.getProjectIntentHotelId());
        if(projectHotelBidTempInfo == null) {
            projectHotelBidTempInfo = new ProjectHotelBidTempInfoEntity();
            projectHotelBidTempInfo.setProjectIntentHotelId(req.getProjectIntentHotelId());
            projectHotelBidTempInfo.setBidInfoJson(JsonUtil.objectToJson(req));
            projectHotelBidTempInfo.setCreator(userSession.getUsername());
            projectHotelBidTempInfoMapper.insert(projectHotelBidTempInfo);
        } else {
            projectHotelBidTempInfo.setModifier(userSession.getUsername());
            projectHotelBidTempInfo.setBidInfoJson(JsonUtil.objectToJson(req));
            projectHotelBidTempInfoMapper.updateById(projectHotelBidTempInfo);
        }
    }

    /**
     * 获取报价模板
     */
    public SubmitHotelBidRequest getBidTemplate(Integer projectIntentHotelId){
        ProjectHotelBidTempInfoEntity projectHotelBidTempInfo = projectHotelBidTempInfoMapper.selectById(projectIntentHotelId);
        if(Objects.nonNull(projectHotelBidTempInfo) && projectHotelBidTempInfo.getBidInfoJson() != null){
            return JsonUtil.jsonToBeanDateSerializer(projectHotelBidTempInfo.getBidInfoJson(), SubmitHotelBidRequest.class, "yyyy-MM-dd HH:mm:ss");
        }
        return null;
    }



    public HeatMapBidInfoResponse queryHeatMapBidInfo(QueryMapPoiBidHotelInfoRequest queryMapHotelPoiBidHotelInfoRequest) throws Exception{
        String cityCode = null;
        HotelResponse hotelResponse = null;

        if(queryMapHotelPoiBidHotelInfoRequest.getHotelId() != null) {
            List<HotelResponse> hotelResponses = hotelMapper.selectHotelInfoByIds(Collections.singletonList(queryMapHotelPoiBidHotelInfoRequest.getHotelId()));
            if(CollectionUtils.isNotEmpty(hotelResponses)){
                hotelResponse = hotelResponses.get(0);
                HotelUtility.calculateHotelResponseNullGoogleLngLat(hotelResponse);
            }
            cityCode = hotelResponse.getCity();
        } else if(queryMapHotelPoiBidHotelInfoRequest.getPoiId() != null){
            List<ProjectPoiInfoResponse>  projectPoiInfoResponses = projectPoiMapper.selectMapProjectPoiInfo(queryMapHotelPoiBidHotelInfoRequest.getProjectId(), queryMapHotelPoiBidHotelInfoRequest.getPoiId(), null, null, queryMapHotelPoiBidHotelInfoRequest.getDistance());
            ProjectPoiInfoResponse poiInfoResponse;
            if(CollectionUtils.isNotEmpty(projectPoiInfoResponses)){
                poiInfoResponse = projectPoiInfoResponses.get(0);
                cityCode = poiInfoResponse.getCityCode();
            }

        }
        // 定义返回值
        HeatMapBidInfoResponse heatMapBidInfoResponse = new HeatMapBidInfoResponse();
        heatMapBidInfoResponse.setHexagonsStatList(new ArrayList<>());

        // 查询十公里范围内的历史报价数据
        Map<Long, QueryHistoryProjectInfoResponse> queryBidHotelInfoQueryResponseMap = new HashMap<>();
        List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoList = projectHotelHistoryDataMapper.queryHistoryProjectHotelList(queryMapHotelPoiBidHotelInfoRequest.getProjectId(), null, null, cityCode, null);
        queryHistoryProjectInfoList = queryHistoryProjectInfoList.stream().filter(o -> LocationUtil.isInLngLatInDistanceRange(10, o.getLatGoogle(), o.getLngGoogle(), queryMapHotelPoiBidHotelInfoRequest.getLatGoogle(), queryMapHotelPoiBidHotelInfoRequest.getLngGoogle())).collect(Collectors.toList());
        queryBidHotelInfoQueryResponseMap = queryHistoryProjectInfoList.stream().collect(Collectors.toMap(QueryHistoryProjectInfoResponse::getHotelId, Function.identity()));

        if(!queryBidHotelInfoQueryResponseMap.isEmpty()) {
            List<HexagonsStat> uniqueHexagonList = new ArrayList<>();
            // 获取酒店对应六边形
            if(hotelResponse != null) {
                HotelHexagonLngLatEntity hotelHexagonLngLat = hotelHexagonLngLatManager.getByHotelIfNullThenInit(hotelResponse);
                if(hotelHexagonLngLat.getHexagonLngLat10() == null){
                    log.error("hotelHexagonLngLat.getHexagonLngLat10() is null {}", JsonUtil.objectToJson(hotelResponse));
                } else {
                    uniqueHexagonList = JsonUtil.jsonToList(hotelHexagonLngLat.getHexagonLngLat10(), HexagonsStat.class);
                }
            } else {
                // 生成POI对应六边形
                Set<String> uniqueHexagonStringSet = new HashSet<>();
                HexagonStatUtil.generateHexagons(new BigDecimal[]{queryMapHotelPoiBidHotelInfoRequest.getLngGoogle(), queryMapHotelPoiBidHotelInfoRequest.getLatGoogle()}, 6, uniqueHexagonStringSet, uniqueHexagonList);
            }
            // 设置热力图统计值
            for(Long hotelId : queryBidHotelInfoQueryResponseMap.keySet()){
                QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse = queryBidHotelInfoQueryResponseMap.get(hotelId);
                //判断酒店是否再六边形范围内
                for(HexagonsStat hexagonsStat : uniqueHexagonList){
                    boolean isInPolygon = LocationUtil.isInPolygon(queryHistoryProjectInfoResponse.getLngGoogle(), queryHistoryProjectInfoResponse.getLatGoogle(), hexagonsStat.getLanlatList());
                    if(isInPolygon){
                        hexagonsStat.setStat(hexagonsStat.getStat() + queryHistoryProjectInfoResponse.getRoomNightCount());
                        break;
                    }
                }
            }
            uniqueHexagonList = uniqueHexagonList.stream().filter(o -> o.getStat() > 0).collect(Collectors.toList());
            heatMapBidInfoResponse.setHexagonsStatList(uniqueHexagonList);
        }

        return heatMapBidInfoResponse;
    }

    public Map<Integer, List<QueryProjectPoiHotelStatResponse>> queryMapPoiBidStat(HttpServletRequest request, ProjectMapPoiStatRequest projectMapPoiStatRequest){
        int languageId = GenericAppUtility.getRequestHeaderLanguage(request);
        // 初始化统计集合
        Map<Integer, List<QueryProjectPoiHotelStatResponse>> poiStatMap = new HashMap<>();
        poiStatMap.put(PoiStatTypeEnum.NIGHTROOM_COUNT.key, new LinkedList<>());
        poiStatMap.put(PoiStatTypeEnum.SALE_AMOUNT.key, new LinkedList<>());
        poiStatMap.put(PoiStatTypeEnum.SERVICE_POINT.key, new LinkedList<>());
        poiStatMap.put(PoiStatTypeEnum.BID_WEIGHT.key, new LinkedList<>());
        poiStatMap.put(PoiStatTypeEnum.SAVED_AMOUNT.key, new LinkedList<>());

        // 查询去年酒店项目报价ID
        ProjectEntity project = projectMapper.selectById(projectMapPoiStatRequest.getProjectId());

        // 查询项目POI信息
        List<ProjectPoiInfoResponse> projectPoiList = projectPoiMapper.selectMapProjectPoiInfo(projectMapPoiStatRequest.getProjectId(), projectMapPoiStatRequest.getPoiId(), null, null,projectMapPoiStatRequest.getDistance());
        ProjectPoiInfoResponse projectPoi = null;
        if(CollectionUtils.isNotEmpty(projectPoiList)){
            projectPoi = projectPoiList.get(0);
        }

        // 历史数据查询当前酒店城市的所有报价
        List<QueryHistoryProjectInfoResponse> projectHistoryDataList = projectHotelHistoryDataMapper.queryHistoryProjectHotelList(projectMapPoiStatRequest.getProjectId(), null, null,projectPoi.getCityCode(), projectMapPoiStatRequest.getPoiId());
        projectHistoryDataList = projectHistoryDataList.stream().filter(o -> o.getPoiId() > 0 && o.getPoiDistance().compareTo(new BigDecimal(projectMapPoiStatRequest.getDistance())) <= 0).collect(Collectors.toList());

        projectHistoryDataList.forEach(item -> {
            item.setHotelName(GenericAppUtility.getName(languageId, item.getHotelId(), item.getHotelNameEnUs(), item.getHotelNameZhCn()));
        });
        // 排名的酒店ID
        Set<Long> poiHotelIdSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(projectHistoryDataList)){
            // 成交间夜数前十
            List<QueryHistoryProjectInfoResponse> nightRoomOrderList = projectHistoryDataList.stream().sorted(Comparator.comparing(QueryHistoryProjectInfoResponse::getRoomNightCount).reversed()).collect(Collectors.toList());
            List<QueryProjectPoiHotelStatResponse> nightRoomQueryProjectPoiHotelStatResponseList = new LinkedList<>();
            for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : nightRoomOrderList){
                poiHotelIdSet.add(queryHistoryProjectInfoResponse.getHotelId());
                QueryProjectPoiHotelStatResponse queryProjectPoiHotelStatResponse = new QueryProjectPoiHotelStatResponse();
                queryProjectPoiHotelStatResponse.setPoiStatType(PoiStatTypeEnum.NIGHTROOM_COUNT.key);
                queryProjectPoiHotelStatResponse.setStatData(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()));
                queryProjectPoiHotelStatResponse.setHotelId(queryHistoryProjectInfoResponse.getHotelId());
                queryProjectPoiHotelStatResponse.setHotelName(queryHistoryProjectInfoResponse.getHotelName());
                nightRoomQueryProjectPoiHotelStatResponseList.add(queryProjectPoiHotelStatResponse);
                if(nightRoomQueryProjectPoiHotelStatResponseList.size() >= 10){
                    break;
                }
            }
            poiStatMap.put(PoiStatTypeEnum.NIGHTROOM_COUNT.key, nightRoomQueryProjectPoiHotelStatResponseList);

            // 成交金额前十
            List<QueryHistoryProjectInfoResponse> saleAmountQueryProjectPoiHotelStatResponseList = projectHistoryDataList.stream().sorted(Comparator.comparing(QueryHistoryProjectInfoResponse::getTotalAmount).reversed()).collect(Collectors.toList());
            List<QueryProjectPoiHotelStatResponse> saleAmountProjectPoiHotelStatResponseList = new LinkedList<>();
            for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : saleAmountQueryProjectPoiHotelStatResponseList){
                poiHotelIdSet.add(queryHistoryProjectInfoResponse.getHotelId());
                QueryProjectPoiHotelStatResponse queryProjectPoiHotelStatResponse = new QueryProjectPoiHotelStatResponse();
                queryProjectPoiHotelStatResponse.setPoiStatType(PoiStatTypeEnum.SALE_AMOUNT.key);
                queryProjectPoiHotelStatResponse.setStatData(queryHistoryProjectInfoResponse.getTotalAmount());
                queryProjectPoiHotelStatResponse.setHotelId(queryHistoryProjectInfoResponse.getHotelId());
                queryProjectPoiHotelStatResponse.setHotelName(queryHistoryProjectInfoResponse.getHotelName());
                saleAmountProjectPoiHotelStatResponseList.add(queryProjectPoiHotelStatResponse);
                if(saleAmountProjectPoiHotelStatResponseList.size() >= 10){
                    break;
                }
            }
            poiStatMap.put(PoiStatTypeEnum.SALE_AMOUNT.key, saleAmountProjectPoiHotelStatResponseList);


            // 计算节省金额排名
            List<QueryHistoryProjectInfoResponse> savedAmountDisplayList = projectHistoryDataList.stream().filter(o -> o.getSavedAmount()  != null && o.getSavedAmount().compareTo(BigDecimal.ZERO) > 0).sorted(Comparator.comparing(QueryHistoryProjectInfoResponse::getSavedAmount).reversed()).collect(Collectors.toList());
            List<QueryProjectPoiHotelStatResponse> savedAmountProjectPoiHotelStatResponseList = new LinkedList<>();
            for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : savedAmountDisplayList){
                poiHotelIdSet.add(queryHistoryProjectInfoResponse.getHotelId());
                QueryProjectPoiHotelStatResponse queryProjectPoiHotelStatResponse = new QueryProjectPoiHotelStatResponse();
                queryProjectPoiHotelStatResponse.setPoiStatType(PoiStatTypeEnum.SAVED_AMOUNT.key);
                queryProjectPoiHotelStatResponse.setStatData(queryHistoryProjectInfoResponse.getSavedAmount());
                queryProjectPoiHotelStatResponse.setHotelId(queryHistoryProjectInfoResponse.getHotelId());
                queryProjectPoiHotelStatResponse.setHotelName(queryHistoryProjectInfoResponse.getHotelName());
                savedAmountProjectPoiHotelStatResponseList.add(queryProjectPoiHotelStatResponse);
                if(savedAmountProjectPoiHotelStatResponseList.size() >= 10){
                    break;
                }
            }
            poiStatMap.put(PoiStatTypeEnum.SAVED_AMOUNT.key, savedAmountProjectPoiHotelStatResponseList);

            // 查询去年服务分排名
            List<Long> hotelIdList = projectHistoryDataList.stream().map(QueryHistoryProjectInfoResponse::getHotelId).collect(Collectors.toList());
            List<ProjectIntentHotelEntity> lastYearProjectIntentHotelList = projectIntentHotelMapper.selectByProjectId(project.getRelatedProjectId());
            lastYearProjectIntentHotelList = lastYearProjectIntentHotelList.stream().filter(o -> Objects.equals(o.getBidState(), HotelBidStateEnum.BID_WINNING.bidState) && hotelIdList.contains(o.getHotelId())).collect(Collectors.toList());
            lastYearProjectIntentHotelList = lastYearProjectIntentHotelList.stream().sorted(Comparator.comparing(ProjectIntentHotelEntity::getHotelServicePoints).reversed()).collect(Collectors.toList());
            List<QueryProjectPoiHotelStatResponse> servicePointProjectPoiHotelStatResponseList = new LinkedList<>();
            for(ProjectIntentHotelEntity projectIntentHotel : lastYearProjectIntentHotelList){
                poiHotelIdSet.add(projectIntentHotel.getHotelId());
                QueryProjectPoiHotelStatResponse queryProjectPoiHotelStatResponse = new QueryProjectPoiHotelStatResponse();
                queryProjectPoiHotelStatResponse.setPoiStatType(PoiStatTypeEnum.SERVICE_POINT.key);
                queryProjectPoiHotelStatResponse.setStatData(projectIntentHotel.getHotelServicePoints());
                queryProjectPoiHotelStatResponse.setHotelId(projectIntentHotel.getHotelId());
                servicePointProjectPoiHotelStatResponseList.add(queryProjectPoiHotelStatResponse);
                if(servicePointProjectPoiHotelStatResponseList.size() >= 10){
                    break;
                }
            }
            poiStatMap.put(PoiStatTypeEnum.SERVICE_POINT.key, servicePointProjectPoiHotelStatResponseList);
        }

        // 获取3公里内的酒店报价
        List<BidHotelInfoQueryResponse> bidHotelInfoQueryResponseList = projectIntentHotelMapper.queryProjectBidHotelInfo(project.getProjectId(), projectPoi.getCityCode());
        Map<Long, BidHotelInfoQueryResponse> hotelProjectIntentHotelMap = bidHotelInfoQueryResponseList.stream().collect(Collectors.toMap(BidHotelInfoQueryResponse::getHotelId, Function.identity()));
        bidHotelInfoQueryResponseList = bidHotelInfoQueryResponseList.stream().filter(o -> Objects.equals(o.getBidState(), HotelBidStateEnum.BID_WINNING.bidState) || Objects.equals(o.getBidState(), HotelBidStateEnum.NEW_BID.bidState))
                .collect(Collectors.toList());
        for(BidHotelInfoQueryResponse hotelResponse : bidHotelInfoQueryResponseList){
            if(!LocationUtil.isInLngLatInDistanceRange(projectMapPoiStatRequest.getDistance(), projectPoi.getLatGoogle(), projectPoi.getLngGoogle(), hotelResponse.getLatGoogle(), hotelResponse.getLngGoogle())){
                hotelProjectIntentHotelMap.remove(hotelResponse.getHotelId());
            }
        }

        // 计算排名
        List<BidHotelInfoQueryResponse> orderProjectIntentHotelList = new ArrayList<>(hotelProjectIntentHotelMap.values()).stream()
                .filter(o -> o.getBidWeight() != null && o.getBidWeight().compareTo(BigDecimal.ZERO) > 0).sorted(Comparator.comparing(BidHotelInfoQueryResponse::getBidWeight).reversed()).collect(Collectors.toList());
        List<QueryProjectPoiHotelStatResponse> bidWeightProjectPoiHotelStatResponseList = new LinkedList<>();
        for(BidHotelInfoQueryResponse bidHotelInfoQueryResponse : orderProjectIntentHotelList){
            poiHotelIdSet.add(bidHotelInfoQueryResponse.getHotelId());
            QueryProjectPoiHotelStatResponse queryProjectPoiHotelStatResponse = new QueryProjectPoiHotelStatResponse();
            queryProjectPoiHotelStatResponse.setPoiStatType(PoiStatTypeEnum.BID_WEIGHT.key);
            queryProjectPoiHotelStatResponse.setStatData(bidHotelInfoQueryResponse.getBidWeight());
            queryProjectPoiHotelStatResponse.setHotelId(bidHotelInfoQueryResponse.getHotelId());
            queryProjectPoiHotelStatResponse.setHotelName(GenericAppUtility.getName(languageId, bidHotelInfoQueryResponse.getHotelId(), bidHotelInfoQueryResponse.getHotelNameEnUs(), bidHotelInfoQueryResponse.getHotelNameZhCn()));
            bidWeightProjectPoiHotelStatResponseList.add(queryProjectPoiHotelStatResponse);
            if(bidWeightProjectPoiHotelStatResponseList.size() >= 10){
                break;
            }
        }
        poiStatMap.put(PoiStatTypeEnum.BID_WEIGHT.key, bidWeightProjectPoiHotelStatResponseList);

        // 设置hotelName
        if(!poiHotelIdSet.isEmpty()) {
            List<HotelResponse> hotelResponseList = hotelMapper.selectHotelInfoByIds(new ArrayList<>(poiHotelIdSet));
            Map<Long, HotelResponse> hotelResponseMap = hotelResponseList.stream().collect(Collectors.toMap(HotelResponse::getHotelId, Function.identity()));
            for(Integer poiStatType : poiStatMap.keySet()){
                List<QueryProjectPoiHotelStatResponse> queryProjectPoiHotelStatResponses = poiStatMap.get(poiStatType);
                for(QueryProjectPoiHotelStatResponse queryProjectPoiHotelStatResponse : queryProjectPoiHotelStatResponses){
                    HotelResponse hotelResponse = hotelResponseMap.get(queryProjectPoiHotelStatResponse.getHotelId());
                    if(hotelResponse != null) {
                        queryProjectPoiHotelStatResponse.setHotelName(GenericAppUtility.getName(languageId, hotelResponse.getHotelId(), hotelResponse.getHotelNameEnUs(), hotelResponse.getHotelNameZhCn()));
                    }
                }
            }
        }

        // 设置返回response
        return poiStatMap;
    }

    public void rejectNegotiation(RejectNegotiationRequest rejectNegotiationRequest){
        UserSession userSession = UserSession.get();

        // 查询报价
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.selectByProjectHotelId(rejectNegotiationRequest.getProjectId(), rejectNegotiationRequest.getHotelId());
        if(projectIntentHotel == null){
            GenericAppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);

        } else if(!Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.UNDER_NEGOTIATION.bidState)){
            GenericAppUtility.serviceError(ErrorCode.BID_STATE_NOT_UNDER_NEGOTIATION);
        }
        UpdateHotelBidStateRequest updateHotelBidStateRequest = new UpdateHotelBidStateRequest();
        updateHotelBidStateRequest.setProjectId(projectIntentHotel.getProjectId());
        updateHotelBidStateRequest.setHotelIds(Collections.singletonList(projectIntentHotel.getHotelId()));
        updateHotelBidStateRequest.setBidState(HotelBidStateEnum.REJECT_NEGOTIATION.bidState);
        updateHotelBidStateRequest.setModifier(userSession.getUsername());
        updateHotelBidStateRequest.setRejectNegotiationRemark(rejectNegotiationRequest.getRejectNegotiationRemark());
        int updateRecord = projectIntentHotelMapper.updateBidState(updateHotelBidStateRequest);
        if(updateRecord > 0){
            // 记录操作日志
            String operateContent = "BID_STATE_UPDATE_TO_ " + HotelBidStateEnum.REJECT_NEGOTIATION.value + " ，留言：" + rejectNegotiationRequest.getRejectNegotiationRemark();
            if(!StringUtils.isEmpty(rejectNegotiationRequest.getRejectNegotiationRemark())){
                    ProjectHotelRemarkEntity projectHotelRemark = new ProjectHotelRemarkEntity();
                    projectHotelRemark.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                    projectHotelRemark.setProjectId(projectIntentHotel.getProjectId());
                    projectHotelRemark.setHotelId(projectIntentHotel.getHotelId());
                    projectHotelRemark.setRemarkType(Objects.requireNonNull(HotelBidStateEnum.getHotelBidStateEnum(updateHotelBidStateRequest.getBidState())).name());
                    projectHotelRemark.setRemark(rejectNegotiationRequest.getRejectNegotiationRemark());
                    projectHotelRemark.setCreator(userSession.getUsername());
                    projectHotelRemarkMapper.insert(projectHotelRemark);

            }
            saveOperateLog(projectIntentHotel, userSession, operateContent);

        } else {
           GenericAppUtility.serviceError(ErrorCode.UPDATE_FAILED);
        }

    }

    public boolean isNeedHotelGroupApprove(Integer projectId, HotelEntity hotel, UserSession userSession) {
        // 查询酒店机构的品牌ID
        if(hotel == null || hotel.getHotelBrandId() == null){
            return false;
        }
        if(Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.HOTELGROUP.key)){
            return false;
        }

        // 查询项目开始审核的酒店集团机构
        List<Integer> projectHotelGroupOrgIdList = projectIntentHotelGroupMapper.queryNeedApproveHotelGroupOrgId(projectId);
        if(CollectionUtils.isEmpty(projectHotelGroupOrgIdList)){
            return false;
        }

        // 查询需要审核的所有品牌
        List<Long> brandIds = orgRelatedHotelBrandMapper.queryHotelGroupBrandIds(projectHotelGroupOrgIdList);
        if(CollectionUtils.isEmpty(brandIds)){
            return false;
        }

        // 集团机构没有针对 这个项目开启审核
        if(Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.HOTELGROUP.key)){
            if(!projectHotelGroupOrgIdList.contains(userSession.getUserOrg().getOrgId())){
                return false;
            }
        }


        // 品牌是否包括酒店品牌
        return brandIds.contains(hotel.getHotelBrandId());
    }

    /**
     * 检查 同房档group是否重复 比较价格星期是否有交叉重叠
     */
    public Response compareApplicableWeeks(ProjectIntentHotelEntity projectIntentHotel, Integer hotelPriceLevelId, Integer hotelPriceGroupId, Integer lra, String updateApplicableWeeks, boolean isUpdate) {
        List<String> applicableWeeks = Arrays.asList(updateApplicableWeeks.split(","));
        // Lanyon导入数据不需要检查
        if(BidUtil.isLanyonImport(projectIntentHotel) == RfpConstant.constant_1){
            return null;
        }

        List<ProjectHotelPriceGroupEntity> projectHotelPriceGroupList = projectHotelPriceGroupMapper.selectByHotelPriceLevelId(hotelPriceLevelId);
        for(ProjectHotelPriceGroupEntity dbProjectHotelPriceGroup : projectHotelPriceGroupList){
            if(isUpdate && dbProjectHotelPriceGroup.getHotelPriceGroupId().equals(hotelPriceGroupId)){
                continue;
            }
            if(Objects.equals(dbProjectHotelPriceGroup.getLra(), lra)){
                for(String week : applicableWeeks){
                    if(dbProjectHotelPriceGroup.getApplicableWeeks().contains(week)){
                        Response response = new Response();
                        response.setResult(ReturnResultEnum.FAILED.errorNo);
                        response.setMsg(ErrorCode.HOTEL_PRICE_GROUP_WEEK_DAY_REPEAT);
                        return response;
                    }
                }
            }
        }
        return null;
    }

    public List<HotelMinPriceResponse> queryHotelMinPrice(int languageId, List<Integer> projectIntentHotelIds, boolean isIncludeRoomName){
        if(org.springframework.util.CollectionUtils.isEmpty(projectIntentHotelIds)){
            return new ArrayList<>();
        }
        Map<Integer, List<ProjectHotelPriceAndGroupInfoVO>> projectHotelPriceMap = projectHotelPriceMapper.selectPriceInfo(projectIntentHotelIds).stream().collect(Collectors.groupingBy(ProjectHotelPriceAndGroupInfoVO::getProjectIntentHotelId));
        List<HotelMinPriceResponse> hotelMinPriceResponseList = new ArrayList<>();
        List<Long> hotelIdList = new ArrayList<>();
        List<Integer> hotelPriceLevelIdList = new ArrayList<>();
        for(Integer projectIntentHotelId : projectIntentHotelIds){
            List<ProjectHotelPriceAndGroupInfoVO> projectHotelPriceList = projectHotelPriceMap.get(projectIntentHotelId);
            if(CollectionUtils.isNotEmpty(projectHotelPriceList)){
                ProjectHotelPriceAndGroupInfoVO minProjectHotelPrice = projectHotelPriceList.get(0);
                HotelMinPriceResponse hotelMinPriceResponse = new HotelMinPriceResponse();
                hotelMinPriceResponse.setProjectIntentHotelId(projectIntentHotelId);
                hotelMinPriceResponse.setHotelId(minProjectHotelPrice.getHotelId());
                hotelMinPriceResponse.setHotelPriceLevelId(minProjectHotelPrice.getHotelPriceLevelId());
                hotelMinPriceResponse.setMinPrice(minProjectHotelPrice.getOnePersonPrice());
                hotelMinPriceResponse.setIsIncludeBreakfast(minProjectHotelPrice.getIsIncludeBreakfast());
                hotelMinPriceResponse.setBreakfastNum(BreakfastNumEnum.getEnumByIncludeBreakfast(Objects.equals(minProjectHotelPrice.getIsIncludeBreakfast(), RfpConstant.constant_1), 1));
                hotelMinPriceResponseList.add(hotelMinPriceResponse);
                hotelPriceLevelIdList.add(minProjectHotelPrice.getHotelPriceLevelId());
                hotelIdList.add(minProjectHotelPrice.getHotelId());
            }
        }
        // 设置最低价格房档房型信息
        if(!org.springframework.util.CollectionUtils.isEmpty(hotelMinPriceResponseList) && isIncludeRoomName) {
            List<PriceApplicableRoomEntity> priceApplicableRoomList = priceApplicableRoomMapper.selectPriceApplicableRoomList(hotelPriceLevelIdList);
            Map<Integer, List<PriceApplicableRoomEntity>> priceApplicableRoomMap = priceApplicableRoomList.stream().collect(Collectors.groupingBy(PriceApplicableRoomEntity::getHotelPriceLevelId));
            List<RoomNameInfoVO> roomNameInfoVOList = cachedHotelService.queryRoomNameListByHotelIds(languageId, hotelIdList);
            Map<Long, RoomNameInfoVO> roomNameInfoVOMap = roomNameInfoVOList.stream().collect(Collectors.toMap(RoomNameInfoVO::getRoomId, Function.identity()));
            for(HotelMinPriceResponse hotelMinPriceResponse : hotelMinPriceResponseList){
                List<PriceApplicableRoomInfoResponse> priceApplicableRoomInfoResponses = new ArrayList<>();
                List<PriceApplicableRoomEntity> priceLevelApplicableRoomList = priceApplicableRoomMap.get(hotelMinPriceResponse.getHotelPriceLevelId());
                // 为空导入数据
                if(CollectionUtils.isEmpty(priceLevelApplicableRoomList)){
                    ProjectHotelPriceLevelEntity projectHotelPriceLevel = projectHotelPriceLevelMapper.queryByHotelPriceLevelId(hotelMinPriceResponse.getHotelPriceLevelId());
                    hotelMinPriceResponse.setRoomTypeDesc(projectHotelPriceLevel.getLanyonRoomDesc());
                    continue;
                }
                for(PriceApplicableRoomEntity priceApplicableRoomEntity : priceLevelApplicableRoomList){
                    PriceApplicableRoomInfoResponse priceApplicableRoomInfoResponse = new PriceApplicableRoomInfoResponse();
                    BeanUtils.copyProperties(priceApplicableRoomEntity, priceApplicableRoomInfoResponse);
                    // 设置房型名称, 可能是自定义房型名称, 也可能是选择的房型 id 对应的房型名称
                    if (Objects.isNull(priceApplicableRoomEntity.getRoomTypeId())) {
                        priceApplicableRoomInfoResponse.setRoomTypeName(priceApplicableRoomEntity.getCustomRoomTypeName());
                    } else {
                        RoomNameInfoVO roomNameInfoVO = roomNameInfoVOMap.get(priceApplicableRoomEntity.getRoomTypeId());
                        priceApplicableRoomInfoResponse.setRoomTypeName(roomNameInfoVO.getRoomName());
                    }
                    priceApplicableRoomInfoResponses.add(priceApplicableRoomInfoResponse);
                }
                hotelMinPriceResponse.setRoomResponseList(priceApplicableRoomInfoResponses);
                if(CollectionUtils.isNotEmpty(priceApplicableRoomInfoResponses)) {
                    hotelMinPriceResponse.setRoomTypeDesc(priceApplicableRoomInfoResponses.get(0).getRoomTypeName());
                    if(priceApplicableRoomInfoResponses.size() > 1){
                        hotelMinPriceResponse.setRoomTypeDesc(hotelMinPriceResponse.getRoomTypeDesc() + ",..");
                    }
                }
            }
        }
        return hotelMinPriceResponseList;
    }


    public String notifyBidderConsumer(Integer projectIntentHotelId, String operator) {
        String result = "";

        String contactName;
        String email = "";
        String mobile = "";
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.selectById(projectIntentHotelId);
        HotelBidDetailRequest hotelBidDetailRequest = new HotelBidDetailRequest();
        hotelBidDetailRequest.setProjectId(projectIntentHotel.getProjectId());
        hotelBidDetailRequest.setHotelId(projectIntentHotel.getHotelId());
        hotelBidDetailRequest.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        HotelBidDetailResponse hotelBidDetailResponse = this.queryHotelBidDetail(null, hotelBidDetailRequest);
        hotelBidDetailResponse.setBidHotelPriceLevelInfoVOList(querBidHotelPriceLevelInfoList(UserSession.get(), LanguageEnum.EN_US.key, projectIntentHotel.getProjectIntentHotelId(), projectIntentHotel, projectIntentHotel.getHotelId(),null));
        ProjectEntity project = projectMapper.selectById(projectIntentHotel.getProjectId());
        boolean isNotify = false;
        email = projectIntentHotel.getHotelBidContactEmail();
        contactName = projectIntentHotel.getHotelBidContactName();
        mobile = projectIntentHotel.getHotelBidContactMobile();


        // 签约状态通知，根据报价合同的报价来源，通知不同的报价人，比如：是由酒店集团报价的或导入的，报价状态通知酒店集团报价人，由酒店单店报价的，通知酒店单店的报价联系人。
        if(Objects.equals(projectIntentHotel.getBidOrgType(), OrgTypeEnum.HOTELGROUP.key) &&  (!jodd.util.StringUtil.isEmpty(projectIntentHotel.getHotelGroupBidContactEmail()))){
            email = projectIntentHotel.getHotelGroupBidContactEmail();
            contactName = projectIntentHotel.getHotelGroupBidContactName();
            mobile = projectIntentHotel.getHotelGroupBidContactMobile();
        }
        if(StringUtility.notNullAndNotEmpty(email)){
            String baseApplicableDays = "";
            String season1ApplicableDays = "";
            String season2ApplicableDays = "";
            for (BidApplicableDayVO priceApplicableDay : hotelBidDetailResponse.getBidApplicableDayList()) {
                String dateRange = com.fangcang.grfp.core.util.DateUtil.dateToString(priceApplicableDay.getStartDate()) + " To " + com.fangcang.grfp.core.util.DateUtil.dateToString(priceApplicableDay.getEndDate()) + ", ";
                if (priceApplicableDay.getPriceType() == HotelPriceTypeEnum.BASE_PRICE.key) {
                    baseApplicableDays = baseApplicableDays + dateRange;
                } else if (priceApplicableDay.getPriceType() == HotelPriceTypeEnum.SEASON_1_PRICE.key) {
                    season1ApplicableDays = season1ApplicableDays + dateRange;
                } else if (priceApplicableDay.getPriceType() == HotelPriceTypeEnum.SEASON_2_PRICE.key) {
                    season2ApplicableDays = season2ApplicableDays + dateRange;
                }
            }
            if (StringUtility.notNullAndNotEmpty(baseApplicableDays)) {
                baseApplicableDays = baseApplicableDays.substring(0, baseApplicableDays.length() - 2);
            }
            if (StringUtility.notNullAndNotEmpty(season1ApplicableDays)) {
                season1ApplicableDays = season1ApplicableDays.substring(0, season1ApplicableDays.length() - 2);
            }
            if (StringUtility.notNullAndNotEmpty(season2ApplicableDays)) {
                season2ApplicableDays = season2ApplicableDays.substring(0, season2ApplicableDays.length() - 2);
            }
            String unApplicableDays = "";
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(hotelBidDetailResponse.getBidUnApplicableDayList()))
                for (BidUnApplicableDayVO priceUnApplicableDay : hotelBidDetailResponse.getBidUnApplicableDayList()) {
                    String dateRange = com.fangcang.grfp.core.util.DateUtil.dateToString(priceUnApplicableDay.getStartDate()) + " To " + com.fangcang.grfp.core.util.DateUtil.dateToString(priceUnApplicableDay.getEndDate()) + ", ";
                    unApplicableDays = unApplicableDays + dateRange;
                }
            if (StringUtility.notNullAndNotEmpty(unApplicableDays)) {
                unApplicableDays = unApplicableDays.substring(0, unApplicableDays.length() - 2);
            }

            String sb =
                    "<!DOCTYPE html>\n" +
                            "<html lang=\"en\">\n" +
                            "\n" +
                            "<head>\n" +
                            "    <meta charset=\"UTF-8\">\n" +
                            "    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n" +
                            "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                            "    <title>Globale RFP</title>\n" +
                            "</head>\n" +

                            "\n" +
                            "<body style=\"background: rgb(232, 232, 237);\">\n" +
                            "    <div style=\"background: rgb(232, 232, 237);width: 100% ;padding: 10px;\">\n" +
                            "        <div class=\"content\" style=\"width: 1040px;border-radius: 5px;margin: 20px auto;\">\n" +
                            "            <div class=\"headInfo\"\n" +
                            "                style=\"height: 64px;background-color: #008489;display: flex;align-items: center;padding-left: 40px;border-radius: 5px 5px 0 0;\">\n" +
                            "                <div class=\"tit1\" style=\"font-weight: 900;font-style: normal;font-size: 24px;letter-spacing: 1px;color: #FFFFFF;\">\n" +
                            "                    RFP Notify\n" +
                            "                </div>\n" +
                            "            </div>\n" +
                            "            <div class=\"titInfo\"\n" +
                            "                style=\"height: 94px;background-color: rgba(235, 244, 255, 1);padding: 20px 40px;font-size: 13px;box-sizing: border-box;\">\n" +
                            "                <div style=\"display: flex; align-items: center; justify-content: space-between;\">\n" +
                            "                    <div style=\"color: #444444;\">\n" +
                            "\t\t\t\t\tDear \n" +
                            "\t\t\t\t\t<span style=\"color: #0D529C;\">hotelName：</span>\n" +
                            "                        <div style=\"margin-top:0px;text-indent: 26px;\">\n" +
                            "                            You Join\n" +
                            "\t\t\t\t\t\t\t<span\n" +
                            "                                style=\"color: #FF4200;\">projectName</span> Bidder ，Now Status：\n" +
                            "\t\t\t\t\t\t\t\t<span\n" +
                            "                                style=\"color:#FF4200; font-size:20px\" >bidStateValue</span>   Please follow bid\n" +
                            "                        </div>\n" +
                            "                    </div>\n" +
                            "                    <div style=\"display: flex; align-items: center;\">\n" +
                            "               \n" +
                            "                        <div>\n" +
                            "                           <a href=\"https://www.agree-ease.com/login\" style=\"width: 140px;text-decoration:none;height: 40px;line-height: 40px;text-align: center;color: #fff;background-color: rgb(251, 126, 51);border-radius: 3px; cursor: pointer; border: 0px; display: block;\">Login</a>" +
                            "\t\t\t\t\t\t\t</div>\n" +
                            "                    </div>\n" +
                            "                </div>\n" +
                            "            </div>\n" +
                            "            <div class=\"contactInfo\"\n" +
                            "                style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
                            "                <div style=\"font-size: 18px;\">\n" +
                            "                    Bidding Organization Introduction\n" +
                            "                </div>\n" +
                            "                <div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\">\n" +
                            "                    <div style=\"font-size: 13px;\">\n" +
                            "                        <div style=\"display:flex;\">\n" +
                            "                            <div style=\"width:130px;color: #A1A1A1;;\">Org Name</div>\n" +
                            "                            <div>orgName</div>\n" +
                            "                        </div>\n" +
                            "                        <div style=\"margin-top:20px;display:flex;\">\n" +
                            "                            <div style=\"width:130px;color: #A1A1A1;\">Introduction</div>\n" +
                            "                            <div style=\"width:525px;\">\n" +
                            "                                companyProfile\n" +
                            "                            </div>\n" +
                            "                        </div>\n" +
                            "                    </div>\n" +
                            "                    <div style=\"width: 237px;height:147px; border:1px solid rgba(222, 224, 229, 1);\">\n" +
                            "                        <img src=\"logoUrl\" alt=\"\"\n" +
                            "                            style=\"width: 100%;height:145px;object-fit: contain;\">\n" +
                            "                    </div>\n" +
                            "                </div>\n" +
                            "            </div>\n" +
                            "            <div class=\"contactInfo\"\n" +
                            "                style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
                            "                <div style=\"font-size: 18px;\">\n" +
                            "                    Project Basic Information\n" +
                            "                </div>\n" +
                            "                <div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\">\n" +
                            "                    <div style=\"font-size: 13px;\">\n" +
                            "                        <div style=\"display:flex;\">\n" +
                            "                            <div style=\"width:130px;color: #A1A1A1;;\">Project name*</div>\n" +
                            "                            <div style=\"display: flex;align-items: center;\">\n" +
                            "                                <span>projectName</span>\n" +
                            "                              <div\n" +
                            "                                    style=\"width:56px;height:20px;line-height: 20px;text-align: center;color: rgb(161, 161, 161);border:1px solid rgba(222, 224, 229, 1);margin: 0 5px 0 10px;\">\n" +
                            "                                    projectTypeValue</div>\n" +
                            "                                <div\n" +
                            "                                    style=\"width:45px;height:20px;line-height: 20px;text-align: center;color: #fff;background-color: rgb(251, 126, 51);\">\n" +
                            "                                    projectStateValue</div>\n" +
                            "                            </div>\n" +
                            "                        </div>\n" +
                            "                        <div style=\"margin-top:20px;display:flex;\">\n" +
                            "                            <div style=\"width:130px;color: #A1A1A1;\">Organization Name </div>\n" +
                            "                            <div>orgName</div>\n" +
                            "                        </div>\n" +
                            "                        <div style=\"margin-top:20px;display:flex;\">\n" +
                            "                            <div style=\"width:130px;color: #A1A1A1;\">*</div>\n" +
                            "                            <div></div>\n" +
                            "                        </div>\n" +
                            "                        <div style=\"margin-top:20px;display:flex;\">\n" +
                            "                            <div style=\"width:130px;color: #A1A1A1;\">Bid Time*</div>\n" +
                            "                            <div>\n" +
                            "                                <span>enrollStartTime</span> —— <span>enrollEndTime</span>\n" +
                            "                            </div>\n" +
                            "                        </div>\n" +
                            "                    </div>\n" +
                            "                </div>\n" +
                            "            </div>\n" +
                            "        <div class=\"contactInfo\" \n" +
                            "               style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\"> \n" +
                            "               <div style=\"font-size: 18px;\"> \n" +
                            "                       Contact Information \n" +
                            "                       </div> \n" +
                            "               <div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\"> \n" +
                            "                 <div style=\"font-size: 13px;\"> \n" +
                            "                   <div style=\"display:flex;\"> \n" +
                            "                     <div style=\"width:130px;color: #A1A1A1;\">Hotel Contact Person</div> \n" +
                            "                     <div>bidContactName</div> \n" +
                            "                   </div> \n" +
                            "                   <div style=\"margin-top:20px;display:flex;\"> \n" +
                            "                     <div style=\"width:130px;color: #A1A1A1;\">Contact Number</div> \n" +
                            "                     <div style=\"width:525px;\"> \n" +
                            "                       bidContactMobile \n" +
                            "                       </div> \n" +
                            "                  </div>\n" +
                            "                   <div style=\"margin-top:20px;display:flex;\">\n" +
                            "                    <div style=\"width:130px;color: #A1A1A1;\">Contact Email</div>\n" +
                            "                     <div style=\"width:525px;\">\n" +
                            "                       bidContactEmail\n" +
                            "                    </div>\n" +
                            "                   </div>\n" +
                            "                 </div>\n" +
                            "               </div>\n" +
                            "             </div>\n" +
                            "<div class=\"quotation-table\"> \n" +
                            "       <div class=\"head\">\n" +
                            "       <div class=\"name\">Quotation date </div> \n" +
                            "    </div>\n" +
                            "<table class=\"table-box table-box-price\" border=\"1px solid #e6ebf1\">\n" +
                            "<tbody>\n" +
                            "<tr>\n" +
                            "<td class=\"td-name\">\n" +
                            "<p class=\"label\">Base Date <span class=\"star\">*</span></p>\n" +
                            "</td>\n" +
                            "<td>\n" +
                            "<p class=\"val\">baseApplicableDays</p>\n" +
                            "</td>\n" +
                            "</tr>\n" +
                            "<tr>\n" +
                            "<td class=\"td-name\">\n" +
                            "<p class=\"label\">Season1 Date</p>\n" +
                            "</td>\n" +
                            "<td>\n" +
                            "<p class=\"val\">season1ApplicableDays</p>\n" +
                            "</td>\n" +
                            "</tr>\n" +
                            "<tr>\n" +
                            "<td class=\"td-name\">\n" +
                            "<p class=\"label\">Season2 Date</p>\n" +
                            "</td>\n" +
                            "<td>\n" +
                            "<p class=\"val\">season2ApplicableDays</p>\n" +
                            "</td>\n" +
                            "</tr>\n" +
                            "<tr>\n" +
                            "<td class=\"td-name\">\n" +
                            "<p class=\"label\">Unapplicable Date</p>\n" +
                            "</td>\n" +
                            "<td>\n" +
                            "<p class=\"val\">unApplicableDays</p>\n" +
                            "</td>\n" +
                            "</tr>\n" +
                            "</tbody>\n" +
                            "</table>\n" +
                            "</div>\n" +
                            "<div class=\"quotation-table quotation-detail\"> \n" +
                            "<h3>Price Level Detail</h3>\n";
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(hotelBidDetailResponse.getBidHotelPriceLevelInfoVOList())) {
                for (BidHotelPriceLevelInfoVO bidHotelPriceLevelInfoVO : hotelBidDetailResponse.getBidHotelPriceLevelInfoVOList()) {
                    String hotelPriceLevel =
                            "<div class=\"head\">\n" +
                                    "<div class=\"name\"> Room Level " + bidHotelPriceLevelInfoVO.getRoomLevelNo() + "：</div>\n";
                    String levelRoomInfo = "<div>";
                    for (BidApplicableRoomVO priceApplicableRoomInfoResponse : bidHotelPriceLevelInfoVO.getRoomList()) {
                        String roomTypeIdDesc = priceApplicableRoomInfoResponse.getRoomTypeId() == null ? "" : " (" + priceApplicableRoomInfoResponse.getRoomTypeId() +") ";
                        levelRoomInfo = levelRoomInfo + "<p>" + priceApplicableRoomInfoResponse.getRoomName() + roomTypeIdDesc + "</p>";
                    }
                    levelRoomInfo = levelRoomInfo + "</div><p>";
                    if (bidHotelPriceLevelInfoVO.getBigBedRoomCount() != null) {
                        levelRoomInfo = levelRoomInfo + "Twin Room<span class=\"c-orange\">" + bidHotelPriceLevelInfoVO.getBigBedRoomCount() + "</span>";
                    }
                    if (bidHotelPriceLevelInfoVO.getDoubleBedRoomCount() != null) {
                        levelRoomInfo = levelRoomInfo + " Double Room<span class=\"c-orange\">" + bidHotelPriceLevelInfoVO.getDoubleBedRoomCount() + "</span>";
                    }
                    if (bidHotelPriceLevelInfoVO.getTotalRoomCount() != null) {
                        levelRoomInfo = levelRoomInfo + " Total<span class=\"c-orange\">" + bidHotelPriceLevelInfoVO.getTotalRoomCount() + "</span>";
                    }
                    levelRoomInfo = levelRoomInfo + "</p></div>\n";
                    hotelPriceLevel = hotelPriceLevel + levelRoomInfo;

                    hotelPriceLevel = hotelPriceLevel + "<table class=\"table-box\" border=\"1px solid #e6ebf1\"> \n" +
                            "<thead> \n" +
                            "<tr>\n" +
                            "<th>Base price</th>\n" +
                            "<th class=\"w-160\">Season1 price</th>\n" +
                            "<th class=\"w-160\">Season2 price</th>\n" +
                            "<th>Applicable Week</th>\n" +
                            "<th>LRA</th>\n" +
                            "<th class=\"w-160\">Remark</th>\n" +
                            "</tr>\n" +
                            "</thead>\n" +
                            "<tbody>\n";
                    sb = sb + hotelPriceLevel;
                    for (BidHotelPriceGroupVO projectHotelPriceGroupResponse : bidHotelPriceLevelInfoVO.getBidHotelPriceGroupList()) {
                        String breakfast = projectHotelPriceGroupResponse.getIsIncludeBreakfast() == RfpConstant.constant_1 ? "INB" :"EXB";
                        Map<Integer, List<BidHotelPriceVO>> priceTypePriceListMap = projectHotelPriceGroupResponse.getBidHotelPriceList().stream().collect(Collectors.groupingBy(BidHotelPriceVO::getPriceType));
                        String projectHotelPriceInfo = "";
                        String basePriceInfo = "";
                        List<BidHotelPriceVO> basePriceList = priceTypePriceListMap.get(HotelPriceTypeEnum.BASE_PRICE.key);
                        for (BidHotelPriceVO projectHotelPrice : basePriceList) {
                            basePriceInfo = basePriceInfo + " <div class=\"row\">";
                            basePriceInfo = basePriceInfo + "<p>" + breakfast + "</p>";
                            basePriceInfo = basePriceInfo + "<p class=\"price\">"+ projectIntentHotel.getCurrencyCode() +"x1 "  + projectHotelPrice.getOnePersonPrice() + "</p>";
                            basePriceInfo = basePriceInfo + "<p class=\"price\">"+ projectIntentHotel.getCurrencyCode() +"x2 "  + projectHotelPrice.getTwoPersonPrice() + "</p>";
                            basePriceInfo = basePriceInfo + "</div>";
                        }

                        projectHotelPriceInfo = "<tr>\n";
                        // Base Price
                        projectHotelPriceInfo = projectHotelPriceInfo + "<td class=\"price w-120\">" + basePriceInfo + "</td>";
                        String season1Price = "";
                        List<BidHotelPriceVO> season1PriceList = priceTypePriceListMap.get(HotelPriceTypeEnum.SEASON_1_PRICE.key);
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(season1PriceList)) {
                            for (BidHotelPriceVO projectHotelPrice : season1PriceList) {
                                season1Price = season1Price + " <div class=\"row\">";
                                season1Price = season1Price + "<p class=\"price\">"+ projectIntentHotel.getCurrencyCode() +"x1 "  + projectHotelPrice.getOnePersonPrice() + "</p>";
                                season1Price = season1Price + "<p class=\"price\">"+ projectIntentHotel.getCurrencyCode() +"x2 "  + projectHotelPrice.getTwoPersonPrice() + "</p>";
                                season1Price = season1Price + "</div>";
                            }
                        }
                        // Season1 Price
                        projectHotelPriceInfo = projectHotelPriceInfo + "<td class=\"price w-120\">" + season1Price + "</td>";
                        String season2Price = "";
                        List<BidHotelPriceVO> season2PriceList = priceTypePriceListMap.get(HotelPriceTypeEnum.SEASON_2_PRICE.key);
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(season2PriceList)) {
                            for (BidHotelPriceVO projectHotelPrice : season2PriceList) {
                                season2Price = " <div class=\"row\">";
                                season2Price = season2Price + "<p class=\"price\">"+ projectIntentHotel.getCurrencyCode() +"x1 "  + projectHotelPrice.getOnePersonPrice() + "</p>";
                                season2Price = season2Price + "<p class=\"price\">"+ projectIntentHotel.getCurrencyCode() +"x2 "  + projectHotelPrice.getTwoPersonPrice() + "</p>";
                                season2Price = season2Price + "</div>";
                            }
                        }
                        // Season2 Price
                        projectHotelPriceInfo = projectHotelPriceInfo + "<td class=\"price w-120\">" + season2Price + "</td>";

                        // WeekDay
                        projectHotelPriceInfo = projectHotelPriceInfo + "<td class=\"w-120\"> <div class=\"week-box\">" + ApplicableWeeksTypeEnum.getApplicableWeeksDesc(LanguageEnum.EN_US.key, projectHotelPriceGroupResponse.getApplicableWeeks()) + "</div></td>";

                        // LRA
                        projectHotelPriceInfo = projectHotelPriceInfo + "<td>" + (projectHotelPriceGroupResponse.getLra() == RfpConstant.constant_1 ? "LRA" : "No LRA") + "</td>";

                        // remark
                        String remark = jodd.util.StringUtil.isNotEmpty(projectHotelPriceGroupResponse.getRemark()) ? projectHotelPriceGroupResponse.getRemark() : "";
                        projectHotelPriceInfo = projectHotelPriceInfo + "<td class=\"w-160\">" + remark + "</td>";

                        sb = sb + projectHotelPriceInfo + "</tr>";

                    }
                    sb = sb + "</tbody></table>\n";

                }
            }
            sb = sb + "</div>\n";
            String bidStrage = "";
            bidStrage = "<div class=\"contactInfo\" style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\">\n";
            bidStrage = bidStrage + "<div style=\"font-size: 18px;\">Hotel Service Commitment</div>\n";
            bidStrage = bidStrage + "<div class=\"shrink-box\" style=\"margin-top:20px;\">\n";
            if (hotelBidDetailResponse.getBidProjectStrategy() != null) {
                bidStrage = bidStrage + "<div class=\"item\">\n";
                bidStrage = bidStrage + getHotelBidStrategyItemHtml(hotelBidDetailResponse.getBidProjectStrategy().getSupportPayAtHotel(), "Support pay at hotel");
                bidStrage = bidStrage + getHotelBidStrategyItemHtml(hotelBidDetailResponse.getBidProjectStrategy().getSupportVccPay(), "Support central payment");
                bidStrage = bidStrage + getHotelBidStrategyItemHtml(hotelBidDetailResponse.getBidProjectStrategy().getSupportNoGuarantee(), "No deposit required for POA bookings");
                bidStrage = bidStrage + "</div>\n";

                bidStrage = bidStrage + "<div class=\"item\">\n";
                bidStrage = bidStrage + getHotelBidStrategyItemHtml(hotelBidDetailResponse.getBidProjectStrategy().getSupportCheckinInfo(), "提供入住明细信息");
                bidStrage = bidStrage + getHotelBidStrategyItemHtml(hotelBidDetailResponse.getBidProjectStrategy().getSupportPayEarlyCheckout(), "Hotel need to offer check-in & check-out details");
                bidStrage = bidStrage + getHotelBidStrategyItemHtml(hotelBidDetailResponse.getBidProjectStrategy().getSupportWifi(), "Free WIFI service provided in hotel rooms");
                bidStrage = bidStrage + "</div>\n";

                bidStrage = bidStrage + "<div class=\"item\">\n";
                bidStrage = bidStrage + getHotelBidStrategyItemHtml(hotelBidDetailResponse.getBidProjectStrategy().getSupportIncludeTaxService(), "The hotel quotation includes taxes and service fees");
                bidStrage = bidStrage + "</div>\n";
            }
            sb = sb + bidStrage;

            // 自定义策略
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(hotelBidDetailResponse.getBidCustomStrategyList())) {
                String customBidStrategy = "";
                List<List<BidCustomStrategyVO>> subProjectCustomBidStrategyList = ListUtil.split(hotelBidDetailResponse.getBidCustomStrategyList(), 3);

                for (List<BidCustomStrategyVO> subList : subProjectCustomBidStrategyList) {
                    customBidStrategy = customBidStrategy + "<div class=\"item\">\n";
                    for (BidCustomStrategyVO projectCustomBidStrategy : subList) {
                        customBidStrategy = customBidStrategy + getHotelBidCustomItemHtml(projectCustomBidStrategy);
                    }
                    customBidStrategy = customBidStrategy + "</div>\n";
                }
                sb = sb + customBidStrategy;
            }
            sb = sb + "</div></div>\n";


            sb = sb + "</div>\n" +
                    "</div>\n" +
                    "</div>\n" +
                    "</div>\n" +

                    "<div class=\"contactInfo\" style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
                    "<div style=\"font-size: 18px;\">Others </div>\n" +
                    "<div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\"> \n" +
                    "<div style=\"font-size: 13px;\">\n" +
                    "<div style=\"display:flex;\">\n" +
                    "<div style=\"width:130px;color: #A1A1A1;\">Employee Rights and Benefits</div>\n";
            if (jodd.util.StringUtil.isNotEmpty(hotelBidDetailResponse.getProjectIntentHotelBid().getEmployRight())) {
                sb = sb + "<div>" + hotelBidDetailResponse.getProjectIntentHotelBid().getEmployRight() + "</div>\n";
            } else {
                sb = sb + "<div>-</div>\n";
            }
            sb = sb + "</div>\n" +
                    "</div>\n" +
                    "</div>\n" +
                    "</div>\n" +

                    "<div class=\"contactInfo\" style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
                    "                <div style=\"font-size: 18px;\">\n" +
                    "                    Project Introduction\n" +
                    "                </div>\n" +
                    "                <div id=\"content\" style=\"margin-top:20px;\">\n" +
                    "\n" + "introduction" +
                    "                </div>\n" +
                    "\n" +
                    "            </div>\n" +
                    "            <div class=\"footer\"\n" +
                    "                style=\"height: 60px;line-height: 60px;background-color: rgba(235, 244, 255, 1);;font-size: 13px;padding-left: 40px;color: #A0A7BB;border-radius:0 0 5px 5px;\">\n" +
                    "                <span style=\"cursor: pointer;margin-right:20px;\">Do you want to become its travel hotel supplier？\n" +
                    "\t\t\t\t<a href=\"http://https://www.agree-ease.com/login\"   style=\"color: rgb(2, 167, 240);text-decoration:underline;\">Login RFP</a>\n" +
                    "\t\t\t\t</span>\n" +
                    "\t\t\t\t\t\t\n" +
                    "                <span>If you don't have an account, please contact the operations department to activate it: <span style=\"color: rgb(255, 96, 0);\">400-1866-919</span></span>\n" +
                    "            </div>\n" +
                    "        </div>\n" +
                    "    </div>\n" +
                    "</div>\n" +
                    "</body>\n" +
                    "\n" +
                    "<script type=\"text/javascript\">\n" +
                    "\t\n" +
                    "\t   let content = document.getElementById('content');\n" +
                    "    content.innerHTML =  `introduction` " +
                    "</script>\n" +

                    "  <style>\n" +
                    " .quotation-table {\n" +
                    "          padding: 20px 40px 0;\n" +
                    "           box-sizing: border-box;\n" +
                    "           background-color: #fff;\n" +
                    "           font-size: 14px;\n" +
                    "       }\n" +

                    ".quotation-table .c-orange {\n" +
                    "           color: #ff6000;\n" +
                    "        }\n" +


                    ".quotation-table .head {\n" +
                    "         display: flex;\n" +
                    "           color: #222;\n" +
                    "           align-items: center;\n" +
                    "           justify-content: flex-start;\n" +
                    "       }\n" +

                    ".quotation-table .head p {\n" +
                    "           color: #898377;\n" +
                    "       }\n" +

                    ".quotation-table .head .name {\n" +
                    "           font-size: 18px;\n" +
                    "           margin-right: 10px;\n" +
                    "       }\n" +

                    ".table-box {\n" +
                    "           border: 1px solid #e6ebf1;\n" +
                    "       }\n" +

                    ".table-box .row {\n" +
                    "           display: flex;\n" +
                    "           align-items: center;\n" +
                    "           justify-content: flex-start;\n" +
                    "           box-sizing: border-box;\n" +
                    "       }\n" +
                    "  table-box .row p {\n" +
                    "      padding: 0;\n" +
                    "      margin: 5px;\n" +
                    "  }\n" +
                    ".table-box .row .label {\n" +
                    "           width: 155px;\n" +
                    "           padding-left: 10px;\n" +
                    "           background-color: #f5f5f5;\n" +
                    "           margin: 0;\n" +
                    "          color: #777777;\n" +
                    "          box-sizing: border-box;\n" +
                    "          font-size: 13px;\n" +
                    "           border-bottom: 1px solid #e6ebf1;\n" +
                    "           border-right: 1px solid #e6ebf1;\n" +
                    "        }\n" +

                    ".table-box .row:last-child .label {\n" +
                    "          border-bottom: 0;\n" +
                    "       }\n" +

                    ".table-box .row .label .star {\n" +
                    "           color: #FD5A60;\n" +
                    "       }\n" +

                    ".table-box .row p {\n" +
                    "padding: 0;\n" +
                    "margin: 5px;\n" +
                    "}\n" +

                    ".table-box .row .val {\n" +
                    "           width: 100%;\n" +
                    "           color: #1B1B1B;\n" +
                    "           font-size: 14px;\n" +
                    "           border-bottom: 1px solid #e6ebf1;\n" +
                    "            padding-left: 10px;\n" +
                    "       }\n" +

                    ".table-box .row:last-child .val {\n" +
                    "           border: none;\n" +
                    "       }\n" +

                    ".quotation-detail h3 {\n" +
                    "           font-weight: normal;\n" +
                    "           font-size: 18px;\n" +
                    "       }\n" +

                    ".quotation-detail .img-room {\n" +
                    "           display: flex;\n" +
                    "           align-items: center;\n" +
                    "           width: 180px;\n" +
                    "           height: 80px;\n" +
                    "           background-color: #fff;\n" +
                    "           box-shadow: 1px 1px 6px #ccc;\n" +
                    "           margin-right: 10px;\n" +
                    "       }\n" +

                    ".quotation-detail .img-room img {\n" +
                    "           display: inline-block;\n" +
                    "           width: 180px;\n" +
                    "           height: 80px;\n" +
                    "       }\n" +

                    "   .table-box {\n" +
                    "           margin-top: 10px;\n" +
                    "           border-spacing: 0;\n" +
                    "           border-collapse: collapse;\n" +
                    "           border-color: #e6ebf1;\n" +
                    "       }\n" +
                    ".quotation-detail .table-box {\n" +
                    "           margin-top: 10px;\n" +
                    "           border-spacing: 0;\n" +
                    "           border-collapse: collapse;\n" +
                    "          border-color: #e6ebf1;\n" +

                    "      }\n" +

                    ".quotation-detail .head {\n" +
                    "           margin-top: 20px;\n" +
                    "       }\n" +

                    " .table-box-price td {\n" +
                    "width: auto !important;\n" +
                    "}\n" +

                    ".table-box-price .td-name {\n" +
                    "   width: 120px !important;\n" +
                    "   color: #777;\n" +
                    "   background-color: #f5f5f5;\n" +
                    "}\n" +

                    ".table-box-price .td-name .star {\n" +
                    "color: #FD5A60;\n" +
                    "}\n" +

                    ".table-box th,\n" +
                    ".table-box td {\n" +
                    "          width: 75px;\n" +
                    "           height: 30px;\n" +
                    "           padding: 0 10px;\n" +
                    "           text-align: left;\n" +
                    "           background-color: #f5f5f5;\n" +
                    "           font-size: 13px;\n" +
                    "          font-weight: normal;\n" +
                    "          margin: 0;\n" +
                    "          border: 1px solid #e6ebf1;\n" +
                    "           color: #777777;\n" +
                    "       }\n" +

                    ".table-box th {\n" +
                    "           border: none;\n" +
                    "       }\n" +

                    ".table-box td {\n" +
                    "           background: #fff;\n" +
                    "           color: #1B1B1B;\n" +
                    "      }\n" +

                    ".w-150 {\n" +
                    "           width: 150px !important;\n" +
                    "       }\n" +

                    " .w-120 {\n" +
                    "          width: 120px !important;\n" +
                    "          }\n" +

                    ".w-160 {\n" +
                    "           width: 160px !important; \n" +
                    "        }\n" +

                    ".price-box p {\n" +
                    "           margin: 0;\n" +
                    "       }\n" +

                    ".price-box.up {\n" +
                    "           color: #FF6600;\n" +
                    "       }\n" +

                    ".price-box.down {\n" +
                    "           color: #02A460;\n" +
                    "      }\n" +

                    ".week-box {\n" +
                    "           display: flex;\n" +
                    "           align-items: center;\n" +
                    "       }\n" +

                    ".week-box p {\n" +
                    "          width: 20px;\n" +
                    "           height: 20px;\n" +
                    "           text-align: center;\n" +
                    "           color: #6A84A2;\n" +
                    "           border: 1px solid rgba(106, 132, 162, 1);\n" +
                    "          border-radius: 2px;\n" +
                    "          margin-right: 2px;\n" +
                    "       }\n" +

                    ".shrink-box .item {\n" +
                    "          display: flex;\n" +
                    "           align-items: center;\n" +
                    "           justify-content: space-between;\n" +
                    "       }\n" +

                    ".shrink-box .item p {\n" +
                    "           font-size: 14px;\n" +
                    "           flex: 1;\n" +
                    "      }\n" +

                    ".shrink-box .item img {\n" +
                    "          display: inline-block;\n" +
                    "           width: 13px;\n" +
                    "           height: 13px;\n" +
                    "       }\n" +

                    ".no-shrink {\n" +
                    "          color: #D9001B;\n" +
                    "       }\n" +
                    "</style>\n" +
                    "<footer style=\"margin-top: 3%\">\n" +
                    "<br/>\n" +
                    "<div style=\"margin-top: 20px;\">\n" +
                    "<strong>AMT Global RFP team</strong><br><br>\n" +
                    "<img src=\"https://grfp-prod-public.tos-cn-hongkong.volces.com/org/org/logo.png\" alt=\"AMT Logo\" width=\"260\"><br><br>\n" +
                    "<strong>Wuhan Tian Xia Fang Cang Technology Co., Ltd.（AMT）</strong><br>\n"+
                    "<strong>Email:</strong> <a href=\"mailto:<EMAIL>\"><EMAIL></a><br>\n" +
                    "<strong>Address:</strong> Floor 2, 1278 Heping Avenue, Qingshan District, Wuhan\n"+
                    "</div>\n"+
                    "</footer>\n" +
                    "</html>";
            String hotelName1 = sb.replace("hotelName", hotelBidDetailResponse.getHotelInfo().getHotelName());
            String projectName1 = hotelName1.replace("projectName", project.getProjectName());
            String projectTypeValue1 = projectName1.replace("projectTypeValue", GenericAppUtility.getText(LanguageEnum.EN_US.key, "PROJECT_TYPE_" + project.getProjectType()) );
            String projectStateValue1 = projectTypeValue1.replace("projectStateValue", GenericAppUtility.getText(LanguageEnum.EN_US.key, "PROJECT_STATE_" + project.getProjectState()) );
            String enrollStartTime1 = projectStateValue1.replace("enrollStartTime", com.fangcang.grfp.core.util.DateUtil.dateToString(project.getPriceMonitorStartDate()));
            String enrollEndTime1 = enrollStartTime1.replace("enrollEndTime", com.fangcang.grfp.core.util.DateUtil.dateToString(project.getPriceMonitorEndDate()));
            String diffMinAmount1 = enrollEndTime1.replace("diffMinAmount", project.getDiffMinAmount() == null ? "" : project.getDiffMinAmount().toString());
            String diffMaxAmount1 = diffMinAmount1.replace("diffMaxAmount", project.getDiffMaxAmount() == null ? "" : project.getDiffMaxAmount().toString());
            String introduction1 = diffMaxAmount1.replace("introduction", jodd.util.StringUtil.isEmpty(project.getIntroduction()) ? "" : project.getIntroduction());
            String bidStateValue1 = introduction1.replace("bidStateValue", GenericAppUtility.getText(LanguageEnum.EN_US.key, "BID_STATE_" + projectIntentHotel.getBidState()) );

            OrgEntity org = orgMapper.selectById(project.getTenderOrgId());
            String orgName1 = bidStateValue1.replace("orgName", org.getOrgName());
            String logoUrl1 = orgName1.replace("logoUrl", jodd.util.StringUtil.isEmpty(org.getLogoUrl()) ? "" : org.getLogoUrl());
            String baseApplicableDays1 = logoUrl1.replace("baseApplicableDays", baseApplicableDays);
            String season1ApplicableDays1 = baseApplicableDays1.replace("season1ApplicableDays", season1ApplicableDays);
            String season2ApplicableDays1 = season1ApplicableDays1.replace("season2ApplicableDays", season2ApplicableDays);
            String unApplicableDays1 = season2ApplicableDays1.replace("unApplicableDays", unApplicableDays);

            String bidContactName1 = unApplicableDays1.replace("bidContactName", jodd.util.StringUtil.isNotEmpty(contactName) ? contactName : "");
            String bidContactEmail1 = bidContactName1.replace("bidContactEmail", jodd.util.StringUtil.isNotEmpty(email) ? email : "");
            String bidContactMobile1 = bidContactEmail1.replace("bidContactMobile", jodd.util.StringUtil.isNotEmpty(mobile) ? mobile : "");

            List<AttachmentFileVO> projectEmailAttachments = null;
            try {
                projectEmailAttachments = cachedProjectService.queryProjectEmailAttachmentList(project.getProjectId());
                String subjectName = GenericAppUtility.getText(LanguageEnum.EN_US.key, "NOTIFY_EMAIL_SUBJECT");
                mailManager.sendNotifyMail(subjectName, email, bidContactMobile1, projectEmailAttachments);
                isNotify = true;
            } catch (Exception ex){
                log.error(ExceptionUtility.getDetailedExceptionString(ex));
            }
        }

        // 更新报价状态为已经通知
        if(isNotify) {
            ProjectIntentHotelEntity projectIntentHotelEntity = new ProjectIntentHotelEntity();
            projectIntentHotel.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            projectIntentHotel.setNotifyStatus(RfpConstant.constant_1);
            projectIntentHotel.setNotifyOperator("System");
            projectIntentHotel.setNotifyTime(new Date());
            projectIntentHotel.setModifier("System");
            projectIntentHotelMapper.updateNotify(projectIntentHotelEntity);
        }

        return ResultEnum.SUCCESS.value;
    }


    private String getHotelBidStrategyItemHtml(Integer isSupport, String name) {
        String hotelBidStrategyItem = "";
        String commitment = GenericAppUtility.getText(LanguageEnum.EN_US.key, "COMMITMENT");
        if(isSupport == null || isSupport == RfpConstant.constant_0){
            hotelBidStrategyItem = hotelBidStrategyItem + "<p class=\"no-shrink\">No Commitment: \n";
        } else {
            hotelBidStrategyItem = hotelBidStrategyItem + "<p><img\n" +
                    "                            src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAGKADAAQAAAABAAAAGAAAAADB/VeXAAACeklEQVRIDbVWv2sUURD+ZnfhQEhhe1gIB2IdGwvTWQQEo9iKomZNJ8QmbkwQwVOCYH9J5AqtNRECFsH/QAttVDgQUVsRG8W9W+d7b/fd7mX33Iv6itt5s9988+bH2znBuLWxdgiI55DglMJaSJKmgYt80WcPgh0g2Ea49KmKRkpfPFxrIo5vK8ElJfVLMZlSpK8H6CIIbuHKEh0X1l4H63dPA8ljJZ4qIP+0EfkOyHlcXX6Wh3r5DTp3riEZPJ2YnCQ8EG3JkVvDCHhyAoCi0xy4pjiAeGezSKwD5rwfv93Xycu8Ml1+cJQ1sadlQSfNeZ5YRPMuP52KXOTUJWArJvEHdTC+W5z1qCArWLjZxua9WfQHW1qMhkGwuyQ4rBGwz/dDLr801xcMORnno+f6Gxly/hjOeM5LL5HTW4HhypMRZW4r3+B5s1rIR07ZaU+rvOr2FPSCsgatgpK59L0zerJzSnJZk/ij8F5Eb60/gzB64fSWfFcZDzqdFVoaQXr9h2+iNFwgXO5C/BMazUfzWuQ1GslxLNx44+DV5ExT03aRQxthFdbIasPoJdA4pk7u48DUDC6ufHbwceQpSLDefqeejjgjI8hXfZzUNL0q6nO7GuQQec8IejmzVDS53C1EkgfVIbf4nmc/uXnrTK5wUp+ct2xHI9DvOS9F6RpxMhE5OYNt+y3qtDe05POlPoxSayK4rn39oKQVK8xkU2sY2i7isDDf8wos+ztJurXJyUVOXdaBmUQ6LIBBlYsJ9MqhXOl0sw5obSfRokp/44S2i9ksIO1w4HDH9V9HJh0wEg4LaJEqu4vAdBmMYmkzMo+J2BtBZsjnP/jb8hu7y+XfAiFXpgAAAABJRU5ErkJggg==\"\n" +
                    "                    alt=\"\">"+ commitment+": ";
        }
        hotelBidStrategyItem = hotelBidStrategyItem + name + "</p>";
        return hotelBidStrategyItem;
    }

    private String getHotelBidCustomItemHtml(BidCustomStrategyVO bidCustomStrategyVO) {
        String hotelBidStrategyItem = "";
        String commitment = GenericAppUtility.getText(LanguageEnum.EN_US.key, "COMMITMENT");
        if(bidCustomStrategyVO.getStrategyType() == CustomStrategyTypeEnum.YSE_OR_NO.key) {
            if (bidCustomStrategyVO.getSupportStrategyName() == null || bidCustomStrategyVO.getSupportStrategyName() == RfpConstant.constant_0) {
                hotelBidStrategyItem = hotelBidStrategyItem + "<p class=\"no-shrink\">No Commitment: \n";
            } else {
                hotelBidStrategyItem = hotelBidStrategyItem + "<p><img\n" +
                        "                            src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAGKADAAQAAAABAAAAGAAAAADB/VeXAAACeklEQVRIDbVWv2sUURD+ZnfhQEhhe1gIB2IdGwvTWQQEo9iKomZNJ8QmbkwQwVOCYH9J5AqtNRECFsH/QAttVDgQUVsRG8W9W+d7b/fd7mX33Iv6itt5s9988+bH2znBuLWxdgiI55DglMJaSJKmgYt80WcPgh0g2Ea49KmKRkpfPFxrIo5vK8ElJfVLMZlSpK8H6CIIbuHKEh0X1l4H63dPA8ljJZ4qIP+0EfkOyHlcXX6Wh3r5DTp3riEZPJ2YnCQ8EG3JkVvDCHhyAoCi0xy4pjiAeGezSKwD5rwfv93Xycu8Ml1+cJQ1sadlQSfNeZ5YRPMuP52KXOTUJWArJvEHdTC+W5z1qCArWLjZxua9WfQHW1qMhkGwuyQ4rBGwz/dDLr801xcMORnno+f6Gxly/hjOeM5LL5HTW4HhypMRZW4r3+B5s1rIR07ZaU+rvOr2FPSCsgatgpK59L0zerJzSnJZk/ij8F5Eb60/gzB64fSWfFcZDzqdFVoaQXr9h2+iNFwgXO5C/BMazUfzWuQ1GslxLNx44+DV5ExT03aRQxthFdbIasPoJdA4pk7u48DUDC6ufHbwceQpSLDefqeejjgjI8hXfZzUNL0q6nO7GuQQec8IejmzVDS53C1EkgfVIbf4nmc/uXnrTK5wUp+ct2xHI9DvOS9F6RpxMhE5OYNt+y3qtDe05POlPoxSayK4rn39oKQVK8xkU2sY2i7isDDf8wos+ztJurXJyUVOXdaBmUQ6LIBBlYsJ9MqhXOl0sw5obSfRokp/44S2i9ksIO1w4HDH9V9HJh0wEg4LaJEqu4vAdBmMYmkzMo+J2BtBZsjnP/jb8hu7y+XfAiFXpgAAAABJRU5ErkJggg==\"\n" +
                        "                    alt=\"\">" + commitment + ": ";
            }
            hotelBidStrategyItem = hotelBidStrategyItem + bidCustomStrategyVO.getStrategyName() + "</p>";
        } else if(bidCustomStrategyVO.getStrategyType() == CustomStrategyTypeEnum.TEXT.key) {
            hotelBidStrategyItem = hotelBidStrategyItem + "<p>" + bidCustomStrategyVO.getStrategyName() + ": " + bidCustomStrategyVO.getSupportStrategyText() + "</p>";
        } else if(bidCustomStrategyVO.getStrategyType() == CustomStrategyTypeEnum.RADIO.key || bidCustomStrategyVO.getStrategyType() == CustomStrategyTypeEnum.CHECKBOX.key) {
            hotelBidStrategyItem = hotelBidStrategyItem + "<p>" + bidCustomStrategyVO.getStrategyName() + ": ";
            String options = "";
            for(BidCustomStrategyVO.CustomStrategyBidOption option : bidCustomStrategyVO.getOptions()){
                if(option.getIsSupport() == RfpConstant.constant_1){
                    options = options + option.getOptionName() + ",";
                }
            }
            if(StringUtil.isValidString(options)){
                options = options.substring(0, options.length() -1);
            }
            hotelBidStrategyItem = hotelBidStrategyItem + options + "</p>";
        }
        return hotelBidStrategyItem;
    }

    public void isHotelSatisfyProjectBaseInfo(IntentHotelRequest intentHotelRequest) {
        /**
         1.  酒店集团/酒店报价都会调用 isHotelSatisfyProjectBaseInfo 接口
         2.   如果酒店是邀约酒店或者项目是邀约项目，检查通过，否则检查项目基础信息是否符合项目要求。
         3.   isHotelSatisfyProjectBaseInfo 返回成功
         如果项目是在线合同的获取合同确认页面，否则进入确认报价。
         4.   isHotelSatisfyProjectBaseInfo 返回失败，提示失败信息。
         */

        Map<Long, List<Long>> projectInviteHotelMap = new HashMap<>();
        for (String projectHotelIdKey : intentHotelRequest.getProjectIntentHotelParamList()) {
            // ProjectID
            Integer projectId = Integer.valueOf(projectHotelIdKey.substring(0, projectHotelIdKey.indexOf("_")));
            Long hotelId = Long.valueOf(projectHotelIdKey.substring(projectHotelIdKey.indexOf("_") + 1));

            // 判断时间是否在项目时间内（仅判断公共项目）
            ProjectEntity project = projectMapper.selectById(projectId);
            Date enrollEndTime = DateUtil.endOfDay(project.getPriceMonitorEndDate());
            Date now = new Date();
            if (now.compareTo(enrollEndTime) > 0) {
                GenericAppUtility.serviceError(ErrorCode.CANNOT_BID_DUE_TO_AFTER_BID_END_DATE_TIME);
            }

        }
    }

    private List<UploadFileVO> convertEmployeeRightUrl(List<UploadFileVO> urls){
        if(CollectionUtils.isNotEmpty(urls)){
            for(UploadFileVO fileVo : urls){
                if(fileVo.getFileUrl().contains("temp")){
                    ossManager.tempToPublic(fileVo.getFileKey());
                    String url = ossManager.generateUrlPublic(fileVo.getFileKey());
                    fileVo.setFileUrl(url);
                }
            }
        }
        return urls;
    }


}
