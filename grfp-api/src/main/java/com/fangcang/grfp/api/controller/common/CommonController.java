package com.fangcang.grfp.api.controller.common;


import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.common.service.CommonService;
import com.fangcang.grfp.api.controller.common.vo.*;
import com.fangcang.grfp.api.controller.org.service.OrgService;
import com.fangcang.grfp.api.controller.org.vo.QueryOrgByNameRequest;
import com.fangcang.grfp.api.controller.project.service.ProjectService;
import com.fangcang.grfp.api.controller.project.vo.QueryProjectByNameRequest;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.IdRequest;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.DestinationResponse;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.FindHotelListResponse;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.GetHotelSearchResponse;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.manager.BidMapManager;
import com.fangcang.grfp.core.usersession.Anon;
import com.fangcang.grfp.core.vo.*;
import com.fangcang.grfp.core.vo.request.common.QueryAttachmentInfoRequest;
import com.fangcang.grfp.core.vo.response.common.AttachmentInfoResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Api(tags="公共接口")
@RestController
@RequestMapping("/Api/Common")
public class CommonController extends BaseController {

	@Autowired
	private CommonService commonService;
	@Autowired
	private OrgService orgService;
	@Autowired
	private BidMapManager bidMapManager;

	@Resource
	private ProjectService projectService;

	// ------------------------------------------------------------------------------------------------- Public Method

	@Anon
	@PostMapping("/Test")
	public Result<String> test(HttpServletRequest request){
		return Result.ok("Test");
	}

	@Anon
	@ApiOperation("获取文字资源")
	@PostMapping("/GetTextResources")
	public Result<TextResourcesVO> getTextResources(HttpServletRequest request, HttpServletResponse response, @RequestBody TextResourcesRequest textResourcesRequest){
		TextResourcesVO textResourcesVO = commonService.getTextResources(AppUtility.getRequestHeaderLanguage(request), textResourcesRequest.getTextUpdatedTime());
		return Result.ok(textResourcesVO);
	}

	@Anon
	@ApiOperation("获取系统信息")
	@PostMapping("/AppInfo")
	public Result<AppInfoVO> appInfo(HttpServletRequest request, HttpServletResponse response){
		AppInfoVO appInfoVO = commonService.getAppInfo(request);
		return Result.ok(appInfoVO);
	}

	@Anon
	@ApiOperation("发送电邮验证码")
	@PostMapping("/SendSmsCode")
	public Result<Void> sendSmsCode(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody SendSmsCodeRequest sendSmsCodeRequest){
		commonService.sendSMSCode(sendSmsCodeRequest);
		return Result.ok();
	}

	@ApiOperation("根据名称查询国家")
	@PostMapping("/QueryCountryByName")
	public Result<List<CountryNameVO>> queryCountryByName(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody QueryByNameRequest queryByNameRequest){
		return Result.ok(commonService.queryCountryByName(AppUtility.getRequestHeaderLanguage(request), queryByNameRequest.getName()));
	}

	@ApiOperation("根据名称查询省份")
	@PostMapping("/QueryProvinceByName")
	public Result<List<ProvinceNameVO>> queryProvinceByName(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody QueryProvinceByNameRequest queryProvinceByNameRequest){
		return Result.ok(commonService.queryProvinceByName(AppUtility.getRequestHeaderLanguage(request), queryProvinceByNameRequest.getCountryCode(), queryProvinceByNameRequest.getName()));
	}

	@ApiOperation("根据名称查询城市")
	@PostMapping("/QueryCityByName")
	public Result<List<CityNameVO>> queryCityByName(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody QueryByNameRequest queryByNameRequest){
		int languageId = AppUtility.getRequestHeaderLanguage(request);
		LanguageEnum languageEnum = LanguageEnum.getEnumByKey(languageId);
		return Result.ok(commonService.queryCityByName(request, queryByNameRequest.getName(), languageEnum.value));
	}

	@ApiOperation("根据名称查询酒店集团")
	@PostMapping("/QueryHotelGroupByName")
	public Result<List<HotelGroupVO>> queryHotelGroupByName(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody QueryByNameRequest queryByNameRequest){
		return Result.ok(commonService.queryHotelGroupByName(AppUtility.getRequestHeaderLanguage(request), queryByNameRequest.getName()));
	}

	@ApiOperation("根据名称查询酒店品牌集团")
	@PostMapping("/QueryHotelBrandByName")
	public Result<List<HotelBrandVO>> queryHotelBrandByName(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody QueryHotelBrandByNameRequest queryHotelBrandByNameRequest){
		return Result.ok(commonService.queryHotelBrandByName(AppUtility.getRequestHeaderLanguage(request), queryHotelBrandByNameRequest.getHotelGroupId(), queryHotelBrandByNameRequest.getName()));
	}

	@ApiOperation("根据名称查找酒店")
	@PostMapping("/QueryHotelByName")
	@ResponseBody
	public Result<List<DestinationHotelVO>> queryHotelByName(HttpServletRequest request, HttpServletResponse response,
															 @Valid @RequestBody QueryHotelByNameRequest queryHotelByNameRequest) {
		int languageId = AppUtility.getRequestHeaderLanguage(request);
		LanguageEnum languageEnum = LanguageEnum.getEnumByKey(languageId);
		return Result.ok(commonService.queryHotelByName(request, queryHotelByNameRequest.getCityCode(), queryHotelByNameRequest.getName(), languageEnum.value));
	}


	@ApiOperation("文件上传 类型 1-机构logo 2-机构签约主体营业执照 3-正文合同模板(不含技术定位符)，供投标等预览时用 " +
			" 4-机构签约主体授权书  5-合同  6-合同正式模板 7-项目介绍 17-报价任务")
	@PostMapping("/UploadFile")
	public Result<UploadFileResultVO> uploadFile(MultipartFile uploadFile, Integer businessType) throws Exception {
		return Result.ok(commonService.uploadFile(uploadFile, businessType, null, true));
	}

	@ApiOperation("文件上传（上传公开通） 类型 20 项目电邮附件")
	@PostMapping("/UploadFileBindExternalId")
	public Result<UploadFileResultVO> uploadFileBindExternalId(MultipartFile uploadFile, Integer businessType, Long externalId) throws Exception {
		return Result.ok(commonService.uploadFile(uploadFile, businessType, externalId, false));
	}

	/**
	 * 删除文件
	 *
	 * @param deleteFileDtoRequest
	 * @return
	 */
	@ApiOperation("删除文件")
	@PostMapping(value = "/DeleteByFileId")
	@UserAuditLog("公共接口-删除文件")
	public Result<Void> deleteByFileId(@RequestBody DeleteFileDtoRequest deleteFileDtoRequest) {
		commonService.deleteAttachmentFile(deleteFileDtoRequest.getFileId());
		return Result.ok();
	}

	@ApiOperation("查询附件信息")
	@PostMapping(value = "/QueryAttachmentInfo")
	public Result<List<AttachmentInfoResponse>> queryAttachmentInfo(@RequestBody QueryAttachmentInfoRequest queryAttachmentInfoParam) {
		return Result.ok(commonService.queryAttachmentInfo(queryAttachmentInfoParam));
	}

	@ApiOperation("获取文件模板URL地址")
	@PostMapping("/GetTemplateFileUrl")
	public Result<String> getTemplateFileUrl(HttpServletRequest request, @Valid @RequestBody GetTemplateFileUrlRequest getTemplateFileUrlRequest) {
		return Result.ok(commonService.getDownlandTemplateFileUrl(getTemplateFileUrlRequest.getTemplateFileName()));
	}

	@ApiOperation("查询导入记录信息")
	@PostMapping("/QueryImportRecord")
	public Result<ImportRecordVO> queryImportStatus(HttpServletRequest request, @Valid @RequestBody IdRequest<Long> sysImportRecordId) {
		return Result.ok(commonService.queryImportRecord(sysImportRecordId.getId(), request));
	}

	@ApiOperation("根据机构名称查询机构")
	@PostMapping("/QueryOrgByName")
	@ResponseBody
	public Result<List<OrgNameVO>> queryOrgByName(HttpServletRequest request, HttpServletResponse response,
												  @Valid @RequestBody QueryOrgByNameRequest queryOrgByNameRequest) {

		return Result.ok(orgService.queryOrgByName(request, queryOrgByNameRequest.getOrgName(), queryOrgByNameRequest.getOrgTypeIdList(), queryOrgByNameRequest.getLimitCount()));
	}

	@ApiOperation("根据名称查询落地酒店")
	@PostMapping("/QueryLocalHotelByName")
	@ResponseBody
	public Result<List<HotelNameVO>> queryLocalHotelByName(HttpServletRequest request, HttpServletResponse response,
													 @Valid @RequestBody QueryByNameRequest queryByNameRequest) {
		return Result.ok(commonService.queryLocalHotelByName(AppUtility.getRequestHeaderLanguage(request), queryByNameRequest.getName()));
	}

	// ---------------------------------------------------------------------  目的查询 ---------

	@ApiOperation("目的地查询")
	@PostMapping("/SearchDestination")
	@ResponseBody
	public Result<DestinationResponse> searchDestination(HttpServletRequest request, HttpServletResponse response,
														 @Valid @RequestBody SearchDestinationRequest searchDestinationRequest) {
		return Result.ok(commonService.searchDestination(request, searchDestinationRequest));
	}

	@ApiOperation("通过目的地ID获取酒店过滤条件 （只返回城市和酒店）")
	@PostMapping("/GetHotelSearchByDestination")
	@ResponseBody
	public Result<GetHotelSearchResponse> getHotelSearchByDestination(HttpServletRequest request, HttpServletResponse response,
																	  @Valid @RequestBody GetHotelSearchByDestinationRequest getHotelSearchByDestinationRequest) {
		return Result.ok(commonService.getHotelSearchByDestination(request, getHotelSearchByDestinationRequest));
	}

	@ApiOperation("通过目的地条件查询酒店列表")
	@PostMapping("/FindHotelListByDestination")
	@ResponseBody
	public Result<FindHotelListResponse> findHotelListByDestination(HttpServletRequest request, HttpServletResponse response,
																	  @Valid @RequestBody FindHotelListByDestinationRequest findHotelListByDestinationRequest) {
		return Result.ok(commonService.findHotelListByDestination(request, findHotelListByDestinationRequest));
	}

	@ApiOperation("根据币种编号查询币种名称")
	@PostMapping("/QueryCurrencyByCode")
	@ResponseBody
	public Result<List<CurrencyNameVO>> queryCurrencyByCode(HttpServletRequest request, HttpServletResponse response,
																	@Valid @RequestBody QueryCurrencyByCodeRequest queryCurrencyByCodeRequest) {
		return Result.ok(commonService.queryCurrencyByCodeRequest(request, queryCurrencyByCodeRequest));
	}

	@ApiOperation("获取枚举类型数据")
	@PostMapping("/GetEnumTypes")
	@ResponseBody
	public Result<Map<String, List<EnumItemVO>>> getEnumTypes(HttpServletRequest request, HttpServletResponse response,
												   @Valid @RequestBody EnumRequest enumRequest) {
		int languageId = AppUtility.getRequestHeaderLanguage(request);
		return Result.ok(commonService.getEnumTypes(enumRequest.getEnumTypes(), languageId));
	}

	@ApiOperation("查询导出状态")
	@PostMapping("/QueryExportStatus")
	public Result<QueryExportStatusResponse> queryExportStatus(@RequestBody @Valid QueryExportStatusRequest request) {
		return Result.ok(commonService.queryExportStatus(request));
	}

	/**
	 * 测试生产电邮模板
	 */
	@RequestMapping(value = "hotelPrice/notifyBidderConsumer/{projectIntentHotelId}", method = RequestMethod.GET)
	public void notifyBidderConsumer(@PathVariable("projectIntentHotelId") Integer projectIntentHotelId) {
		bidMapManager.notifyBidderConsumer(projectIntentHotelId, "");
	}

	@ApiOperation("根据名称查找项目")
	@PostMapping("/QueryProjectByName")
	@ResponseBody
	public Result<List<ProjectNameVO>> queryProjectByName(@Valid @RequestBody QueryProjectByNameRequest request) {
		return Result.ok(projectService.queryProjectByName(request));
	}

	@ApiOperation("查询常用币种")
	@PostMapping("/QueryPopCurrency")
	@ResponseBody
	public Result<PopCurrencyVO> queryPopCurrency() {
		return Result.ok(commonService.queryPopCurrency());
	}
}
