package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 项目邀请酒店
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_invite_hotel")
public class ProjectInviteHotelEntity extends BaseVO {


    /**
     * 项目ID
     */
    @TableId(value = "project_id", type = IdType.ASSIGN_ID)
    private Integer projectId;

    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
