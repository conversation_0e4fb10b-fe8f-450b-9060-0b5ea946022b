package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import lombok.Data;

@Data
public class Charge {

    /**
     * 起始值
     */
    private String rangeFrom;

    /**
     * 结束值
     */
    private String rangeTo;

    /**
     * 收费类型（1-明确金额；2-占房费百分比；3-占成人均价百分比）
     */
    private String chargeManner;

    /**
     * 收费金额或比例
     */
    private Double chargeValue;

    /**
     * 币种
     */
    private String currency;

    /**
     * 收费频率（0-每晚；1-每次入住）
     */
    private String chargeFrequency;

    /**
     * 加床类型（2-加床；3-加婴儿床）
     */
    private String addBedType;

    /**
     * 金额
     */
    private String amount;

}
