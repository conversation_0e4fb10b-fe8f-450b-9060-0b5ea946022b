package com.fangcang.grfp.api.controller.hotelgroup.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * 修改酒店集团审核接口
 */
@ApiModel("修改酒店集团审核请求")
@Getter
@Setter
public class UpdateGroupApproveRequest extends BaseVO {

    // 酒店集团报价id
    @ApiModelProperty("酒店集团报价id")
    @NotNull
    private Integer projectIntentHotelGroupId;

    // 是否开启酒店集团审核
    @ApiModelProperty("是否开启酒店集团审核 1:是,0:否")
    @NotNull
    private Integer isOpenGroupApprove;

}
