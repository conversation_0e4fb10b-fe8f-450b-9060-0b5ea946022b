package com.fangcang.grfp.api.controller.poi.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fangcang.grfp.core.base.ImportVO;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class ImportPoiVO extends ImportVO {

    private static final long serialVersionUID = 6673184677160932559L;

    @ExcelProperty("Org Name")
    private String orgName;

    @ExcelProperty("City")
    private String cityName;

    @ExcelProperty("POI Name")
    private String poiName;

    @ExcelProperty("POI Address")
    private String poiAddress;

    @ExcelProperty("Google Longitude")
    private BigDecimal lngGoogle;

    @ExcelProperty("Google Latitude")
    private BigDecimal latGoogle;



}
