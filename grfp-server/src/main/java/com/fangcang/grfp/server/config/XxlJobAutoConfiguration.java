package com.fangcang.grfp.server.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 配置
 */
@Configuration(proxyBeanMethods = false)
public class XxlJobAutoConfiguration {

    @Value("${xxl-job.adminAddresses}")
    private String adminAddress;

    @Value("${xxl-job.executorAppName}")
    private String executorAppName;

    @Value("${xxl-job.executorLogPath}")
    private String executorLogPath;

    @Value("${xxl-job.logRetentionDays}")
    private int logRetentionDays;

    @Value("${xxl-job.accessToken:}")
    private String accessToken;

    @Bean
    @ConditionalOnMissingBean
    public XxlJobSpringExecutor xxlJobExecutor() {
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddress);
        xxlJobSpringExecutor.setAppName(executorAppName);
        xxlJobSpringExecutor.setLogPath(executorLogPath);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);

        if (StringUtils.isNotBlank(accessToken)) {
            xxlJobSpringExecutor.setAccessToken(accessToken);
        }
        return xxlJobSpringExecutor;
    }

}
