package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("获取文字资源请求")
@Getter
@Setter
public class TextResourcesRequest extends BaseVO {

	// ------------------------------------------------------------- Private Member Variable
	
	@ApiModelProperty("文字资源更新时间 yyyy-MM-dd HH:mm:ss")
	protected String textUpdatedTime;

}
