package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@ApiModel(description = "查询导出状态请求")
public class QueryExportStatusRequest extends BaseVO {

    @ApiModelProperty(value = "请求序列号")
    @NotBlank
    private String requestSequenceNo;

}
