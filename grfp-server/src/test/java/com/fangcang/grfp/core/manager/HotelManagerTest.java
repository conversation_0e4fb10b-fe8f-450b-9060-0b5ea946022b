package com.fangcang.grfp.core.manager;

import com.fangcang.grfp.core.entity.HotelEntity;
import com.fangcang.grfp.core.mapper.HotelMapper;
import com.fangcang.grfp.core.vo.response.hotel.RoomInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest(classes = com.fangcang.grfp.server.GRfpServerApplication.class)
@Slf4j
public class HotelManagerTest {

    @Resource
    private HotelManager hotelManager;
    @Resource
    private HotelMapper hotelMapper;

    @Test

    void testSyncHotelInfo() {
        Assertions.assertDoesNotThrow(() -> {
/**
            List<Long> hotelIdList = hotelMapper.selectHotelIdList().stream().map(HotelEntity::getHotelId).collect(Collectors.toList());
            hotelIdList.forEach(item -> {
                hotelManager.syncHotelInfo(Lists.newArrayList(item));
            });
   **/
            hotelManager.syncHotelInfo(Lists.newArrayList(901682L,363668L,1367588L,1499313L,158960L,274186L,1496201L,1744867L,552468L,
             404692L));


        });




    }

    @Test
    void testQueryRoomInfoListByHotelId() {
        List<RoomInfoVO> roomInfoVOList = hotelManager.queryRoomInfoListByHotelId(2, 110319L);
        Assertions.assertNotNull(roomInfoVOList);
    }

}
