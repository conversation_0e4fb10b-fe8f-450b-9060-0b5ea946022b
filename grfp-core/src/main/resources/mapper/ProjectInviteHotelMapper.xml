<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectInviteHotelMapper">

    <insert id="batchInsertOrUpdate">
        INSERT INTO t_project_invite_hotel (project_id, hotel_id, creator, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.projectId}, #{item.hotelId}, #{item.creator}, #{item.createTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        modifier = VALUES(creator),
        modify_time = NOW()
    </insert>
    <select id="queryProjectInviteHotelList"
            resultType="com.fangcang.grfp.core.vo.response.project.QueryHotelGroupInviteHotelVO">
        SELECT
        h.hotel_id AS hotelId,
        CASE WHEN #{language} = 1 THEN h.name_en_us ELSE h.name_zh_cn END AS hotelName,
        h.hotel_star AS hotelStar,
        CASE WHEN #{language} = 1 THEN h.address_en_us ELSE h.address_zh_cn END AS hotelAddress,
        h.telephone AS hotelPhone,
        CASE WHEN #{language} = 1 THEN c.name_en_us ELSE c.name_zh_cn END AS cityName,
        CASE WHEN #{language} = 1 THEN hg.name_en_us ELSE hg.name_zh_cn END AS hotelGroupName,
        CASE WHEN #{language} = 1 THEN hb.name_en_us ELSE hb.name_zh_cn END AS hotelBrandName,
        rh.bright_spot AS brightSpot,
        h.main_pic_url AS hotelImageUrl,
        pih.creator AS creator,
        pih.create_time AS createTime
        FROM t_project_invite_hotel pih
        LEFT JOIN t_hotel h ON pih.hotel_id = h.hotel_id
        LEFT JOIN t_hotel_group hg ON h.hotel_group_id = hg.hotel_group_id
        LEFT JOIN t_hotel_brand hb ON h.hotel_brand_id = hb.hotel_brand_id
        LEFT JOIN t_city c ON h.city_code = c.city_code AND h.country_code = c.country_code AND h.province_code = c.province_code
        LEFT JOIN t_recommend_hotel rh ON h.hotel_id = rh.hotel_id
        WHERE pih.project_id = #{request.projectId}
        <if test="request.hotelId != null">
            AND h.hotel_id = #{request.hotelId}
        </if>
        <if test="request.cityCode != null and request.cityCode != ''">
            AND h.city_code = #{request.cityCode}
        </if>
        <if test="request.hotelGroupId != null">
            AND h.hotel_group_id = #{request.hotelGroupId}
        </if>
        <if test="request.hotelBrandId != null">
            AND h.hotel_brand_id = #{request.hotelBrandId}
        </if>
        ORDER BY pih.create_time DESC
    </select>

    <delete id="deleteByProjectIdAndHotelIds">
        DELETE FROM t_project_invite_hotel 
        WHERE project_id = #{projectId} 
        AND hotel_id IN 
        <foreach collection="hotelIds" item="hotelId" open="(" separator="," close=")">
            #{hotelId}
        </foreach>
    </delete>
</mapper>
