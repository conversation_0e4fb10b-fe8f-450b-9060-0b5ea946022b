package com.fangcang.grfp.core.remote.tmchub.manager;

import cn.hutool.core.date.DatePattern;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fangcang.grfp.core.cached.CachedSysConfigService;
import com.fangcang.grfp.core.constant.SysConfig;
import com.fangcang.grfp.core.constant.TmcHubRequestTypeConstant;
import com.fangcang.grfp.core.dto.dhub.request.Header;
import com.fangcang.grfp.core.dto.dhub.request.Request;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.DestinationRequest;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.FindHotelListRequest;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.GetHotelSearchRequest;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.HotelIdListRequest;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.HotelImageRequest;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.HotelIncrementRequest;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.HotelInfoRequest;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.QueryCityListRequest;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.QueryCountryListRequest;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.QueryGroupBrandListRequest;
import com.fangcang.grfp.core.dto.dhub.request.product.HotelLowestPriceRequest;
import com.fangcang.grfp.core.dto.dhub.response.Response;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.DestinationResponse;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.FindHotelListResponse;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.GetHotelSearchResponse;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.HotelIdListResponse;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.HotelImageResponse;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.HotelInfoResponse;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.IncrementResponse;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.QueryCityListResponse;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.QueryCountryListResponse;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.QueryGroupBrandListResponse;
import com.fangcang.grfp.core.dto.dhub.response.product.HotelLowestPriceResponse;
import com.fangcang.grfp.core.remote.tmchub.request.SignatureHelpRequestDto;
import com.fangcang.grfp.core.util.GenericAppUtility;
import com.fangcang.grfp.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateLimiterConfig;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 调用TMC Hub 接口
 */
@Component
@Slf4j
public class TmcHubApiManager {


    @Autowired
    private CachedSysConfigService cachedSysConfigService;

    @Resource
    private RedissonClient redissonClient;

    @Value("${tmcHubApi.RateLimiter:1}")
    private int commonRateLimiter;
    /**
     * Query country list from Dhub
     */
    public Response<QueryCountryListResponse> queryCountryList(QueryCountryListRequest request, SignatureHelpRequestDto signatureParam) {
        signatureParam = ObjectUtils.defaultIfNull(signatureParam, defaultSignatureParam());
        signatureParam.setRequestType(TmcHubRequestTypeConstant.QUERY_COUNTRY_LIST);
        return this.executeApiRequest(request, QueryCountryListResponse.class, signatureParam);
    }

    /**
     * Query city list
     */
    public Response<QueryCityListResponse> queryCityList(QueryCityListRequest request, SignatureHelpRequestDto signatureParam) {
        signatureParam = ObjectUtils.defaultIfNull(signatureParam, defaultSignatureParam());
        signatureParam.setRequestType(TmcHubRequestTypeConstant.QUERY_CITY_LIST);
        return this.executeApiRequest(request, QueryCityListResponse.class, signatureParam);
    }

    /**
     * Query group brand list
     */
    public Response<QueryGroupBrandListResponse> queryGroupBrandList(QueryGroupBrandListRequest request, SignatureHelpRequestDto signatureParam) {
        signatureParam = ObjectUtils.defaultIfNull(signatureParam, defaultSignatureParam());
        signatureParam.setRequestType(TmcHubRequestTypeConstant.QUERY_GROUP_BRAND_LIST);
        return this.executeApiRequest(request, QueryGroupBrandListResponse.class, signatureParam);
    }

    /**
     * Query hotel info
     */
    public Response<HotelInfoResponse> queryHotelInfo(HotelInfoRequest request, SignatureHelpRequestDto signatureParam) {
        signatureParam = ObjectUtils.defaultIfNull(signatureParam, defaultSignatureParam());
        signatureParam.setRequestType(TmcHubRequestTypeConstant.QUERY_HOTEL_INFO);
        return this.executeApiRequest(request, HotelInfoResponse.class, signatureParam);
    }

    /**
     * Query hotel increment
     */
    public Response<IncrementResponse> queryHotelIncrement(HotelIncrementRequest request, SignatureHelpRequestDto signatureParam) {
        signatureParam = ObjectUtils.defaultIfNull(signatureParam, defaultSignatureParam());
        signatureParam.setRequestType(TmcHubRequestTypeConstant.QUERY_HOTEL_INCREMENT);
        return this.executeApiRequest(request, IncrementResponse.class, signatureParam);
    }

    /**
     * Get destination
     */
    public Response<DestinationResponse> getDestination(DestinationRequest request, SignatureHelpRequestDto signatureParam) {
        signatureParam = ObjectUtils.defaultIfNull(signatureParam, defaultSignatureParam());
        signatureParam.setRequestType(TmcHubRequestTypeConstant.GET_DESTINATION);
        return this.executeApiRequest(request, DestinationResponse.class, signatureParam);
    }

    /**
     * Query hotel list
     */
    public Response<HotelIdListResponse> queryHotelIdList(HotelIdListRequest request, SignatureHelpRequestDto signatureParam) {
        signatureParam = ObjectUtils.defaultIfNull(signatureParam, defaultSignatureParam());
        signatureParam.setRequestType(TmcHubRequestTypeConstant.QUERY_HOTEL_ID_LIST);
        return this.executeApiRequest(request, HotelIdListResponse.class, signatureParam);
    }

    /**
     * Query hotel image
     */
    public Response<HotelImageResponse> queryHotelImage(HotelImageRequest request, SignatureHelpRequestDto signatureParam) {
        signatureParam = ObjectUtils.defaultIfNull(signatureParam, defaultSignatureParam());
        signatureParam.setRequestType(TmcHubRequestTypeConstant.QUERY_HOTEL_IMAGE);
        return this.executeApiRequest(request, HotelImageResponse.class, signatureParam);
    }

    /**
     *  获取酒店过滤条件
     *  getHotelSearch
     */
    public Response<GetHotelSearchResponse> getHotelSearch(GetHotelSearchRequest request, SignatureHelpRequestDto signatureParam) {
        signatureParam = ObjectUtils.defaultIfNull(signatureParam, defaultSignatureParam());
        signatureParam.setRequestType(TmcHubRequestTypeConstant.GET_HOTEL_SEARCH);
        return this.executeApiRequest(request, GetHotelSearchResponse.class, signatureParam);
    }

    /**
     * 根据目的地查询酒店列表
     */
    public Response<FindHotelListResponse> findHotelList(FindHotelListRequest request, SignatureHelpRequestDto signatureParam) {
        signatureParam = ObjectUtils.defaultIfNull(signatureParam, defaultSignatureParam());
        signatureParam.setRequestType(TmcHubRequestTypeConstant.FIND_HOTEL_LIST);
        return this.executeApiRequest(request, FindHotelListResponse.class, signatureParam);
    }

    /**
     * 根据目的地查询酒店列表
     */
    public Response<HotelLowestPriceResponse> queryHotelLowestPrice(HotelLowestPriceRequest request, SignatureHelpRequestDto signatureParam) {
        signatureParam = ObjectUtils.defaultIfNull(signatureParam, defaultSignatureParam());
        signatureParam.setRequestType(TmcHubRequestTypeConstant.QUERY_HOTEL_LOWEST_PRICE);
        return this.executeApiRequest(request, HotelLowestPriceResponse.class, signatureParam);
    }


    /**
     * Generic execute method
     */
    private <T, R> Response<R> executeApiRequest(T request, Class<R> responseClass, SignatureHelpRequestDto signatureParam) {
        // 限流
        RRateLimiter rateLimiter = getRateLimiter(signatureParam.getRequestType(), commonRateLimiter);
        rateLimiter.acquire();

        // Build base api request
        Request<T> baseApiRequest = buildRequest(request, signatureParam);
        String url = cachedSysConfigService.getValue(SysConfig.TMC_HUB_API_URL) + "/" + signatureParam.getRequestType();
        // Request dhub
        try (HttpResponse httpResponse = sendPostRequest(url, baseApiRequest)) {
            log.info("DHUB API Response: {}, Request Body: {}", signatureParam.getRequestType(), baseApiRequest);
            if(TmcHubRequestTypeConstant.QUERY_HOTEL_INCREMENT.equals(signatureParam.getRequestType()) ||
                    TmcHubRequestTypeConstant.GET_DESTINATION.equals(signatureParam.getRequestType())
            ){
                log.info("DHUB API Request: {}, Body: {}, Response: {}", signatureParam.getRequestType(), baseApiRequest, httpResponse.body());
            }
            //log.warn("DHUB API Request: {}, Body: {}, Response: {}", signatureParam.getRequestType(), baseApiRequest, httpResponse.body());

            // Handle failed responses
            if (!httpResponse.isOk()) {
                log.error("Failed to call DHUB API: {}, Status code: {}", signatureParam.getRequestType(), httpResponse.getStatus());
                return new Response<>();
            }

            // Deserialization response
            return JsonUtil.fromJson(httpResponse.body(), Response.class, responseClass);
        } catch (Throwable e) {
            log.error("Exception occurred while calling DHUB API: {}, Body: {}", signatureParam.getRequestType(), baseApiRequest, e);
            return new Response<>();
        }
    }

    /**
     * Send POST request with JSON payload
     */
    private HttpResponse sendPostRequest(String url, Request<?> body) {
        return HttpRequest.post(url)
            .header("Content-Type", "application/json")
            .body(JsonUtil.objectToJsonDateSerializer(body, DatePattern.NORM_DATETIME_PATTERN))
            .execute();
    }

    /**
     * Build base api request
     */
    private <T> Request<T> buildRequest(T request, SignatureHelpRequestDto signatureParam) {
        Request<T> baseApiRequest = new Request<>();
        baseApiRequest.setHeader(buildHeader(signatureParam));
        baseApiRequest.setBusinessRequest(request);
        return baseApiRequest;
    }

    /**
     * Build common headers
     */
    private Header buildHeader(SignatureHelpRequestDto signatureParam) {
        long timestamp = System.currentTimeMillis();
        Header header = new Header();
        header.setPartnerCode(signatureParam.getPartnerCode());
        header.setTimestamp(timestamp);
        header.setRequestType(signatureParam.getRequestType());
        header.setSignature(generateSignature(timestamp, signatureParam));
        header.setVersion("1.0.0");
        return header;
    }

    /**
     * Generate signature
     * Rule: UPPER(MD5(timestamp + partnerCode + UPPER(MD5(secureKey)) + requestType))
     */
    private String generateSignature(long timestamp, SignatureHelpRequestDto signatureParam) {
        // MD5(secureKey)
        String secureKeyHash = DigestUtils.md5DigestAsHex(signatureParam.getSecurityKey().getBytes(StandardCharsets.UTF_8)).toUpperCase();
        // timestamp + partnerCode + secureKeyHash + requestType
        String signatureBase = timestamp + signatureParam.getPartnerCode() + secureKeyHash + signatureParam.getRequestType();
        // MD5(timestamp + partnerCode + secureKeyHash + requestType)
        return DigestUtils.md5DigestAsHex(signatureBase.getBytes(StandardCharsets.UTF_8)).toUpperCase();
    }

    /**
     * 构建限流器
     * @param requestPerSecond 每秒请求数
     */
    public RRateLimiter getRateLimiter(String key, long requestPerSecond) {
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);

        // 未设置过限流, getConfig 会报错
        rateLimiter.trySetRate(RateType.OVERALL, requestPerSecond, 1, RateIntervalUnit.SECONDS);

        RateLimiterConfig config = rateLimiter.getConfig();
        if (config.getRate() != requestPerSecond) {
            log.info("限流配置变更, 刷新配置. key: {}, rate: {}", key, requestPerSecond);
            rateLimiter.setRate(RateType.OVERALL, requestPerSecond, 1, RateIntervalUnit.SECONDS);
        }

        return rateLimiter;
    }

    /**
     * 默认签名参数
     */
    private SignatureHelpRequestDto defaultSignatureParam() {
        SignatureHelpRequestDto signatureParam = new SignatureHelpRequestDto();
        signatureParam.setPartnerCode(GenericAppUtility.getSysInfo(SysConfig.TMC_HUB_PARTNER_CODE));
        signatureParam.setSecurityKey(GenericAppUtility.getSysInfo(SysConfig.TMC_HUB_SECURITY_KEY));
        return signatureParam;
    }

}
