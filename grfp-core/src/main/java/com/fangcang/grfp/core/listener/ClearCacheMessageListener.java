package com.fangcang.grfp.core.listener;

import com.fangcang.grfp.core.cached.CachedCityService;
import com.fangcang.grfp.core.cached.CachedHotelBrandService;
import com.fangcang.grfp.core.cached.CachedHotelGroupService;
import com.fangcang.grfp.core.cached.CachedHotelService;
import com.fangcang.grfp.core.constant.CacheDateType;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.util.JsonUtil;
import com.fangcang.grfp.core.vo.ClearCacheMessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

/**
 * Redis 监听消息清空缓存
 */
@Component
@Slf4j
public class ClearCacheMessageListener implements MessageListener {

    @Autowired
    private CachedHotelBrandService cachedHotelBrandService;
    @Autowired
    private CachedHotelGroupService cachedHotelGroupService;
    @Autowired
    private CachedHotelService cachedHotelService;
    @Autowired
    private CachedCityService cachedCityService;

    @Override
    public void onMessage(Message message, byte[] bytes) {
        String msg = new String(message.getBody());
        log.info("receive message:{}", msg);
        ClearCacheMessageVO clearCacheMessageVO = JsonUtil.jsonToBean(msg, ClearCacheMessageVO.class);
        if(clearCacheMessageVO.getDataType() == CacheDateType.HOTEL_BRAND) {
            log.info("clear HOTEL_BRAND cache");
            cachedHotelBrandService.clearNameMap(LanguageEnum.EN_US.key);
            cachedHotelBrandService.clearNameMap(LanguageEnum.ZH_CN.key);
        }
        if(clearCacheMessageVO.getDataType() == CacheDateType.HOTELGROUP) {
            log.info("clear HOTELGROUP cache");
            cachedHotelGroupService.clearNameMap(LanguageEnum.EN_US.key);
            cachedHotelGroupService.clearNameMap(LanguageEnum.ZH_CN.key);
        }
        if(clearCacheMessageVO.getDataType() == CacheDateType.HOTEL) {
            log.info("clear HOTEL cache");
            cachedHotelService.clearNameMap(LanguageEnum.EN_US.key);
            cachedHotelService.clearNameMap(LanguageEnum.ZH_CN.key);
        }
        if(clearCacheMessageVO.getDataType() == CacheDateType.CITY) {
            log.info("clear CITY cache");
            cachedCityService.clearNameMap(LanguageEnum.EN_US.key);
            cachedCityService.clearNameMap(LanguageEnum.ZH_CN.key);
        }
    }
}
