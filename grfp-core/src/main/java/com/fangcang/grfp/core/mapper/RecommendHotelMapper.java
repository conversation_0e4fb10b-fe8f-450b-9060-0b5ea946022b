package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.entity.RecommendHotelEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 酒店推荐表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
public interface RecommendHotelMapper extends BaseMapper<RecommendHotelEntity> {

    default RecommendHotelEntity selectRecommendHotelByHotelId(Long hotelId, int state) {
        LambdaQueryWrapper<RecommendHotelEntity>lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(RecommendHotelEntity::getHotelId, hotelId);
        lambdaQueryWrapper.eq(RecommendHotelEntity::getState, state);
        return selectOne(lambdaQueryWrapper);
    }

    List<RecommendHotelEntity> selectRecommendHotelByHotelIds(@Param("hotelIds") List<Long> hotelIds);
}
