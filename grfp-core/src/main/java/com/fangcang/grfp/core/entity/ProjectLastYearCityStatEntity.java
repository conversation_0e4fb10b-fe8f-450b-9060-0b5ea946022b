package com.fangcang.grfp.core.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 项目去年城市统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_last_year_city_stat")
public class ProjectLastYearCityStatEntity extends BaseVO {


    /**
     * 项目ID
     */
    @TableId(value = "project_id", type = IdType.ASSIGN_ID)
    private Integer projectId;

    /**
     * 城市编号
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 总间夜数
     */
    @TableField("total_room_night")
    private Integer totalRoomNight;

    /**
     * 总销售金额
     */
    @TableField("total_sales_amount")
    private BigDecimal totalSalesAmount;

    /**
     * 总间夜数
     */
    @TableField("total_room_night_500")
    private Integer totalRoomNight500;

    /**
     * 400-500 间夜
     */
    @TableField("total_room_night_400_to_500")
    private Integer totalRoomNight400To500;

    /**
     * 300-400 间夜
     */
    @TableField("total_room_night_300_to_400")
    private Integer totalRoomNight300To400;

    /**
     * 200-300 间夜
     */
    @TableField("total_room_night_200_to_300")
    private Integer totalRoomNight200To300;

    /**
     * 100-200 间夜
     */
    @TableField("total_room_night_100_to_200")
    private Integer totalRoomNight100To200;

    /**
     * 100以内间夜
     */
    @TableField("total_room_night_100")
    private Integer totalRoomNight100;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
