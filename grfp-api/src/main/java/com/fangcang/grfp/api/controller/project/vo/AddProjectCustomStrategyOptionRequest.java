package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(description = "项目自定义策略选项请求")
public class AddProjectCustomStrategyOptionRequest extends BaseVO {

    @ApiModelProperty(value = "选型文本", required = true)
    @NotBlank
    private String optionName;

}
