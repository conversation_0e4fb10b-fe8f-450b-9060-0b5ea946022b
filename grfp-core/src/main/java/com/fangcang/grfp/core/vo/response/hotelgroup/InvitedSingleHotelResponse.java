package com.fangcang.grfp.core.vo.response.hotelgroup;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("被邀请单体酒店信息")
@Getter
@Setter
public class InvitedSingleHotelResponse {

    @ApiModelProperty("项目ID")
    private Integer projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("酒店ID")
    private Long hotelId;

    @ApiModelProperty("酒店名称")
    private String hotelName;

    @ApiModelProperty("城市")
    private String cityName;

    @ApiModelProperty("签约类型")
    private String tenderTypeDesc;

    @ApiModelProperty("项目发布机构名称")
    private String publishOrgName;

    // 内部字段，用于数据处理
    @ApiModelProperty(hidden = true)
    private String cityCode;

    @ApiModelProperty(hidden = true)
    private String hotelNameEnUs;

    @ApiModelProperty(hidden = true)
    private String hotelNameZhCn;

    @ApiModelProperty(hidden = true)
    private String cityNameEnUs;

    @ApiModelProperty(hidden = true)
    private String cityNameZhCn;

    @ApiModelProperty(hidden = true)
    private Integer tenderType;
} 