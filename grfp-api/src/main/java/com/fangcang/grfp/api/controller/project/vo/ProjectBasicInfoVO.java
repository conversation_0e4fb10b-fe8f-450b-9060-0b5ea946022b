package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel("项目基本信息")
@Getter
@Setter
public class ProjectBasicInfoVO extends BaseVO {

    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    private Integer projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
     * 项目类型(1：酒店)，跟合同模板业务类型保持一致
     */
    @ApiModelProperty("项目类型(1：酒店)，跟合同模板业务类型保持一致")
    private Integer projectType;

    /**
     * 项目状态(0：未启动，1：招标中(已启动)，2：招标完成，3：已废标)
     */
    @ApiModelProperty("项目状态(0：未启动，1：招标中(已启动)，2：招标完成，3：已废标)")
    private Integer projectState;

    /**
     * 招标机构id
     */
    @ApiModelProperty( "招标机构id")
    private Integer tenderOrgId;

    /**
     * 招标机构名称
     */
    @ApiModelProperty( "招标机构名称")
    private String tenderOrgName;

    /**
     * 招标方式(1-公开招标，2-邀请招标)
     */
    @ApiModelProperty( "招标方式(1-公开招标，2-邀请招标)")
    private Integer tenderType;

    /**
     * 招标方项目联系人
     */
    @ApiModelProperty( "招标方项目联系人")
    private String contactName;

    /**
     * 招标方项目人手机号码
     */
    @ApiModelProperty( "招标方项目人手机号码")
    private String contactMobile;

    /**
     * 报价开始时间
     */
    @ApiModelProperty( "报价开始时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern =  "yyyy-MM-dd")
    private Date bidStartTime;

    /**
     * 报价结束时间
     */
    @ApiModelProperty( "报价结束时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern =  "yyyy-MM-dd")
    private Date bidEndTime;

    /**
     * 第一轮报价开始时间
     */
    @ApiModelProperty( "第一轮报价开始时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern =  "yyyy-MM-dd")
    private Date firstBidStartTime;

    /**
     * 第一轮报价结束时间
     */
    @ApiModelProperty( "第一轮报价结束时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern =  "yyyy-MM-dd")
    private Date firstBidEndTime;

    /**
     * 第二轮报价开始时间
     */
    @ApiModelProperty( "第二轮报价开始时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern =  "yyyy-MM-dd")
    private Date secondBidStartTime;

    /**
     * 第二轮报价结束时间
     */
    @ApiModelProperty( "第二轮报价结束时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern =  "yyyy-MM-dd")
    private Date secondBidEndTime;

    /**
     * 第三轮报价开始时间
     */
    @ApiModelProperty( "第三轮报价开始时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern =  "yyyy-MM-dd")
    private Date thirdBidStartTime;

    /**
     * 第三轮报价结束时间
     */
    @ApiModelProperty( "第三轮报价结束时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern =  "yyyy-MM-dd")
    private Date thirdBidEndTime;

    /**
     * 预估采购数量，项目类型为酒店时即为酒店数量
     */
    @ApiModelProperty( "预估采购数量")
    private Integer tenderCount;

    /**
     * 协议报价开始日期
     */
    @ApiModelProperty( "协议报价开始日期")
    @JsonFormat(pattern =  "yyyy-MM-dd")
    private Date priceMonitorStartDate;

    /**
     * 协议报价结束日期
     */
    @ApiModelProperty( "协议报价结束日期")
    @JsonFormat(pattern =  "yyyy-MM-dd")
    private Date priceMonitorEndDate;

    /**
     * 报价状态更新通知方式(0-手工，1-自动)
     */
    @ApiModelProperty( "报价状态更新通知方式(0-手工，1-自动)")
    private Integer bidStateUpdatedNotifyMode;


    /**
     * 排序(大到小)
     */
    @ApiModelProperty( "排序(大到小)")
    private Integer displayOrder;


    @ApiModelProperty( "项目介绍")
    private String introduction;

    /**
     * 创建人
     */
    @ApiModelProperty( "创建人")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty( "创建时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern =  "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty( "修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @ApiModelProperty( "修改时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern =  "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

}
