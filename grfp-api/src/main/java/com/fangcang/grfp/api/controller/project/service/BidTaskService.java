package com.fangcang.grfp.api.controller.project.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.api.controller.project.vo.*;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.base.ImportRowErrorVO;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.dto.excel.ExportExcelContext;
import com.fangcang.grfp.core.entity.HotelEntity;
import com.fangcang.grfp.core.entity.ImportBidTaskEntity;
import com.fangcang.grfp.core.entity.OrgEntity;
import com.fangcang.grfp.core.entity.ProjectEntity;
import com.fangcang.grfp.core.entity.ProjectIntentHotelEntity;
import com.fangcang.grfp.core.enums.GeneratedBidStatusEnum;
import com.fangcang.grfp.core.enums.OrgTypeEnum;
import com.fangcang.grfp.core.enums.YesOrNoEnum;
import com.fangcang.grfp.core.manager.BidManager;
import com.fangcang.grfp.core.manager.ExcelManager;
import com.fangcang.grfp.core.manager.HotelManager;
import com.fangcang.grfp.core.mapper.ImportBidTaskMapper;
import com.fangcang.grfp.core.mapper.OrgMapper;
import com.fangcang.grfp.core.mapper.ProjectIntentHotelMapper;
import com.fangcang.grfp.core.mapper.ProjectMapper;
import com.fangcang.grfp.core.mapper.TextResourceMapper;
import com.fangcang.grfp.core.oss.OssManager;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.JsonUtil;
import com.fangcang.grfp.core.vo.ImportBidTaskListVO;
import com.fangcang.grfp.core.vo.ImportStandardBidVO;
import com.fangcang.grfp.core.vo.request.bid.CreateBidRequest;
import com.fangcang.grfp.core.vo.request.bid.ImportBidTaskListRequest;
import com.fangcang.grfp.core.vo.request.bid.ValidateBidContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BidTaskService {

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private OssManager ossManager;

    @Resource
    private ImportBidTaskMapper importBidTaskMapper;

    @Resource
    private BidManager bidManager;

    @Resource
    private HttpServletRequest httpServletRequest;

    @Resource
    private OrgMapper orgMapper;

    @Resource
    private ExcelManager excelManager;

    @Resource
    private HotelManager hotelManager;

    @Resource
    private ProjectIntentHotelMapper projectIntentHotelMapper;

    /**
     * 添加导入报价任务
     */
    public void addImportBidTask(AddImportBidTaskRequest request) {
        UserSession userSession = UserSession.get();
        // 获取语言
        int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);

        // 校验项目是否存在
        ProjectEntity project = projectMapper.selectById(request.getProjectId());
        if (Objects.isNull(project)) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        // 迁移桶
        ossManager.tempToPublic(request.getFileKey());
        InputStream inputStream = ossManager.getObjectPublic(request.getFileKey());

        // 构建预期表头
        Map<Integer, List<String>> expectedHeaderMap = ExcelManager.buildExpectedHeaders(ImportStandardBidVO.class, 1);

        // 读取文件
        List<ImportStandardBidVO> importList = new ArrayList<>();
        EasyExcel.read(inputStream, ImportStandardBidVO.class, new AnalysisEventListener<ImportStandardBidVO>() {

            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {

                Integer rowIndex = context.readRowHolder().getRowIndex();
                // 校验表头
                List<String> actualHeaders = new ArrayList<>(headMap.values());
                List<String> expectedHeaders = expectedHeaderMap.get(rowIndex);
                if (Objects.nonNull(expectedHeaders) && !expectedHeaders.equals(actualHeaders)) {
                    log.error("表头不匹配！预期: {} 实际: {}", expectedHeaders, actualHeaders);
                    AppUtility.serviceError(ErrorCode.IMPORT_EXCEL_TEMPLATE_NOT_MATCH);
                }
            }

            @Override
            public void invoke(ImportStandardBidVO data, AnalysisContext context) {
                data.setRowNum(context.readRowHolder().getRowIndex() + 1);
                importList.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {

            }

        }).sheet().headRowNumber(2).doRead();

        // 校验导入的数据是否为空
        if (CollUtil.isEmpty(importList)) {
            AppUtility.serviceError(ErrorCode.IMPORT_DATA_CANNOT_BE_EMPTY);
        }
        // 最多不能超过 1w
        if (importList.size() > 10000) {
            AppUtility.serviceError(ErrorCode.IMPORT_DATA_EXCEED_LIMIT);
        }

        // 查询项目已经导入的未生成报价任务
        List<ImportBidTaskEntity> waitingGenerateList = importBidTaskMapper.selectByProjectIdAndGenerateBidStatus(request.getProjectId(), GeneratedBidStatusEnum.WAITING_GENERATE.getKey());
        // 项目+集团酒店编码   与  当前未导入任务重复 则修改当前未导入任务
        Map<String, ImportBidTaskEntity> propCodeMap = waitingGenerateList.stream()
            .filter(o -> StringUtils.isNotBlank(o.getPropCode())).collect(Collectors.toMap(ImportBidTaskEntity::getPropCode, Function.identity()));
        // 项目+amadeus chain code+amadeus hotel code   与当前未导入任务重复 则修改当前未导入任务
        Map<String, ImportBidTaskEntity> amadusCodeMap = waitingGenerateList.stream()
            .filter(o -> StringUtils.isNotBlank(o.getAmadeusChainCode()) || StringUtils.isNotBlank(o.getAmadeusHotelCode()))
            .collect(Collectors.toMap(o-> String.format("%s:%s", o.getAmadeusChainCode(), o.getAmadeusHotelCode()), Function.identity()));
        // 项目+酒店名称 （客户或酒店集团提供的酒店名 不是匹配到房仓的酒店名称） 与当前未导入任务重复 则修改当前未导入任务
        Map<String, ImportBidTaskEntity> propNameMap = waitingGenerateList.stream().filter(o -> StringUtils.isNotBlank(o.getPropName()))
            .collect(Collectors.toMap(ImportBidTaskEntity::getPropName, Function.identity()));

        // 去重集合
        Set<String> importKeySet = new HashSet<>();
        // 生成批次 id
        String batchId = IdUtil.fastSimpleUUID();

        importList.forEach(importBidTask -> {
            // 设置项目 ID
            importBidTask.setProjectId(project.getProjectId());

            // 去重
            if(StringUtils.isBlank(importBidTask.getPropCode()) && StringUtils.isBlank(importBidTask.getAmadeusChainCode()) &&
                StringUtils.isBlank(importBidTask.getAmadeusHotelCode()) && StringUtils.isBlank(importBidTask.getPropName())) {
                return;
            }

            // 检查是否重复
            String key = importBidTask.getPropCode() + "_" + importBidTask.getAmadeusChainCode() + "_" + importBidTask.getAmadeusHotelCode() + "_" + importBidTask.getPropName();
            if(importKeySet.contains(key)){
                return;
            }

            // 初始化
            ImportBidTaskEntity bidTask = new ImportBidTaskEntity();
            bidTask.setFileKey(request.getFileKey());
            bidTask.setFileName(request.getFileName());
            bidTask.setBatchId(batchId);
            bidTask.setPropCode(importBidTask.getPropCode());
            bidTask.setAmadeusChainCode(importBidTask.getAmadeusChainCode());
            bidTask.setAmadeusHotelCode(importBidTask.getAmadeusHotelCode());
            bidTask.setPropName(importBidTask.getPropName());
            bidTask.setPropPhone(importBidTask.getPropPhone());
            bidTask.setPropAdd(importBidTask.getPropAddress());
            bidTask.setProjectId(request.getProjectId());
            bidTask.setGenerateBidStatus(GeneratedBidStatusEnum.WAITING_GENERATE.key);
            bidTask.setCreator(userSession.getUsername());
            bidTask.setModifier(userSession.getUsername());

            // 存在则更新
            Optional.ofNullable(propCodeMap.get(importBidTask.getPropCode())).ifPresent(task -> bidTask.setId(task.getId()));
            String chainCodeHotelCodeKey = String.format("%s:%s", importBidTask.getAmadeusChainCode(), importBidTask.getAmadeusHotelCode());
            Optional.ofNullable(amadusCodeMap.get(chainCodeHotelCodeKey)).ifPresent(task -> bidTask.setId(task.getId()));
            Optional.ofNullable(propNameMap.get(importBidTask.getPropName())).ifPresent(task -> {bidTask.setId(task.getId());});

            // 校验
            List<ImportRowErrorVO> rowErrorList = new ArrayList<>();
            ValidateBidContext validateBidContext = new ValidateBidContext();
            validateBidContext.setLanguage(language);
            validateBidContext.setBidOrgType(OrgTypeEnum.HOTEL.key);
            validateBidContext.setIsUpload(YesOrNoEnum.YES.getKey());
            List<CreateBidRequest> createBidRequests = bidManager.validateAndGenerateCreateBidRequest(validateBidContext, Collections.singletonList(importBidTask), rowErrorList);
            bidTask.setIsValidateError(rowErrorList.isEmpty() ? 0 : 1);
            bidTask.setValidateErrorDetail(JsonUtil.objectToJson(CollUtil.isEmpty(rowErrorList) ? new Object() : rowErrorList.get(0)));
            bidTask.setDataDetail(JsonUtil.objectToJson(importBidTask));
            bidTask.setHotelId(importBidTask.getHotelId());

            importKeySet.add(key);

            // 保存
            if (Objects.isNull(bidTask.getId())) {
                importBidTaskMapper.insert(bidTask);
            } else {
                importBidTaskMapper.updateById(bidTask);
            }
        });
    }

    /**
     * 删除导入报价任务
     */
    public void deleteImportBidTask(@Valid DeleteImportBidTaskRequest request) {
        // 只删除未生成报价的
        LambdaQueryWrapper<ImportBidTaskEntity> queryWrapper = Wrappers.lambdaQuery(ImportBidTaskEntity.class);
        queryWrapper.in(ImportBidTaskEntity::getId, request.getImportBidTaskIds());
        queryWrapper.eq(ImportBidTaskEntity::getGenerateBidStatus, GeneratedBidStatusEnum.WAITING_GENERATE.getKey());
        importBidTaskMapper.delete(queryWrapper);
    }

    /**
     * 查询导入报价任务详情
     */
    public ImportBidTaskDetailVO queryImportBidTaskDetail(Long id) {
        // 获取语言
        int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);

        // 查询任务
        ImportBidTaskEntity importBidTask = importBidTaskMapper.selectById(id);
        if (Objects.isNull(importBidTask)) {
            AppUtility.serviceError(ErrorCode.IMPORT_BID_TASK_NOT_EXIST);
        }

        // 查询任务详情
        ImportStandardBidVO importStandardBidVO = JsonUtil.jsonToBean(importBidTask.getDataDetail(), ImportStandardBidVO.class);

        // 返回
        ImportBidTaskDetailVO res = new ImportBidTaskDetailVO();
        res.setId(importBidTask.getId());
        res.setFileName(importBidTask.getFileName());
        res.setFileUrl(ossManager.generateUrlPublic(importBidTask.getFileKey()));
        res.setDetail(importStandardBidVO);
        return res;
    }

    /**
     * 更新报价任务
     */
    public void updateImportBidTask(UpdateBidTaskRequest request) {
        // 查询报价任务
        ImportBidTaskEntity importBidTask = importBidTaskMapper.selectById(request.getId());
        if (Objects.isNull(importBidTask)) {
            AppUtility.serviceError(ErrorCode.IMPORT_BID_TASK_NOT_EXIST);
        }

        // 校验状态是否可修改
        if (!Objects.equals(importBidTask.getGenerateBidStatus(), GeneratedBidStatusEnum.WAITING_GENERATE.getKey())) {
            AppUtility.serviceError(ErrorCode.IMPORT_BID_TASK_CANNOT_BE_UPDATE);
        }

        // 校验报价
        List<ImportRowErrorVO> rowErrorList = new ArrayList<>();
        ValidateBidContext validateBidContext = new ValidateBidContext();
        validateBidContext.setLanguage(AppUtility.getRequestHeaderLanguage(httpServletRequest));
        validateBidContext.setBidOrgType(OrgTypeEnum.HOTEL.key);
        validateBidContext.setIsUpload(YesOrNoEnum.YES.getKey());
        request.getDetail().setProjectId(importBidTask.getProjectId());
        List<CreateBidRequest> createBidRequests = bidManager.validateAndGenerateCreateBidRequest(validateBidContext, Collections.singletonList(request.getDetail()), rowErrorList);

        // 更新
        ImportBidTaskEntity updateEntity = new ImportBidTaskEntity();
        updateEntity.setId(importBidTask.getId());
        updateEntity.setDataDetail(JsonUtil.objectToJson(request.getDetail()));
        updateEntity.setIsValidateError(rowErrorList.isEmpty() ? 0 : 1);
        updateEntity.setValidateErrorDetail(JsonUtil.objectToJson(CollUtil.isEmpty(rowErrorList) ? new Object() : rowErrorList.get(0)));
        updateEntity.setHotelId(request.getDetail().getHotelId());
        importBidTaskMapper.updateById(updateEntity);

        // 如果生成报价且校验不通过, 返回校验异常
        if (request.getGeneratedBid()) {
            if (YesOrNoEnum.YES.getKey().equals(updateEntity.getIsValidateError())) {
                AppUtility.serviceError(ErrorCode.IMPORT_BID_TASK_VALIDATE_NO_PASSED);
            }
            // 生成报价
            bidManager.batchCreateBid(createBidRequests, null, UserSession.get());
            // 更新报价状态
            importBidTaskMapper.updateGenerateBidStatus(GeneratedBidStatusEnum.GENERATED.key, UserSession.get().getUsername(), Collections.singletonList(request.getId()));
        }
    }

    /**
     * 批量生成报价
     */
    public void batchGenerateBid(@Valid BatchGenerateBidRequest request) {
        // 获取语言
        int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);

        // 批量查询报价任务
        List<ImportBidTaskEntity> tasks = importBidTaskMapper.selectBatchIds(request.getTaskIds());

        // 校验是否有任务已经生成报价
        List<Long> generatedBidTaskIds = tasks.stream()
            .filter(task -> Objects.equals(task.getGenerateBidStatus(), GeneratedBidStatusEnum.GENERATED.getKey()))
            .map(ImportBidTaskEntity::getId)
            .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(generatedBidTaskIds)) {
            // 某某报价任务已经生成报价
            AppUtility.serviceError(ErrorCode.BID_TASK_HAS_GENERATED, generatedBidTaskIds);
        }

        // 提取任务导入报价参数
        List<ImportStandardBidVO> importList = tasks.stream().map(task -> {
            ImportStandardBidVO importStandardBid = JsonUtil.jsonToBean(task.getDataDetail(), ImportStandardBidVO.class);
            importStandardBid.setProjectId(task.getProjectId());
            return importStandardBid;
        }).collect(Collectors.toList());

        // 批量校验
        List<ImportRowErrorVO> rowErrorList = new ArrayList<>();
        ValidateBidContext validateBidContext = new ValidateBidContext();
        validateBidContext.setBidOrgType(OrgTypeEnum.HOTEL.key);
        validateBidContext.setLanguage(AppUtility.getRequestHeaderLanguage(httpServletRequest));
        validateBidContext.setIsUpload(YesOrNoEnum.YES.getKey());
        List<CreateBidRequest> createBidRequests = bidManager.validateAndGenerateCreateBidRequest(validateBidContext, importList, rowErrorList);

        // 判断是否有错误
        if (CollUtil.isNotEmpty(rowErrorList)) {
            // 将所有错误信息汇总返回给前端
            excelManager.fillRowErrorMessage(language, rowErrorList);
            String errorMessage = rowErrorList.stream().map(ImportRowErrorVO::getErrorMsg).collect(Collectors.joining(","));
            AppUtility.serviceError(errorMessage);
        }

        // 校验没有任何错误, 生成报价
        bidManager.batchCreateBid(createBidRequests, null, UserSession.get());

        // 更新任务生成报价状态
        importBidTaskMapper.updateGenerateBidStatus(GeneratedBidStatusEnum.GENERATED.key, UserSession.get().getUsername(), request.getTaskIds());
    }

    /**
     * 查询导入报价任务列表
     */
    public PageVO<ImportBidTaskListVO> queryImportBidTaskList(@Valid ImportBidTaskListRequest request) {
        // 获取语言
        int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);

        // 查询列表
        IPage<ImportBidTaskListVO> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<ImportBidTaskListVO> pageRes = importBidTaskMapper.selectByCondition(page, request);
        List<ImportBidTaskListVO> records = pageRes.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageVO<>(0, 0, records);
        }

        // 查询机构
        Set<Integer> orgIds = records.stream().map(ImportBidTaskListVO::getTenderOrgId).collect(Collectors.toSet());
        List<OrgEntity> orgList = orgMapper.selectBatchIds(orgIds);
        Map<Integer, OrgEntity> orgMap = orgList.stream().collect(Collectors.toMap(OrgEntity::getOrgId, Function.identity()));

        // 查询错误码信息
        Map<Long, ImportRowErrorVO> errorRowMap = records.stream()
            .filter(item -> StringUtils.isNotBlank(item.getValidateErrorDetail()))
            .collect(Collectors.toMap(ImportBidTaskListVO::getId, e -> JsonUtil.jsonToBean(e.getValidateErrorDetail(), ImportRowErrorVO.class)));
        excelManager.fillRowErrorMessage(language, errorRowMap.values());

        // 处理数据
        records.forEach(item -> {
            item.setCityName(AppUtility.getCityName(language, item.getCityCode()));
            item.setHotelName(AppUtility.getHotelName(language, item.getHotelId()));
            item.setAddress(AppUtility.getName(language, item.getAddressEnUs(), item.getAddressEnUs(), item.getAddressZhCn()));
            item.setProjectOrgName(Optional.ofNullable(orgMap.get(item.getTenderOrgId())).map(OrgEntity::getOrgName).orElse(null));
            if (StringUtils.isNotBlank(item.getValidateErrorDetail())) {
                item.setValidateErrorDetail(errorRowMap.get(item.getId()).getErrorMsg());
            }
        });
        return new PageVO<>(pageRes.getTotal(), pageRes.getPages(), records);
    }

    /**
     * 导出导入报价任务
     */
    public void exportImportBidTaskList(ImportBidTaskListRequest request, HttpServletResponse httpServletResponse) {
        // 导出文件名称
        String fileName = "报价任务" + "-" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);

        // 写入数据
        ExportExcelContext<ExportTenderPriceVO> exportContext = new ExportExcelContext<>();
        exportContext.setHttpServletResponse(httpServletResponse);
        exportContext.setLanguage(AppUtility.getRequestHeaderLanguage(httpServletRequest));
        exportContext.setRequestNo(request.getRequestNo());
        exportContext.setExportVOClass(ExportTenderPriceVO.class);
        exportContext.setFileName(fileName);
        exportContext.setExportData(queryImportBidTaskDetail(request));
        excelManager.generalExport(exportContext);
    }

    /**
     * 查询导入报价任务详情
     */
    public List<ExportTenderPriceVO> queryImportBidTaskDetail(ImportBidTaskListRequest request) {
        // 查询数据
        List<ImportBidTaskEntity> tasks = importBidTaskMapper.selectDataDetailByCondition(request);

        // 查询酒店信息
        Set<Long> hotelIds = tasks.stream().map(ImportBidTaskEntity::getHotelId).filter(Objects::nonNull).collect(Collectors.toSet());
        // 这里不同步了, 之前导入的时候应该都同步过了
        List<HotelEntity> hotelList = hotelManager.queryHotelBasicInfo(hotelIds, false);
        Map<Long, HotelEntity> hotelMap = hotelList.stream().collect(Collectors.toMap(HotelEntity::getHotelId, Function.identity()));

        // 转换数据
        return tasks.stream()
            .filter(e -> hotelMap.containsKey(e.getHotelId()))
            .map(e -> JsonUtil.jsonToBean(e.getDataDetail(), ExportTenderPriceVO.class))
            .collect(Collectors.toList());
    }

    /**
     * 查询报价任务报价信息
     */
    public ImportBidTaskBidInfoVO queryImportBidTaskBidInfo(Long id) {
        // 查询报价任务
        ImportBidTaskEntity importBidTask = importBidTaskMapper.selectById(id);
        if (Objects.isNull(importBidTask)) {
            AppUtility.serviceError(ErrorCode.IMPORT_BID_TASK_NOT_EXIST);
        }

        // 校验是否生成报价
        if (!Objects.equals(importBidTask.getGenerateBidStatus(), GeneratedBidStatusEnum.GENERATED.getKey())) {
            AppUtility.serviceError(ErrorCode.IMPORT_BID_TASK_NOT_GENERATED);
        }

        // 查询项目酒店报价
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.selectByProjectIdAndHotelId(importBidTask.getProjectId(), importBidTask.getHotelId());
        if (Objects.isNull(projectIntentHotel)) {
            AppUtility.serviceError(ErrorCode.PROJECT_INTENT_HOTEL_NOT_EXIST);
        }

        // 返回结果
        ImportBidTaskBidInfoVO res = new ImportBidTaskBidInfoVO();
        res.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        res.setProjectId(projectIntentHotel.getProjectId());
        res.setHotelId(projectIntentHotel.getHotelId());
        return res;
    }
}
