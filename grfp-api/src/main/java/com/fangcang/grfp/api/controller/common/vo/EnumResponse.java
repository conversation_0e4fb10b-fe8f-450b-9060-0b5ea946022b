package com.fangcang.grfp.api.controller.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel("枚举响应")
@Getter
@Setter
public class EnumResponse {

    @ApiModelProperty("枚举类型")
    private String enumType;

    @ApiModelProperty("枚举项列表")
    private List<EnumItemVO> enumItems;

    public EnumResponse() {}

    public EnumResponse(String enumType, List<EnumItemVO> enumItems) {
        this.enumType = enumType;
        this.enumItems = enumItems;
    }
} 