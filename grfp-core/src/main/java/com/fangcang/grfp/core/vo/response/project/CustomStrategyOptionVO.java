package com.fangcang.grfp.core.vo.response.project;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@ApiModel(description = "自定义采购策略选项响应")
public class CustomStrategyOptionVO extends BaseVO {

    @ApiModelProperty(value = "选项 id")
    private Long id;

    @ApiModelProperty(value = "策略 ID")
    private Long strategyId;

    @ApiModelProperty(value = "排序")
    private Integer displayOrder;

    @ApiModelProperty(value = "选项名称")
    private String optionName;

    @ApiModelProperty(value = "权重分值")
    private BigDecimal weightScore;
}
