package com.fangcang.grfp.core.entity;

import com.fangcang.grfp.core.base.BaseVO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 文字资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_text_resource")
public class TextResourceEntity extends BaseVO {


    /**
     * 文字资源ID
     */
    @TableField("text_resource_id")
    private Integer textResourceId;

    /**
     * 文字资源编号
     */
    @TableId(value = "text_resource_code")
    private String textResourceCode;

    /**
     * 文字资源 文字资源类型 1:网页文本，2：提示信息
     */
    @TableField(value = "text_resource_type")
    private Integer textResourceType;
    /**
     * 英文文字翻译
     */
    @TableField("value_en_us")
    private String valueEnUs;

    /**
     * 中文文字翻译
     */
    @TableField("value_zh_cn")
    private String valueZhCn;


    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
