 package com.fangcang.grfp.core.enums;

 /**
  * 非POI热订区域邀约推荐:原因
  */
 public enum RecommendNoPoiHotArealEnum {

    ROOM_NIGHT_MORE_THEN_100(1, "非POI热订区域高产酒店，建议邀约"), // 如果酒店去年预订间夜超过100间夜
    SAME_LEVEL_ROOM_NIGHT_FIRST(2, "非POI热订区域酒店，同档员工最常订酒店"), // 如果酒店在当前星级预订间夜量第一
    OTA_49(3, "非POI热订区域超高评分酒店，建议邀约"), // 如果酒店评分4.9分及以上
    OPEN_IN_2_YEAR(4, "非POI热订区域新开业酒店，建议邀约"), // 酒店为近2年开业
    LAST_YEAR_ROOM_NIGHT_600(5, "非POI热订区域商旅常订酒店，建议邀约"); // 如果酒店商旅近12个月预订总间夜超过600间夜


    public int key;

    public String value;

    RecommendNoPoiHotArealEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (RecommendNoPoiHotArealEnum recommendNoPoiHotArealEnum : RecommendNoPoiHotArealEnum.values()) {
            if (recommendNoPoiHotArealEnum.key == key) {
                value = recommendNoPoiHotArealEnum.value;
                break;
            }
        }
        return value;
    }
}
