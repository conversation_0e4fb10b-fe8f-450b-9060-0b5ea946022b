package com.fangcang.grfp.core.enums;

/**
 * <AUTHOR>
 * @date 2024/6/29 16:27
 */
public enum RoomLevelEnum {

    ZERO(0, "未知房档"),

    ONE(1, "房档一"),

    <PERSON><PERSON><PERSON>(2, "房档二"),

    <PERSON><PERSON><PERSON><PERSON>(3, "房档三"),

    FOUR(4, "房档四"),

    FIVE(5, "房档五"),

    S<PERSON>(6, "房档六"),

    SEVEN(7, "房档七"),

    EIGHT(8, "房档八"),

    NINE(9, "房档九"),

    TEN(10, "房档十");


    public Integer key;
    public String value;

    RoomLevelEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(Integer key) {
        if(key == null){
            return ZERO.value;
        }
        String value = null;
        for (RoomLevelEnum roomLevelEnum : RoomLevelEnum.values()) {
            if (roomLevelEnum.key.equals(key)) {
                value = roomLevelEnum.value;
                break;
            }
        }
        if(value == null){
            return "Level " + key;
        }
        return value;
    }

}

