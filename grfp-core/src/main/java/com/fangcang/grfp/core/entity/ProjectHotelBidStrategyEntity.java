package com.fangcang.grfp.core.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 项目酒店报价策略
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_hotel_bid_strategy")
public class ProjectHotelBidStrategyEntity extends BaseVO {


    /**
     * 项目意向酒店ID
     */
    @TableId(value = "project_intent_hotel_id", type = IdType.ASSIGN_ID)
    private Integer projectIntentHotelId;

    /**
     * 项目id
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店id
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 酒店是否须支持员工到店付款: 1-是 0-否
     */
    @TableField("support_pay_at_hotel")
    private Integer supportPayAtHotel;

    /**
     * 酒店是否需支持 VCC 公司统一支付: 1-是 0-否
     */
    @TableField("support_vcc_pay")
    private Integer supportVccPay;

    /**
     * 酒店是否须支持提供入住明细信息: 1-是 0-否
     */
    @TableField("support_checkin_info")
    private Integer supportCheckinInfo;

    /**
     * 酒店是否须支持到店付免担保: 1-是 0-否
     */
    @TableField("support_no_guarantee")
    private Integer supportNoGuarantee;

    /**
     * 酒店是否须支持提前离店按实际入住金额收款: 1-是 0-否
     */
    @TableField("support_pay_early_checkout")
    private Integer supportPayEarlyCheckout;




    /**
     * 报价是否需要包括税费和服务费: 1-是 0-否
     */
    @TableField("support_include_tax_service")
    private Integer supportIncludeTaxService;

    /**
     * 酒店房间是否需提供免费 WIFI 服务: 1-是 0-否
     */
    @TableField("support_wifi")
    private Integer supportWifi;

    /**
     * 到店付免担保最晚保留时间
     */
    @TableField("late_reserve_time")
    private String lateReserveTime;

    /**
     * 超出最晚保留时间采取措施：1-酒店直接与入住人或预订联系人联系，确认入住时间；0-取消订单
     */
    @TableField("do_after_late_reserve_time")
    private Integer doAfterLateReserveTime;

    /**
     * 免费取消限制天数
     */
    @TableField("support_cancel_day")
    private Integer supportCancelDay;

    /**
     * 免费取消限制时间
     */
    @TableField("support_cancel_time")
    private String supportCancelTime;

    /**
     * 有无佣金 1-有 0-否
     */
    @TableField("has_commission")
    private Integer hasCommission;

    /**
     * 佣金比例
     */
    @TableField("commission")
    private BigDecimal commission;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
