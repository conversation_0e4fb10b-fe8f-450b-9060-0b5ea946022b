package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import lombok.Data;

import java.util.List;

@Data
public class HotelFacilityNewResponseDto {

    /**
     * 设施id
     */
    private String featureId;

    /**
     * 设施名称
     */
    private String name;

    /**
     * 设施类型编码
     */
    private String typeCode;

    /**
     * 设施类型名称
     */
    private String typeName;

    /**
     * 设施类别
     * (1-酒店设施 2-房型设施)
     */
    private String categoryType;

    /**
     * 设施状态
     * 房型设施状态，0-全部房型无，1-全部房型有，2-部分房型有
     */
    private Integer status;

    /**
     * 适用房型（设施状态为：2-部分房型有），存在适用房型节点
     */
    private List<ApplicableRoomTypeID> applicableRoomTypeIDs;
}
