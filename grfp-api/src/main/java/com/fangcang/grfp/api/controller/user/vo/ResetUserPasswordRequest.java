package com.fangcang.grfp.api.controller.user.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel("重置用户密码")
@Getter
@Setter
public class ResetUserPasswordRequest extends BaseVO {

    @ApiModelProperty(value = "用户ID")
    @NotNull
    private Integer userId;

}
