package com.fangcang.grfp.core.mapper;

import com.fangcang.grfp.core.entity.HotelGroupDefaultApplicableDayEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.vo.BidApplicableDayVO;
import com.fangcang.grfp.core.vo.HotelGroupDefaultApplicableDayVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 项目意向酒店集团默认适用日期 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface HotelGroupDefaultApplicableDayMapper extends BaseMapper<HotelGroupDefaultApplicableDayEntity> {

    List<BidApplicableDayVO> queryDefaultApplicableDayInfo(Integer projectIntentHotelGroupId);

    int deleteHotelGroupDefaultApplicableDay(int projectIntentHotelGroupId);

    int batchInsert(@Param("list") List<HotelGroupDefaultApplicableDayEntity> entities);
}
