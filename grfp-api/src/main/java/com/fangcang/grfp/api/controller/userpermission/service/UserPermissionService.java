package com.fangcang.grfp.api.controller.userpermission.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.api.controller.userpermission.vo.AddUserPermission;
import com.fangcang.grfp.api.controller.userpermission.vo.UserPermissionNameVO;
import com.fangcang.grfp.api.controller.userpermission.vo.UserPermissionVO;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.constant.UserPermission;
import com.fangcang.grfp.core.entity.UserPermissionEntity;
import com.fangcang.grfp.core.enums.OrgTypeEnum;
import com.fangcang.grfp.core.enums.RoleCodeEnum;
import com.fangcang.grfp.core.mapper.UserPermissionMapper;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.vo.request.userpermission.QueryUserPermissionRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserPermissionService {

    @Autowired
    private UserPermissionMapper userPermissionMapper;

    public List<UserPermissionNameVO> getUserPermissionList(HttpServletRequest request) {
        int languageId = AppUtility.getRequestHeaderLanguage(request);
        List<UserPermissionNameVO> userPermissionVOList = new ArrayList<>();

        //获取权限列表
        List<String> permissionList = UserPermission.userPermissionList;
        for(int i = 0; i < permissionList.size(); i++) {
            String permission = permissionList.get(i);
            UserPermissionNameVO userPermissionVO = new UserPermissionNameVO();
            userPermissionVO.setPermission(permission);
            userPermissionVO.setName(UserPermission.getPermissionName(languageId, permission));
            userPermissionVOList.add(userPermissionVO);
        }
        return userPermissionVOList;
    }

    public void addUserPermission(AddUserPermission userPermission) {
        UserSession userSession = UserSession.get();
        if(!userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.PLATFORM.key)){
            AppUtility.serviceError(ErrorCode.NO_PERMISSION_ACCESS);
        }
        List<String> userPermissionList = userPermissionMapper.queryPermissions(userPermission.getOrgType(), userPermission.getRoleCode());
        if(userPermissionList.contains(userPermission.getPermission())){
            AppUtility.serviceError(ErrorCode.USER_PERMISSION_EXIST);
        }
        UserPermissionEntity userPermissionEntity = new UserPermissionEntity();
        userPermissionEntity.setOrgType(userPermission.getOrgType());
        userPermissionEntity.setRoleCode(userPermission.getRoleCode());
        userPermissionEntity.setPermission(userPermission.getPermission());
        userPermissionEntity.setCreator(userSession.getUsername());
        userPermissionEntity.setModifier(userSession.getUsername());
        userPermissionMapper.insert(userPermissionEntity);
    }

    public void deleteUserPermissionList(int userPermissionId) {
        userPermissionMapper.deleteById(userPermissionId);
    }

    public PageVO<UserPermissionVO> queryUserPermissionPage(HttpServletRequest request, QueryUserPermissionRequest req){
        int languageId = AppUtility.getRequestHeaderLanguage(request);
        IPage<UserPermissionEntity> page = new Page<>(req.getPageIndex(), req.getPageSize());
        userPermissionMapper.queryUserPermissionPage(page, req);
        List<UserPermissionVO> userPermissionVOList = page.getRecords().stream().map(item -> {
            UserPermissionVO userPermissionVO = new UserPermissionVO();
            BeanUtils.copyProperties(item, userPermissionVO);
            userPermissionVO.setOrgTypeName(AppUtility.getText(languageId, OrgTypeEnum.getOrgTextCodeByKey(item.getOrgType())));
            userPermissionVO.setPermissionName(UserPermission.getPermissionName(languageId, item.getPermission()));
            userPermissionVO.setRoleName(RoleCodeEnum.getRoleName(languageId, item.getRoleCode()));
            return userPermissionVO;
        }).collect(Collectors.toList());
        return new PageVO<>(page.getTotal(), page.getPages(), userPermissionVOList);
    }

}
