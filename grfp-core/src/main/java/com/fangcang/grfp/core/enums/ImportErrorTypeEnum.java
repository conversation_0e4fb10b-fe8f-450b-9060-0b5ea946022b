package com.fangcang.grfp.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导入错误类型枚举
 * 目前已知的需要返回给前端的类型就两种:
 * 1. 表头错误
 * 2. 导入数据部分失败
 * 后续如果有其他类型的错误, 可以继续扩展, 其它错误认为是开发过程中的 bug
 *
 */
@Getter
@AllArgsConstructor
public enum ImportErrorTypeEnum {

    /**
     * 未知错误
     */
    UNKNOWN(1, "IMPORT_UNKNOWN_ERROR"),

    /**
     * 表头错误
     */
    HEADER_ERROR(2, "IMPORT_HEADER_ERROR"),

    /**
     * 导入数据部分失败
     */
    PARTIAL_FAIL(3, "IMPORT_PARTIAL_FAIL");


    /**
     * 类型
     */
    private final Integer type;

    /**
     * 错误代码
     */
    private final String errorCode;

}
