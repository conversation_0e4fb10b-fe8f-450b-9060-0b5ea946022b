package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("保存报价模板请求数据")
@Getter
@Setter
public class SaveBidTemplateRequest extends BaseVO {

    @ApiModelProperty("项目意向酒店ID")
    @NotNull
    private Integer projectIntentHotelId;

    @ApiModelProperty("报价信息Json")
    private String bidInfoJson;

}
