package com.fangcang.grfp.core.vo.request.hotel;

import com.fangcang.grfp.core.base.BaseVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 订单监控请求对象
 */
@Getter
@Setter
public class DisHotelDailyOrderRequest extends BaseVO {

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 类型，1-酒店 2-城市
     */
    private Integer type;

    /**
     * 订单排行榜类型，1-节省 2-浪费
     */
    private Integer saveOrWasteType;

    /**
     * 排序字段 0-间夜量 1-订单金额 2-节省金额 3-节省率 4-抽样率
     */
    private Integer sortColumn;

    /**
     * 排序类型 0-升序 1-降序
     */
    private Integer sortType;

    /**
     * 酒店Id
     */
    private Long hotelId;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 用户id
     */
    private List<Long> userIds;

    /**
     * 用户请求序号
     */
    private String requestSequenceNo;

    /**
     * 用户关联机构
     */
    private List<Long> userRelatedOrgIdList;

    /**
     * 省份
     */
    private String province;

    /**
     * 是否关注酒店 (1:关注)
     */
    private Integer isFollowHotel;

    /**
     * 省份 ID
     */
    private Long provinceId;

    /**
     * 酒店ID集合
     */
    private List<Long> hotelIdList;

}
