package com.fangcang.grfp.core.cached.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fangcang.grfp.core.cached.CachedHotelBrandService;
import com.fangcang.grfp.core.entity.HotelBrandEntity;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.mapper.HotelBrandMapper;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CachedHotelBrandServiceImpl implements CachedHotelBrandService {

    @Autowired
    private HotelBrandMapper hotelBrandMapper;

    @Cacheable(value="cachedHotelBrandService.getNameMap", key = "#languageId", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public Map<Long, String> getNameMap(int languageId) {
        Map<Long, String> nameMap = new HashMap<>();
        LambdaQueryWrapper<HotelBrandEntity>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(languageId == LanguageEnum.EN_US.key){
            lambdaQueryWrapper.select(HotelBrandEntity::getHotelBrandId, HotelBrandEntity::getNameEnUs);
        }
        if(languageId == LanguageEnum.ZH_CN.key){
            lambdaQueryWrapper.select(HotelBrandEntity::getHotelBrandId, HotelBrandEntity::getNameZhCn);
        }
        List<HotelBrandEntity> hotelBrandEntityList = hotelBrandMapper.selectList(lambdaQueryWrapper);
        hotelBrandEntityList.forEach(item -> {
            if(languageId == LanguageEnum.EN_US.key){
                nameMap.put(item.getHotelBrandId(), item.getNameEnUs());
            }
            if(languageId == LanguageEnum.ZH_CN.key){
                nameMap.put(item.getHotelBrandId(), item.getNameZhCn());
            }
        });
        if(nameMap.isEmpty()){
            return null;
        }
        log.info("HotelBrandNameMap size {}, {}", LanguageEnum.getValueByKey(languageId), nameMap.size());
        return nameMap;
    }

    @Override
    @CacheEvict(value="cachedHotelBrandService.getNameMap", key = "#languageId", cacheManager = "ehCacheCacheManager")
    public void clearNameMap(int languageId) {
        log.info("clear HotelBrand NameMap {}", LanguageEnum.getValueByKey(languageId));
    }

}
