package com.fangcang.grfp.server.job;

import cn.hutool.core.collection.CollUtil;
import com.fangcang.grfp.core.constant.CurrencyConstant;
import com.fangcang.grfp.core.entity.CurrencyExchangeRateEntity;
import com.fangcang.grfp.core.manager.CurrencyExchangeRateManager;
import com.fangcang.grfp.core.mapper.CurrencyExchangeRateMapper;
import com.fangcang.grfp.core.remote.currency.dto.CurrencyResponse;
import com.fangcang.grfp.core.remote.currency.dto.ExchangeRate;
import com.fangcang.grfp.core.remote.currency.manager.CurrencyApiManager;
import com.fangcang.grfp.core.remote.currency.response.BaseResponse;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.vo.request.QueryCurrencyExchangeRateRequest;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Stream;


@Component
@Slf4j
@JobHandler(value = SyncCurrencyExchangeRateJob.TASK_NAME)
public class SyncCurrencyExchangeRateJob extends IJobHandler {

    public static final String TASK_NAME = "SyncCurrencyExchangeRateJob";

    @Resource
    private CurrencyApiManager currencyApiManager;

    @Resource
    private CurrencyExchangeRateMapper currencyExchangeRateMapper;

    @Resource
    private CurrencyExchangeRateManager currencyExchangeRateManager;

    /**
     * Synchronize currency data to the database
     */
    @Override
    public ReturnT<String> execute(String s) {
        log.info("Sync currency exchange data job start");
        try {
            List<CurrencyExchangeRateEntity> entityList = new ArrayList<>();
            QueryCurrencyExchangeRateRequest queryCountryListRequest = new QueryCurrencyExchangeRateRequest();
            queryCountryListRequest.setCurrency(CurrencyConstant.USD); // 以USD为基准利率
            BaseResponse<CurrencyResponse> response = currencyApiManager.queryCurrencyExchangeRate(queryCountryListRequest);
            if (Objects.isNull(response) || Objects.isNull(response.getResult()) || CollUtil.isEmpty(response.getResult().getList())) {
                log.error("Query currency exchangeRate data  is empty");
                return ReturnT.FAIL;
            }

            // Convert data
            convertData(response.getResult().getList(), entityList);

            // Insert or update data into the database
            saveData(new ArrayList<>(entityList));

        } catch (Exception e) {
            log.error("Sync currency data job error", e);
            return ReturnT.FAIL;
        }
        log.info("Sync currency data end");
        return ReturnT.SUCCESS;
    }


    /**
     * Convert exchangeRateMap to List<CurrencyExchangeRateEntity>
     */
    private void convertData(Map<String, ExchangeRate> exchangeRateMap, List<CurrencyExchangeRateEntity> entityList) {

        for (Map.Entry<String, ExchangeRate> entry : exchangeRateMap.entrySet()) {
            ExchangeRate exchangeRate = entry.getValue();
            CurrencyExchangeRateEntity currencyEntity = new CurrencyExchangeRateEntity();
            currencyEntity.setCurrencyCode(entry.getKey());
            currencyEntity.setCurrencyName(exchangeRate.getName());
            currencyEntity.setExchangeRate(exchangeRate.getRate());
            BigDecimal inverseRate = new BigDecimal(1).divide(exchangeRate.getRate(), 10, RoundingMode.HALF_UP);
            currencyEntity.setInverseExchangeRate(inverseRate);
            currencyEntity.setIsAutoSync(1);
            currencyEntity.setCreator(UserSession.SYSTEM_USER_NAME);
            currencyEntity.setModifier(UserSession.SYSTEM_USER_NAME);
            entityList.add(currencyEntity);
        }

    }

    /**
     * Insert or update data into the database
     */
    private void saveData(List<CurrencyExchangeRateEntity> currencyEntityList) {
        if (CollUtil.isNotEmpty(currencyEntityList)) {
            currencyExchangeRateMapper.batchUpsert(currencyEntityList);
            // 记录日志
            currencyExchangeRateManager.recordCurrencyExchangeLog(currencyEntityList);
        }
    }
}
