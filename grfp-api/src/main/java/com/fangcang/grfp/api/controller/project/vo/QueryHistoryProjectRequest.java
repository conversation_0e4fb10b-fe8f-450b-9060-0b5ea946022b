package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(description = "查询项目绑定机构历史项目请求")
public class QueryHistoryProjectRequest extends PageQuery {

    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull
    private Integer projectId;

}
