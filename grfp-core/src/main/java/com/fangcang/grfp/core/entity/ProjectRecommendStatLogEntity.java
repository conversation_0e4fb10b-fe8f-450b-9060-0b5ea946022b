package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 项目历史数据推荐统计日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_recommend_stat_log")
public class ProjectRecommendStatLogEntity extends BaseVO {


    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 统计参考编号
     */
    @TableField("stat_reference_no")
    private String statReferenceNo;

    /**
     * 统计名称
     */
    @TableField("stat_name")
    private String statName;

    /**
     * 统计开始时间
     */
    @TableField("begin_time")
    private Date beginTime;

    /**
     * 统计结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 是否结束
     */
    @TableField("is_finished")
    private Integer isFinished;

    /**
     * 统计结果
     */
    @TableField("result")
    private Integer result;

    /**
     * 统计结果信息
     */
    @TableField("result_msg")
    private String resultMsg;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;


}
