package com.fangcang.grfp.core.vo.response.city;

import com.fangcang.grfp.core.base.BaseVO;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CityVO extends BaseVO {

    /**
     * 城市代码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityNameZhCn;

    /**
     * 城市名称
     */
    private String cityNameEnUs;

    /**
     * 省份代码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceNameZhCn;

    /**
     * 省份名称
     */
    private String provinceNameEnUs;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryNameZhCn;

    /**
     * 国家名称
     */
    private String countryNameEnUs;

}
