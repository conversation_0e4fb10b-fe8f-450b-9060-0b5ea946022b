package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 项目意向酒店集团默认适用日期
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_hotel_group_default_applicable_day")
public class HotelGroupDefaultApplicableDayEntity extends BaseVO {


    /**
     * 默认适用日期ID
     */
    @TableId(value = "default_applicable_day_id", type = IdType.AUTO)
    private Integer defaultApplicableDayId;

    /**
     * 酒店集团意向ID
     */
    @TableField("project_intent_hotel_group_id")
    private Integer projectIntentHotelGroupId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 价格类型
     */
    @TableField("price_type")
    private Integer priceType;

    /**
     * 开始时间
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 结束时间
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
