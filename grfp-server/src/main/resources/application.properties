# Spring Application
spring.application.name=grfp-server
spring.profiles.active=dev

#################################################################################################################

# Spring Web Server (Tomcat)
server.port=6889
server.servlet.context-path=/grfp-server
server.servlet.session.timeout=6h

#################################################################################################################

# Logging
logging.level.root=INFO
logging.level.com.fangcang.grfp.core.mapper=DEBUG

#################################################################################################################

# Cache
spring.cache.ehcache.config=classpath:ehcache.xml
spring.cache.redis.time-to-live=600s
spring.cache.redis.key-prefix=grfp:cache:

#################################################################################################################

mybatis-plus.mapper-locations=classpath:mapper/**/*.xml
mybatis-plus.configuration.map-underscore-to-camel-case=true

#################################################################################################################
# Spring Session
spring.session.store-type=redis

# User Session Store (User session maxIdle should be slightly longer than the KeepAlive polling interval. Eg. 60 seconds more 43200 = 12h)
user-session-store.max-idle-in-sec=43200

##################################################################################################################

# logging
web.logging.request.enabled=true;
web.logging.body.enabled=true


##################################################################################################################
# swagger
web.swagger.enabled=true

# spring mail
spring.mail.default-encoding=utf-8
spring.mail.host=smtp.office365.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=Fangcang123!@
spring.mail.protocol=smtp
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

# oss
grfp.oss.endpoint=tos-cn-guangzhou.volces.com
grfp.oss.region=cn-guangzhou
grfp.oss.access-key-id=AKLTZDNlMTZmMTIwOGVmNDBiNTkyZTVlZDc2YTlkOTM2OTk
grfp.oss.access-key-secret=TnpGaU9HTXpaV1ZtTnpBd05ESmpOamhrTlRnd1lqRXlOVGs1WVdGaU9UVQ==
grfp.oss.bucket-public=grfp-dev-public
grfp.oss.bucket-public-domain=https://grfp-dev-public.tos-cn-guangzhou.volces.com
grfp.oss.bucket-temp=grfp-dev-temp
grfp.oss.bucket-temp-domain=https://grfp-dev-temp.tos-cn-guangzhou.volces.com