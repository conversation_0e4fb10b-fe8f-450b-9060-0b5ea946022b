package com.fangcang.grfp.core.enums;

import com.fangcang.grfp.core.util.GenericAppUtility;

/**
 * 招标方式
 */
public enum TenderTypeEnum {

    PUBLIC(1, "公开签约"), INVITE(2, "邀请签约");

    public Integer key;
    public String value;

    TenderTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String geTextByKey(Integer key, int languageId) {
        String value = null;
        for (TenderTypeEnum tenderTypeEnum : TenderTypeEnum.values()) {
            if (tenderTypeEnum.key.equals(key)) {
                value = GenericAppUtility.getText(languageId, "TENDER_TYPE_" + tenderTypeEnum.name());
                break;
            }
        }
        return value;
    }

    public static TenderTypeEnum getEnumByKey(Integer key) {
        TenderTypeEnum tenderTypeEnum = null;
        for (TenderTypeEnum tenderType : TenderTypeEnum.values()) {
            if (tenderTypeEnum.key.equals(key)) {
                tenderTypeEnum = tenderType;
                break;
            }
        }
        return tenderTypeEnum;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
