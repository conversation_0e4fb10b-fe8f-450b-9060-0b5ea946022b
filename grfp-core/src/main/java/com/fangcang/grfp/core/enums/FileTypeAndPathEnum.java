package com.fangcang.grfp.core.enums;

/**
 * <AUTHOR>
 * @date 2022/9/9 15:45
 */
public enum FileTypeAndPathEnum {

    ORG_LOGO(1, "org/logo", "机构logo"),

    ORG_SUBJECT_BUSINESS_LICENSE(2, "org/subject/licensee", "机构签约主体营业执照"),

    CONTRACT_NAIN_TEMPLATE(3, "contractmaintemplate/", "正文合同模板(不含技术定位符)，供投标等预览时用"),

    ORG_SUBJECT_CERTIFICATE_OF_AUTHORIZATION(4, "org/subject/authorization", "机构关系证明或授权书"),
    
    CONTRACT(5, "contract/", "合同"),

    CONTRACT_FINAL_TEMPLATE(6, "contractfinaltemplate", "合同正式模板"),

    PROJECT_INTRODUCTION(7, "project/introduction", "项目简介"),

    EXPORT_EXCEL_TEMPLATE(8,"excel/template","导出报表模板"),

    AUTH_CONTRACT(9,"authcontract","授权合同"),

    SIGN_CONTRACT(10,"signcontract","签章合同"),

    EXPORT_EXCEL_TEMPLATE_ORDER_MONITOR_HOTEL(11,"excel/template/order/monitor/hotel","订单监控酒店维度导出模板"),

    EXPORT_EXCEL_TEMPLATE_ORDER_MONITOR_CITY(12,"excel/template/order/monitor/city","订单监控城市维度导出模板"),

    HOTEL_APPLY_ONLINE_CONTRACT(13, "hotelApplyOnline/contract", "酒店上线申请合同附件"),

    HOTEL_APPLY_ONLINE_OTHER(14, "hotelApplyOnline/other", "酒店上线申请其他附件"),

    IMPORT_EXCEL_TEMPLATE(15,"excel/template/import","导入报表模板"),

    CHANNEL(16, "org/channel", "渠道合作商图片"),

    BID_TASK_FILE(17, "bid-task", "报价任务"),

    DOWNLOAD_EXCEL_TEMP_FILE(18, "/temp/download", "临时下载文件"),

    IMPORT_FILE_OSS_DIR(19, "excel/import", "导入文件 OSS 目录"),

    PROJECT_EMAIL_ATTACHMENT(20, "project/attachment", "项目电邮附件");

    public Integer businessType;

    public String filePath;

    public String message;

    FileTypeAndPathEnum(Integer businessType, String filePath, String message) {
        this.businessType = businessType;
        this.filePath = filePath;
        this.message = message;
    }

    public static String getFilePathByBusinessType(Integer businessType) {
        if (businessType == null) {
            return null;
        }
        for (FileTypeAndPathEnum responseEnum : FileTypeAndPathEnum.values()) {
            if (responseEnum.businessType.intValue() == businessType.intValue()) {
                return responseEnum.filePath;
            }
        }
        return null;
    }
}
