package com.fangcang.grfp.core.vo.request;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("酒店集团列表分页查询")
@Getter
@Setter
public class ListHotelGroupDataRequest extends PageQuery {

    @ApiModelProperty(value = "酒店集团ID")
    private Long hotelGroupId;

    @ApiModelProperty(value = "酒店集团名称")
    private String hotelGroupName;

    @ApiModelProperty(value = "是否有效 1:是，0:否")
    private Boolean isActive;

}
