package com.fangcang.grfp.core.vo.response.project;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("分页查询酒店集团下意向酒店列表")
public class QueryHotelGroupInviteHotelVO extends BaseVO {

    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;

    @ApiModelProperty(value = "酒店名称")
    private String hotelName;

    @ApiModelProperty(value = "酒店星级")
    private String hotelStar;

    @ApiModelProperty(value = "酒店星级描述")
    private String hotelStarDesc;

    @ApiModelProperty(value = "地址")
    private String hotelAddress;

    @ApiModelProperty(value = "电话")
    private String hotelPhone;

    @ApiModelProperty(value = "城市")
    private String cityName;

    @ApiModelProperty(value = "酒店集团名称")
    private String hotelGroupName;

    @ApiModelProperty(value = "酒店品牌名称")
    private String hotelBrandName;

    @ApiModelProperty(value = "亮点")
    private String brightSpot;

    @ApiModelProperty(value = "主图URL")
    private String hotelImageUrl;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
