package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.HotelGroupLogEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 酒店集团日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface HotelGroupLogMapper extends BaseMapper<HotelGroupLogEntity> {

    /**
     * 根据酒店集团ID查询每个集团 ID 最新的日志
     */
    List<HotelGroupLogEntity> selectLatestByGroupIds(@Param("hotelGroupIds") Collection<Long> hotelGroupIds);

    /**
     * batch insert
     */
    void batchInsert(@Param("list") List<HotelGroupLogEntity> list);

}
