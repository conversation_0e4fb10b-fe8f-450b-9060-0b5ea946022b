package com.fangcang.grfp.api.controller.org.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("修改机构请求")
@Getter
@Setter
public class UpdateOrgRequest extends BaseVO {

    @ApiModelProperty(value = "机构ID")
    @NotNull
    private Integer orgId;

    @ApiModelProperty(value = "机构名称")
    @NotBlank
    private String orgName;

    @ApiModelProperty(value = "联系人")
    @NotBlank
    private String contactName;

    @ApiModelProperty(value = "联系人电话区号")
    @NotBlank
    private String contactMobileAreaCode;

    @ApiModelProperty(value = "联系人电话")
    @NotBlank
    private String contactMobile;

    @ApiModelProperty(value = "联系人电邮")
    @NotBlank
    @Email
    private String contactEmail;

    @ApiModelProperty(value = "财务联系人")
    private String financialContactName;

    @ApiModelProperty(value = "财务联系人电话区号")
    private String financialContactMobileAreaCode;

    @ApiModelProperty(value = "财务联系人电话")
    private String financialContactMobile;

    @ApiModelProperty(value = "财务联系人电邮")
    private String financialContactEmail;

    @ApiModelProperty(value = "详细地址")
    private String addressDetail;

    @ApiModelProperty(value = "公司简介")
    private String companyProfile;

    @ApiModelProperty(value = "机构logo地址")
    private String logoUrl;


}
