package com.fangcang.grfp.api.controller.userpermission.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("删除用户权限")
@Getter
@Setter
public class DeleteUserPermissionRequest extends BaseVO {

    @ApiModelProperty("用户权限ID")
    private Integer userPermissionId;
}
