package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.CityEntity;
import com.fangcang.grfp.core.vo.CityNameVO;
import com.fangcang.grfp.core.vo.request.ListCityDataRequest;
import com.fangcang.grfp.core.vo.response.city.CityVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 城市 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface CityMapper extends BaseMapper<CityEntity> {

    /**
     * 批量插入
     */
    void batchUpsert(@Param("list") Collection<CityEntity> cityEntities);

    /**
     * 根据城市编码查询城市
     */
    default CityEntity selectByCountryCodeAndCityCode(@Param("countryCode") String countryCode, @Param("cityCode") String cityCode) {
        if (countryCode == null || cityCode == null) {
            return null;
        }
        LambdaQueryWrapper<CityEntity> queryWrapper = Wrappers.lambdaQuery(CityEntity.class)
            .eq(CityEntity::getCountryCode, countryCode)
            .eq(CityEntity::getCityCode, cityCode)
            .last("limit 1");
        return this.selectOne(queryWrapper);
    }

    List<CityNameVO> selectCityNameList(@Param("languageId") Integer languageId,
                                        @Param("countryCode") String countryCode,
                                           @Param("cityName") String cityName,
                                           @Param("limitCount") Integer limitCount);

    /**
     * 分页查询数据
     */
    IPage<CityEntity> listDataPage(IPage<CityEntity> page, @Param("query") ListCityDataRequest query);

    /**
     * 根据城市编码查询城市列表
     */
    List<CityVO> selectCityVOList(@Param("cityCodes") Collection<String> cityCodes);

    /**
     * 根据城市编码查询城市信息
     */
    default CityEntity selectByCityCode(String cityCode) {
        LambdaQueryWrapper<CityEntity> queryWrapper = Wrappers.lambdaQuery(CityEntity.class);
        queryWrapper.eq(CityEntity::getCityCode, cityCode);
        return this.selectOne(queryWrapper);
    }

    /**
     * 根据城市名称查询城市列表
     */
    List<CityEntity> selectByCityName(@Param("cityNames") Collection<String> cityNames);
}
