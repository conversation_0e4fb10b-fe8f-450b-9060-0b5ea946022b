package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel("邀约邮件预览响应")
public class EmailResponse extends BaseVO {

    @ApiModelProperty("招标项目名称")
    private String projectName;

    @ApiModelProperty("项目类型")
    private String projectTypeValue;

    @ApiModelProperty("项目状态")
    private String projectStateValue;

    @ApiModelProperty("报名开始时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enrollStartTime;

    @ApiModelProperty("报名结束时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enrollEndTime;

    @ApiModelProperty("最小标值")
    private BigDecimal diffMinAmount;

    @ApiModelProperty("最大标值")
    private BigDecimal diffMaxAmount;

    @ApiModelProperty("项目简介")
    private String introduction;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("机构简介")
    private String companyProfile;

    @ApiModelProperty("机构logo地址")
    private String logoUrl;

    @ApiModelProperty("招标方式(1-公开招标，2-邀请招标)")
    private Integer tenderType;

    @ApiModelProperty("招标方式描述")
    private String tenderTypeValue;
}
