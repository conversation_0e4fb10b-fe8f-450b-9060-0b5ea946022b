package com.fangcang.grfp.core.base;

public class AppLogicException extends RuntimeException {

	// ---------------------------------------------------------------------------------------------------- Private Member Static Variables
	
	private static final long serialVersionUID = 1L;
	
	// ---------------------------------------------------------------------------------------------------- Private Member Variables
	
	private String errorCode;
	
	// ---------------------------------------------------------------------------------------------------- Constructor
	
	public AppLogicException(String errorCode, String errorMessage) {
		super(errorMessage);
		
		this.errorCode = errorCode;
	}

	// ---------------------------------------------------------------------------------------------------- Getters/Setters

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	// ---------------------------------------------------------------------------------------------------- Public Static Methods

	public static void createAndThrow(String errorCode, String errorMessage) throws AppLogicException {
		throw new AppLogicException(errorCode, errorMessage);
	}

}
