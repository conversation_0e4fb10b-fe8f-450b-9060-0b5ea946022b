package com.fangcang.grfp.api;

import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.cached.*;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.oss.OssManager;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.annotation.PreDestroy;

@SpringBootApplication
@MapperScan(basePackages = {"com.fangcang.grfp.core.mapper"})
@ComponentScan(basePackages = {"com.fangcang.grfp.core","com.fangcang.grfp.api"})
@EnableAsync
@Slf4j
public class GRfpApiApplication {

    public final static String VERSION = "v1.0.0-20250808";

    @Autowired
    private CachedSysConfigService cachedSysConfigService;
    @Autowired
    private CachedTextResourceService cachedTextResourceService;
    @Autowired
    private CachedHotelBrandService cachedHotelBrandService;
    @Autowired
    private CachedHotelGroupService cachedHotelGroupService;
    @Autowired
    private CachedHotelService cachedHotelService;
    @Autowired
    private CachedCityService cachedCityService;
    @Autowired
    private OssManager ossManager;
    @Autowired
    private CachedProjectService cachedProjectService;

    public static void main(String[] args) {
        SpringApplication.run(GRfpApiApplication.class, args);
    }

    @EventListener(ApplicationReadyEvent.class)
    public void afterStarted() {
        AppUtility.setCachedSysConfigService(cachedSysConfigService);
        AppUtility.setCachedTextResourceService(cachedTextResourceService);
        AppUtility.setCachedHotelBrandService(cachedHotelBrandService);
        AppUtility.setCachedHotelGroupService(cachedHotelGroupService);
        AppUtility.setCachedHotelService(cachedHotelService);
        AppUtility.setCachedCityService(cachedCityService);
        AppUtility.setCachedProjectService(cachedProjectService);
        AppUtility.setCosManager(ossManager);
        log.info("==============================GrfpApi 系统启动完成" + VERSION + "===========================================");
        cachedHotelBrandService.getNameMap(LanguageEnum.EN_US.key);
        cachedHotelBrandService.getNameMap(LanguageEnum.ZH_CN.key);
        cachedHotelGroupService.getNameMap(LanguageEnum.EN_US.key);
        cachedHotelGroupService.getNameMap(LanguageEnum.ZH_CN.key);
        cachedCityService.getNameMap(LanguageEnum.EN_US.key);
        cachedCityService.getNameMap(LanguageEnum.ZH_CN.key);

    }

    @PreDestroy
    public void onDestroy() throws Exception {
        log.info("GrfpApi " + VERSION + " is already [***stopped***]");
    }


}
