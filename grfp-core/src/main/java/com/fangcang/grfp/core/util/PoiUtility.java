package com.fangcang.grfp.core.util;

import com.fangcang.grfp.core.vo.MinDistancePoiInfo;
import com.fangcang.grfp.core.vo.response.project.ProjectPoiVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

public class PoiUtility {

    private static Logger logger = LoggerFactory.getLogger(PoiUtility.class);

    // 根据经纬度获取最小距离POI信息
    public static MinDistancePoiInfo filteMinDistancePoiInfo(BigDecimal lngGoogle, BigDecimal latGoogle, List<ProjectPoiVO> cityPoiList){
        MinDistancePoiInfo minDistancePoiInfo = new MinDistancePoiInfo();
        // BigDecimal lngGoogle, BigDecimal latGoogle, BigDecimal lngBaidu, BigDecimal latBaidu){
        if (lngGoogle == null || latGoogle == null) {
            return minDistancePoiInfo;
        }
        BigDecimal minDistance = BigDecimal.ZERO;
        String poiName = "";
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(cityPoiList)) {
            return  minDistancePoiInfo;
        }

        for (ProjectPoiVO poi : cityPoiList) {
            if (poi.getLngGoogle() == null || poi.getLatGoogle() == null) {
                continue;
            }
            try {
                BigDecimal distance = LocationUtil.getKmDistance(poi.getLatGoogle(), poi.getLngGoogle(), latGoogle, lngGoogle);
                if (minDistance.compareTo(BigDecimal.ZERO) == 0 || distance.compareTo(minDistance) < 0) {
                    minDistance = distance;
                    poiName = poi.getPoiName();
                }
            } catch (Exception e) {
                logger.error("计算poi距离异常，poiId" + poi.getPoiId(), e);
            }
        }

        if (minDistance.compareTo(BigDecimal.ZERO) > 0) {
            minDistancePoiInfo.setMinDistance(minDistance);
            minDistancePoiInfo.setPoiName(poiName);
            minDistancePoiInfo.setMinDistanceKmFormat(minDistancePoiInfo.getMinDistance().setScale(2, RoundingMode.HALF_UP) + "km");
            return minDistancePoiInfo;
        }
        return minDistancePoiInfo;

    }
}
