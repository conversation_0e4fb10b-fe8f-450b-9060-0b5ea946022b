package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.HotelViolationsMonitorEntity;
import com.fangcang.grfp.core.vo.response.hotel.HotelViolationCountStatDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 酒店违规监控表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface HotelViolationsMonitorMapper extends BaseMapper<HotelViolationsMonitorEntity> {

    // 查下项目酒店违规次数统计
    List<HotelViolationCountStatDto> queryHotelViolationCountStat(@Param("projectId") Integer projectId, @Param("hotelIdList") List<Long> hotelIdList);
}
