package com.fangcang.grfp.api.controller.account.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("修改自己密码")
@Getter
@Setter
public class UpdateSelfPasswordRequest extends BaseVO {

    @ApiModelProperty(value = "用户密码")
    @NotNull
    private String password;

}
