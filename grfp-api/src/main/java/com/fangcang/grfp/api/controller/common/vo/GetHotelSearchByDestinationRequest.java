package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@ApiModel("获取酒店目的地查询数据列表")
@Getter
@Setter
public class GetHotelSearchByDestinationRequest extends BaseVO {

    @ApiModelProperty("目的地ID")
    @NotBlank
    private String destinationId;

}
