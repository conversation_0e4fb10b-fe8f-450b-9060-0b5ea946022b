package com.fangcang.grfp.api.controller.textresource.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@ApiModel("获取文字资源请求")
@Getter
@Setter
public class GetTextResourceRequest extends BaseVO {

	// ---------------------------------------------------------------------------------------------------- Private Member

	@ApiModelProperty(value = "文字资源编码", required = true)
	@NotBlank
	protected String textResourceCode;

	@ApiModelProperty(value = "文字资源类型 1:网页文本,2:消息文本")
	private Integer textResourceType;


}
