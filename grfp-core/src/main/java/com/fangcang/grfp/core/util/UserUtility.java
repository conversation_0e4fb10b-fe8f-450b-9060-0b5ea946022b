package com.fangcang.grfp.core.util;

import com.fangcang.grfp.core.constant.RedisConstant;
import com.fangcang.grfp.core.entity.UserEntity;
import com.fangcang.grfp.core.enums.RoleCodeEnum;
import com.fangcang.grfp.core.usersession.UserSession;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

public class UserUtility {

	public static final String DEFAULT_PSWD = "666666";

	/**
	 * 加工密码，和登录一致。
	 *
	 * @param user
	 * @return
	 */
	public static UserEntity md5Pswd(UserEntity user) {
		//密码为   email + '#' + pswd，然后MD5
		user.setPassword(md5Pswd(user.getEmail(), user.getPassword()));
		return user;
	}

	/**
	 * 字符串返回值
	 *
	 * @param userAccount
	 * @param pswd
	 * @return
	 */
	public static String md5Pswd(String userAccount, String pswd) {
		pswd = String.format("%s#%s", userAccount.toUpperCase(), pswd); // 账号加密不区分大小写
		pswd = MathUtil.getMD5(pswd);
		return pswd;
	}

	public static String getSmsCodeKey(String email, Integer type) {
		String key = RedisConstant.REDIS_KEY_USER_PREFIX + email + ":" + type;
		return key;
	}

	public static String createLinkString(Map<String, Object> map) {
		ArrayList<String> list = new ArrayList<String>();
		for (Map.Entry<String, Object> entry : map.entrySet()) {
			if (entry.getValue() != "") {
				list.add(entry.getKey() + "=" + entry.getValue() + "&");
			}
		}
		int size = list.size();
		String[] arrayToSort = list.toArray(new String[size]);
		Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < size; i++) {
			sb.append(arrayToSort[i]);
		}
		return sb.toString();
	}

	public static Integer getEmployUserId(UserSession userSession){
		if(userSession.getRoleCode().equals(RoleCodeEnum.EMPLOYEE.key)){
			return userSession.getUserId();
		}
		return null;
	}
	public static void main(String[] args) {
		System.out.println(md5Pswd("<EMAIL>","666666"));
	}

}
