package com.fangcang.grfp.core.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class UserOrgVO extends BaseVO {

    /**
     * 机构ID
     */
    @ApiModelProperty("机构ID")
    private Integer orgId;

    /**
     * 机构名称
     */
    @ApiModelProperty("机构名称")
    private String orgName;

    /**
     * 机构类型 1平台，2酒店，3企业，4酒店集团
     */
    @ApiModelProperty("机构类型 1平台，2酒店，3企业，4酒店集团")
    private Integer orgType;


    /**
     * 机构类型 1平台，2酒店，3企业，4酒店集团
     */
    @ApiModelProperty("机构类型名称")
    private String orgTypeName;

    /**
     * 机构logo地址
     */
    @ApiModelProperty("机构logo地址")
    private String logoUrl;

    /**
     * 状态(1：有效，0：无效)
     */
    @ApiModelProperty("状态(1：有效，0：无效)")
    private Integer state;



}
