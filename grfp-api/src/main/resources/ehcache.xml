<?xml version="1.0" encoding="UTF-8"?>
<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://ehcache.org/ehcache.xsd">

	<diskStore path="java.io.tmpdir" />
	
	<!-- The defaultCache does not provide "defaults" for every cache, but its just a way of specifying configuration for caches that can/are added dynamically -->
	<defaultCache maxElementsInMemory="10000" eternal="false" timeToIdleSeconds="600" timeToLiveSeconds="600" overflowToDisk="false"></defaultCache>

	<!-- ******************************************************************* -->

	<cache name="cachedSysConfigService.getValue"
		   maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

	<cache name="cachedTextResourceService.getWebValue"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

	<cache name="cachedTextResourceService.getMsgValue"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

	<cache name="cachedTextResourceService.getLatestUpdatedTime"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

	<cache name="cachedTextResourceService.getTextResourcesVO"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

	<cache name="cachedHotelBrandService.getNameMap"
		maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="600" timeToLiveSeconds="600" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="cachedHotelGroupService.getNameMap"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="600" timeToLiveSeconds="600" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="cachedHotelService.getNameMap"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="600" timeToLiveSeconds="600" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="cachedCountryService.getByCountryCode"
		maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="600" timeToLiveSeconds="600" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="cachedCityService.getByCityCode"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="600" timeToLiveSeconds="600" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="cachedCityService.getNameMap"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="600" timeToLiveSeconds="600" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="commonService.queryHotelByName"
		maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="commonService.queryCityByName"
		maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="commonService.queryCountryByName"
		maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="commonService.queryHotelGroupByName"
		maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="commonService.queryHotelBrandByName"
		maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="commonService.queryLocalHotelByName"
		   maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="commonService.queryProvinceByName"
		   maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="cachedProjectService.getById"
		   maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />


	<cache name="cachedProjectService.queryProjectWhiteHotelIdList"
		maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="cachedHotelService.getId"
		   maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="30" timeToLiveSeconds="30" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="cachedHotelGroupService.getHotelGroupUserRelatedInfoVO"
		   maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="cachedHotelService.queryOrgRelatedInfoVO"
		   maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="5" timeToLiveSeconds="5" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="cachedCurrencyService.queryCurrencyNameList"
		   maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="120" timeToLiveSeconds="120" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="cachedCurrencyService.getCurrencyRateInfo"
		   maxElementsInMemory="200" eternal="false" timeToIdleSeconds="120" timeToLiveSeconds="300" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

	<cache name="cachedProjectIntentHotelService.queryProjectBidHotelInfo"
		   maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="cachedProjectService.queryProjectEmailAttachmentList"
		   maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="30" timeToLiveSeconds="30" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />
	<cache name="cachedHotelService.queryRoomNameListByHotelIds"
		   maxElementsInMemory="2000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />


</ehcache>
