package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@ApiModel(description = "新增自定义采购策略请求")
public class AddProjectCustomTendStrategyRequest extends BaseVO {

    private static final long serialVersionUID = 907919343830591951L;

    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "策略名称", required = true)
    @NotBlank
    private String strategyName;

    @ApiModelProperty(value = "策略类型: 1-是或否 2-文本 3-多选选项 4-单选选项", required = true)
    @NotNull
    private Integer strategyType;

    @ApiModelProperty(value = "策略选项, 当策略类型为 3 是必须有值")
    @Valid
    private List<AddProjectCustomStrategyOptionRequest> options;

}
