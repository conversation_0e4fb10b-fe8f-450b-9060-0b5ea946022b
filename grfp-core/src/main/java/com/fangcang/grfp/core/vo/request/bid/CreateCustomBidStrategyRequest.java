package com.fangcang.grfp.core.vo.request.bid;

import com.fangcang.grfp.core.vo.CustomStrategyBidOptionVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CreateCustomBidStrategyRequest {

    /**
     * 项目自定义采购策略id
     */
    private Integer customTendStrategyId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 策略类型 策略回类型：1：是或否,2:文本
     */
    private Integer strategyType;
    /**
     * 是否支持策略，1-是，0-否
     */
    private Integer supportStrategyName;

    /**
     * 支持策略文本
     */
    private String supportStrategyText;

    /**
     * 选项
     */
    private List<CustomStrategyBidOptionVO> options;

}
