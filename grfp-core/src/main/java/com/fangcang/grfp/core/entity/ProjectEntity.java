package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 项目
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project")
public class ProjectEntity extends BaseVO {


    /**
     * 项目ID
     */
    @TableId(value = "project_id", type = IdType.AUTO)
    private Integer projectId;

    /**
     * 项目名称
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 项目类型(1：酒店)，跟合同模板业务类型保持一致
     */
    @TableField("project_type")
    private Integer projectType;

    /**
     * 项目状态(0：未启动，1：招标中(已启动)，2：招标完成，3：已废标)
     */
    @TableField("project_state")
    private Integer projectState;

    /**
     * 招标机构id
     */
    @TableField("tender_org_id")
    private Integer tenderOrgId;

    /**
     * 招标方式(1-公开招标，2-邀请招标)
     */
    @TableField("tender_type")
    private Integer tenderType;

    /**
     * 招标方项目联系人
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 招标方项目人手机号码
     */
    @TableField("contact_mobile")
    private String contactMobile;

    /**
     * 报价开始时间
     */
    @TableField("bid_start_time")
    private Date bidStartTime;

    /**
     * 报价结束时间
     */
    @TableField("bid_end_time")
    private Date bidEndTime;

    /**
     * 第一轮报价开始时间
     */
    @TableField("first_bid_start_time")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstBidStartTime;

    /**
     * 第一轮报价结束时间
     */
    @TableField("first_bid_end_time")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstBidEndTime;

    /**
     * 第二轮报价开始时间
     */
    @TableField("second_bid_start_time")
    private Date secondBidStartTime;

    /**
     * 第二轮报价结束时间
     */
    @TableField("second_bid_end_time")
    private Date secondBidEndTime;

    /**
     * 第三轮报价开始时间
     */
    @TableField("third_bid_start_time")
    private Date thirdBidStartTime;

    /**
     * 第三轮报价结束时间
     */
    @TableField("third_bid_end_time")
    private Date thirdBidEndTime;

    /**
     * 预估采购数量，项目类型为酒店时即为酒店数量
     */
    @TableField("tender_count")
    private Integer tenderCount;

    /**
     * 协议报价开始日期
     */
    @TableField("price_monitor_start_date")
    private Date priceMonitorStartDate;

    /**
     * 协议报价结束日期
     */
    @TableField("price_monitor_end_date")
    private Date priceMonitorEndDate;

    /**
     * 报价状态更新通知方式(0-手工，1-自动)
     */
    @TableField("bid_state_updated_notify_mode")
    private Integer bidStateUpdatedNotifyMode;

    /**
     * 项目简介
     */
    @TableField("introduction")
    private String introduction;

    /**
     * 关联项目 ID
     */
    @TableField("related_project_id")
    private Integer relatedProjectId;

    /**
     * 排序(大到小)
     */
    @TableField("display_order")
    private Integer displayOrder;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 创建时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 公司差标最小值
     */
    @TableField("diff_min_amount")
    private BigDecimal diffMinAmount;

    /**
     * 公司差标最大值
     */
    @TableField("diff_max_amount")
    private BigDecimal diffMaxAmount;

    /**
     * 招标预算总价
     */
    @TableField("budget_total_amount")
    private BigDecimal budgetTotalAmount;


}
