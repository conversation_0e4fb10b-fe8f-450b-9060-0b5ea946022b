package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("获取酒店过滤条件数据")
public class GetHotelSearchResponse {

    /**
     * 酒店品牌列表
     */
    @ApiModelProperty("酒店品牌列表")
    private List<HotelBrand> hotelBrandList;

    /**
     * 酒店集团列表
     */
    @ApiModelProperty("酒店集团列表")
    private List<HotelGroup> hotelGroupList;

    /**
     * 酒店标签列表
     */
    @ApiModelProperty("酒店标签列表")
    private List<HotelLabel> hotelLabelList;

    /**
     * 酒店星级列表
     */
    @ApiModelProperty("酒店星级列表")
    private List<HotelStar> hotelStarList;

    /**
     * 酒店分类列表
     */
    @ApiModelProperty("酒店分类列表")
    private List<HotelSubCategory> hotelSubCategoryList;

    /**
     * 酒店热门设施列表
     */
    @ApiModelProperty("酒店热门设施列表")
    private List<HotelFacility> hotelFacilityList;

    /**
     * 房型热门设施列表
     */
    @ApiModelProperty("房型热门设施列表")
    private List<RoomFacility> roomFacilityList;

}
