package com.fangcang.grfp.core.enums;

import com.fangcang.grfp.core.util.GenericAppUtility;

public enum HotelStarEnum {
    FIVESTAR("19", "五星"),
    QUISAFIVESTAR("29", "豪华型"),
    FOURSTAR("39", "四星"),
    QUISAFOURSTAR("49", "高档型"),
    THREESTAR("59", "三星"),
    QUISATHREESTAR("64", "舒适型"),
    TWOSTAR("69", "二星"),
    QUISATWOSTAR("66", "经济型"),
    BELOWTWOSTAR("79", "二星以下");

    public String key;
    public String value;

    private HotelStarEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getTextByKey(String key, int languageId) {
        String value = null;
        for(HotelStarEnum starEnum : HotelStarEnum.values()) {
            if(starEnum.key.equals(key)) {
                value =  GenericAppUtility.getText(languageId, "HOTEL_STAR_" + starEnum.name());
                break;
            }
        }
        return value;
    }

}
