package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.dto.dhub.response.baseinfo.FindHotelInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("项目邀请酒店信息")
public class ProjectInvitedHotelInfo extends FindHotelInfo {

    @ApiModelProperty("项目ID")
    private Integer projectId;

    @ApiModelProperty("是否邀请 1:是,0:否")
    private Integer isInvited;
}
