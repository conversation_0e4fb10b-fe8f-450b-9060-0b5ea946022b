package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.HGroupDefaultCusStrategyOptionEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 酒店集团默认报价自定义策略选项 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
public interface HGroupDefaultCusStrategyOptionMapper extends BaseMapper<HGroupDefaultCusStrategyOptionEntity> {

    /**
     * 新增或编辑
     */
    void batchUpsert(@Param("list") List<HGroupDefaultCusStrategyOptionEntity> hotelGroupDefaultCusStrategyOptionList);

    /**
     * 根据项目意向酒店集团ID和策略 ID 查询
     */
    List<HGroupDefaultCusStrategyOptionEntity> selectByProjectIntentHotelGroupIdAndStrategyIds(@Param("projectIntentHotelGroupId") Integer projectIntentHotelGroupId, @Param("strategyIds") Set<Long> strategyIds);
}
