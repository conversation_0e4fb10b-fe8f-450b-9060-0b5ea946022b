<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectCustomStrategyOptionMapper">

    <insert id="batchInsert">
        insert into t_project_custom_strategy_option (strategy_id, option_name, display_order, creator)
        values
            <foreach collection="list" item="item" separator=",">
                (#{item.strategyId}, #{item.optionName}, #{item.displayOrder}, #{item.creator})
            </foreach>
    </insert>

    <select id="selectByStrategyIds" resultType="com.fangcang.grfp.core.vo.response.project.CustomStrategyOptionVO">
        select id, option_name, strategy_id, display_order, weight_score
        from t_project_custom_strategy_option
        where strategy_id in
        <foreach collection="strategyIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by display_order, id
    </select>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
                update t_project_custom_strategy_option
                set weight_score = #{item.weightScore},
                    modifier = #{item.modifier}
                where id = #{item.id}
        </foreach>
    </update>
</mapper>
