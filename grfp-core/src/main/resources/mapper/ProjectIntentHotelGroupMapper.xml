<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectIntentHotelGroupMapper">

    <select id="queryInviteHotelGroup"
            resultType="com.fangcang.grfp.core.vo.response.project.InviteHotelGroupVO">
        SELECT
        o.org_id AS hotelGroupOrgId,
        o.org_name AS orgName,
        o.logo_url AS logoUrl,
        o.contact_name AS contactName,
        o.contact_mobile AS contactMobile,
        o.contact_email AS contactEmail,
        g.project_intent_hotel_group_id AS projectIntentHotelGroupId,
        IF(g.project_intent_hotel_group_id IS NULL, 0, 1) AS inviteState,
        IFNULL(g.invite_send_email_status, 0) AS sendMailStatus,
        IFNULL(g.is_brand_limit, 0) AS isBrandLimit
        FROM grfp.t_org o
        LEFT JOIN grfp.t_project_intent_hotel_group g
        ON o.org_id = g.hotel_group_org_id
        AND g.project_id = #{request.projectId}
        WHERE o.org_type = 4
        AND o.state = 1
        <if test="request.groupName != null and request.groupName != ''">
            AND o.org_name LIKE CONCAT('%', #{request.groupName}, '%')
        </if>
        <if test="request.inviteState != null and request.inviteState == 1">
            AND EXISTS (
            SELECT 1 FROM grfp.t_project_intent_hotel_group tg
            WHERE tg.hotel_group_org_id = o.org_id
            AND tg.project_id = #{request.projectId}
            )
        </if>
        <if test="request.inviteState != null and request.inviteState == 0">
            AND NOT EXISTS (
            SELECT 1 FROM grfp.t_project_intent_hotel_group tg
            WHERE tg.hotel_group_org_id = o.org_id
            AND tg.project_id = #{request.projectId}
            )
        </if>
    </select>
    <select id="selectInvitedCount" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        t_project_intent_hotel_group g,
        (
            SELECT
            t1.PROJECT_ID,
            t1.HOTEL_ID
            FROM
            t_project_invite_hotel t1
            WHERE
            t1.PROJECT_ID IN ( SELECT PROJECT_ID FROM T_PROJECT WHERE PROJECT_STATE = 1 )
            AND t1.HOTEL_ID NOT IN ( SELECT t2.HOTEL_ID FROM T_PROJECT_INTENT_HOTEL t2 WHERE t1.project_id = t2.project_id AND (t2.BID_STATE >= 1 OR t2.hotel_group_approve_status > 0) )
        ) pih,
        t_project p,
        t_org o,
        t_hotel h
        WHERE
        g.project_id = p.project_id
        AND pih.project_id = p.project_id
        AND pih.hotel_id = h.hotel_id
        AND p.tender_org_id = o.org_id
        AND h.hotel_brand_id IN
        <foreach collection="hotelBrandIdList" item="hotelBrandId" separator="," open="(" close=")">
            #{hotelBrandId}
        </foreach>
        AND g.hotel_group_org_id = #{hotelGroupOrgId}
        AND h.is_active = 1
        AND p.project_state = 1
        <if test="employeeUserId != null">
            AND  g.hotel_group_contact_uid = #{employeeUserId}
        </if>
    </select>

    <select id="queryBidStatCount"
            resultType="com.fangcang.grfp.core.vo.BidStateCountVO">
        SELECT
            COUNT(ph.BID_STATE) AS bidStateCount,
            ph.BID_STATE AS bidState
        FROM
            t_project p,
            t_org o,
            t_project_intent_hotel ph,
            t_hotel h
        WHERE
         p.tender_org_id = o.org_id
        and p.project_id = ph.project_id
        and h.hotel_id = ph.hotel_id
        and h.is_active=1
        and (ph.hotel_group_approve_status = 0 || ph.hotel_group_approve_status = 2 || ph.hotel_group_approve_status IS NULL)
        <if test="hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
            AND ((ph.BID_ORG_ID = #{hotelGroupOrgId} AND ph.BID_ORG_TYPE = 4)
            OR (h.hotel_brand_id IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach> AND ph.BID_ORG_TYPE = 2)
            )
        </if>
        GROUP BY ph.BID_STATE
    </select>

    <select id="queryNoBidCount"  resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM t_project_intent_hotel_group g,
            t_project p,
            t_org o,
            t_project_intent_hotel ph,
            t_hotel h
        WHERE g.project_id = p.project_id
        AND p.tender_org_id = o.org_id
        AND g.hotel_group_org_id = #{hotelGroupOrgId}
        AND p.project_id = ph.project_id
        AND h.hotel_id = ph.hotel_id
        AND h.is_active=1
        AND ph.bid_state = 0
        AND ph.HOTEL_GROUP_APPROVE_STATUS IS NULL
        AND ph.INVITE_STATUS = 1
        AND p.project_state = 1
        <if test="employeeUserId != null">
            and g.hotel_group_contact_uid = #{employUserId}
        </if>
        <if test="hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
            AND h.hotel_brand_id IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach>
        </if>
    </select>


    <select id="queryProjectOverview"
            parameterType="com.fangcang.grfp.core.vo.request.hotelgroup.QueryProjectOverviewRequest"
            resultType="com.fangcang.grfp.core.vo.response.hotelgroup.QueryProjectOverviewResponse">
        select
                p.project_id as projectId,
                p.project_name as projectName,
                o.org_name as orgName,
                p.project_type as projectType,
                p.tender_type as tenderType,
                p.tender_count as tenderCount,
                p.bid_start_time as bidStartTime,
                p.bid_end_time as bidEndTime,
                p.first_bid_start_time as firstBidStartTime,
                p.first_bid_end_time as firstBidEndTime,
                p.second_bid_start_time as secondBidStartTime,
                p.second_bid_end_time as secondBidEndTime,
                p.third_bid_start_time as thirdBidStartTime,
                p.third_bid_end_time as thirdBidEndTime,
                g.hotel_group_contact_name as hotelGroupContactName,
                p.project_state as projectState,
                g.project_intent_hotel_group_id as projectIntentHotelGroupId,
                P.PRICE_MONITOR_START_DATE as priceMonitorStartDate,
                p.PRICE_MONITOR_END_DATE as priceMonitorEndDate,
                p.DISPLAY_ORDER AS displayOrder,
                g.IS_OPEN_GROUP_APPROVE AS isOpenGroupApprove,
                g.IS_BRAND_LIMIT AS isBrandLimit
        from t_project_intent_hotel_group g,
            t_project p,
            t_org o
        where g.project_id = p.project_id
        and p.tender_org_id = o.org_id
        and g.is_active=1
        and g.hotel_group_org_id = #{query.orgId}
        <if test="query.searchProjectName != null and query.searchProjectName !=''">
            and p.project_name like concat('%',#{query.searchProjectName},'%')
        </if>
        <if test="query.searchOrgName != null and query.searchOrgName !=''">
            and o.org_name like concat('%',#{query.searchOrgName},'%')
        </if>
        <if test="query.projectState == null or query.projectState == 0">
            and p.project_state != 0
        </if>
        <if test="query.projectState != null and query.projectState != 0">
            and p.project_state = #{query.projectState}
        </if>
        <if test="query.employeeUserId != null">
            and g.hotel_group_contact_uid = #{query.employeeUserId}
        </if>
        <if test="query.quoteStatus != null and query.quoteStatus == -1">
            and p.project_state = 1
        </if>
        order by p.display_order desc, p.create_time desc
    </select>

    <select id="queryProjectBidInfo"
            parameterType="com.fangcang.grfp.core.vo.request.hotelgroup.QueryProjectOverviewRequest"
            resultType="com.fangcang.grfp.core.vo.response.hotelgroup.QueryProjectOverviewResponse">
        SELECT
            p.project_id as projectId,
            p.project_name as projectName,
            o.org_name as orgName,
            p.tender_type as tenderType,
            p.project_type as projectType,
            p.tender_count as tenderCount,
            p.first_bid_start_time as firstBidStartTime,
            p.first_bid_end_time as firstBidEndTime,
            p.second_bid_start_time as secondBidStartTime,
            p.second_bid_end_time as secondBidEndTime,
            p.third_bid_start_time as thirdBidStartTime,
            p.third_bid_end_time as thirdBidEndTime,
            p.project_state as projectState,
            h.hotel_id as hotelId,
            h.city_code as cityCode,
            h.hotel_brand_id as hotelBrandId,
            ph.project_intent_hotel_id as projectIntentHotelId,
            ph.hotel_bid_contact_name as hotelBidContactName,
            ph.hotel_bid_contact_mobile as hotelBidContactMobile,
            ph.hotel_bid_contact_email as hotelBidContactEmail,
            ph.HOTEL_GROUP_BID_CONTACT_NAME as hotelGroupBidContactName,
            ph.HOTEL_GROUP_BID_CONTACT_MOBILE as hotelGroupBidContactMobile,
            ph.HOTEL_GROUP_BID_CONTACT_EMAIL as hotelGroupBidContactEmail,
            ph.hotel_sales_contact_name as hotelSalesContactName,
            ph.hotel_sales_contact_mobile as hotelSalesContactMobile,
            ph.hotel_sales_contact_email as hotelSalesContactEmail,
            ph.BID_ORG_TYPE as bidOrgType,
            ph.BID_STATE as bidState,
            ph.HOTEL_GROUP_APPROVE_STATUS as hotelGroupApproveStatus,
            P.PRICE_MONITOR_START_DATE as priceMonitorStartDate,
            p.PRICE_MONITOR_END_DATE as priceMonitorEndDate
        FROM t_project p,
            t_org o,
            t_project_intent_hotel ph,
            t_hotel h
        WHERE
        p.tender_org_id = o.org_id
        and p.project_id = ph.project_id
        and h.hotel_id = ph.hotel_id
        and h.is_active=1
        <if test="query.searchProjectName != null and query.searchProjectName !=''">
            and p.project_name like concat(concat('%',#{query.searchProjectName}),'%')
        </if>
        <if test="query.searchOrgName != null and query.searchOrgName !=''">
            and o.org_name like concat(concat('%',#{query.searchOrgName}),'%')
        </if>
        <if test="query.projectState == null or query.projectState == 0">
            and p.project_state != 0
        </if>
        <if test="query.projectState != null and query.projectState != 0">
            and p.project_state = #{query.projectState}
        </if>
        <if test="query.quoteStatus != null and query.quoteStatus > -1">
            and ph.bid_state = #{query.quoteStatus}
        </if>
        <if test="query.quoteStatus == 0 and query.hotelGroupApproveStatus == null">
            AND ph.INVITE_STATUS = 1
            <if test="query.hotelGroupBrandIdList != null and query.hotelGroupBrandIdList.size() > 0">
                AND h.hotel_brand_id IN
                <foreach collection="query.hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                    #{hotelGroupBrandId}
                </foreach>
            </if>
            and p.project_state = 1
            and ph.HOTEL_GROUP_APPROVE_STATUS IS NULL
        </if>
        <if test="query.hotelGroupApproveStatus != null">
            and ph.HOTEL_GROUP_APPROVE_STATUS = #{query.hotelGroupApproveStatus}
        </if>
        <if test="query.quoteStatus !=0 and query.hotelGroupBrandIdList != null and query.hotelGroupBrandIdList.size() > 0">
            AND ((ph.BID_ORG_ID = #{query.orgId} AND ph.BID_ORG_TYPE = 4)
            OR (h.hotel_brand_id IN
            <foreach collection="query.hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach> AND ph.BID_ORG_TYPE = 2)
            )
        </if>
        <if test="query.hotelId != null">
            and ph.hotel_id = #{query.hotelId}
        </if>
        <if test="query.hotelBrandId != null and query.hotelBrandId > 0">
            and h.hotel_brand_id = #{query.hotelBrandId}
        </if>
        <if test="query.bidOrgType != null">
            and ph.bid_org_type = #{query.bidOrgType}
        </if>
        order by p.display_order DESC, p.create_time desc
    </select>

    <select id="queryHotelGroupProjectIntentGroup" resultType="com.fangcang.grfp.core.entity.ProjectIntentHotelGroupEntity">
        SELECT * FROM t_project_intent_hotel_group WHERE hotel_group_org_id = #{hotelGroupOrgId} AND project_id = #{projectId}
    </select>

    <select id="queryNeedApproveHotelGroupOrgId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT
            HOTEL_GROUP_ORG_ID
        FROM
            t_project_intent_hotel_group
        WHERE
            PROJECT_ID = #{projectId}
          AND IS_ACTIVE = 1
          AND IS_OPEN_GROUP_APPROVE = 1
    </select>
</mapper>
