package com.fangcang.grfp.api.controller.poi;

import com.fangcang.grfp.api.controller.poi.service.PoiService;
import com.fangcang.grfp.api.controller.poi.vo.AddPoiRequest;
import com.fangcang.grfp.api.controller.poi.vo.AddPoiResponse;
import com.fangcang.grfp.api.controller.poi.vo.UpdatePoiRequest;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.IdRequest;
import com.fangcang.grfp.core.base.IdVO;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.constant.UserPermission;
import com.fangcang.grfp.core.usersession.RequiresPermissions;
import com.fangcang.grfp.core.vo.request.poi.ListPoiRequest;
import com.fangcang.grfp.core.vo.response.poi.ListPoiVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;

@Api(tags="POI 接口")
@RestController
@RequestMapping("/Api/Poi")
public class PoiController {

    @Resource
    private PoiService poiService;

    @ApiOperation("新增 POI")
    @PostMapping("/Add")
    @UserAuditLog("POI-新增 POI")
    @RequiresPermissions(UserPermission.POI)
    public Result<AddPoiResponse> add(@Valid @RequestBody AddPoiRequest req) {
        return Result.ok(poiService.addPoi(req));
    }

    @ApiOperation("删除 POI")
    @PostMapping("/Delete")
    @UserAuditLog("POI-删除 POI")
    @RequiresPermissions(UserPermission.POI)
    public Result<Void> delete(@Valid @RequestBody IdRequest<Long> req) {
        poiService.deletePoi(req.getId());
        return Result.ok();
    }

    @ApiOperation("更新 POI")
    @PostMapping("/Update")
    @UserAuditLog("POI-更新 POI")
    @RequiresPermissions(UserPermission.POI)
    public Result<Void> update(@Valid @RequestBody UpdatePoiRequest req) {
        poiService.updatePoi(req);
        return Result.ok();
    }

    @ApiOperation("查询 POI 列表")
    @PostMapping("/List")
    @RequiresPermissions(UserPermission.POI)
    public Result<PageVO<ListPoiVO>> list(@Valid @RequestBody ListPoiRequest req) {
        return Result.ok(poiService.queryPoiList(req));
    }

    @ApiOperation("导入 POI")
    @PostMapping(value = "/Import")
    @UserAuditLog("POI-导入 POI")
    @RequiresPermissions(UserPermission.POI)
    public Result<IdVO<Long>> importPoi(@RequestParam("file") MultipartFile file) throws IOException {
        return Result.ok(poiService.importPoi(file));
    }

}
