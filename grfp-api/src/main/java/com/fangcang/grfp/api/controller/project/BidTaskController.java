package com.fangcang.grfp.api.controller.project;

import com.fangcang.grfp.api.controller.project.service.BidTaskService;
import com.fangcang.grfp.api.controller.project.vo.*;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.IdRequest;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.constant.UserPermission;
import com.fangcang.grfp.core.enums.OrgTypeEnum;
import com.fangcang.grfp.core.usersession.RequiresPermissions;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.vo.ImportBidTaskListVO;
import com.fangcang.grfp.core.vo.request.bid.ImportBidTaskListRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 报价任务 Controller
 */
@Api(tags = "报价任务")
@RestController
@RequestMapping("/Api/BidTask/")
public class BidTaskController {

    @Resource
    private BidTaskService bidTaskService;

    @ApiOperation("添加导入报价任务")
    @PostMapping("/AddImportBidTask")
    @UserAuditLog("报价任务-添加导入报价任务")
    @RequiresPermissions(UserPermission.BID_TASK_C_U_D)
    public Result<Void> addImportBidTask(@RequestBody @Valid AddImportBidTaskRequest request) {
        bidTaskService.addImportBidTask(request);
        return Result.ok();
    }

    @ApiOperation("删除导入报价任务")
    @PostMapping("/DeleteImportBidTask")
    @UserAuditLog("报价任务-删除导入报价任务")
    @RequiresPermissions(UserPermission.BID_TASK_C_U_D)
    public Result<Void> deleteImportBidTask(@RequestBody @Valid DeleteImportBidTaskRequest request) {
        UserSession userSession = UserSession.get();
        // 酒店和就酒店集团不能查看
        if (userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTEL.key ||
            userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTELGROUP.key) {
            AppUtility.serviceError(ErrorCode.PERMISSION_DENIED);
        }
        bidTaskService.deleteImportBidTask(request);
        return Result.ok();
    }

    @ApiOperation("查询导入报价任务详情")
    @PostMapping("/QueryImportBidTaskDetail")
    @RequiresPermissions(UserPermission.BID_TASK_C_U_D)
    public Result<ImportBidTaskDetailVO> queryImportBidTaskDetail(@RequestBody @Valid IdRequest<Long> request) {
        UserSession userSession = UserSession.get();
        // 酒店和就酒店集团不能查看
        if (userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTEL.key ||
            userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTELGROUP.key) {
            AppUtility.serviceError(ErrorCode.PERMISSION_DENIED);
        }
        return Result.ok(bidTaskService.queryImportBidTaskDetail(request.getId()));
    }

    @ApiOperation("更新导入报价任务")
    @PostMapping("/UpdateImportBidTask")
    @UserAuditLog("报价任务-更新导入报价任务")
    @RequiresPermissions(UserPermission.BID_TASK_C_U_D)
    public Result<Void> updateImportBidTask(@RequestBody @Valid UpdateBidTaskRequest request) {
        UserSession userSession = UserSession.get();
        // 酒店和就酒店集团不能查看
        if (userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTEL.key ||
            userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTELGROUP.key) {
            AppUtility.serviceError(ErrorCode.PERMISSION_DENIED);
        }
        bidTaskService.updateImportBidTask(request);
        return Result.ok();
    }

    @ApiOperation("批量生成报价")
    @PostMapping("/BatchGenerateBid")
    @UserAuditLog("报价任务-批量生成报价")
    @RequiresPermissions(UserPermission.BID_TASK_C_U_D)
    public Result<Void> batchGenerateBid(@RequestBody @Valid BatchGenerateBidRequest request) {
        UserSession userSession = UserSession.get();
        // 酒店和就酒店集团不能查看
        if (userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTEL.key ||
            userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTELGROUP.key) {
            AppUtility.serviceError(ErrorCode.PERMISSION_DENIED);
        }
        bidTaskService.batchGenerateBid(request);
        return Result.ok();
    }

    @ApiOperation("查询报价任务列表")
    @PostMapping("/QueryImportBidTaskList")
    @RequiresPermissions(UserPermission.BID_TASK_C_U_D)
    public Result<PageVO<ImportBidTaskListVO>> queryImportBidTaskList(@RequestBody @Valid ImportBidTaskListRequest request) {
        UserSession userSession = UserSession.get();
        // 酒店和就酒店集团不能查看
        if (userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTEL.key ||
            userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTELGROUP.key) {
            AppUtility.serviceError(ErrorCode.PERMISSION_DENIED);
        }
        return Result.ok(bidTaskService.queryImportBidTaskList(request));
    }

    @ApiOperation("导出报价任务列表")
    @PostMapping("/ExportImportBidTaskList")
    @UserAuditLog("报价任务-导出报价任务列表")
    @RequiresPermissions(UserPermission.BID_TASK_C_U_D)
    public void exportImportBidTaskList(@RequestBody @Valid ImportBidTaskListRequest request, HttpServletResponse httpServletResponse) {
        bidTaskService.exportImportBidTaskList(request, httpServletResponse);
    }

    @ApiOperation("查询报价任务报价信息")
    @PostMapping("/QueryImportBidTaskBidInfo")
    @RequiresPermissions(UserPermission.BID_TASK_C_U_D)
    public Result<ImportBidTaskBidInfoVO> queryImportBidTaskBidInfo(@RequestBody @Valid IdRequest<Long> request) {
        UserSession userSession = UserSession.get();
        // 酒店和就酒店集团不能查看
        if (userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTEL.key ||
            userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.HOTELGROUP.key) {
            AppUtility.serviceError(ErrorCode.PERMISSION_DENIED);
        }
        return Result.ok(bidTaskService.queryImportBidTaskBidInfo(request.getId()));
    }

}
