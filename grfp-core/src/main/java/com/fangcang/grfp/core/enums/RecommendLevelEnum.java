package com.fangcang.grfp.core.enums;

public enum RecommendLevelEnum {

    SSS(1, "SSS"),
    SS(2, "SS"),
    S(3, "S"),
    A(4, "A"),
    B(5, "B"),
    C(6, "C");

    public int key;

    public String value;
    RecommendLevelEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (RecommendLevelEnum recommendLevelEnum : RecommendLevelEnum.values()) {
            if (recommendLevelEnum.key == key) {
                value = recommendLevelEnum.value;
                break;
            }
        }
        return value;
    }
}
