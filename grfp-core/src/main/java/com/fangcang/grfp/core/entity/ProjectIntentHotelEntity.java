package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 项目酒店意向表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_intent_hotel")
public class ProjectIntentHotelEntity extends BaseVO {


    /**
     * 项目酒店意向ID
     */
    @TableId(value = "project_intent_hotel_id", type = IdType.AUTO)
    private Integer projectIntentHotelId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 酒店机构ID
     */
    @TableField("hotel_org_id")
    private Integer hotelOrgId;


    /**
     * 酒店指派销售跟进人id(招投标项目时指派的)
     */
    @TableField("hotel_contact_uid")
    private Integer hotelContactUid;

    /**
     * 酒店指派销售跟进人姓名(招投标项目时指派的)
     */
    @TableField("hotel_contact_name")
    private String hotelContactName;

    /**
     * 企业(分销商)跟进人电话(招投标项目时指派的)
     */
    @TableField("distributor_contact_uid")
    private Integer distributorContactUid;

    /**
     * 企业(分销商)跟进人电话(招投标项目时指派的)
     */
    @TableField("distributor_contact_name")
    private String distributorContactName;

    /**
     * 平台跟进人id
     */
    @TableField("platform_contact_uid")
    private Integer platformContactUid;

    /**
     * 平台跟进人姓名
     */
    @TableField("platform_contact_name")
    private String platformContactName;

    /**
     * 酒店销售联系人姓名(默认取推荐酒店表数据)
     */
    @TableField("hotel_sales_contact_name")
    private String hotelSalesContactName;

    /**
     * 酒店销售联系人电话
     */
    @TableField("hotel_sales_contact_mobile")
    private String hotelSalesContactMobile;

    /**
     * 酒店销售联系人电邮
     */
    @TableField("hotel_sales_contact_email")
    private String hotelSalesContactEmail;

    /**
     * 酒店报价联系人
     */
    @TableField("hotel_bid_contact_name")
    private String hotelBidContactName;

    /**
     * 酒店报价联系人手机号码
     */
    @TableField("hotel_bid_contact_mobile")
    private String hotelBidContactMobile;

    /**
     * 酒店报价联系人电邮
     */
    @TableField("hotel_bid_contact_email")
    private String hotelBidContactEmail;

    /**
     * 酒店报价联系人
     */
    @TableField("hotel_group_bid_contact_name")
    private String hotelGroupBidContactName;

    /**
     * 酒店报价联系人手机号码
     */
    @TableField("hotel_group_bid_contact_mobile")
    private String hotelGroupBidContactMobile;

    /**
     * 酒店报价联系人电邮
     */
    @TableField("hotel_group_bid_contact_email")
    private String hotelGroupBidContactEmail;

    /**
     * 平台报价跟进人姓名
     */
    @TableField("hotel_price_follow_name")
    private String hotelPriceFollowName;


    /**
     * 平台线上跟进人姓名
     */
    @TableField("online_follow_name")
    private String onlineFollowName;


    /**
     * 平台线上跟进人姓名
     */
    @TableField("monitor_follow_name")
    private String monitorFollowName;

    /**
     * 邀约邮件发送状态(0：未发送邀约邮件，1：已发送邀约邮件)
     */
    @TableField("send_mail_status")
    private Integer sendMailStatus;

    /**
     * 邀约状态(0：未邀请，1：已邀请)，企业主动邀请的记录默认就是已邀请，非企业邀请的酒店指派销售人时默认就是未邀请
     */
    @TableField("invite_status")
    private Integer inviteStatus;

    /**
     * 最近一次邀约时间
     */
    @TableField("last_invite_time")
    private Date lastInviteTime;

    /**
     * 标书状态(0：未投标，1：新标(酒店提交报价，企业还未处理)，2：议价中，3：已中标，4：已否决，5：放弃报价)
     */
    @TableField("bid_state")
    private Integer bidState;

    /**
     * 近一年采购间夜数
     */
    @TableField("last_year_room_night")
    private Integer lastYearRoomNight;

    /**
     * 采购均价
     */
    @TableField("tender_avg_price")
    private BigDecimal tenderAvgPrice;


    /**
     * 币种
     */
    @TableField("currency_code")
    private String currencyCode;


    /**
     * 投标总权重(投标时根据项目权重和酒店投标策略计算生成)
     */
    @TableField("bid_weight")
    private BigDecimal bidWeight;

    /**
     * 酒店服务分
     */
    @TableField("hotel_service_points")
    private BigDecimal hotelServicePoints;

    /**
     * 是否上传报价(0：否，1：是)
     */
    @TableField("is_upload")
    private Integer isUpload;

    /**
     * 报价导入源数据 1:加力,2:Lanyon
     */
    @TableField("bid_upload_source")
    private Integer bidUploadSource;

    /**
     * 报价机构ID
     */
    @TableField("bid_org_id")
    private Integer bidOrgId;

    /**
     * 报价机构类型 2:酒店，4:酒店集团
     */
    @TableField("bid_org_type")
    private Integer bidOrgType;

    /**
     * 酒店既然审核状态 0:不需要审核，1:等待审核，2:审核通过，3:审核未通过
     */
    @TableField("hotel_group_approve_status")
    private Integer hotelGroupApproveStatus;

    /**
     * 酒店集团拒绝原因
     */
    @TableField("hotel_group_reject_approve_reason")
    private String hotelGroupRejectApproveReason;


    /**
     * 员工权益
     */
    @TableField("employee_right")
    private String employeeRight;


    /**
     * 员工权益上传文件
     */
    @TableField("employee_right_file_url")
    private String employeeRightFileUrl;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @TableField("reject_negotiation_remark")
    private String rejectNegotiationRemark;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 通知状态 1:已经通知, 0:待通知，null:不通知
     */
    @TableField("notify_status")
    private Integer notifyStatus;

    /**
     * 通知人
     */
    @TableField("notify_operator")
    private String notifyOperator;

    /**
     * 通知时间
     */
    @TableField("notify_time")
    private Date notifyTime;


}
