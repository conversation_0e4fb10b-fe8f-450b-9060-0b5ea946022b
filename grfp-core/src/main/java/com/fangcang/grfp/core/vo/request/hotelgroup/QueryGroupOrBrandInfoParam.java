package com.fangcang.grfp.core.vo.request.hotelgroup;

import com.fangcang.grfp.core.base.PageQuery;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/9 17:57
 */
@Getter
@Setter
public class QueryGroupOrBrandInfoParam extends PageQuery {

    //集团id
    private Collection<Long> groupIdList;

    //集团或品牌名称
    private String groupOrBrandName;

    //0代表查集团 ，1代表查名牌
    private int queryType;

}
