package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("酒店房档价格组信息")
@Getter
@Setter
public class BidHotelPriceGroupVO extends BaseVO {

    @ApiModelProperty("酒店价格房档ID 新增为null")
    private Integer hotelPriceLevelId;

    @ApiModelProperty("酒店价格组ID 新增为null")
    private Integer hotelPriceGroupId;

    @ApiModelProperty("酒店房档价格信息")
    @NotNull
    private List<BidHotelPriceVO> bidHotelPriceList;

    @ApiModelProperty("适用星期，按国际规范，星期天是第1天")
    @NotBlank
    private String applicableWeeks;

    @ApiModelProperty("lra承诺：1-是，0-否")
    @NotNull
    private Integer lra;

    @ApiModelProperty("是否包含早餐：1-是，0-否")
    @NotNull
    private Integer isIncludeBreakfast;

    @ApiModelProperty("是否被锁定")
    private Integer isLocked;

    @ApiModelProperty("备注")
    private String remark;
}
