package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 酒店数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_hotel_data")
public class HotelDataEntity extends BaseVO {


    /**
     * 酒店ID
     */
    @TableId(value = "hotel_id", type = IdType.ASSIGN_ID)
    private Long hotelId;

    /**
     * 数据(英文)
     */
    @TableField("data_en_us")
    private String dataEnUs;

    /**
     * 数据(中文)
     */
    @TableField("data_zh_cn")
    private String dataZhCn;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
