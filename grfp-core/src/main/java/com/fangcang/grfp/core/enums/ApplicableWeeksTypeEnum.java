package com.fangcang.grfp.core.enums;

import com.fangcang.grfp.core.util.GenericAppUtility;
import com.fangcang.grfp.core.util.StringUtil;

/**
 * 适用星期枚举类，按国际规范，星期天是第1天
 * @auther chenjian<PERSON>
 * @date 2022/11/3
*/
public enum ApplicableWeeksTypeEnum {
    ONE(2, "一"),
    TWO(3, "二"),
    THREE(4, "三"),
    FOUR(5, "四"),
    FIVE(6, "五"),
    SIX(7, "六"),
    SEVEN(1, "日");

    public int key;
    public String value;


    private ApplicableWeeksTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static int getKeyByValue(String value) {
        int key = 0;
        for (ApplicableWeeksTypeEnum assureStateEnum : ApplicableWeeksTypeEnum.values()) {
            if (assureStateEnum.value.equals(value)) {
                key = assureStateEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        for (ApplicableWeeksTypeEnum assureStateEnum : ApplicableWeeksTypeEnum.values()) {
            if (assureStateEnum.key == key) {
                value = assureStateEnum.value;
                break;
            }
        }
        return value;
    }

    public static ApplicableWeeksTypeEnum getEnumByKey(int key) {
        ApplicableWeeksTypeEnum assureStateEnum = null;
        for (ApplicableWeeksTypeEnum assureState : ApplicableWeeksTypeEnum.values()) {
            if (assureState.key == key) {
                assureStateEnum = assureState;
                break;
            }
        }
        return assureStateEnum;
    }

    public int getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getApplicableWeeksDesc(int languageId, String weekDays) {
        String applicableWeeksDesc = "";
        if (StringUtil.isValidString(weekDays)) {
            String[] split = weekDays.split(",");
            StringBuffer buffer = new StringBuffer();
            for (String s : split) {
                String valueByKey = GenericAppUtility.getText(languageId, "WEEK_DAY_" + s);
                buffer.append(valueByKey + ",");
            }
            applicableWeeksDesc = buffer.substring(0, buffer.length() - 1);
        }
        return applicableWeeksDesc;
    }

}
