package com.fangcang.grfp.api.controller.poi.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@ApiModel(description = "新增 POI 响应")
@AllArgsConstructor
@NoArgsConstructor
public class AddPoiResponse extends BaseVO {

    private static final long serialVersionUID = 1666183928454920635L;

    @ApiModelProperty(value = "POI ID", required = true)
    private Long poiId;

}
