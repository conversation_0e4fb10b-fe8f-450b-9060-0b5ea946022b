<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <property name="spring.application.name" value="grfp-server"/>

    <property name="logDir" value="./logs/grfp-server"/>
    <!-- 日志输出格式 -->
    <property name="pattern" value="%date{yyyy-MM-dd HH:mm:ss.SSS}|%thread|%5p|%logger{50}.%method:%line|%msg %ex [%X{traceId}] ------%n"/>
    <!-- 设置文件大小切片 -->
    <property name="maxFileSize" value="50MB"/>
    <!-- 设置总容量回滚 -->
    <property name="totalSizeCap" value="300M"/>
    <!-- 设置最大历史文件保留天数 -->
    <property name="maxHistory" value="3"/>

    <!-- 服务器环境 -->

    <!-- 定义全部日志输出格式与位置 -->
    <appender name="commonAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>${pattern}</Pattern>
            <charset>utf8</charset>
        </encoder>
        <!-- 定义全部日志输出位置与文件名 -->
        <file>${logDir}/info.log</file>
        <!-- 定义日志切片后存放的位置 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logDir}/info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <!-- 定义错误日志输出 -->
    <appender name="errorAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>${pattern}</Pattern>
            <charset>utf8</charset>
        </encoder>
        <!-- 定义错误日志输出文件与位置 -->
        <file>${logDir}/error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <!-- 定义日志切片后存放的位置 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logDir}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
    </appender>


  <!-- 定义非指定包全部统一info级别输出日志 -->
    <root level="info">
        <appender-ref ref="commonAppender"/>
        <appender-ref ref="errorAppender"/>
    </root>

    <logger name="com.fangcang.grfp.core.mapper" level="debug" additivity="false">
        <appender-ref ref="commonAppender" />
    </logger>

    <!-- 定义控制台输出 -->
    <appender name="stdoutAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${pattern}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="stdoutAppender"/>
    </root>
</configuration>