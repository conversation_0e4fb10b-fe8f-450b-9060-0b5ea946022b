package com.fangcang.grfp.core.enums;

/**
 * 导入推荐酒店分类
 */
public enum RecommendHotelNameEnum {

    TOTAL_RECOMMEND("TotalRecommendHotel", "可邀约酒店总数"),
    FREQUENCY_RECOMMEND("FrequencyRecommendHotel", "高频预订邀约推荐"),
    FREQUENCY_SAME_LEVEL_RECOMMEND("FrequencySameLevelRecommendHotel", "高频预订同档推荐"),
    POI_NEAR_HOTEL_RECOMMEND("PoiNearHotelRecommendHotel", "POI周边优质酒店邀约推荐"),
    NO_POI_HOT_AREA_RECOMMEND("NoPoiHotAreaRecommendHotel", "非POI热订区域邀约推荐"),
    AREA_GATHER_RECOMMEND("AreaGatherRecommendHotel", "散布聚量邀约推荐"),
    HIGH_QUALITY_RECOMMEND("HighQualityRecommendHotel", "优质商旅酒店推荐"),
    SAVED_HOTEL_RECOMMEND("SavedHotelRecommendHotel", "节省明星酒店推荐");


    public String key;

    public String value;

    RecommendHotelNameEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(String key) {
        String value = null;
        for (RecommendHotelNameEnum recommendHotelNameEnum : RecommendHotelNameEnum.values()) {
            if (recommendHotelNameEnum.key.equals(key)) {
                value = recommendHotelNameEnum.value;
                break;
            }
        }
        return value;
    }
}
