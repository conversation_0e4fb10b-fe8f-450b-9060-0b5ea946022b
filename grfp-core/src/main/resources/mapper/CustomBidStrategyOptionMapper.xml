<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.CustomBidStrategyOptionMapper">
    <insert id="batchUpsert">
        insert into t_custom_bid_strategy_option (option_id, strategy_id, project_intent_hotel_id, hotel_id, project_id, option_name, is_support, creator)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.optionId}, #{item.strategyId}, #{item.projectIntentHotelId}, #{item.hotelId}, #{item.projectId}, #{item.optionName}, #{item.isSupport}, #{item.creator})
        </foreach>
        on duplicate key update option_name = values(option_name),
                                is_support  = values(is_support),
                                modifier    = values(modifier)
    </insert>
</mapper>
