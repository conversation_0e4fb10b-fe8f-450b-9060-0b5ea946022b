package com.fangcang.grfp.core.remote.currency.manager;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fangcang.grfp.core.cached.CachedSysConfigService;
import com.fangcang.grfp.core.constant.SysConfig;
import com.fangcang.grfp.core.remote.currency.dto.CurrencyResponse;
import com.fangcang.grfp.core.remote.currency.response.BaseResponse;
import com.fangcang.grfp.core.util.JsonUtil;
import com.fangcang.grfp.core.vo.request.QueryCurrencyExchangeRateRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 调用TMC Hub 接口
 */
@Component
@Slf4j
public class CurrencyApiManager {

    @Autowired
    private CachedSysConfigService cachedSysConfigService;

    /**
     * Query USD to other Currency exchangeRate
     */
    public BaseResponse<CurrencyResponse> queryCurrencyExchangeRate(QueryCurrencyExchangeRateRequest request) {
        return this.executeSingleCurrencyApiRequest(request, CurrencyResponse.class);
    }

    /**
     * Generic execute method
     */
    private <T, R> BaseResponse<R> executeSingleCurrencyApiRequest(QueryCurrencyExchangeRateRequest request, Class<R> responseClass) {

        // Build base api request
        String url = cachedSysConfigService.getValue(SysConfig.API_CURRENCY_EXCHANGE_URL);
        String path = "/exchange/single";
        StringBuilder urlBuilder = null;
        urlBuilder = new StringBuilder(url + path);
        urlBuilder.append("?currency=" + request.getCurrency());

        String appCode = cachedSysConfigService.getValue(SysConfig.API_CURRENCY_EXCHANGE_APP_CODE);
        log.info("开始调用币种汇率接口，请求为：" + urlBuilder.toString());

        String response = "";
        // Request currency
        try (HttpResponse httpResponse = sendGetRequest(urlBuilder.toString(), appCode)) {

            // Handle failed responses
            if (!httpResponse.isOk()) {
                log.error("Failed to call Currency API, Status code: {}", httpResponse.getStatus());
                return new BaseResponse<>();
            }
            log.info(httpResponse.body());
            // Deserialization response
            return JsonUtil.fromJson(httpResponse.body(), BaseResponse.class, responseClass);
        } catch (Throwable e) {
            log.error("Exception occurred while calling Currency API, ", e);
            return new BaseResponse<>();
        }
    }

    /**
     * Send Get request
     */
    private HttpResponse sendGetRequest(String url, String appCode) {
        return HttpRequest.get(url)
                .header("Content-Type", "application/json")
                .header("Authorization", "APPCODE "+ appCode)
                .execute();
    }

}
