package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 价格不可用日期
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_price_unapplicable_day")
public class PriceUnapplicableDayEntity extends BaseVO {


    /**
     * 项目不可以日期ID
     */
    @TableId(value = "price_unapplicable_day_id", type = IdType.AUTO)
    private Integer priceUnapplicableDayId;

    /**
     * 项目意向酒店ID
     */
    @TableField("project_intent_hotel_id")
    private Integer projectIntentHotelId;

    /**
     * 项目id
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店id
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 开始时间
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 结束时间
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
