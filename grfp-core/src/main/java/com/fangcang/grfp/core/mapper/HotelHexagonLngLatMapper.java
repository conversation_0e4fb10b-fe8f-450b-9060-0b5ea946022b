package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.HotelHexagonLngLatEntity;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 酒店六边形坐标 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface HotelHexagonLngLatMapper extends BaseMapper<HotelHexagonLngLatEntity> {

    HotelHexagonLngLatEntity selectByHotelId(Long hotelId);

    void updateByHotelId(@Param("entity") HotelHexagonLngLatEntity hotelHexagonLngLat);
}
