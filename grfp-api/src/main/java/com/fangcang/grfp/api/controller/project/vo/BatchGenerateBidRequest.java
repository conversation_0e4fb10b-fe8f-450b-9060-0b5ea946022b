package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.util.Set;

@Getter
@Setter
@ApiModel(description = "批量生成报价请求")
public class BatchGenerateBidRequest extends BaseVO {

    @ApiModelProperty(value = "任务 id", required = true)
    @NotEmpty
    private Set<Long> taskIds;

}
