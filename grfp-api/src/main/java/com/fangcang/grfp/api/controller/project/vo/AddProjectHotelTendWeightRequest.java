package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@ApiModel(description = "新增或修改酒店项目招标权重配置请求")
public class AddProjectHotelTendWeightRequest extends BaseVO {

    private static final long serialVersionUID = 7541909963450953440L;

    @ApiModelProperty(value = "项目ID", required = true)
    private Integer projectId;

    @ApiModelProperty(value = "自定义采购策略")
    @Valid
    private List<UpdateCustomTendStrategyWeightRequest> projectCustomTendStrategies;

    @ApiModelProperty(value = "间夜量权重（不少于120间夜）")
    private BigDecimal whtRoomNight = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否启用间夜量权重")
    private Integer whtRoomNightState = 0;

    @ApiModelProperty(value = "间夜量(额外加分项)权重（不少于600间夜）")
    private BigDecimal whtRoomNightEx = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否启用间夜量(额外加分项)权重")
    private Integer whtRoomNightExState = 0;

    @ApiModelProperty(value = "城市权重")
    private BigDecimal whtCity = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否启用城市权重")
    private Integer whtCityState = 0;

    @ApiModelProperty(value = "位置权重")
    private BigDecimal whtLocation = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否启用位置权重")
    private Integer whtLocationState = 0;

    @ApiModelProperty(value = "价格优势权重（低于15%）")
    private BigDecimal whtPriceAdvantage = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否启用价格优势权重")
    private Integer whtPriceAdvantageState = 0;

    @ApiModelProperty(value = "价格优势(额外加分项)权重（低于25%）")
    private BigDecimal whtPriceAdvantageEx = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否启用价格优势(额外加分项)权重")
    private Integer whtPriceAdvantageExState = 0;

    @ApiModelProperty(value = "ota评分权重")
    private BigDecimal whtOtaScore = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否启用ota评分权重")
    private Integer whtOtaScoreState = 0;

    @ApiModelProperty(value = "公司统一支付权重")
    private BigDecimal whtCoPay = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否启用公司统一支付权重")
    private Integer whtCoPayState = 0;

    @ApiModelProperty(value = "早餐权重")
    private BigDecimal whtBreakfast = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否启用早餐权重")
    private Integer whtBreakfastState = 0;

    @ApiModelProperty(value = "lra权重")
    private BigDecimal whtLra = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否启用lra权重")
    private Integer whtLraState = 0;

    @ApiModelProperty(value = "退改规则权重")
    private BigDecimal whtCancel = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否启用退改规则权重（注意字段名拼写一致性）")
    private Integer whtCancelState = 0;

    @ApiModelProperty(value = "退改规则(额外加分项)权重")
    private BigDecimal whtTotalWeight;

}
