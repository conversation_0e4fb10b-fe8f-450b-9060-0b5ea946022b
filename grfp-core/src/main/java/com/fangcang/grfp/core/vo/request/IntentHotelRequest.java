package com.fangcang.grfp.core.vo.request;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @auther cjh
 * @description
 * @date 2022/10/21
 */
@Data
@ApiModel("检查报价项目信息请求")
public class IntentHotelRequest extends BaseVO {


    // Key: projectId_hotelId
    @ApiModelProperty("项目酒店ID参数 projectId_hotelId")
    private List<String> projectIntentHotelParamList;

    //酒店机构id或酒店集团机构id
    @ApiModelProperty("酒店机构id或酒店集团机构id")
    private Long supplyOrgId;


}
