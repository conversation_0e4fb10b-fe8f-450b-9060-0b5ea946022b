javax.validation.constraints.NotBlank.message = VALIDATE_REQUEST_PARAMETER_CANNOT_BE_EMPTY
javax.validation.constraints.NotEmpty.message = VALIDATE_REQUEST_PARAMETER_CANNOT_BE_EMPTY
javax.validation.constraints.NotNull.message = VALIDATE_REQUEST_PARAMETER_CANNOT_BE_NULL
javax.validation.constraints.Email.message = VALIDATE_REQUEST_PARAMETER_EMAIL_ERROR
javax.validation.constraints.Size.message=VALIDATE_REQUEST_PARAMETER_LENGTH_MUST_BE_BETWEEN_X_AND_Y
javax.validation.constraints.InCodeTextEnum.message=VALIDATE_REQUEST_ENUM_PARAMETER_ERROR
javax.validation.constraints.Min.message=VALIDATE_REQUEST_PARAMETER_MUST_BE_GREATER_THAN_OR_EQUAL_TO_X
javax.validation.constraints.Max.message=VALIDATE_REQUEST_PARAMETER_MUST_BE_LESS_THAN_OR_EQUAL_TO_X
javax.validation.constraints.Pattern.message=VALIDATE_REQUEST_PARAMETER_MUST_MATCH_PATTERN
org.hibernate.validator.constraints.Length.message=VALIDATE_REQUEST_PARAMETER_LENGTH_MUST_BE_BETWEEN_X_AND_Y
org.hibernate.validator.constraints.Range.message=VALIDATE_REQUEST_PARAMETER_RANGE_MUST_BE_BETWEEN_X_AND_Y

error.sys.SystemError=SYSTEM_ERROR
error.sys.RequestParameterError=REQUEST_PARAMETER_ERROR
error.sys.SizeIsTooLarge=SIZE_IS_TOO_LARGE