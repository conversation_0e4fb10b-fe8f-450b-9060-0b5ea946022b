package com.fangcang.grfp.api.util;

import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.dto.dhub.response.Response;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.DestinationResponse;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.enums.OrgTypeEnum;
import com.fangcang.grfp.core.oss.OssManager;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.GenericAppUtility;
import com.fangcang.grfp.core.util.IntegerUtility;
import com.fangcang.grfp.core.util.StringUtility;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

public class AppUtility extends GenericAppUtility {


	private static OssManager ossManager;

	// ---------------------------------------------------------------------------------------------------- Public Static Method

	public static void setCosManager(OssManager ossManager) {
		AppUtility.ossManager = ossManager;
	}

	public static int getRequestHeaderLanguage(HttpServletRequest request) {
		return IntegerUtility.parseNullOrEmptyString(request.getHeader(HTTP_HEADERS_LANGUAGE), LanguageEnum.EN_US.key);
	}



	public static String getFileKeyFromUrl(String url){
		String key = ossManager.getKeyFromUrl(url);
		if(ossManager.isTempUrl(url)){
			ossManager.tempToPublic(key);
		}
		return key;
	}


	public static void doUpdateOneRecord(int updateCount){
		if(updateCount != 1){
			AppUtility.serviceError(ErrorCode.UPDATE_FAILED);
		}
	}

	public static void doInsert(int insertCount){
		if(insertCount != 1){
			AppUtility.serviceError(ErrorCode.ADD_FAILED);
		}
	}

	public static void doDelete(int deleteCount){
		if(deleteCount != 1){
			AppUtility.serviceError(ErrorCode.DELETE_FAILED);
		}
	}

	public static boolean isTmcHubApiResponseSucceeded(Response<DestinationResponse> response){
		return response != null && RfpConstant.TMC_HUB_SUCCESS_RESULT_CODE.equals( response.getReturnCode())
				&& response.getBussinessResponse() != null;
	}

	public static void isPlatformUser(UserSession userSession){
		if(!Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.PLATFORM.key)){
			AppUtility.serviceError(ErrorCode.NO_PERMISSION_ACCESS);
		}
	}


}
