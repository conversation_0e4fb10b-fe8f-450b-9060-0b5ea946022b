package com.fangcang.grfp.api.controller.user;

import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.user.service.UserService;
import com.fangcang.grfp.api.controller.user.vo.*;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.constant.UserPermission;
import com.fangcang.grfp.core.usersession.RequiresPermissions;
import com.fangcang.grfp.core.vo.ListUserVO;
import com.fangcang.grfp.core.vo.request.QueryListUserPageRequest;
import com.fangcang.grfp.core.vo.request.SystemOperateRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;


@Api(tags = "用户管理")
@RestController
@RequestMapping("/Api/User")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    @ApiOperation("用户列表")
    @PostMapping("/List")
    @ResponseBody
    @RequiresPermissions(UserPermission.USER)
    public Result<PageVO<ListUserVO>> list(HttpServletRequest request, @Valid @RequestBody QueryListUserPageRequest query) {
        return Result.ok(userService.queryUserVoPageList(request, query));
    }

    @ApiOperation("启用/停用")
    @PostMapping("/UpdateState")
    @ResponseBody
    @RequiresPermissions(UserPermission.USER_C_U_D)
    @UserAuditLog("用户管理-启用/停用")
    public Result<Void> updateState(HttpServletRequest request, @Valid @RequestBody UpdateUserStateRequest updateUserStateRequest) {
        userService.updateUserState(request, updateUserStateRequest);
        return Result.ok();
    }

    @ApiOperation("新增用户")
    @PostMapping("/AddUser")
    @ResponseBody
    @UserAuditLog("用户管理-新增用户")
    @RequiresPermissions(UserPermission.USER_C_U_D)
    public Result<Void> addUser(HttpServletRequest request, @Valid @RequestBody AddUserRequest addUserRequest) {
        userService.addUser(request, addUserRequest);
        return Result.ok();
    }

    @ApiOperation("修改用户")
    @PostMapping("/UpdateUser")
    @ResponseBody
    @UserAuditLog("用户管理-修改用户")
    @RequiresPermissions(UserPermission.USER_C_U_D)
    public Result<Void> updateUser(HttpServletRequest request, @Valid @RequestBody UpdateUserRequest updateUserRequest) {
        userService.updateUser(request, updateUserRequest);
        return Result.ok();
    }

    @ApiOperation("删除用户")
    @PostMapping("/DeleteUser")
    @ResponseBody
    @RequiresPermissions(UserPermission.USER_C_U_D)
    @UserAuditLog("用户管理-删除用户")
    public Result<Void> deleteUser(HttpServletRequest request, @Valid @RequestBody DeleteUserRequest deleteUserRequest) {
        userService.delete(request, deleteUserRequest);
        return Result.ok();
    }

    @ApiOperation("重置用户密码")
    @PostMapping("/ResetPassword")
    @ResponseBody
    @RequiresPermissions(UserPermission.USER_C_U_D)
    @UserAuditLog("用户管理-重置用户密码")
    public Result<Void> resetPassword(HttpServletRequest request, @Valid @RequestBody ResetUserPasswordRequest resetUserPasswordRequest) {
        userService.resetPassword(request, resetUserPasswordRequest);
        return Result.ok();
    }

    /**
     * 联想查询员工列表
     * @return
     */
    @ApiOperation("联想查询员工列表")
    @PostMapping("/queryUserList")
    @ResponseBody
    @RequestMapping(value = "queryUserList", method = RequestMethod.POST)
    public Result<List<ListUserVO>> queryUserList(@RequestBody QueryUserRequest queryUserRequest) {
        List<ListUserVO> listUserVOS = userService.queryUserList(queryUserRequest);
        return Result.ok(listUserVOS);
    }

    @ApiOperation("重置所有用户密码")
    @PostMapping("/ResetAllUserPwd")
    @ResponseBody
    @RequiresPermissions(UserPermission.USER_C_U_D)
    public Result<Void> resetAllUserPwd(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody SystemOperateRequest systemOperateRequest) {
        userService.resetAllPassword(request, systemOperateRequest);
        return Result.ok();
    }

}
