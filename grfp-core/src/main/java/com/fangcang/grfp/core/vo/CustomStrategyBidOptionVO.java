package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@ApiModel("报价策略选项")
public class CustomStrategyBidOptionVO extends BaseVO {

    @ApiModelProperty(value = "选项 id")
    private Long optionId;

    @ApiModelProperty(value = "选项名称")
    private String optionName;

    @ApiModelProperty(value = "是否支持: 1-支持 0-不支持")
    private Integer isSupport = 0;

}
