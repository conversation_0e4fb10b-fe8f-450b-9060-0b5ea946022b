<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectHotelHistoryDataMapper">
    <insert id="batchUpsert">
        insert into t_project_hotel_history_data (project_id, hotel_id, room_night_count, total_amount, is_uploaded, city_code, creator, modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectId}, #{item.hotelId}, #{item.roomNightCount}, #{item.totalAmount}, #{item.isUploaded}, #{item.cityCode}, #{item.creator}, #{item.modifier})
        </foreach>
        on duplicate key update room_night_count = values(room_night_count),
                                total_amount     = values(total_amount),
                                is_uploaded      = values(is_uploaded),
                                city_code        = values(city_code),
                                modifier         = values(modifier)
    </insert>

    <select id="selectByCondition" resultType="com.fangcang.grfp.core.vo.response.project.HotelHistoryTradeDataListVO">
        select t1.project_id     as projectid,
               t1.hotel_id       as hotelid,
               t2.hotel_star,
               t2.city_code,
               t1.room_night_count,
               t1.total_amount,
               t1.create_time,
               t1.creator,
               t1.recommend_level,
               t2.hotel_brand_id as hotelBrandId,
               t2.hotel_group_id as hotelGroupId
        from t_project_hotel_history_data t1
                 inner join t_hotel t2 on t1.hotel_id = t2.hotel_id and t2.is_active = 1
        <where>
            t1.project_id = #{req.projectId}
              and t1.is_uploaded = 1
            <if test="req.cityCode != null and req.cityCode != ''">
                and t2.city_code = #{req.cityCode}
            </if>
            <if test="req.hotelId != null and req.hotelId > 0">
                and t1.hotel_id = #{req.hotelId}
            </if>
            <if test="req.hotelBrandId != null and req.hotelBrandId > 0">
                and t2.hotel_brand_id = #{req.hotelBrandId}
            </if>
            <if test="req.hotelGroupId != null and req.hotelGroupId > 0">
                and t2.hotel_group_id = #{req.hotelGroupId}
            </if>
        </where>
        order by t1.room_night_count desc
    </select>

    <select id="queryHistoryProjectHotelList"  resultType="com.fangcang.grfp.core.vo.response.project.QueryHistoryProjectInfoResponse">
        SELECT
            phis.project_id AS projectId,
            phis.hotel_id AS hotelId,
            phis.room_night_count AS roomNightCount,
            phis.total_amount AS totalAmount,-- 成交金额
            phis.city_order AS cityOrder,
            phis.saved_amount AS savedAmount,
            phis.POI_ID AS poiId,
            phis.poi_distance AS poiDistance,
            phis.recommend_level AS recommendLevel,-- 推荐等级
            phis.adjust_lowest_price AS adjustLowestPrice,
            phis.total_violations_count AS totalViolationsCount,
            phis.ota_min_price AS otaMinPrice,
            phis.ota_max_price AS otaMaxPrice,
            phis.min_max_ota_price_date AS minMaxOtaPriceDate,
            phis.lowest_price AS lowestPrice,
            phis.lowest_price_date AS lowestPriceDate,
            phis.service_point AS servicePoint,
            phis.adjust_lowest_price AS adjustLowestPrice,
            th.city_code AS cityCode,
            th.name_en_us AS hotelNameEnUs,
            th.name_zh_cn AS hotelNameZhCn,
            th.lng_baidu AS lngBaidu,
            th.lat_baidu AS latBaidu,
            th.LNG_GOOGLE AS lngGoogle,
            th.lat_google AS latGoogle,
            th.RATING as rating,
            IFNULL(th.HOTEL_STAR, '') as hotelStar,
            th.opening_date as praciceDate
        FROM
            t_project_hotel_history_data phis
        left join t_hotel th on phis.hotel_id = th.hotel_id
        <if test="cityCode != null and cityCode != ''">
            AND th.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        WHERE
        phis.PROJECT_ID = #{projectId}
        <if test="hotelId != null and hotelId > 0">
            AND phis.hotel_id = #{hotelId,jdbcType=BIGINT}
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND phis.CITY_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="poiId != null and poiId > 0">
            AND phis.POI_ID = #{poiId,jdbcType=BIGINT}
        </if>
        <if test="hotelIdList != null and hotelIdList.size() > 0">
            AND phis.hotel_id IN
            <foreach collection="hotelIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and th.is_active=1
        AND phis.IS_UPLOADED = 1
        ORDER BY phis.room_night_count DESC
    </select>


    <update id="updateHistoryProjectDataStat">
        UPDATE t_project_hotel_history_data
        SET city_order        = #{req.cityOrder},
            city_code         = #{req.cityCode},
            saved_amount      = #{req.savedAmount},
            saved_amount_rate = #{req.savedAmountRate}
        WHERE project_id = #{req.projectId}
          AND hotel_id = #{req.hotelId}
    </update>

    <update id="updateHistoryProjectPoi">
        UPDATE t_project_hotel_history_data
        SET poi_id       = #{req.poiId},
            poi_distance = #{req.poiDistance, jdbcType=DECIMAL}
        WHERE project_id = #{req.projectId,jdbcType=BIGINT}
          AND hotel_id = #{req.hotelId,jdbcType=BIGINT}
    </update>

    <update id="updateLastStatReferenceNo">
        UPDATE t_project_hotel_history_data
        SET LAST_STAT_REFERENCE_NO = #{statReferenceNo},
            MODIFIER               = #{modifier}
        WHERE PROJECT_ID = #{projectId}
    </update>

    <update id="resetHistoryProjectHotelViolationsCount">
        UPDATE
            t_project_hotel_history_data
        SET TOTAL_VIOLATIONS_COUNT = null
        WHERE PROJECT_ID = #{projectId}
    </update>

    <update id="updateHistoryProjectHotelViolationsCount">
        UPDATE
        t_project_hotel_history_data
        SET total_violations_count = #{violationsCount}
        WHERE project_id = #{projectId}
        AND hotel_id IN
        <foreach collection="hotelIdList" item="hotelId" separator="," close=")" open="(">
            #{hotelId}
        </foreach>
    </update>

    <update id="resetHistoryProjectHotelServicePoint">
        UPDATE
        t_project_hotel_history_data
        SET SERVICE_POINT = NULL
        WHERE PROJECT_ID = #{projectId}
    </update>

    <update id="updateHistoryProjectHotelServicePoint">
        UPDATE
        t_project_hotel_history_data
        SET SERVICE_POINT = #{servicePoint}
        WHERE PROJECT_ID = #{projectId}
        AND HOTEL_ID IN
        <foreach collection="hotelIdList" item="hotelId" separator="," close=")" open="(">
            #{hotelId}
        </foreach>
    </update>

    <update id="updateHistoryProjectHotelMinMaxOtaPrice">
        UPDATE
            t_project_hotel_history_data
        SET OTA_MIN_PRICE          = #{minPrice},
            OTA_MAX_PRICE          = #{maxPrice},
            MIN_MAX_OTA_PRICE_DATE = #{minMaxOtaPriceDate}
        WHERE PROJECT_ID = #{projectId}
          AND HOTEL_ID = #{hotelId}
    </update>

    <select id="queryNeedUpdateLowestPriceHotelIds" resultType="java.lang.Long">
        SELECT hotel_id
        FROM t_project_hotel_history_data
        WHERE project_id = #{projectId}
          AND (lowest_price IS NULL OR lowest_price_date &lt; NOW() - INTERVAL 30 DAY)
    </select>

    <update id="updateHistoryProjectHotelLowestPrice">
        UPDATE
            t_project_hotel_history_data
        SET LOWEST_PRICE           = #{lowestPrice},
            ADJUST_LOWEST_PRICE    = #{adjustLowestPrice},
            LOWEST_PRICE_ITEM_INFO = #{lowestPriceItemInfo},
            LOWEST_PRICE_DATE      = #{lowestPriceDate}
        WHERE PROJECT_ID = #{projectId}
          AND HOTEL_ID = #{hotelId}
    </update>

    <select id="queryAllRecommendHistoryProjectHotelList" resultType="com.fangcang.grfp.core.vo.response.project.QueryHistoryProjectInfoResponse">
        SELECT
        phis.PROJECT_ID AS projectId,
        phis.HOTEL_ID AS hotelId,
        phis.room_night_count AS roomNightCount,
        phis.TOTAL_AMOUNT AS totalAmount,-- 成交金额
        phis.CITY_ORDER AS cityOrder,
        phis.SAVED_AMOUNT AS savedAmount,
        phis.POI_ID AS poiId,
        phis.POI_DISTANCE AS poiDistance,
        phis.RECOMMEND_LEVEL AS recommendLevel,-- 推荐等级
        phis.LOWEST_PRICE AS lowestPrice,
        phis.ADJUST_LOWEST_PRICE AS adjustLowestPrice,
        th.city_code AS cityCode,
        th.LNG_BAIDU AS lngBaidu,
        th.LAT_BAIDU AS latBaidu,
        th.hotel_star AS hotelStar,
        th.rating AS rating,
        rh.REQUIRED_ROOM_NIGHT AS requiredRoomNight,
        rh.REFERENCE_PRICE AS referencePrice
        FROM
        t_project_hotel_history_data phis
        left join t_hotel th on phis.hotel_id = th.hotel_id
        left join T_RECOMMEND_HOTEL rh on phis.hotel_id = rh.hotel_id AND rh.STATE = 1 AND rh.REQUIRED_ROOM_NIGHT is not null
        WHERE
        phis.PROJECT_ID = #{projectId}
        <if test="cityCode != null and cityCode != ''">
            AND phis.CITY_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        and th.is_active = 1
        ORDER BY phis.room_night_count DESC
    </select>

    <update id="clearHistoryProjectHotelRecommendLevel">
        UPDATE
        t_project_hotel_history_data
        SET RECOMMEND_LEVEL = null,
        PRICE_LEVEL_ROOM_NIGHT = null
        WHERE PROJECT_ID = #{projectId}
    </update>

    <update id="updateRecommendLevelAndPriceLevelRoomNight">
        UPDATE
        t_project_hotel_history_data
        SET RECOMMEND_LEVEL = #{recommendLevel,jdbcType=INTEGER},
        PRICE_LEVEL_ROOM_NIGHT = #{priceLevelRoomNight,jdbcType=INTEGER}
        WHERE PROJECT_ID = #{projectId}
        AND HOTEL_ID = #{hotelId}
    </update>

    <insert id="batchMergeHistoryResponse">
        INSERT INTO t_project_hotel_history_data (project_id,
                                                  hotel_id,
                                                  city_code,
                                                  room_night_count,
                                                  total_amount,
                                                  saved_amount,
                                                  saved_amount_rate,
                                                  lowest_price,
                                                  adjust_lowest_price,
                                                  lowest_price_date,
                                                  lowest_price_item_info,
                                                  is_uploaded,
                                                  creator,
                                                  create_time,
                                                  modifier,
                                                  modify_time)
        <foreach collection="historyProjects" item="item" separator=",">
            ( #{item.projectId,jdbcType=BIGINT},
                #{item.hotelId,jdbcType=BIGINT},
                #{item.cityCode,jdbcType=VARCHAR},
                #{item.roomNightCount,jdbcType=INTEGER},
                #{item.totalAmount,jdbcType=DECIMAL},
                #{item.savedAmount,jdbcType=DECIMAL},
                #{item.savedAmountRate,jdbcType=DECIMAL},
                #{item.lowestPrice,jdbcType=DECIMAL},
                #{item.adjustLowestPrice,jdbcType=DECIMAL},
                #{item.lowestPriceDate,jdbcType=DATE},
                #{item.lowestPriceItemInfo,jdbcType=VARCHAR},
                #{item.isUploaded,jdbcType=INTEGER},
                #{item.creator,jdbcType=VARCHAR},
                NOW(),
                #{item.creator,jdbcType=VARCHAR},
                NOW())
        </foreach>
        ON DUPLICATE KEY UPDATE room_night_count       = VALUES(room_night_count),
                                total_amount           = VALUES(total_amount),
                                city_code              = VALUES(city_code),
                                saved_amount           = VALUES(saved_amount),
                                saved_amount_rate      = VALUES(saved_amount_rate),
                                lowest_price           = VALUES(lowest_price),
                                lowest_price_date      = VALUES(lowest_price_date),
                                adjust_lowest_price    = VALUES(adjust_lowest_price),
                                lowest_price_item_info = VALUES(lowest_price_item_info),
                                is_uploaded            = VALUES(is_uploaded),
                                modifier               = VALUES(creator)
    </insert>

    <select id="queryRecommendHistoryProjectHotelList"  resultType="com.fangcang.grfp.core.vo.response.project.QueryHistoryProjectInfoResponse">
        SELECT
            phis.PROJECT_ID AS projectId,
            phis.HOTEL_ID AS hotelId,
            phis.ROOM_NIGHT_COUNT AS roomNightCount,--成交间夜数
            phis.TOTAL_AMOUNT AS totalAmount,-- 成交金额
            phis.CITY_ORDER AS cityOrder,
            phis.SAVED_AMOUNT AS savedAmount,
            phis.POI_ID AS poiId,
            phis.POI_DISTANCE AS poiDistance,
            phis.RECOMMEND_LEVEL AS recommendLevel,-- 推荐等级
            phis.LOWEST_PRICE AS lowestPrice,
            phis.ADJUST_LOWEST_PRICE AS adjustLowestPrice,
            th.city_code AS cityCode,
            th.LNG_BAIDU AS lngBaidu,
            th.LAT_BAIDU AS latBaidu,
            th.LNG_GOOGLE AS lngGoogle,
            th.LAT_GOOGLE AS latGoogle,
            th.name_en_us AS nameEnUs,
            th.name_zh_cn AS nameZhCn,
            th.hotel_star AS hotelStar,
            th.rating AS rating,
            rh.REQUIRED_ROOM_NIGHT AS requiredRoomNight,
            rh.REFERENCE_PRICE AS referencePrice
        FROM
            t_project_hotel_history_data phis
        left join t_hotel th on phis.hotel_id = th.hotel_id
        left join T_RECOMMEND_HOTEL rh on phis.hotel_id = rh.hotel_id
        WHERE
        phis.PROJECT_ID = #{projectId}
        <if test="cityCode != null and cityCode != ''">
            AND phis.CITY_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        and rh.STATE = 1
        and th.is_active=1
        AND rh.REQUIRED_ROOM_NIGHT is not null
        ORDER BY phis.ROOM_NIGHT_COUNT DESC
    </select>
</mapper>
