package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户审计日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sys_audit_log")
public class SysAuditLogEntity extends BaseVO {


    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 成员 id
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 请求时间
     */
    @TableField("request_time")
    private Date requestTime;

    /**
     * 响应时间
     */
    @TableField("response_time")
    private Date responseTime;

    /**
     * 请求 ip
     */
    @TableField("client_ip")
    private String clientIp;

    /**
     * 请求 user agent
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 请求 uri
     */
    @TableField("request_uri")
    private String requestUri;

    /**
     * 请求参数. 超长截断
     */
    @TableField("request_params")
    private String requestParams;

    /**
     * 响应结果. 超长截断
     */
    @TableField("response")
    private String response;

    /**
     * 响应结果 code
     */
    @TableField("response_code")
    private String responseCode;

    /**
     * 功能. 格式: 菜单-动作
     */
    @TableField("function_name")
    private String functionName;

    /**
     * 自定义扩展参数
     */
    @TableField("extend_param")
    private String extendParam;

    /**
     * 记录的创建时间
     */
    @TableField("create_time")
    private Date createTime;


}
