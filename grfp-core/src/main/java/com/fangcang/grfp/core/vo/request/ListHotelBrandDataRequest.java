package com.fangcang.grfp.core.vo.request;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("酒店品牌列表分页查询")
@Getter
@Setter
public class ListHotelBrandDataRequest extends PageQuery {

    @ApiModelProperty(value = "酒店集团ID")
    private Long hotelGroupId;

    @ApiModelProperty(value = "酒店品牌ID")
    private Long hotelBrandId;

    @ApiModelProperty(value = "酒店品牌名称")
    private String hotelBrandName;

    @ApiModelProperty(value = "是否有效 1:是，0:否")
    private Boolean isActive;

}
