package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("用户列表")
@Getter
@Setter
public class ListUserVO extends BaseVO {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("机构ID")
    private Long orgId;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("用户角色")
    private String roleCode;

    @ApiModelProperty("手机号码区号")
    private String mobileAreaCode;

    @ApiModelProperty("手机号码")
    private String mobile;

    @ApiModelProperty("电邮")
    private String email;

    @ApiModelProperty("状态")
    private Integer state;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("机构类型")
    private Integer orgType;

}
