package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("酒店集团提交报价请求数据")
@Getter
@Setter
public class SubmitHotelBidRequest extends BaseVO {

    @ApiModelProperty("项目意向酒店ID")
    @NotNull
    private Integer projectIntentHotelId;

    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;

    @ApiModelProperty("酒店ID")
    @NotNull
    private Long hotelId;

    @ApiModelProperty("币种")
    @NotBlank
    private String currencyCode;

    /**
     * 酒店报价联系人
     */
    @ApiModelProperty("酒店报价联系人")
    private String hotelBidContactName;

    /**
     * 酒店报价联系人手机号码
     */
    @ApiModelProperty("酒店报价联系人手机号码")
    private String hotelBidContactMobile;

    /**
     * 酒店报价联系人电邮
     */
    @ApiModelProperty("酒店报价联系人电邮")
    private String hotelBidContactEmail;

    /**
     * 酒店报价联系人
     */
    @ApiModelProperty("酒店集团报价联系人")
    private String hotelGroupBidContactName;

    /**
     * 酒店报价联系人手机号码
     */
    @ApiModelProperty("酒店报价联系人手机号码")
    private String hotelGroupBidContactMobile;

    /**
     * 酒店报价联系人电邮
     */
    @ApiModelProperty("酒店报价联系人电邮")
    private String hotelGroupBidContactEmail;

    @ApiModelProperty("项目酒店报价策略")
    @NotNull
    private BidProjectStrategyVO bidProjectStrategy;

    @ApiModelProperty("项目酒店自定义报价策略")
    @NotNull
    private List<BidCustomStrategyVO> bidCustomStrategyList;

    @ApiModelProperty("可用日期列表")
    @NotNull
    private List<BidApplicableDayVO> bidApplicableDayList;

    @ApiModelProperty("不可用日期列表")
    @NotNull
    private List<BidUnApplicableDayVO> bidUnApplicableDayList;

    @ApiModelProperty("税费设置")
    @NotNull
    private BidHotelTaxSettingsVO bidHotelTaxSettings;

    @ApiModelProperty("房档价格信息")
    private List<BidHotelPriceLevelInfoVO> bidHotelPriceLevelInfoVOList;

    @ApiModelProperty("员工权益")
    private String employeeRight;

    @ApiModelProperty("员工权益上传文件")
    private List<UploadFileVO> employeeRightFileUrl;


}
