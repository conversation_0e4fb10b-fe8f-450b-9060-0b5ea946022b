package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@ApiModel("根据名称查询项目请求")
@Getter
@Setter
public class QueryProjectByNameRequest extends BaseVO {

    @ApiModelProperty(value="项目名称", required = true)
    @NotBlank
    private String projectName;

    @ApiModelProperty(value = "返回数量 默认20")
    private Integer limitCount = 20;
}
