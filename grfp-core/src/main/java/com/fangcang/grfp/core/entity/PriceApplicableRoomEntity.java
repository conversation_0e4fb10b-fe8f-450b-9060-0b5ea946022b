package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 价格可用房型
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_price_applicable_room")
public class PriceApplicableRoomEntity extends BaseVO {


    /**
     * 价格可用房型ID
     */
    @TableId(value = "price_applicable_room_id", type = IdType.AUTO)
    private Integer priceApplicableRoomId;

    /**
     * 项目意向酒店ID
     */
    @TableField("project_intent_hotel_id")
    private Integer projectIntentHotelId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 价格房档ID
     */
    @TableField("hotel_price_level_id")
    private Integer hotelPriceLevelId;

    /**
     * 房型ID
     */
    @TableField("room_type_id")
    private Long roomTypeId;

    /**
     * 排序
     */
    @TableField("display_order")
    private Integer displayOrder;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 自定义房型名称
     */
    @TableField("custom_room_type_name")
    private String customRoomTypeName;

}
