package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@ApiModel("报价房档信息")
@Getter
@Setter
public class BidHotelPriceLevelInfoVO extends BaseVO {

    @ApiModelProperty("酒店价格房档ID, 新增为空，修改传ID")
    private Integer hotelPriceLevelId;

    @ApiModelProperty("房档编号 1,2,3,4")
    @NotNull
    private Integer roomLevelNo;

    @ApiModelProperty("大床房数量")
    private Integer bigBedRoomCount;

    @ApiModelProperty("双床房数量")
    private Integer doubleBedRoomCount;

    @ApiModelProperty("总房数量")
    private Integer totalRoomCount;

    @ApiModelProperty("房型名称描述")
    private String roomNameDesc;

    @ApiModelProperty("币种")
    private String currencyCode;

    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty("房档房型列表")
    @NotNull
    private List<BidApplicableRoomVO> roomList;

    @ApiModelProperty("价格组列表")
    @NotNull
    private List<BidHotelPriceGroupVO> bidHotelPriceGroupList;

    @ApiModelProperty("展示币种")
    private String viewCurrencyCode;

    @ApiModelProperty("报价币种对展示币种汇率")
    private BigDecimal viewCurrencyExchangeRate;

}
