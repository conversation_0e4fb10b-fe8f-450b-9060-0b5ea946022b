package com.fangcang.grfp.api.controller.project.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(description = "批量指派跟进人请求")
public class AssignFollowerRequest {

    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull
    private Long projectId;

    @ApiModelProperty(value = "酒店ID列表", required = true)
    @NotEmpty
    private List<Long> hotelIds;

    @ApiModelProperty(value = "企业跟进人ID", required = true)
    @NotNull
    private Integer distributorContactUid;

    @ApiModelProperty(value = "企业跟进人名称", required = true)
    @NotEmpty
    private String distributorContactName;
} 