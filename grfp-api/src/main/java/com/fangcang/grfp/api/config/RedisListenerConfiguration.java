package com.fangcang.grfp.api.config;

import com.fangcang.grfp.core.constant.TopicChannelName;
import com.fangcang.grfp.core.listener.ClearCacheMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

/**
 * 注册监听器
 */
@Configuration
public class RedisListenerConfiguration {

    @Autowired
    private ClearCacheMessageListener clearCacheMessageListener;

    @Bean
    public ApplicationListener<ContextRefreshedEvent> contextRefreshedEventApplicationListener() {
        return event -> {
            RedisMessageListenerContainer container = event.getApplicationContext().getBean(RedisMessageListenerContainer.class);
            container.addMessageListener(clearCacheMessageListener, new ChannelTopic(TopicChannelName.CLEAR_CACHE));
        };
    }
}
