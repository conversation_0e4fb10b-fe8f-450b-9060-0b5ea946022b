package com.fangcang.grfp.core.vo.request;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("项目列表分页查询")
@Getter
@Setter
public class ListProjectRequest extends PageQuery {

    @ApiModelProperty(value = "招标机构ID")
    private Integer tenderOrgId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "项目状态 项目状态(0：未启动，1：招标中(已启动)，2：招标完成，3：已废标)  文字资源编号：PROJECT_STATE_xx")
    private Integer projectState;


}
