package com.fangcang.grfp.core.auditlog;

import com.fangcang.grfp.core.base.BaseVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 审计日志
 */
@Getter
@Setter
public class UserAuditLogVO extends BaseVO {


    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 请求时间
     */
    private Date requestTime;

    /**
     * 响应时间
     */
    private Date responseTime;

    /**
     * 请求 ip
     */
    private String clientIp;

    /**
     * 请求 user agent
     */
    private String userAgent;

    /**
     * 请求 uri
     */
    private String requestUri;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 响应结果
     */
    private String response;

    /**
     * 响应结果 code
     */
    private String responseCode;

    /**
     * 功能. 格式: 菜单-动作
     */
    private String functionName;

    /**
     * 自定义扩展参数
     */
    private String extendParam;

}
