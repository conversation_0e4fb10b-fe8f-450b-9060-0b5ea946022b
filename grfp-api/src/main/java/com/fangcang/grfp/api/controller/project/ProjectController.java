package com.fangcang.grfp.api.controller.project;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.project.service.ProjectService;
import com.fangcang.grfp.api.controller.project.vo.*;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.IdRequest;
import com.fangcang.grfp.core.base.IdVO;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.constant.UserPermission;
import com.fangcang.grfp.core.entity.ProjectLanyonViewKeysEntity;
import com.fangcang.grfp.core.enums.CustomStrategyTypeEnum;
import com.fangcang.grfp.core.enums.OrgTypeEnum;
import com.fangcang.grfp.core.enums.YesOrNoEnum;
import com.fangcang.grfp.core.manager.BidMapManager;
import com.fangcang.grfp.core.usersession.RequiresPermissions;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.vo.LanyonImportColumnVO;
import com.fangcang.grfp.core.vo.ListProjectVO;
import com.fangcang.grfp.core.vo.request.ListProjectRequest;
import com.fangcang.grfp.core.vo.request.project.*;
import com.fangcang.grfp.core.vo.response.project.CustomTendStrategyVO;
import com.fangcang.grfp.core.vo.response.project.HotelHistoryTradeDataListVO;
import com.fangcang.grfp.core.vo.response.project.InviteHotelGroupVO;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelTendStrategyVO;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelTendWeightVO;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelWhiteVO;
import com.fangcang.grfp.core.vo.response.project.ProjectIntentHotelVO;
import com.fangcang.grfp.core.vo.response.project.ProjectPoiVO;
import com.fangcang.grfp.core.vo.response.project.QueryHotelGroupInviteHotelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Api(tags = "项目")
@RestController
@RequestMapping("/Api/Project")
public class ProjectController extends BaseController {

    @Autowired
    private ProjectService projectService;
    @Autowired
    private BidMapManager bidMapManager;

    @ApiOperation("项目列表")
    @PostMapping("/List")
    @ResponseBody
    public Result<PageVO<ListProjectVO>> list(HttpServletRequest request, @Valid @RequestBody ListProjectRequest listProjectRequest) {
        return Result.ok(projectService.queryProjectPage(request, listProjectRequest));
    }

    @ApiOperation("查询项目基本信息")
    @PostMapping("/QueryProjectBasicInformation")
    @ResponseBody
    public Result<ProjectBasicInfoVO> queryProjectBasicInformation(HttpServletRequest request, @Valid @RequestBody GetProjectInfoRequest listProjectRequest) {
        return Result.ok(projectService.queryProjectBasicInfo(request, listProjectRequest));
    }

    @ApiOperation("新增项目")
    @PostMapping("/AddProject")
    @ResponseBody
    @UserAuditLog("项目-新增项目")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Integer> addProject(HttpServletRequest request, @Valid @RequestBody AddProjectRequest addProjectRequest) {
        return Result.ok(projectService.addProject(request, addProjectRequest));
    }

    @ApiOperation("修改项目")
    @PostMapping("/UpdateProject")
    @ResponseBody
    @UserAuditLog("项目-修改项目")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Integer> updateProject(HttpServletRequest request, @Valid @RequestBody UpdateProjectRequest updateProjectRequest) {
        return Result.ok(projectService.updateProject(request, updateProjectRequest));
    }

    @ApiOperation("修改项目介绍")
    @PostMapping("/UpdateIntroduction")
    @ResponseBody
    @UserAuditLog("项目-修改项目介绍")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Integer> updateIntroduction(HttpServletRequest request, @Valid @RequestBody UpdateProjectIntroductionRequest updateProjectRequest) {
        return Result.ok(projectService.updateProjectIntroduction(request, updateProjectRequest));
    }


    @ApiOperation("修改项目状态")
    @PostMapping("/UpdateProjectState")
    @ResponseBody
    @UserAuditLog("项目-修改项目状态")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Integer> updateProjectState(HttpServletRequest request, @Valid @RequestBody UpdateProjectStateRequest updateProjectStateRequest) {
        return Result.ok(projectService.updateProjectState(request, updateProjectStateRequest));
    }

    @ApiOperation("项目意向邀请酒店列表查询")
    @PostMapping("/QueryInvitedHotelList")
    @ResponseBody
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<PageVO<ProjectInvitedHotelInfo>> queryInvitedHotelList(HttpServletRequest request, @Valid @RequestBody QueryProjectInvitedHotelList queryInvitedHotelList) {
        return Result.ok(projectService.queryProjectInvitedHotelList(request, queryInvitedHotelList));
    }

    @ApiOperation("邀请酒店")
    @PostMapping("/InviteHotels")
    @ResponseBody
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> inviteHotels(HttpServletRequest request, @Valid @RequestBody InviteHotelsRequest inviteHotelsRequest) {
        projectService.inviteHotels(request, inviteHotelsRequest);
        return Result.ok();
    }

    @ApiOperation("分页查询项目意向酒店集团")
    @PostMapping("/QueryInviteHotelGroup")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<PageVO<InviteHotelGroupVO>> queryInviteHotelGroup(@RequestBody @Validated QueryInviteHotelGroupRequest request) {
        return Result.ok(projectService.queryInviteHotelGroup(request));
    }

    @ApiOperation("酒店集团邀约-加入意向")
    @PostMapping("/JoinIntentHotelGroup")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> joinIntentHotelGroup(@RequestBody @Validated JoinIntentHotelGroupRequest request) {
        projectService.joinIntentHotelGroup(request);
        return Result.ok();
    }

    @ApiOperation("酒店集团邀约-移除意向")
    @PostMapping("/RemoveIntentHotelGroup")
    @UserAuditLog("酒店集团邀约-移除意向")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> removeIntentHotelGroup(@Valid @RequestBody JoinIntentHotelGroupRequest req) {
        projectService.removeIntentHotelGroup(req);
        return Result.ok();
    }

    @ApiOperation("酒店邀约-获取邮件预览信息")
    @PostMapping("/checkSendHotelInvite")
    public Result<EmailResponse> checkSendHotelInvite(@Valid @RequestBody JoinIntentHotelRequest req) {
        return Result.ok(projectService.checkSendHotelInvite(req));
    }

    @ApiOperation("酒店邀约-批量发送邮件邀约")
    @PostMapping("/batchSendHotelInvite")
    @UserAuditLog("项目-批量发送邮件邀约")
    public Result<Void> batchSendHotelInvite(@Valid @RequestBody JoinIntentHotelRequest req) {
        projectService.batchSendHotelInvite(req);
        return Result.ok();
    }

    @ApiOperation("酒店集团邀约-获取邮件预览信息")
    @PostMapping("/checkSendHotelGroupInvite")
    public Result<EmailResponse> checkSendHotelGroupInvite(@Valid @RequestBody JoinIntentHotelGroupRequest req) {
        return Result.ok(projectService.checkSendHotelGroupInvite(req));
    }

    @ApiOperation("酒店集团邀约-发送邮件邀约")
    @PostMapping("/batchSendHotelGroupInvite")
    @UserAuditLog("酒店集团邀约-发送邮件邀约")
    public Result<Void> batchSendHotelGroupInvite(@Valid @RequestBody JoinIntentHotelGroupRequest req) {
        projectService.batchSendHotelGroupInvite(req);
        return Result.ok();
    }

    @ApiOperation("酒店集团邀约-设置品牌报价限制")
    @PostMapping("/SetBrandLimit")
    @UserAuditLog("酒店集团邀约-设置品牌报价限制")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> setBrandLimit(@Valid @RequestBody SetBrandLimitRequest req) {
        projectService.setBrandLimit(req);
        return Result.ok();
    }

    @ApiOperation("导入酒店集团意向酒店")
    @PostMapping(value = "/ImportHotelGroupIntentHotel")
    @UserAuditLog("项目-导入酒店集团意向酒店")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<IdVO<Long>> importHotelGroupIntentHotel(@RequestParam("file") MultipartFile file, @RequestParam("projectId") Integer projectId) throws IOException {
        return Result.ok(projectService.importHotelGroupIntentHotel(file, projectId));
    }


    @ApiOperation("分页查询酒店集团下意向酒店列表")
    @PostMapping("/QueryHotelGroupInviteHotel")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<PageVO<QueryHotelGroupInviteHotelVO>> queryHotelGroupInviteHotel(@RequestBody @Valid QueryHotelGroupInviteHotelRequest request) {
        return Result.ok(projectService.queryHotelGroupInviteHotel(request));
    }

    @ApiOperation("删除酒店集团下意向酒店")
    @PostMapping("/DeleteHotelGroupInviteHotel")
    @UserAuditLog("项目-删除酒店集团下意向酒店")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> deleteHotelGroupInviteHotel(@RequestBody @Valid DeleteHotelGroupInviteHotelRequest request) {
        projectService.deleteHotelGroupInviteHotel(request);
        return Result.ok();
    }

    @ApiOperation("批量添加项目 POI")
    @PostMapping("/BatchAddProjectPoi")
    @UserAuditLog("项目-批量添加项目 POI")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> batchAddProjectPoi(@Valid @RequestBody AddProjectPoiRequest req) {
        projectService.batchAddProjectPoi(req);
        return Result.ok();
    }

    @ApiOperation("删除项目 POI")
    @PostMapping("/DeleteProjectPoi")
    @UserAuditLog("项目-删除项目 POI")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> deleteProjectPoi(@Valid @RequestBody DeleteProjectPoiRequest req) {
        projectService.deleteProjectPoi(req);
        return Result.ok();
    }

    @ApiOperation("查询项目 POI 信息")
    @PostMapping("/QueryProjectPoiInfo")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<List<ProjectPoiVO>> queryProjectPoiInfo(@Valid @RequestBody IdRequest<Integer> req) {
        return Result.ok(projectService.queryProjectPoiInfo(req.getId()));
    }

    @ApiOperation("新增自定义采购策略")
    @PostMapping("/AddProjectCustomTendStrategy")
    @UserAuditLog("项目-新增自定义采购策略")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<IdVO<Long>> addProjectCustomTendStrategy(@Valid @RequestBody AddProjectCustomTendStrategyRequest req) {
        if (CustomStrategyTypeEnum.isOptionType(req.getStrategyType()) && CollUtil.isEmpty(req.getOptions())) {
            AppUtility.serviceError(ErrorCode.PROJECT_STRATEGY_OPTION_CANNOT_EMPTY);
        }
        return Result.ok(new IdVO<>(projectService.addProjectCustomTendStrategy(req)));
    }

    @ApiOperation("删除自定义采购策略")
    @PostMapping("/DeleteProjectCustomTendStrategy")
    @UserAuditLog("项目-删除自定义采购策略")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> deleteProjectCustomTendStrategy(@Valid @RequestBody DeleteProjectCustomTendStrategyRequest req) {
        projectService.deleteProjectCustomTendStrategy(req);
        return Result.ok();
    }

    @ApiOperation("更新自定义采购策略排序")
    @PostMapping("/SwitchCustomTendStrategyDisplayOrder")
    @UserAuditLog("项目-更新自定义采购策略排序")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> switchCustomTendStrategyDisplayOrder(@Valid @RequestBody SwitchCustomTendStrategyDisplayOrderRequest req) {
        projectService.switchCustomTendStrategyDisplayOrder(req);
        return Result.ok();
    }

    @ApiOperation("查询项目自定义采购策略")
    @PostMapping("/QueryProjectCustomTendStrategy")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<PageVO<CustomTendStrategyVO>> queryProjectCustomTendStrategy(@Valid @RequestBody QueryCustomTendStrategyRequest req) {
        return Result.ok(projectService.queryProjectCustomTendStrategy(req));
    }

    @ApiOperation("新增或修改酒店项目招标采购策略")
    @PostMapping("/AddOrUpdateProjectHotelTendStrategy")
    @UserAuditLog("项目-新增或修改酒店项目招标采购策略")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> addOrUpdateProjectHotelTendStrategy(@Valid @RequestBody AddProjectHotelTendStrategyRequest req) {
        // 校验
        if (YesOrNoEnum.YES.getKey().equals(req.getSupportPriceLimit())) {
            if (req.getLimitMinPrice() == null || req.getLimitMaxPrice() == null) {
                AppUtility.serviceError(ErrorCode.PROJECT_TEND_STRATEGY_LIMIT_PRICE_IS_NULL);
            }
            if (NumberUtil.isLess(req.getLimitMaxPrice(), req.getLimitMinPrice())) {
                AppUtility.serviceError(ErrorCode.PROJECT_TEND_STRATEGY_PRICE_INVALID);
            }
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getSupportMaxNotApplicableDay())) {
            if (Objects.isNull(req.getMaxNotApplicableDay())) {
                AppUtility.serviceError(ErrorCode.PROJECT_TEND_MAX_NOT_APPLICABLE_DAY_IS_NULL);
            }
            if (req.getMaxNotApplicableDay() < 10 || req.getMaxNotApplicableDay() > 100) {
                AppUtility.serviceError(ErrorCode.PROJECT_TEND_MAX_NOT_APPLICABLE_DAY_IS_INVALID);
            }
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getSupportMaxRoomTypeCount())) {
            if (Objects.isNull(req.getMaxRoomTypeCount())) {
                AppUtility.serviceError(ErrorCode.PROJECT_TEND_MAX_ROOM_TYPE_COUNT_IS_NULL);
            }
            if (req.getMaxRoomTypeCount() < 1 || req.getMaxRoomTypeCount() > 10) {
                AppUtility.serviceError(ErrorCode.PROJECT_TEND_MAX_ROOM_TYPE_COUNT_IS_INVALID);
            }
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getSupportSeasonDayLimit())) {
            if (Objects.isNull(req.getMaxSeasonDay())) {
                AppUtility.serviceError(ErrorCode.PROJECT_TEND_MAX_SEASON_DAY_IS_NULL);
            }
            if (req.getMaxSeasonDay() < 1 || req.getMaxSeasonDay() > 120) {
                AppUtility.serviceError(ErrorCode.PROJECT_TEND_MAX_SEASON_DAY_IS_INVALID);
            }
        }
        // 新增
        projectService.addOrUpdateProjectHotelTendStrategy(req);
        return Result.ok();
    }

    @ApiOperation("查询酒店项目招标采购策略")
    @PostMapping("/QueryProjectHotelTendStrategy")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<ProjectHotelTendStrategyVO> queryProjectHotelTendStrategy(@Valid @RequestBody QueryHotelTendStrategyRequest req) {
        return Result.ok(projectService.queryProjectHotelTendStrategy(req));
    }

    @ApiOperation("新增或修改酒店项目招标权重配置")
    @PostMapping("/AddOrUpdateProjectHotelTendWeight")
    @UserAuditLog("项目-新增或修改酒店项目招标权重配置")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> addOrUpdateProjectHotelTendWeight(@Valid @RequestBody AddProjectHotelTendWeightRequest req) {
        projectService.addOrUpdateProjectHotelTendWeight(req);
        return Result.ok();
    }

    @ApiOperation("查询酒店项目招标权重配置")
    @PostMapping("/QueryProjectHotelTendWeight")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<ProjectHotelTendWeightVO> queryProjectHotelTendWeight(@Valid @RequestBody QueryProjectHotelTendWeightRequest req) {
        return Result.ok(projectService.queryProjectHotelTendWeight(req));
    }

    @ApiOperation("新增项目酒店白名单")
    @PostMapping("/AddProjectHotelWhite")
    @UserAuditLog("项目-新增项目酒店白名单")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> addProjectHotelWhite(@Valid @RequestBody AddProjectHotelWhiteRequest req) {
        projectService.addProjectHotelWhite(req);
        return Result.ok();
    }

    @ApiOperation("删除项目酒店白名单")
    @PostMapping("/DeleteProjectHotelWhite")
    @UserAuditLog("项目-删除项目酒店白名单")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> deleteProjectHotelWhite(@Valid @RequestBody DeleteProjectHotelWhiteRequest req) {
        projectService.deleteProjectHotelWhite(req);
        return Result.ok();
    }

    @ApiOperation("查询项目酒店白名单详情")
    @PostMapping("/QueryProjectHotelWhiteDetail")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<PageVO<ProjectHotelWhiteVO>> queryProjectHotelWhiteDetail(@Valid @RequestBody QueryProjectHotelWhiteRequest req) {
        return Result.ok(projectService.queryProjectHotelWhiteDetail(req));
    }

    @ApiOperation("查询项目绑定机构历史项目")
    @PostMapping("/QueryHistoryProjectInfo")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<PageVO<HistoryProjectInfoVO>> queryHistoryProjectInfo(@Valid @RequestBody QueryHistoryProjectRequest req) {
        return Result.ok(projectService.queryHistoryProjectInfo(req));
    }

    @ApiOperation("绑定历史项目")
    @PostMapping("/BindHistoryProject")
    @UserAuditLog("项目-绑定历史项目")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> bindHistoryProject(@Valid @RequestBody BindHistoryProjectRequest req) {
        projectService.bindHistoryProject(req);
        return Result.ok();
    }

    @ApiOperation("查询关联项目信息")
    @PostMapping("/QueryRelatedProjectInfo")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<RelatedProjectInfoVO> queryRelatedProjectInfo(@Valid @RequestBody IdRequest<Integer> req) {
        distributorAndPlatformValidate();
        return Result.ok(projectService.queryRelatedProjectInfo(req));
    }

    @ApiOperation("批量导入项目酒店历史交易数据")
    @PostMapping("/BatchAddHotelHistoryTradeData")
    @UserAuditLog("项目-批量导入项目酒店历史交易数据")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<IdVO<Long>> batchAddHotelHistoryTradeData(@RequestParam("file") MultipartFile file, @RequestParam("projectId") Integer projectId) throws IOException {
        return Result.ok(projectService.batchAddHotelHistoryTradeData(file, projectId));
    }

    @ApiOperation("删除项目酒店历史交易数据")
    @PostMapping("/DeleteHotelHistoryTradeData")
    @UserAuditLog("项目-删除项目酒店历史交易数据")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> deleteHotelHistoryTradeData(@Valid @RequestBody DeleteHotelHistoryTradeDataRequest req) {
        distributorAndPlatformValidate();
        projectService.deleteHotelHistoryTradeData(req);
        return Result.ok();
    }

    @ApiOperation("编辑项目酒店历史交易数据")
    @PostMapping("/EditHotelHistoryTradeData")
    @UserAuditLog("项目-编辑项目酒店历史交易数据")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> editHotelHistoryTradeData(@Valid @RequestBody EditHotelHistoryTradeDataRequest req) {
        distributorAndPlatformValidate();
        projectService.editHotelHistoryTradeData(req);
        return Result.ok();
    }

    @ApiOperation("查询项目酒店历史交易数据列表")
    @PostMapping("/QueryHotelHistoryTradeDataList")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<PageVO<HotelHistoryTradeDataListVO>> queryHotelHistoryTradeDataList(@Valid @RequestBody HotelHistoryTradeDataListRequest req) {
        distributorAndPlatformValidate();
        return Result.ok(projectService.queryHotelHistoryTradeDataList(req));
    }

    @ApiOperation("项目-邀请酒店-导入酒店")
    @PostMapping(value = "/ImportProjectIntentHotel")
    @UserAuditLog("项目-邀请酒店-导入酒店")
    @RequiresPermissions(UserPermission.POI)
    public Result<IdVO<Long>> importProjectIntentHotel(@RequestParam("file") MultipartFile file, @RequestParam("projectId") Integer projectId) throws IOException {
        return Result.ok(projectService.importProjectIntentHotel(file, projectId));
    }

    /**
     * 修改邀约酒店相关信息
     *
     * @param updateProjectIntentHotelDto
     * @return
     */
    @UserAuditLog("项目-邀请酒店-修改邀约酒店相关信息")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    @RequestMapping(value = "/updateProjectIntentHotel", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Result updateProjectIntentHotel(@RequestBody UpdateProjectIntentHotelRequest updateProjectIntentHotelDto) {
        return Result.ok(projectService.updateProjectIntentHotel(updateProjectIntentHotelDto));
    }

    /**
     * 验证当前用户是否是平台或者分销商
     */
    private static void distributorAndPlatformValidate() {
        if (!Objects.equals(UserSession.get().getUserOrg().getOrgType(), OrgTypeEnum.DISTRIBUTOR.key)
            && !Objects.equals(UserSession.get().getUserOrg().getOrgType(), OrgTypeEnum.PLATFORM.key)) {
            AppUtility.serviceError(ErrorCode.NO_PERMISSION_ACCESS);
        }
    }

    @ApiOperation("查询项目意向酒店列表")
    @PostMapping("/QueryProjectIntentHotelList")
    public Result<PageVO<ProjectIntentHotelVO>> queryProjectIntentHotelList(@Valid @RequestBody QueryProjectIntentHotelRequest req) {
        return Result.ok(projectService.queryProjectIntentHotelList(req));
    }

    @ApiOperation("删除项目意向酒店")
    @PostMapping("/DeleteProjectIntentHotel")
    @UserAuditLog("项目-删除意向酒店")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> deleteProjectIntentHotel(@Valid @RequestBody IdRequest<Long> req) {
        projectService.deleteProjectIntentHotel(req.getId());
        return Result.ok();
    }

    @ApiOperation("生成项目 POI 统计")
    @PostMapping("/GenerateHistoryProjectStat")
    @UserAuditLog("项目-生成项目 POI 统计")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> generateHistoryProjectStat(@Valid @RequestBody IdRequest<Integer> req) {
        distributorAndPlatformValidate();
        projectService.generateHistoryProjectStat(req);
        return Result.ok();
    }

    @ApiOperation("更新推荐酒店列表")
    @PostMapping("/GenerateHotelRecommendLevel")
    @UserAuditLog("项目-更新推荐酒店列表")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public Result<Void> generateHotelRecommendLevel(@Valid @RequestBody IdRequest<Integer> req) {
        distributorAndPlatformValidate();
        projectService.generateHotelRecommendLevel(req.getId());
        return Result.ok();
    }

    @ApiOperation("导出报价")
    @PostMapping("/ExportTenderPrice")
    @UserAuditLog("项目-导出报价")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public void exportTenderPrice(@Valid @RequestBody ExportTenderPriceRequest request, HttpServletResponse httpServletResponse) {
        projectService.exportTenderPrice(request, httpServletResponse);
    }

    @ApiOperation("迁移数据")
    @PostMapping("/MigrateTenderPrice")
    @UserAuditLog("项目-迁移数据")
    @RequiresPermissions(UserPermission.PROJECT_C_U_D)
    public void migrateTenderPrice(@Valid @RequestBody MigrateTenderPriceRequest request, HttpServletResponse httpServletResponse) {
        projectService.migrateTenderPrice(request, httpServletResponse);
    }


    @ApiOperation("Lanyon显示设置")
    @PostMapping("/GetAllLanyonColumns")
    public Result<Map<String, List<LanyonImportColumnVO>>> getAllLanyonColumns(@Valid @RequestBody GetProjectRequest request, HttpServletResponse httpServletResponse) {
       return Result.ok(projectService.getAllLanyonColumns());
    }

    @ApiOperation("Lanyon保存显示列")
    @PostMapping("/SaveProjectLanyonColumnViewKeys")
    public Result<Void> saveProjectLanyonColumnViewKeys(@Valid @RequestBody SaveProjectLanyonViewColumnInfoRequest request, HttpServletResponse httpServletResponse) {
        projectService.saveProjectLanyonColumnViewKeys(request);
        return Result.ok();
    }

    /**
     * Lanyon 列显示Key

     */
    @ApiOperation("Lanyon 列显示Key")
    @PostMapping(value = "GetProjectLanyonColumnViewKeys")
    public Result<ProjectLanyonViewKeysEntity> getProjectLanyonColumnViewKeys(@Valid @RequestBody GetProjectRequest request) {
        return Result.ok(projectService.getProjectLanyonViewKeys(request.getProjectId()));
    }


}
