package com.fangcang.grfp.api.controller.project;

import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.project.service.ProjectContractService;
import com.fangcang.grfp.api.controller.project.vo.*;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.IdRequest;
import com.fangcang.grfp.core.base.IdVO;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.constant.UserPermission;
import com.fangcang.grfp.core.manager.BidMapManager;
import com.fangcang.grfp.core.manager.LanyonImportManager;
import com.fangcang.grfp.core.usersession.RequiresPermissions;
import com.fangcang.grfp.core.vo.BidHotelPriceLevelInfoVO;
import com.fangcang.grfp.core.vo.LanyonImportDataVO;
import com.fangcang.grfp.core.vo.request.QueryHotelGroupBidMapHotelListRequest;
import com.fangcang.grfp.core.vo.request.bidmap.*;
import com.fangcang.grfp.core.vo.request.project.QueryProjectHotelListRequest;
import com.fangcang.grfp.core.vo.response.bidmap.*;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Api(tags = "项目签约")
@RestController
@RequestMapping("/Api/ProjectContract")
public class ProjectContractController extends BaseController {

    @Autowired
    private ProjectContractService projectContractService;
    @Autowired
    private BidMapManager bidMapManager;
    @Autowired
    private LanyonImportManager lanyonImportManager;


    @ApiOperation("查询项目酒店列表")
    @PostMapping("/QueryProjectHotelList")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    public Result<PageVO<ProjectHotelListVO>> queryProjectHotelList(@Valid @RequestBody QueryProjectHotelListRequest req) {
        return Result.ok(projectContractService.queryProjectHotelList(req));
    }

    @ApiOperation("查询项目报价国家/城市/集团/品牌统计数据")
    @PostMapping("/QueryProjectBidStatCount")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    public Result<ProjectBidStatCountVO> queryProjectBidStatCount(HttpServletRequest request, @Valid @RequestBody QueryProjectBidStatCountRequest req) {
        return Result.ok(projectContractService.queryProjectBidStatCount(request, req));
    }

    @ApiOperation("查询投标头部详情")
    @PostMapping("/QueryBidHeaderDetails")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    public Result<BidHeaderDetailsVO> queryBidHeaderDetails(@Valid @RequestBody IdRequest<Long> req) {
        return Result.ok(projectContractService.queryBidHeaderDetails(req.getId()));
    }

    @ApiOperation("查询项目详情")
    @PostMapping("/QueryProjectDetails")
    public Result<ProjectDetailsVO> queryProjectDetails(@Valid @RequestBody IdRequest<Long> req) {
        return Result.ok(projectContractService.queryProjectDetails(req.getId()));
    }

    @PostMapping("/ImportContractStatus")
    @ApiOperation("导入签约状态")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    @UserAuditLog("项目管理-签约-导入签约状态")
    public IdVO<Long> importContractStatus(@RequestParam("file") MultipartFile file, @RequestParam("projectId") Long projectId) throws IOException {
        return new IdVO<>(projectContractService.importContractStatus(file, projectId));
    }

    @ApiOperation("通知报价人")
    @PostMapping("/NotifyBidder")
    @UserAuditLog("项目签约-通知报价人")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    public Result<String> notifyBidder(@Valid @RequestBody NotifyBidderRequest req) {
        String message = projectContractService.notifyBidder(req);
        return Result.ok(message);
    }

    @ApiOperation("地图根据报价状态分组查询报价统计")
    @PostMapping("/QueryMapGroupByHotelBidState")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    public Result<List<HotelBidStateResponse>> queryMapGroupByHotelBidState(@Valid @RequestBody BidHotelStatQueryRequest req) {
        return Result.ok(projectContractService.queryMapGroupByHotelBidState(req));
    }

    @ApiOperation("机构删除酒店报价价格组 传hotelPriceGroupId")
    @PostMapping("/DeleteHotelPriceGroup")
    public Result<Void> deleteHotelPriceGroup(HttpServletRequest request, @Valid @RequestBody IdRequest<Integer> req) {
        bidMapManager.deleteHotelPriceGroup(request, req);
        return Result.ok();
    }

    @ApiOperation("地图根据报价状态分组查询酒店报价列表")
    @PostMapping("/QueryMapHotelListByBidState")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    public Result<PageVO<QueryMapBidHotelListResponse>> queryMapHotelListByBidState(HttpServletRequest request, @Valid @RequestBody QueryMapHotelListByBidStateRequest req) {
        return Result.ok(projectContractService.queryMapHotelListByBidState(request, req));
    }

    @ApiOperation("地图查询POI列表")
    @PostMapping("/QueryProjectMapPoiInfo")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    public Result<PageVO<ProjectPoiInfoResponse>> queryProjectMapPoiInfo(HttpServletRequest request, @Valid @RequestBody QueryProjectMapPoiInfoRequest req) {
        return Result.ok(projectContractService.queryProjectMapPoiInfo(request, req));
    }

    @ApiOperation("地图查询酒店或者POI信息")
    @PostMapping("/QueryMapHotelPoiBidHotelInfo")
    public Result<MapPoiBidHotelInfoResponse> queryMapHotelPoiBidHotelInfo(HttpServletRequest request, @Valid @RequestBody QueryMapPoiBidHotelInfoRequest req) throws ExecutionException, InterruptedException {
        return Result.ok(bidMapManager.queryMapHotelPoiBidHotelInfo(request, req));
    }

    @ApiOperation("查看报价详情")
    @PostMapping("/QueryHotelBidDetail")
    public Result<HotelBidDetailResponse> queryHotelBidDetail(HttpServletRequest request, @Valid @RequestBody HotelBidDetailRequest req) {
        return Result.ok(projectContractService.queryHotelBidDetail(request, req));
    }

    @ApiOperation("查询Lanyon导入原数据")
    @PostMapping("/QueryLanyonImportData")
    public Result<LanyonImportDataVO> queryLanyonImportData(HttpServletRequest request, @Valid @RequestBody QueryLanyonImportDataRequest req) {
        return Result.ok(lanyonImportManager.queryLanyonImportDataVO(req.getProjectId(), req.getHotelId(), req.getDataType()));
    }

    @ApiOperation("查询酒店周边六边形坐标")
    @PostMapping("/QueryHeatMapBidInfo")
    public Result<HeatMapBidInfoResponse> queryHeatMapBidInfo(HttpServletRequest request, @Valid @RequestBody QueryMapPoiBidHotelInfoRequest req) throws Exception {
        return Result.ok(bidMapManager.queryHeatMapBidInfo(req));
    }

    @ApiOperation("查询报价房档列表 返回房档")
    @PostMapping("/QueryBidHotelPriceLevelList")
    public Result<List<BidHotelPriceLevelInfoVO>> queryHotelPriceLevelList(HttpServletRequest request, @Valid @RequestBody QueryHotelPriceLevelListRequest req) {
        return Result.ok(projectContractService.querBidHotelPriceLevelInfoList(request, req));
    }

    @ApiOperation("地图签约查询酒店信息")
    @PostMapping("/QueryBidMapHotelInfo")
    public Result<QueryBidMapHotelInfoResponse> queryBidMapHotelInfo(HttpServletRequest request, @Valid @RequestBody QueryHotelGroupBidMapHotelListRequest req) {
        return Result.ok(projectContractService.queryBidMapHotelInfo(request, req));
    }

    @ApiOperation("地图签约POI统计信息")
    @PostMapping("/QueryMapPoiBidStat")
    public Result<Map<Integer, List<QueryProjectPoiHotelStatResponse>>> queryMapPoiBidStat(HttpServletRequest request, @Valid @RequestBody ProjectMapPoiStatRequest req) {
        return Result.ok(bidMapManager.queryMapPoiBidStat(request, req));
    }


    @ApiOperation("修改酒店报价状态")
    @PostMapping("/UpdateProjectIntentHotelBidState")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    @UserAuditLog("项目签约-修改酒店报价状态")
    public Result<Void> updateProjectIntentHotelBidState(HttpServletRequest request, @Valid @RequestBody UpdateHotelBidStateRequest req) {
        bidMapManager.updateProjectIntentHotelBidState(request, req);
        return Result.ok();
    }

    @ApiOperation("修改酒店报价人信息")
    @PostMapping("/UpdateProjectIntentHotelBidContactInfo")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    @UserAuditLog("项目签约-修改酒店报价人信息")
    public Result<Void> updateProjectIntentHotelBidContactInfo(HttpServletRequest request, @Valid @RequestBody UpdateProjectIntentHotelBidContactInfoRequest req) {
        projectContractService.updateProjectIntentHotelBidContactInfo(req);
        return Result.ok();
    }

    @ApiOperation("修改酒店集团报价人信息")
    @PostMapping("/UpdateProjectIntentHotelGroupBidContactInfo")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    @UserAuditLog("项目签约-修改酒店集团报价人信息")
    public Result<Void> updateProjectIntentHotelGroupBidContactInfo(HttpServletRequest request, @Valid @RequestBody UpdateProjectIntentHotelGroupBidContactInfoRequest req) {
        projectContractService.updateProjectIntentHotelGroupBidContactInfo(req);
        return Result.ok();
    }

    @ApiOperation("修改报价日期设置")
    @PostMapping("/UpdateBidDateSetting")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    @UserAuditLog("项目签约-修改报价日期设置")
    public Result<Void> updateBidDateSetting(HttpServletRequest request, @Valid @RequestBody UpdateBidDateSettingRequest req) {
        projectContractService.updateBidDateSetting(req);
        return Result.ok();
    }

    @ApiOperation("修改房档房型")
    @PostMapping("/UpdateBidLevelRoomInfo")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    @UserAuditLog("项目签约-修改房档房型")
    public Result<Void> updateBidLevelRoomInfo(HttpServletRequest request, @Valid @RequestBody UpdateBidLevelRoomInfoRequest req) {
        projectContractService.updateBidLevelRoomInfo(request, req);
        return Result.ok();
    }

    @ApiOperation("修改房档价格组")
    @PostMapping("/UpdateBidHotelPriceGroup")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    @UserAuditLog("项目签约-修改房档价格组")
    public Result<Void> updateBidHotelPriceGroup(HttpServletRequest request, @Valid @RequestBody UpdateBidHotelPriceGroupRequest req) {
        projectContractService.updateBidHotelPriceGroup(request, req);
        return Result.ok();
    }

    /**
     * 锁定报价
     */
    @ApiOperation("锁定报价")
    @PostMapping("/UpdatePriceGroupLocked")
    @RequiresPermissions(UserPermission.PROJECT_CONTRACT_C_U_D)
    @UserAuditLog("项目签约-锁定报价")
    public Result<Void> updatePriceGroupLocked(@RequestBody UpdatePriceGroupLockedRequest updatePriceGroupLockedRequest) {
        projectContractService.updatePriceGroupLocked(updatePriceGroupLockedRequest);
        return Result.ok();
    }

} 