<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.CurrencyExchangeRateMapper">

    <insert id="batchUpsert">
        insert into t_currency_exchange_rate(currency_code,
                                             currency_name,
                                             exchange_rate,
                                             inverse_exchange_rate,
                                             is_auto_sync,
                                             creator,
                                             modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.currencyCode},
             #{item.currencyName},
             #{item.exchangeRate},
             #{item.inverseExchangeRate},
             #{item.isAutoSync},
            #{item.creator},
            #{item.modifier})
        </foreach>
        on duplicate key update currency_name = values(currency_name),
                                exchange_rate = values(exchange_rate),
                                inverse_exchange_rate = values(inverse_exchange_rate),
                                is_auto_sync = values(is_auto_sync),
                                modifier = values(modifier)
    </insert>

    <select id="currencyNameList" resultType="com.fangcang.grfp.core.vo.CurrencyNameVO">
        SELECT
            currency_code AS currencyCode,
            currency_name AS  currencyName
        FROM
            t_currency_exchange_rate
        WHERE 1=1
        <if test="currencyCode != null and currencyCode != ''">
            AND currency_code LIKE CONCAT('%',#{currencyCode},'%')
        </if>
        order by display_order
        <if test="limitCount != null">
            limit #{limitCount}
        </if>
    </select>

    <select id="getCurrencyRateInfoList" resultType="com.fangcang.grfp.core.entity.CurrencyExchangeRateEntity">
        SELECT
            *
        FROM
            `t_currency_exchange_rate` a
        WHERE
            a.currency_code = #{fromCurrencyCode} UNION
        SELECT
            *
        FROM
            `t_currency_exchange_rate` b
        WHERE
            b.currency_code = #{toCurrencyCode}
    </select>

</mapper>
