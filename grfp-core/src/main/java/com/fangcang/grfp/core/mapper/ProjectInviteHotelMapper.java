package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.core.entity.ProjectInviteHotelEntity;
import com.fangcang.grfp.core.vo.request.project.QueryHotelGroupInviteHotelRequest;
import com.fangcang.grfp.core.vo.response.project.QueryHotelGroupInviteHotelVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 项目邀请酒店 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
public interface ProjectInviteHotelMapper extends BaseMapper<ProjectInviteHotelEntity> {

    /**
     * 批量插入或更新
     */
    void batchInsertOrUpdate(@Param("list") List<ProjectInviteHotelEntity> list);


    /**
     * 查询项目邀请酒店列表
     */
    Page<QueryHotelGroupInviteHotelVO> queryProjectInviteHotelList(IPage<?> page, @Param("request") QueryHotelGroupInviteHotelRequest request, @Param("language") Integer language);

    default ProjectInviteHotelEntity queryByProjectHotelId(Integer projectId, Long hotelId){
        LambdaQueryWrapper<ProjectInviteHotelEntity> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProjectInviteHotelEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectInviteHotelEntity::getHotelId, hotelId);
        return selectOne(queryWrapper);
    }

    /**
     * 删除酒店集团下意向酒店
     */
    int deleteByProjectIdAndHotelIds(@Param("projectId") Integer projectId, @Param("hotelIds") List<Long> hotelIds);

}
