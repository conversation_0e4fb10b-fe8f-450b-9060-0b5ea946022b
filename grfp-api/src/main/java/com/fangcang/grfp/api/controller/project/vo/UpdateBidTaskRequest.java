package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.ImportStandardBidVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(description = "更新报价任务请求")
public class UpdateBidTaskRequest extends BaseVO {

    @ApiModelProperty(value = "任务 id")
    private Long id;

    @ApiModelProperty(value = "任务详情")
    @NotNull
    private ImportStandardBidVO detail;

    @ApiModelProperty(value = "是否生成报价")
    private Boolean generatedBid = false;

}
