package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("酒店图片")
public class Image {

    /**
     * 图片ID
     */
    @ApiModelProperty("图片ID")
    private Long imageId;

    /**
     * 图片类型
     */
    @ApiModelProperty("图片类型")
    private Integer imageType;

    /**
     * 是否是主图：1-主图，0-非主图
     */
    @ApiModelProperty("是否是主图：1-主图，0-非主图")
    private Integer isMain;

    /**
     * 图片地址
     */
    @ApiModelProperty("图片地址")
    private String url;

}
