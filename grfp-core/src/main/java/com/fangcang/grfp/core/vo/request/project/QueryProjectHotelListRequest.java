package com.fangcang.grfp.core.vo.request.project;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "查询项目酒店列表请求")
public class QueryProjectHotelListRequest extends PageQuery {

    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull
    private Long projectId;

    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;

    @ApiModelProperty(value = "酒店名称")
    private String hotelName;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "酒店集团ID")
    private Long hotelGroupId;

    @ApiModelProperty(value = "酒店品牌ID")
    private Long hotelBrandId;

    @ApiModelProperty(value = "星级列表")
    private List<Integer> starList;

    @ApiModelProperty(value = "报价状态")
    private Integer bidState;

    @ApiModelProperty(value = "发票类型")
    private String invoiceType;

    @ApiModelProperty(value = "合同状态")
    private String contractStatus;

    @ApiModelProperty(value = "通知状态")
    private String notifyStatus;

    @ApiModelProperty(value = "报价机构类型 2:酒店，4:酒店集团")
    private Integer bidOrgType;

    @ApiModelProperty(value = "平台报价跟进人姓名")
    private String hotelPriceFollowName;
} 