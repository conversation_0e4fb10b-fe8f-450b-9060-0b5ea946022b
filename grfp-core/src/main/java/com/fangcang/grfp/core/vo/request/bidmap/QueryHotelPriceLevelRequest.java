package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.BidHotelPriceLevelInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("查询价格房当")
@Getter
@Setter
public class QueryHotelPriceLevelRequest extends BaseVO {

    @ApiModelProperty("酒店价格房档ID")
    @NotNull
    private Integer hotelPriceLevelId;

}
