package com.fangcang.grfp.core.enums;

public enum HotelPriceTypeEnum {

    BASE_PRICE(1, "协议价"),
    SEASON_1_PRICE(2, "Season1价"),
    SEASON_2_PRICE(3, "Season2价");

    public int key;

    public String value;

    HotelPriceTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static HotelPriceTypeEnum getEnumByKey(Integer key) {
        if (key == null) {
            return null;
        }
        for (HotelPriceTypeEnum hotelPriceTypeEnum : HotelPriceTypeEnum.values()) {
            if (hotelPriceTypeEnum.key == key.intValue()) {
                return hotelPriceTypeEnum;
            }
        }
        return null;
    }

    public static String getDateNameByKey(Integer key) {
        if (key == BASE_PRICE.key) {
            return "基础价日期";
        } else if(key == SEASON_1_PRICE.key){
            return "Season1日期";
        } else if(key == SEASON_2_PRICE.key){
            return "Season2日期";
        } else return "";
    }
}
