package com.fangcang.grfp.api.controller.textresource;

import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.textresource.service.TextResourceService;
import com.fangcang.grfp.api.controller.textresource.vo.*;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.constant.UserPermission;
import com.fangcang.grfp.core.usersession.RequiresPermissions;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.vo.TextResourceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Api(tags = "文字资源")
@RestController
@RequestMapping("/Api/TextResource")
public class TextResourceController extends BaseController {

	// ---------------------------------------------------------------------------------------------------- Private Member

	@Autowired
	private TextResourceService textResourceService;

	// ---------------------------------------------------------------------------------------------------- Public Methods

	@ApiOperation("获取文字资源")
	@PostMapping("/GetTextResource")
	@RequiresPermissions(UserPermission.TEXT_RESOURCE)
	public Result<TextResourceVO> get(HttpServletRequest request, HttpServletResponse response,
									  @Valid @RequestBody GetTextResourceRequest getTextResourceRequest) {
		// Core Logic
		TextResourceVO textResourceVO = textResourceService.get(getTextResourceRequest);

		// Return
		return Result.ok(textResourceVO);

	}
	
	@ApiOperation("获取文字资源列表")
	@PostMapping("/ListTextResource")
	@RequiresPermissions(UserPermission.TEXT_RESOURCE)
	public Result<PageVO<TextResourceVO>> list(HttpServletRequest request, HttpServletResponse response,
											   @Valid @RequestBody ListTextResourceRequest listTextResourceRequest) {

		PageVO<TextResourceVO> pageResult = textResourceService.listPage(listTextResourceRequest);

		// Return
		return Result.ok(pageResult);
		
	}
	
	// ---------------------------------------------------------------------------------------------------- Public Methods

	@ApiOperation("新增文字资源")
	@PostMapping("/DoInsertTextResource")
	@RequiresPermissions(UserPermission.TEXT_RESOURCE)
	@UserAuditLog("文字资源-新增")
	public Result<TextResourceVO> doInsert(HttpServletRequest request, HttpServletResponse response,
			@Valid @RequestBody InsertTextResourceRequest insertTextResourceRequest) {
		
		TextResourceVO textResourceVO = textResourceService.insertTextResource(UserSession.get(), insertTextResourceRequest);
		
		// Return
		return Result.ok(textResourceVO);
		
	}
	
	@ApiOperation("修改文字资源")
	@PutMapping("/DoUpdateTextResource")
	@RequiresPermissions(UserPermission.TEXT_RESOURCE)
	@UserAuditLog("文字资源-修改")
	public Result<TextResourceVO> doUpdate(HttpServletRequest request, HttpServletResponse response,
			@Valid @RequestBody UpdateTextResourceRequest updateTextResourceRequest) {
				
		// Create the return object
		TextResourceVO textResourceVO = textResourceService.updateTextResource(UserSession.get(), updateTextResourceRequest);
				
		// Return
		return Result.ok(textResourceVO);
		
	}
	
	@ApiOperation("删除文字资源")
	@PostMapping("/DoDeleteTextResource")
	@UserAuditLog("文字资源-删除")
	@RequiresPermissions(UserPermission.TEXT_RESOURCE)
	public  Result<Void> doDelete(HttpServletRequest request, HttpServletResponse response,
			@Valid @RequestBody DeleteTextResourceRequest deleteTextResourceRequest) {

        // Core Logic
        textResourceService.delete(UserSession.get(), deleteTextResourceRequest);

        // Return
        return Result.ok();
		
	}

}
