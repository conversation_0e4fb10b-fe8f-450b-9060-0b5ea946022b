package com.fangcang.grfp.core.vo.request.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@ApiModel("查询地图酒店报价和POI信息")
@Getter
@Setter
public class QueryMapPoiBidHotelInfoRequest extends BaseVO {

    // 项目ID
    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;

    // POI ID
    @ApiModelProperty("POI ID")
    private Integer poiId;

    // 酒店ID
    @ApiModelProperty("酒店ID")
    private Long hotelId;

    // 推荐酒店ID
    @ApiModelProperty("推荐酒店ID")
    private Long recommendHotelId;

    //Google经度
    @ApiModelProperty("Google经度")
    private BigDecimal lngGoogle;

    //Google纬度
    @ApiModelProperty("Google纬度")
    private BigDecimal latGoogle;

    // 距离
    @ApiModelProperty("距离")
    private int distance;

    // 酒店价格
    @ApiModelProperty(hidden = true)
    private BigDecimal price;
    // 城市
    @ApiModelProperty(hidden = true)
    private String cityCode;


}
