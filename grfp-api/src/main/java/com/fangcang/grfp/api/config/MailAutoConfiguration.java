package com.fangcang.grfp.api.config;

import com.fangcang.grfp.core.manager.MailManager;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(prefix = "spring.mail", name = "host")
public class MailAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    MailManager mailManager() {
        return new MailManager();
    }

}
