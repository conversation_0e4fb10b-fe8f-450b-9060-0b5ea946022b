package com.fangcang.grfp.api.controller.userpermission;

import com.fangcang.grfp.api.controller.BaseController;
import com.fangcang.grfp.api.controller.userpermission.service.UserPermissionService;
import com.fangcang.grfp.api.controller.userpermission.vo.AddUserPermission;
import com.fangcang.grfp.api.controller.userpermission.vo.DeleteUserPermissionRequest;
import com.fangcang.grfp.api.controller.userpermission.vo.UserPermissionNameVO;
import com.fangcang.grfp.api.controller.userpermission.vo.UserPermissionVO;
import com.fangcang.grfp.core.auditlog.UserAuditLog;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.base.Result;
import com.fangcang.grfp.core.constant.UserPermission;
import com.fangcang.grfp.core.usersession.RequiresPermissions;
import com.fangcang.grfp.core.vo.request.userpermission.QueryUserPermissionRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags = "用户权限管理")
@RestController
@RequestMapping("/Api/UserPermission")
public class UserPermissionController extends BaseController {

    @Autowired
    private UserPermissionService userPermissionService;

    @ApiOperation("用户权限列表查询")
    @PostMapping("/UserPermissionList")
    @ResponseBody
    @RequiresPermissions(UserPermission.USER_PERMISSION_C_U_D)
    public Result<List<UserPermissionNameVO>> queryUserPermissionList(HttpServletRequest request) {
        return Result.ok(userPermissionService.getUserPermissionList(request));
    }

    @ApiOperation("新增用户权限")
    @PostMapping("/AddUserPermission")
    @ResponseBody
    @UserAuditLog("用户权限管理-新增用户权限")
    @RequiresPermissions(UserPermission.USER_PERMISSION_C_U_D)
    public Result<Void> addUserPermission(HttpServletRequest request, @RequestBody AddUserPermission addUserPermission) {
        userPermissionService.addUserPermission(addUserPermission);
        return Result.ok();
    }

    @ApiOperation("删除用户权限")
    @PostMapping("/DeleteUserPermission")
    @ResponseBody
    @UserAuditLog("用户权限管理-删除用户权限")
    @RequiresPermissions(UserPermission.USER_PERMISSION_C_U_D)
    public Result<Void> deleteUserPermission(HttpServletRequest request, @RequestBody DeleteUserPermissionRequest deleteUserPermissionRequest) {
        userPermissionService.deleteUserPermissionList(deleteUserPermissionRequest.getUserPermissionId());
        return Result.ok();
    }

    @ApiOperation("分页查询用户权限")
    @PostMapping("/QueryUserPermission")
    @ResponseBody
    @RequiresPermissions(UserPermission.USER_PERMISSION_C_U_D)
    public Result<PageVO<UserPermissionVO>> queryUserPermissionPage(HttpServletRequest request, @RequestBody QueryUserPermissionRequest queryUserPermissionRequest) {
        return Result.ok(userPermissionService.queryUserPermissionPage(request, queryUserPermissionRequest));
    }




}
