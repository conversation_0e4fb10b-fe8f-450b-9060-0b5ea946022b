package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(value = "迁移报价请求")
public class MigrateTenderPriceRequest extends BaseVO {

    @ApiModelProperty(value = "项目ID")
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "请求序列号")
    private Long requestSequenceNo;

}
