package com.fangcang.grfp.core.cached.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fangcang.grfp.core.cached.CachedHotelBrandService;
import com.fangcang.grfp.core.cached.CachedHotelGroupService;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.entity.HotelBrandEntity;
import com.fangcang.grfp.core.entity.HotelGroupEntity;
import com.fangcang.grfp.core.entity.OrgRelatedHotelBrandEntity;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.mapper.HotelBrandMapper;
import com.fangcang.grfp.core.mapper.HotelGroupMapper;
import com.fangcang.grfp.core.mapper.OrgRelatedHotelBrandMapper;
import com.fangcang.grfp.core.util.GenericAppUtility;
import com.fangcang.grfp.core.vo.HotelGroupUserRelatedInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CachedHotelGroupServiceImpl implements CachedHotelGroupService {

    @Autowired
    private HotelGroupMapper hotelGroupMapper;
    @Autowired
    private OrgRelatedHotelBrandMapper orgRelatedHotelBrandMapper;

    @Cacheable(value="cachedHotelGroupService.getNameMap", key = "#languageId", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public Map<Long, String> getNameMap(int languageId) {
        Map<Long, String> nameMap = new HashMap<>();
        LambdaQueryWrapper<HotelGroupEntity>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(languageId == LanguageEnum.EN_US.key){
            lambdaQueryWrapper.select(HotelGroupEntity::getHotelGroupId, HotelGroupEntity::getNameEnUs);
        }
        if(languageId == LanguageEnum.ZH_CN.key){
            lambdaQueryWrapper.select(HotelGroupEntity::getHotelGroupId, HotelGroupEntity::getNameZhCn);
        }
        List<HotelGroupEntity> hotelGroupEntityList = hotelGroupMapper.selectList(lambdaQueryWrapper);
        hotelGroupEntityList.forEach(item -> {
            if(languageId == LanguageEnum.EN_US.key){
                nameMap.put(item.getHotelGroupId(), item.getNameEnUs());
            }
            if(languageId == LanguageEnum.ZH_CN.key){
                nameMap.put(item.getHotelGroupId(), item.getNameZhCn());
            }
        });
        if(nameMap.isEmpty()){
            return null;
        }
        log.info("HotelGroupNameMap size {}, {}", LanguageEnum.getValueByKey(languageId), nameMap.size());
        return nameMap;
    }

    @Override
    @CacheEvict(value="cachedHotelGroupService.getNameMap", key = "#languageId", cacheManager = "ehCacheCacheManager")
    public void clearNameMap(int languageId) {
        log.info("clear HotelGroup NameMap {}", LanguageEnum.getValueByKey(languageId));
    }

    @Override
    @Cacheable(value="cachedHotelGroupService.getHotelGroupUserRelatedInfoVO", key = "#hotelGroupOrgId + '_' + #employeeUserId", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public HotelGroupUserRelatedInfoVO getHotelGroupUserRelatedInfoVO(Integer hotelGroupOrgId, Integer employeeUserId){
        List<OrgRelatedHotelBrandEntity> orgRelatedHotelBrandList = orgRelatedHotelBrandMapper.queryHotelGroupOrgRelatedBrandList(hotelGroupOrgId);
        Set<Long> hotelBrandIdSet = orgRelatedHotelBrandList.stream().map(OrgRelatedHotelBrandEntity::getHotelBrandId).collect(Collectors.toSet());
        HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfoVO = new HotelGroupUserRelatedInfoVO();
        hotelGroupUserRelatedInfoVO.setHotelGroupOrgId(hotelGroupOrgId);
        hotelGroupUserRelatedInfoVO.setEmployeeUserId(employeeUserId);
        hotelGroupUserRelatedInfoVO.setHotelBrandIdSet(hotelBrandIdSet);
        if(CollectionUtils.isEmpty(hotelBrandIdSet)){
            log.error("酒店集团机构没有配置酒店品牌信息 hotelGroupOrgId {}",hotelGroupOrgId);
            GenericAppUtility.serviceError(ErrorCode.CANNOT_FOUND_RELATED_HOTEL_BRAND_BY_ORG);
        }
        return hotelGroupUserRelatedInfoVO;
    }

}
