<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.TextResourceMapper">

    <select id="getLastUpdatedTime" resultType="date">
        SELECT
            MAX(`modify_time`)
        FROM `t_text_resource`
        <if test="textResourceType != null and textResourceType > 0">
            WHERE `text_resource_type` = #{textResourceType}
        </if>
    </select>

    <select id="queryPageList" resultType="com.fangcang.grfp.core.entity.TextResourceEntity">
        SELECT
            *
        FROM
            `t_text_resource`
        <where>
            <if test="searchTextResourceType != null and searchTextResourceType > 0">
                AND `text_resource_type` = #{searchTextResourceType,jdbcType=INTEGER}
            </if>
            <if test="searchTextResourceCodeLike != null and searchTextResourceCodeLike != ''">
                AND `text_resource_code` like   CONCAT('%', #{searchTextResourceCodeLike}, '%')
            </if>
            <if test="searchTextResourceValueLike != null and searchTextResourceValueLike != ''">
                AND
                (
                `value_en_us` like   CONCAT('%', #{searchTextResourceValueLike}, '%')
                OR `value_zh_cn` like   CONCAT('%', #{searchTextResourceValueLike}, '%')
                )
            </if>
        </where>
        ORDER BY `create_time` DESC
    </select>

    <select id="queryListForExport" resultType="com.fangcang.grfp.core.entity.TextResourceEntity">
        SELECT
            *
        FROM
            `t_text_resource`
        <where>
            <if test="searchTextResourceType != null and searchTextResourceType > 0">
                AND `text_resource_type` = #{searchTextResourceType,jdbcType=INTEGER}
            </if>
            <if test="searchTextResourceCodeLike != null and searchTextResourceCodeLike != ''">
                AND `text_resource_code` like   CONCAT('%', #{searchTextResourceCodeLike}, '%')
            </if>
            <if test="searchTextResourceValueLike != null and searchTextResourceValueLike != ''">
                AND
                (
                `value_en_us` like   CONCAT('%', #{searchTextResourceValueLike}, '%')
                OR `value_zh_cn` like   CONCAT('%', #{searchTextResourceValueLike}, '%')
                )
            </if>
        </where>
        ORDER BY `create_time` DESC
    </select>
</mapper>
