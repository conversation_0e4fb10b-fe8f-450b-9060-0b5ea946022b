package com.fangcang.grfp.core.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.entity.CityEntity;
import com.fangcang.grfp.core.entity.ProvinceEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.Date;

@ApiModel("城市列表信息")
@Getter
@Setter
public class ListCityDataVO extends BaseVO {


    public ListCityDataVO() {
    }

    public ListCityDataVO(CityEntity cityEntity) {
        BeanUtils.copyProperties(cityEntity, this);
    }

    /**
     * 国家编号
     */
    @ApiModelProperty("国家编号")
    private String countryCode;

    /**
     * 省份编号
     */
    @ApiModelProperty("省份编号")
    private String provinceCode;

    /**
     * 城市编号
     */
    @ApiModelProperty("城市编号")
    private String cityCode;

    /**
     * 英文名称
     */
    @ApiModelProperty("英文名称")
    private String nameEnUs;

    /**
     * 中文名称
     */
    @ApiModelProperty("中文名称")
    private String nameZhCn;

    /**
     * 父城市编号
     */
    @ApiModelProperty("父城市编号")
    private String parentCityCode;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

}
