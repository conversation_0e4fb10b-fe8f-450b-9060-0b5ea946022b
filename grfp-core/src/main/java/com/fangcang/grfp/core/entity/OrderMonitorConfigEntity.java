package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 订单监控配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_order_monitor_config")
public class OrderMonitorConfigEntity extends BaseVO {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 机构id
     */
    @TableField("org_id")
    private Long orgId;

    /**
     * 分销商编码
     */
    @TableField("distributor_code")
    private String distributorCode;

    /**
     * 分销商名称
     */
    @TableField("distributor_name")
    private String distributorName;

    /**
     * 有效状态：0-无效，1-有效
     */
    @TableField("state")
    private Integer state;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
