package com.fangcang.grfp.server.config;

import com.fangcang.grfp.core.oss.IOssClient;
import com.fangcang.grfp.core.oss.OssManager;
import com.fangcang.grfp.core.oss.OssProperties;
import com.fangcang.grfp.core.oss.TosClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * cos 配置
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(OssProperties.class)
public class OssAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public OssManager ossManager(IOssClient ossClient, OssProperties properties) {
        return new OssManager(ossClient, properties);
    }

    @Bean
    @ConditionalOnMissingBean
    public IOssClient ossClient(OssProperties properties) {
        String region = properties.getRegion();
        String accessKeyId = properties.getAccessKeyId();
        String accessKeySecret = properties.getAccessKeySecret();
        String endpoint = properties.getEndpoint();
        log.info("---------------Init OssClient ----------------");
        // 默认火山云 tos
       return new TosClient(endpoint, region, accessKeyId, accessKeySecret);
    }
}
