package com.fangcang.grfp.api.controller.currencyexchangerate.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.api.controller.currencyexchangerate.vo.*;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.constant.CurrencyConstant;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.entity.*;
import com.fangcang.grfp.core.manager.CurrencyExchangeRateManager;
import com.fangcang.grfp.core.mapper.*;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.StringUtility;
import com.fangcang.grfp.core.vo.CurrencyCrossRateVO;
import com.fangcang.grfp.core.vo.CurrencyExchangeRateInfoVO;
import com.fangcang.grfp.core.vo.request.ListCurrencyExchangeRateRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CurrencyExchangeRateService {

    @Autowired
    private CurrencyExchangeRateMapper currencyExchangeRateMapper;

    @Autowired
    private CurrencyExchangeRateLogMapper currencyExchangeRateLogMapper;

    @Autowired
    private CurrencyExchangeRateManager exchangeRateManager;

    public PageVO<CurrencyCrossRateVO> currencyExchangeRatePage(HttpServletRequest request, ListCurrencyExchangeRateRequest exchangeRateRequest){
        // from 如果为空 则填充默认USD, 不为空 则填充入参值
        String fromCurrency = StringUtility.notNullAndNotEmpty(exchangeRateRequest.getFromCurrencyCode()) ? exchangeRateRequest.getFromCurrencyCode() : CurrencyConstant.USD;
        // 获取 from 基准汇率
        CurrencyExchangeRateEntity fromEntity = currencyExchangeRateMapper.selectOne(new LambdaQueryWrapper<CurrencyExchangeRateEntity>()
                .eq(CurrencyExchangeRateEntity::getCurrencyCode, fromCurrency));
        // 如果传了 但没有这个币种
        if (fromEntity == null) {
            return new PageVO<>(0,  0, Arrays.asList());
        }
        String toCurrency = exchangeRateRequest.getToCurrencyCode();
        // 如果查询 指定2个币种之间的兑换汇率  fromCurrency --> toCurrency
        if (StringUtility.notNullAndNotEmpty(toCurrency)) {
            CurrencyExchangeRateEntity toCurrencyEntity = currencyExchangeRateMapper.selectOne(new LambdaQueryWrapper<CurrencyExchangeRateEntity>()
                    .eq(CurrencyExchangeRateEntity::getCurrencyCode, toCurrency));
            // 如果传了 但没有这个币种
            if (toCurrencyEntity == null) {
                return new PageVO<>(0,  0, Arrays.asList());
            }
            CurrencyCrossRateVO crossRateVO =  exchangeRateManager.getCurrencyCrossRate(fromCurrency, toCurrency);
            return new PageVO<>(1,  1, Arrays.asList(crossRateVO));
        } else { // currency --> allCurrency

            // 获取to 基准汇率
            IPage<CurrencyExchangeRateEntity> page = new Page<>(exchangeRateRequest.getPageIndex(), exchangeRateRequest.getPageSize());
            LambdaQueryWrapper<CurrencyExchangeRateEntity> queryWrapper = Wrappers.lambdaQuery(CurrencyExchangeRateEntity.class)
                    .orderByDesc(CurrencyExchangeRateEntity::getDisplayOrder);
            page = currencyExchangeRateMapper.selectPage(page, queryWrapper);
            // 计算交叉汇率
            List<CurrencyCrossRateVO> crossRateVoList = new ArrayList<>();
            for(CurrencyExchangeRateEntity toCurrencyEntity : page.getRecords()) {
                CurrencyCrossRateVO crossRateVO = coverToRateVO(fromEntity, toCurrencyEntity);
                crossRateVoList.add(crossRateVO);
            }
            return new PageVO<>((int)page.getTotal(), (int)page.getPages(), crossRateVoList);

        }
    }

    private CurrencyCrossRateVO coverToRateVO(CurrencyExchangeRateEntity fromInfo, CurrencyExchangeRateEntity toInfo) {
        CurrencyCrossRateVO crossRateVO = new CurrencyCrossRateVO();
        if(fromInfo != null && toInfo != null){
            crossRateVO.setCurrencyCodeFrom(fromInfo.getCurrencyCode());
            crossRateVO.setCurrencyNameFrom(fromInfo.getCurrencyName());
            crossRateVO.setCurrencyCodeTo(toInfo.getCurrencyCode());
            crossRateVO.setCurrencyNameTo(toInfo.getCurrencyName());
            crossRateVO.setExchangeRate(toInfo.getExchangeRate().divide(fromInfo.getExchangeRate(),10, RoundingMode.HALF_UP));
            crossRateVO.setInverseExchangeRate(new BigDecimal(1).divide(crossRateVO.getExchangeRate(),10, RoundingMode.HALF_UP));
            if (fromInfo.getModifyTime().after(toInfo.getModifyTime())) {
                crossRateVO.setModifyTime(fromInfo.getModifyTime());
            } else {
                crossRateVO.setModifyTime(toInfo.getModifyTime());
            }
        }
        return crossRateVO;
    }

    public String addCurrencyExchangeRate(HttpServletRequest request, AddCurrencyExchangeRequest addCurrencyExchangeRequest){
        // 检查币种是否重复
        CurrencyExchangeRateEntity fromEntity = currencyExchangeRateMapper.selectOne(new LambdaQueryWrapper<CurrencyExchangeRateEntity>()
                .eq(CurrencyExchangeRateEntity::getCurrencyCode, addCurrencyExchangeRequest.getNewCurrencyCode()));
        if (fromEntity != null) {
            AppUtility.serviceError(ErrorCode.CURRENCY_CODE_EXIST); // 币种已存在 不能新增
        }
        UserSession userSession = UserSession.get();
        // 新币种汇率信息
        CurrencyExchangeRateEntity newCurrency = new CurrencyExchangeRateEntity();
        newCurrency.setCreator(userSession.getUsername());
        newCurrency.setModifier(userSession.getUsername());

        newCurrency.setCurrencyCode(addCurrencyExchangeRequest.getNewCurrencyCode());
        newCurrency.setCurrencyName(addCurrencyExchangeRequest.getNewCurrencyName());
        newCurrency.setExchangeRate(addCurrencyExchangeRequest.getExchangeRate());
        if (addCurrencyExchangeRequest.getDisplayOrder() != null) {
            newCurrency.setDisplayOrder(addCurrencyExchangeRequest.getDisplayOrder());
        }
        newCurrency.setInverseExchangeRate(new BigDecimal(1).divide(addCurrencyExchangeRequest.getExchangeRate(),10, RoundingMode.HALF_UP));
        // 手动添加的币种 is_auto_sync 为0
        newCurrency.setIsAutoSync(0);
        // 新增项目
        AppUtility.doInsert(currencyExchangeRateMapper.insert(newCurrency));

        // 添加日志 t_currency_rate_log
        exchangeRateManager.recordCurrencyExchangeLog(Arrays.asList(newCurrency));

        // 返回新币种
        return addCurrencyExchangeRequest.getNewCurrencyCode();
    }

    /**
     *  获取币种信息
     * @param request
     * @param currencyCode
     * @return
     */
    public CurrencyExchangeRateInfoVO currencyExchangeRateInfo(HttpServletRequest request, String currencyCode) {
        CurrencyExchangeRateInfoVO currencyInfoVO = exchangeRateManager.getCurrencyExchangeRateInfo(currencyCode);
        return currencyInfoVO;
    }

    /**
     *  删除币种信息
     * @param request
     * @param currencyCode
     * @return
     */
    public void deleteCurrencyExchangeRate(HttpServletRequest request, String currencyCode) {
        UserSession userSession = UserSession.get();
        if (currencyCode.equals(CurrencyConstant.USD)) {
            AppUtility.serviceError(ErrorCode.USD_CURRENCY_CAN_NOT_UPDATE); // 基准汇率美元不允许修改
        }
        AppUtility.doUpdateOneRecord(currencyExchangeRateMapper.deleteById(currencyCode));
        // 添加日志 t_currency_exchange_rate_log
        // 新币种汇率信息
        CurrencyExchangeRateEntity newCurrency = new CurrencyExchangeRateEntity();
        newCurrency.setModifier(userSession.getUsername());
        newCurrency.setCurrencyCode(currencyCode);
        exchangeRateManager.recordCurrencyExchangeLog(Arrays.asList(newCurrency));
    }

    public List<CurrencyExchangeRateInfoVO> updateCurrencyExchangeRate(HttpServletRequest request, UpdateCurrencyExchangeRequest updateCurrencyExchangeRequest){
        UserSession userSession = UserSession.get();

        List<UpdateCurrencyExchangeVO>  updateCurrencyList = updateCurrencyExchangeRequest.getUpdateCurrencyList();
        List<CurrencyExchangeRateInfoVO> returnList = new ArrayList<>();

        for(UpdateCurrencyExchangeVO update : updateCurrencyList) {
            // 检查币种是否
            CurrencyExchangeRateEntity fromEntity = currencyExchangeRateMapper.selectOne(new LambdaQueryWrapper<CurrencyExchangeRateEntity>()
                    .eq(CurrencyExchangeRateEntity::getCurrencyCode, update.getCurrencyCode()));
            if (fromEntity.getCurrencyCode().equals(CurrencyConstant.USD)) {
                AppUtility.serviceError(ErrorCode.USD_CURRENCY_CAN_NOT_UPDATE); // 基准汇率美元不允许修改
            }

            if (fromEntity == null) {
                AppUtility.serviceError(ErrorCode.CURRENCY_CODE_NOT_EXIST); // 币种不存在 不能修改
            }

            // 新币种汇率信息
            CurrencyExchangeRateEntity newCurrency = new CurrencyExchangeRateEntity();
            newCurrency.setModifier(userSession.getUsername());
            newCurrency.setCurrencyCode(update.getCurrencyCode());
            newCurrency.setExchangeRate(update.getExchangeRate());
            newCurrency.setCurrencyName(update.getCurrencyName());
            if (update.getDisplayOrder() != null) {
                newCurrency.setDisplayOrder(update.getDisplayOrder());
            }
            newCurrency.setInverseExchangeRate(new BigDecimal(1).divide(update.getExchangeRate(),10, RoundingMode.HALF_UP));
            // 修改汇率
            AppUtility.doUpdateOneRecord(currencyExchangeRateMapper.updateById(newCurrency));
            // 添加日志 t_currency_exchange_rate_log
            exchangeRateManager.recordCurrencyExchangeLog(Arrays.asList(newCurrency));
            // 返回币种信息
            CurrencyExchangeRateInfoVO currencyInfoVO = exchangeRateManager.getCurrencyExchangeRateInfo(newCurrency.getCurrencyCode());
            returnList.add(currencyInfoVO);
        }
        return returnList;
    }

    public PageVO<CurrencyExchangeRateLogVO> currencyExchangeRateLogPage(HttpServletRequest request, CurrencyExchangeRateLogPageRequest exchangeRateLogPageRequest){
        IPage<CurrencyExchangeRateLogEntity> page = new Page<>(exchangeRateLogPageRequest.getPageIndex(), exchangeRateLogPageRequest.getPageSize());
        page = currencyExchangeRateLogMapper.queryPageList(page, exchangeRateLogPageRequest.getCurrencyCode());
        List<CurrencyExchangeRateLogVO> pageVOList = page.getRecords().stream().map(CurrencyExchangeRateLogVO::new).collect(Collectors.toList());
        return new PageVO<>((int)page.getTotal(), (int)page.getPages(), pageVOList);
    }


}
