package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.core.entity.OrgRelatedHotelBrandEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.vo.OrgRelatedHotelBrandVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 机构关联酒店品牌 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
public interface OrgRelatedHotelBrandMapper extends BaseMapper<OrgRelatedHotelBrandEntity> {

    default int deleteByOrgId(Integer orgId){
        return delete(new LambdaQueryWrapper<OrgRelatedHotelBrandEntity>().eq(OrgRelatedHotelBrandEntity::getOrgId, orgId));
    }

    default OrgRelatedHotelBrandEntity selectOne(Integer hotelGroupOrgId, Long hotelGroupId, Long hotelBrandId){
        LambdaQueryWrapper<OrgRelatedHotelBrandEntity>lambdaQueryWrapper = new LambdaQueryWrapper<OrgRelatedHotelBrandEntity>();
        lambdaQueryWrapper.eq(OrgRelatedHotelBrandEntity::getOrgId, hotelGroupOrgId);
        lambdaQueryWrapper.eq(OrgRelatedHotelBrandEntity::getHotelGroupId, hotelGroupId);
        lambdaQueryWrapper.eq(OrgRelatedHotelBrandEntity::getHotelBrandId, hotelBrandId);
        return this.selectOne(lambdaQueryWrapper);
    }

    default int delete(Integer hotelGroupOrgId, Long hotelGroupId, Long hotelBrandId){
        LambdaUpdateWrapper<OrgRelatedHotelBrandEntity> lambdaQueryWrapper = new LambdaUpdateWrapper<OrgRelatedHotelBrandEntity>();
        lambdaQueryWrapper.eq(OrgRelatedHotelBrandEntity::getOrgId, hotelGroupOrgId);
        lambdaQueryWrapper.eq(OrgRelatedHotelBrandEntity::getHotelGroupId, hotelGroupId);
        lambdaQueryWrapper.eq(OrgRelatedHotelBrandEntity::getHotelBrandId, hotelBrandId);
        return this.delete(lambdaQueryWrapper);
    };

    /**
     * 查询酒店集团机构管理品牌
     */
   default List<OrgRelatedHotelBrandEntity> queryHotelGroupOrgRelatedBrandList(int hotelGroupOrgId){
       LambdaQueryWrapper<OrgRelatedHotelBrandEntity>lambdaQueryWrapper = new LambdaQueryWrapper<OrgRelatedHotelBrandEntity>();
       lambdaQueryWrapper.eq(OrgRelatedHotelBrandEntity::getOrgId, hotelGroupOrgId);
       return this.selectList(lambdaQueryWrapper);
   }

    /**
     * 查询酒店集团机构管理品牌
     */
    default List<OrgRelatedHotelBrandEntity> queryHotelGroupOrgRelatedBrandList(int hotelGroupOrgId, Long hotelGroupId){
        LambdaQueryWrapper<OrgRelatedHotelBrandEntity>lambdaQueryWrapper = new LambdaQueryWrapper<OrgRelatedHotelBrandEntity>();
        lambdaQueryWrapper.eq(OrgRelatedHotelBrandEntity::getOrgId, hotelGroupOrgId);
        lambdaQueryWrapper.eq(OrgRelatedHotelBrandEntity::getHotelGroupId, hotelGroupId);
        return this.selectList(lambdaQueryWrapper);
    }

    IPage<OrgRelatedHotelBrandVO> queryPageList(IPage<OrgRelatedHotelBrandVO> page,
                                                                @Param("languageId") Integer languageId,
                                                               @Param("orgId") Integer orgId,
                                                               @Param("hotelGroupId") Long hotelGroupId, @Param("hotelBrandId") Long hotelBrandId);

    List<Long> queryHotelGroupBrandIds(@Param("orgIds") List<Integer> orgIds);

}
