package com.fangcang.grfp.core.enums;

import com.fangcang.grfp.core.util.GenericAppUtility;

/**
 * 报价机构类型枚举
 */
public enum BidOrgTypeEnum {

    HOTEL(2, "酒店", "Hotel"),
    HOTEL_GROUP(4, "酒店集团", "Hotel Group");

    public final Integer key;
    public final String nameZhCn;
    public final String nameEnUs;

    BidOrgTypeEnum(Integer key, String nameZhCn, String nameEnUs) {
        this.key = key;
        this.nameZhCn = nameZhCn;
        this.nameEnUs = nameEnUs;
    }

    public static BidOrgTypeEnum getByKey(Integer key) {
        if (key == null) {
            return null;
        }
        for (BidOrgTypeEnum item : BidOrgTypeEnum.values()) {
            if (item.key.equals(key)) {
                return item;
            }
        }
        return null;
    }

    public static String getNameByKey(Integer key) {
        BidOrgTypeEnum item = getByKey(key);
        return item != null ? item.nameZhCn : "";
    }

    public static String getTextByKey(Integer key, int languageId) {
        BidOrgTypeEnum item = getByKey(key);
        if (item == null) {
            return "";
        }
        String textCode = "BID_ORG_TYPE_" + key;
        return GenericAppUtility.getText(languageId, textCode);
    }
} 