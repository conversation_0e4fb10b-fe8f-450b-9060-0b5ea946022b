2025-05-21 15:07:47.243|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.execute:91|Sync province city data job error org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'parent_city_code' cannot be null
### The error may exist in file [D:\RFP\grfp-project\grfp-core\target\classes\mapper\CityMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: insert into t_city(country_code, province_code, city_code, parent_city_code, name_zh_cn, name_en_us)         values                        (?, ?, ?, ?, ?, ?)          ,              (?, ?, ?, ?, ?, ?)          ,              (?, ?, ?, ?, ?, ?)          ,              (?, ?, ?, ?, ?, ?)          ,              (?, ?, ?, ?, ?, ?)          ,              (?, ?, ?, ?, ?, ?)          ,              (?, ?, ?, ?, ?, ?)          ,              (?, ?, ?, ?, ?, ?)          ,              (?, ?, ?, ?, ?, ?)          ,              (?, ?, ?, ?, ?, ?)          ,              (?, ?, ?, ?, ?, ?)          ,              (?, ?, ?, ?, ?, ?)                   on duplicate key update country_code     = values(country_code),                                 province_code    = values(province_code),                                 city_code        = values(city_code),                                 parent_city_code = values(parent_city_code),                                 name_zh_cn       = values(name_zh_cn),                                 name_en_us       = values(name_en_us)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'parent_city_code' cannot be null
; Column 'parent_city_code' cannot be null; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'parent_city_code' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:87)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy89.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:271)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:60)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy101.batchUpsert(Unknown Source)
	at com.fangcang.grfp.server.job.SyncProvinceCityDataJob.saveData(SyncProvinceCityDataJob.java:161)
	at com.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$execute$1(SyncProvinceCityDataJob.java:85)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at com.fangcang.grfp.server.job.SyncProvinceCityDataJob.execute(SyncProvinceCityDataJob.java:62)
	at com.fangcang.grfp.server.job.SyncProvinceCityDataJobTest.testExecute(SyncProvinceCityDataJobTest.java:21)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:686)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84)
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:212)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:208)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:71)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:135)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:232)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'parent_city_code' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy131.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:56)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.update(MybatisCachingExecutor.java:85)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 76 common frames omitted
 [grfp-api,] ------
2025-05-21 15:15:22.495|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.execute:91|Sync province city data job error java.lang.NullPointerException: null
	at com.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0(SyncProvinceCityDataJob.java:75)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$execute$1(SyncProvinceCityDataJob.java:68)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at com.fangcang.grfp.server.job.SyncProvinceCityDataJob.execute(SyncProvinceCityDataJob.java:62)
	at com.fangcang.grfp.server.job.SyncProvinceCityDataJobTest.testExecute(SyncProvinceCityDataJobTest.java:21)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:686)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84)
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:212)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:208)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:71)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:135)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:232)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
 [grfp-api,] ------
2025-05-21 15:17:31.673|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:17:34.298|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:17:55.162|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:17:58.008|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:17:59.716|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:08.632|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:18.224|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:18.851|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:18.885|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:18.920|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:18.946|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:19.001|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:19.059|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:19.104|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:19.160|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:19.205|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:19.262|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:19.318|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:19.374|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:19.430|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:19.476|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:19.528|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:19.550|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:19.608|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:19.667|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:19.717|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:19.737|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:19.790|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:19.841|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:19.898|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:19.951|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:20.006|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:20.063|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:20.119|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:20.164|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:20.219|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:20.275|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:20.338|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:20.360|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:20.403|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:20.461|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:20.517|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:20.570|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:20.617|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:20.677|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:20.701|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:20.747|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:20.806|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:20.859|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:20.904|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:20.962|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:21.018|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:21.065|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:21.106|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:21.150|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:21.208|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:21.261|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:21.303|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:21.323|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:21.374|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:21.417|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:21.438|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:21.496|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:21.543|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:21.587|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:21.641|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:21.693|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:21.714|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:21.769|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:21.813|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:21.861|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:21.914|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:21.958|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:22.001|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:22.046|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:22.066|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:22.120|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:22.171|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:22.217|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:22.270|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:22.325|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:22.374|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:22.426|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:22.451|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:22.478|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:22.532|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:22.589|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:18:22.634|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:18:22.688|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:12.457|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:12.502|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:12.547|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:12.604|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:12.648|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:12.691|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:12.735|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:12.787|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:12.839|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:12.882|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:12.936|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:12.991|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:13.044|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:13.085|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:13.138|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:13.192|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:13.239|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:13.293|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:13.314|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:13.368|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:13.413|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:13.467|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:13.510|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:13.555|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:13.607|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:13.653|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:13.704|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:13.760|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:13.813|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:13.838|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:13.894|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:13.949|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:13.999|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:14.020|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:14.041|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:14.060|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:14.103|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:14.149|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:14.201|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:14.245|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:14.292|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:14.336|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:14.387|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:14.438|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:14.482|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:14.533|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:14.582|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:14.627|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:14.680|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:14.731|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:14.775|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:14.829|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:14.880|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:14.933|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:14.988|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:15.040|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:15.085|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:15.137|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:15.188|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:15.241|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:15.294|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:15.339|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:15.395|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:15.444|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:15.498|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:15.548|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:15.605|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:15.633|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:15.655|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:15.707|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:15.750|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:15.795|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:15.848|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:15.900|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:15.955|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:16.008|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:16.057|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:16.101|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:16.150|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:16.174|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:16.196|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:16.242|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:16.290|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:16.334|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:16.386|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:16.412|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:16.433|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:16.474|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:16.530|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:16.570|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:16.618|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:16.682|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:16.735|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:16.780|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:16.824|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:16.868|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:16.920|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:16.941|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:16.991|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:17.041|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:17.094|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:17.119|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:17.171|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:17.215|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:17.268|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:17.320|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:17.342|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:17.392|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:17.435|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:17.477|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:17.519|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:17.569|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:17.626|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:17.649|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:17.673|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:17.717|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:17.769|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:17.822|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:17.875|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:17.920|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:17.972|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:18.028|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:18.081|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:18.135|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:18.182|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:18.235|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:18.255|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:18.301|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:18.351|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:18.400|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:18.451|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:18.501|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:18.546|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:18.601|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:18.655|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:18.708|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:18.761|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:18.811|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:18.864|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:18.885|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:18.937|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:18.985|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:19.048|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:19.100|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:19.142|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:19.196|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:19.245|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:19.269|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:19.288|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:19.341|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:19.384|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:19.430|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:19.450|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:19.503|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:19.547|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:19.603|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:19.625|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:19.680|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:19.723|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:19.774|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:19.819|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:19.839|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:19.881|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:19.931|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:19.986|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:20.038|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:20.088|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:20.134|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:20.179|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:20.228|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:20.281|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:20.331|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:20.382|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:20.429|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:20.474|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:20.520|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:20.573|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:20.627|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:20.680|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:20.728|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:20.780|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:20.803|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:20.849|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:20.892|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:20.941|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:20.993|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:21.052|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:21.105|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:21.154|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:21.204|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:21.252|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:21.304|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:21.346|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:21.403|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:21.453|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:21.507|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:21.559|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:21.611|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:21.636|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:21.659|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:21.711|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:21.733|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:21.774|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:21.827|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:21.874|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:21.929|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:21.952|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:22.004|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:19:22.061|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:19:22.082|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:31.524|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:31.957|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:35.439|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:35.969|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:36.347|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:38.378|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:41.059|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:41.658|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:41.704|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:41.734|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:41.758|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:41.840|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:41.896|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:41.948|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:41.966|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:42.019|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:42.038|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:42.089|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:42.139|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:42.195|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:42.260|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:42.326|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:42.349|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:42.412|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:42.472|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:42.536|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:42.581|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:42.638|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:42.662|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:42.685|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:42.745|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:42.802|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:42.858|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:42.923|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:42.973|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:43.032|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:43.089|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:43.117|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:43.171|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:43.242|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:43.290|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:43.353|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:43.414|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:43.464|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:43.512|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:43.537|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:43.586|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:43.611|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:43.667|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:43.719|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:43.774|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:43.832|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:43.888|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:43.916|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:43.971|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:44.030|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:44.049|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:44.108|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:44.162|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:44.219|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:44.270|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:44.332|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:44.387|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:44.437|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:44.489|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:44.539|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:44.593|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:44.653|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:44.713|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:44.743|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:44.766|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:44.824|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:44.846|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:44.906|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:44.958|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:45.012|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:45.069|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:45.130|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:45.183|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:45.240|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:45.296|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:45.322|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:45.369|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:45.426|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:45.452|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:45.510|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:45.534|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:45.564|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:45.586|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:45.646|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:45.668|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:45.715|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:45.766|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:45.822|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:45.875|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:45.931|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:45.983|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:46.040|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:46.092|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:46.148|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:46.191|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:46.243|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:46.284|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:46.311|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:46.358|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:46.421|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:46.449|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:46.477|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:46.504|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:46.531|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:46.588|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:46.641|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:46.693|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:46.744|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:46.796|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:46.845|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:46.899|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:46.959|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:47.015|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:47.074|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:47.131|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:47.188|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:47.241|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:47.302|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:47.354|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:47.407|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:47.460|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:47.486|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:47.533|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:47.582|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:47.605|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:47.627|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:47.683|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:47.739|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:47.792|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:47.843|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:47.895|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:47.931|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:47.952|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:48.008|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:48.061|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:48.110|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:48.157|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:48.206|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:48.261|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:48.336|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:48.391|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:48.419|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:48.472|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:48.525|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:48.582|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:48.637|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:48.689|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:48.747|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:48.790|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:48.842|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:48.861|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:48.919|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:48.972|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:49.021|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:49.067|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:49.091|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:49.144|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:49.199|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:49.242|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:49.297|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:49.316|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:49.372|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:49.429|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:49.485|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:49.539|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:49.567|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:49.614|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:49.641|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:49.660|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:49.729|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:49.785|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:49.843|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:49.886|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:49.946|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:49.988|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:50.037|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:50.086|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:50.141|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:50.194|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:50.241|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:50.289|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:50.336|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:50.355|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:50.412|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:50.469|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:50.533|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:50.556|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:50.614|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:50.673|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:50.722|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:50.769|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:50.828|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:50.850|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:50.908|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:50.958|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:51.014|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:51.067|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:51.126|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:51.178|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:51.228|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:51.269|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:51.326|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:51.378|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:51.425|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:51.474|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:51.499|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:51.542|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:51.598|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:51.652|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:51.714|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:51.765|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:51.810|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:51.853|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:51.911|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:51.966|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:52.014|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:52.058|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:52.116|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:52.168|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:52.227|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:52.278|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:52.327|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:52.346|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:52.402|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:52.452|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:52.500|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:52.559|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:52.587|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:52.640|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:52.686|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:52.739|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:52.789|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:52.844|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:52.903|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:52.925|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:52.972|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:53.024|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:53.085|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:53.137|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:53.194|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:53.215|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:53.272|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:53.322|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:53.380|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:53.422|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:53.470|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:53.524|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:53.573|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:53.619|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:53.667|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:53.723|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:53.778|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:53.830|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:53.888|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:53.943|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:53.993|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:54.043|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:54.100|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:54.151|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:54.198|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:54.242|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:54.302|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:54.353|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:54.380|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:54.436|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:54.484|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:54.537|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:54.595|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:54.635|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:54.680|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:54.734|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:54.800|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:54.842|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:54.890|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:54.914|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:54.938|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:54.992|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:55.057|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:55.107|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:55.159|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:55.211|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:55.272|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:55.329|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:55.395|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:55.439|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:55.510|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:55.577|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:55.652|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:55.711|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:55.776|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:55.835|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:55.910|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:55.974|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:56.033|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:56.053|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:56.123|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:56.171|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:56.234|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:56.289|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:56.346|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:56.369|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:56.496|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:56.518|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:56.593|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:56.617|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:56.686|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:56.737|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:56.799|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:56.849|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:56.915|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:56.980|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:57.046|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:57.102|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:57.189|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:57.243|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:57.296|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:57.348|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:57.404|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:57.458|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:57.514|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:57.535|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:57.603|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:57.626|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:57.737|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:57.761|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:57.823|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:57.884|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:57.947|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:58.012|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:58.061|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:58.109|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:58.167|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:58.230|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:58.256|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:58.283|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:58.305|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:58.371|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:58.433|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:58.492|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:58.551|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:58.603|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:58.675|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:58.721|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:58.781|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:58.837|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:58.866|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:58.912|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:58.969|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:59.020|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:59.078|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:59.126|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:59.182|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:59.233|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:59.277|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:59.333|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:59.359|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:59.411|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:59.475|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:59.526|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:59.576|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:59.628|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:59.657|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:59.691|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:59.717|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:59.771|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:59.827|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:59.853|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:59.880|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:22:59.928|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:22:59.987|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:23:00.032|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:23:00.055|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:23:00.109|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:23:00.167|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:23:00.212|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:23:00.259|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:23:00.300|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:23:00.350|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:23:00.393|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:23:00.441|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:23:00.460|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:23:00.517|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:23:00.568|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:23:00.595|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:23:00.643|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:23:00.668|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:23:00.716|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:23:00.775|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:23:00.818|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:23:37.680|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:76|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:29:29.438|main|ERROR|com.zaxxer.hikari.pool.HikariPool.throwPoolInitializationException:593|HikariPool-1 - Exception during pool initialization. com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:358)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:477)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:560)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:158)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:116)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:79)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:93)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:68)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165)
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	at com.sun.proxy.$Proxy89.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:173)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy95.selectList(Unknown Source)
	at com.fangcang.grfp.server.job.SyncProvinceCityDataJob.execute(SyncProvinceCityDataJob.java:60)
	at com.fangcang.grfp.server.job.SyncProvinceCityDataJobTest.testExecute(SyncProvinceCityDataJobTest.java:21)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:686)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84)
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:212)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:208)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:71)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:135)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:232)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 103 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 106 common frames omitted
 [grfp-api,] ------
2025-05-21 15:29:29.442|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.execute:95|Sync province city data job error org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/fangcang/grfp/core/mapper/CountryMapper.java (best guess)
### The error may involve com.fangcang.grfp.core.mapper.CountryMapper.selectList
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy89.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:173)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy95.selectList(Unknown Source)
	at com.fangcang.grfp.server.job.SyncProvinceCityDataJob.execute(SyncProvinceCityDataJob.java:60)
	at com.fangcang.grfp.server.job.SyncProvinceCityDataJobTest.testExecute(SyncProvinceCityDataJobTest.java:21)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:686)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84)
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:212)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:208)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:71)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:135)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:232)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/fangcang/grfp/core/mapper/CountryMapper.java (best guess)
### The error may involve com.fangcang.grfp.core.mapper.CountryMapper.selectList
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:149)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 74 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:82)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:93)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:68)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165)
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	... 80 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:358)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:477)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:560)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:158)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:116)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:79)
	... 90 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 103 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 106 common frames omitted
 [grfp-api,] ------
2025-05-21 15:30:42.487|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:30:44.210|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:30:54.211|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:30:57.209|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:30:59.216|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:31:10.211|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:31:24.216|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:31:28.223|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:31:28.259|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:31:29.204|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:31:29.274|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:31:30.232|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:31:30.289|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:31:31.223|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:31:31.271|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:31:32.236|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:31:32.295|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:31:33.237|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:31:33.297|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:31:34.227|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:31:34.248|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:31:36.205|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:31:44.209|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:31:44.243|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:33:52.789|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:34:01.027|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:35:00.010|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:35:01.012|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:35:01.051|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: en-US  [grfp-api,] ------
2025-05-21 15:35:07.028|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-21 15:35:42.802|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:80|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
