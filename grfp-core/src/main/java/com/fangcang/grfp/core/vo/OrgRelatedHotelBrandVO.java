package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("机构关联酒店品牌")
@Getter
@Setter
public class OrgRelatedHotelBrandVO extends BaseVO {

    @ApiModelProperty(value = "机构关联酒店品牌Id")
    private Integer orgRelatedHotelBrandId;

    @ApiModelProperty(value = "机构Id")
    private Integer orgId;

    @ApiModelProperty(value = "酒店集团ID")
    private Long hotelGroupId;

    @ApiModelProperty(value = "酒店集团名称")
    private String hotelGroupName;

    @ApiModelProperty(value = "酒店品牌ID")
    private Long hotelBrandId;

    @ApiModelProperty(value = "酒店品牌名称")
    private String hotelBrandName;


}
