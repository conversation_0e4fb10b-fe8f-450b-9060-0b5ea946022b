package com.fangcang.grfp.api.controller.hotelgroup.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.api.controller.hotelgroup.vo.QueryHotelGroupBidTemplateResponse;
import com.fangcang.grfp.api.controller.hotelgroup.vo.UpdateGroupApproveRequest;
import com.fangcang.grfp.api.controller.hotelgroup.vo.UpdateHotelGroupBidTemplateRequest;
import com.fangcang.grfp.api.controller.project.vo.ExportTenderPriceRequest;
import com.fangcang.grfp.api.controller.project.vo.ExportTenderPriceVO;
import com.fangcang.grfp.api.controller.project.vo.ProjectBidStatCountVO;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.base.IdVO;
import com.fangcang.grfp.core.base.ImportRowErrorVO;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.cached.CachedCountryService;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.dto.excel.ExportExcelContext;
import com.fangcang.grfp.core.dto.excel.ImportExcelContext;
import com.fangcang.grfp.core.entity.*;
import com.fangcang.grfp.core.enums.*;
import com.fangcang.grfp.core.manager.*;
import com.fangcang.grfp.core.mapper.*;
import com.fangcang.grfp.core.oss.OssManager;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.*;
import com.fangcang.grfp.core.vo.*;
import com.fangcang.grfp.core.vo.request.QueryBidMapHotelInfoRequest;
import com.fangcang.grfp.core.vo.request.bid.CreateBidRequest;
import com.fangcang.grfp.core.vo.request.bid.ValidateBidContext;
import com.fangcang.grfp.core.vo.request.hotelgroup.QueryHotelGroupApprovalRequest;
import com.fangcang.grfp.core.vo.request.hotelgroup.QueryHotelGroupBidPriceCountRequest;
import com.fangcang.grfp.core.vo.request.hotelgroup.QueryInvitedSingleHotelRequest;
import com.fangcang.grfp.core.vo.request.hotelgroup.QueryProjectOverviewRequest;
import com.fangcang.grfp.core.vo.response.hotelgroup.*;
import com.fangcang.grfp.core.vo.response.hotelprice.HotelMinPriceResponse;
import com.fangcang.grfp.core.vo.response.project.ProjectBidCustomTendStrategyVO;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelTendStrategyVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HotelGroupService {

    @Autowired
    private ProjectIntentHotelGroupMapper projectIntentHotelGroupMapper;
    @Autowired
    private ProjectIntentHotelMapper projectIntentHotelMapper;
    @Autowired
    private LanyonImportColumnMapper lanyonImportColumnMapper;
    @Autowired
    private ProjectMapper projectMapper;
    @Autowired
    private ProjectHotelTendStrategyMapper projectHotelTendStrategyMapper;
    @Autowired
    private HotelGroupDefaultApplicableDayMapper hotelGroupDefaultApplicableDayMapper;
    @Autowired
    private HotelGroupDefaultUnapplicableDayMapper hotelGroupDefaultUnapplicableDayMapper;
    @Autowired
    private HGroupDefaultCusStrategyMapper hGroupDefaultCusStrategyMapper;
    @Autowired
    private HotelMapper hotelMapper;
    @Autowired
    private OrgRelatedHotelBrandMapper orgRelatedHotelBrandMapper;
    @Resource
    private ProjectManager projectManager;
    @Resource
    private ExcelManager excelManager;
    @Resource
    private BidManager bidManager;
    @Autowired
    private LanyonImportManager lanyonImportManager;
    @Autowired
    private BidMapManager bidMapManager;
    @Autowired
    private OssManager ossManager;
    @Resource
    private HGroupDefaultCusStrategyOptionMapper hGroupDefaultCusStrategyOptionMapper;
    @Autowired
    private SysImportRecordMapper sysImportRecordMapper;
    @Autowired
    private CachedCountryService cachedCountryService;



    /**
     * 酒店集团查询报价统计数据
     */
    public QueryBidStatCountVO queryBidStatCount(HttpServletRequest request){
        UserSession userSession = UserSession.get();

        // 定义返回值
        QueryBidStatCountVO queryBidStatCountVO = new QueryBidStatCountVO();

        // 查询集团关联信息
        HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfoVO = AppUtility.getHotelGroupUserRelatedInfoVO(userSession);

        // 查询集团邀约数量
        int inviteCount = projectIntentHotelGroupMapper.selectInvitedCount(hotelGroupUserRelatedInfoVO.getHotelGroupOrgId(),
                hotelGroupUserRelatedInfoVO.getHotelBrandIdSet(), hotelGroupUserRelatedInfoVO.getEmployeeUserId());
        queryBidStatCountVO.setInviteHotelCount(inviteCount);

        // 查询审核数量
        List<ApproveStatusCountVO> approveStatusCountVOList = projectIntentHotelMapper.queryHotelGroupApprovalCount(hotelGroupUserRelatedInfoVO.getHotelGroupOrgId(), hotelGroupUserRelatedInfoVO.getHotelBrandIdSet());
        for(ApproveStatusCountVO approveStatusCountVO : approveStatusCountVOList){
            if(approveStatusCountVO.getApproveStatus() == HotelGroupApproveStatusEnum.WAITING.key){
                queryBidStatCountVO.setWaitingApproveCount(approveStatusCountVO.getApproveStatusCount());
            }
            if(approveStatusCountVO.getApproveStatus() == HotelGroupApproveStatusEnum.REJECT_APPROVED.key){
                queryBidStatCountVO.setRejectedApproveCount(approveStatusCountVO.getApproveStatusCount());
            }
        }

        // 查询报价状态统计数据
        List<BidStateCountVO> bidStateCountList = projectIntentHotelGroupMapper.queryBidStatCount(hotelGroupUserRelatedInfoVO.getHotelGroupOrgId(),
                hotelGroupUserRelatedInfoVO.getHotelBrandIdSet(), hotelGroupUserRelatedInfoVO.getEmployeeUserId());
        for(BidStateCountVO bidStateCountVO : bidStateCountList){
            // 新标
            if(bidStateCountVO.getBidState() == HotelBidStateEnum.NEW_BID.bidState){
                queryBidStatCountVO.setNewBidCount(bidStateCountVO.getBidStateCount());
            }
            // 议价中
            if(bidStateCountVO.getBidState() == HotelBidStateEnum.UNDER_NEGOTIATION.bidState){
                queryBidStatCountVO.setUnderNegotiationCount(bidStateCountVO.getBidStateCount());
            }
            // 修订报价
            if(bidStateCountVO.getBidState() == HotelBidStateEnum.UPDATED_BID.bidState){
                queryBidStatCountVO.setUpdatedBidCount(bidStateCountVO.getBidStateCount());
            }
            // 拒绝议价
            if(bidStateCountVO.getBidState() == HotelBidStateEnum.REJECT_NEGOTIATION.bidState){
                queryBidStatCountVO.setRejectNegotiationCount(bidStateCountVO.getBidStateCount());
            }
            // 中签
            if(bidStateCountVO.getBidState() == HotelBidStateEnum.BID_WINNING.bidState){
                queryBidStatCountVO.setBidWinningCount(bidStateCountVO.getBidStateCount());
            }
            // 已否决
            if(bidStateCountVO.getBidState() == HotelBidStateEnum.REJECTED.bidState){
                queryBidStatCountVO.setRejectCount(bidStateCountVO.getBidStateCount());
            }
        }

        // 未报价数量
        int noBidCount = projectIntentHotelGroupMapper.queryNoBidCount(hotelGroupUserRelatedInfoVO.getHotelGroupOrgId(),
                hotelGroupUserRelatedInfoVO.getHotelBrandIdSet(), hotelGroupUserRelatedInfoVO.getEmployeeUserId());
        queryBidStatCountVO.setNoBidCount(noBidCount);

        return queryBidStatCountVO;
    }


    /**
     * 项目报价总览
     */
    public PageVO<QueryProjectOverviewResponse> queryProjectOverview(HttpServletRequest request,
                                                                     QueryProjectOverviewRequest queryProjectOverviewRequest){
        UserSession userSession = UserSession.get();
        IPage<QueryProjectOverviewResponse> page = new Page<>(queryProjectOverviewRequest.getPageIndex(), queryProjectOverviewRequest.getPageSize());

        // 酒店集团关联信息
        HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfoVO = AppUtility.getHotelGroupUserRelatedInfoVO(userSession);
        queryProjectOverviewRequest.setHotelGroupBrandIdList(hotelGroupUserRelatedInfoVO.getHotelBrandIdSet());
        queryProjectOverviewRequest.setEmployeeUserId(hotelGroupUserRelatedInfoVO.getEmployeeUserId());
        queryProjectOverviewRequest.setOrgId(hotelGroupUserRelatedInfoVO.getHotelGroupOrgId());

        // 查询报价总览
        boolean isBidOverView = queryProjectOverviewRequest.getQuoteStatus() != null && queryProjectOverviewRequest.getQuoteStatus() == -1;
        if(isBidOverView){
            projectIntentHotelGroupMapper.queryProjectOverview(page, queryProjectOverviewRequest);

            Map<Integer, QueryProjectOverviewResponse> queryProjectOverviewResponsMap = new HashMap<>();
            List<Integer> projectIds = new ArrayList<>();
            for (QueryProjectOverviewResponse queryProjectOverviewRespons : page.getRecords()) {
                projectIds.add(queryProjectOverviewRespons.getProjectId());
                queryProjectOverviewResponsMap.put(queryProjectOverviewRespons.getProjectId(), queryProjectOverviewRespons);
                //招标开始时间、招标结束时间、评标结果时间计算
                queryProjectOverviewRespons.setBidStartTime(queryProjectOverviewRespons.getFirstBidStartTime());
                //招标开始时间、招标结束时间、评标结果时间计算
                queryProjectOverviewRespons.setBidStartTime(queryProjectOverviewRespons.getFirstBidStartTime());
                Date bidEndTime = queryProjectOverviewRespons.getThirdBidEndTime() != null ?
                        queryProjectOverviewRespons.getThirdBidEndTime() : queryProjectOverviewRespons.getSecondBidEndTime() != null ?
                        queryProjectOverviewRespons.getSecondBidEndTime() : queryProjectOverviewRespons.getFirstBidEndTime();
                queryProjectOverviewRespons.setBidEndTime(bidEndTime);
                queryProjectOverviewRespons.setBidResultTime(DateUtil.getDate(bidEndTime, 1, 0));
            }

            if(CollectionUtils.isNotEmpty(projectIds)) {
                //查询已报价酒店数
                QueryHotelGroupBidPriceCountRequest queryHotelGroupBidPriceCountRequest = new QueryHotelGroupBidPriceCountRequest();
                queryHotelGroupBidPriceCountRequest.setProjectIds(projectIds);
                queryHotelGroupBidPriceCountRequest.setHotelGroupOrgId(queryProjectOverviewRequest.getOrgId());
                queryHotelGroupBidPriceCountRequest.setQueryType(1);
                queryHotelGroupBidPriceCountRequest.setHotelGroupBrandIdList(hotelGroupUserRelatedInfoVO.getHotelBrandIdSet());
                List<ProjectHotelCountResponse> allBidProjectHotelCountResponses = projectIntentHotelMapper.queryHotelGroupBidPriceCount(queryHotelGroupBidPriceCountRequest);

                //查询议价中酒店数
                queryHotelGroupBidPriceCountRequest.setQueryType(2);
                List<ProjectHotelCountResponse> underNegotiationProjectHotelCountResponses = projectIntentHotelMapper.queryHotelGroupBidPriceCount(queryHotelGroupBidPriceCountRequest);

                //查询已中签酒店数
                queryHotelGroupBidPriceCountRequest.setQueryType(3);
                List<ProjectHotelCountResponse> bidWinningprojectHotelCountResponses = projectIntentHotelMapper.queryHotelGroupBidPriceCount(queryHotelGroupBidPriceCountRequest);

                if (CollectionUtils.isNotEmpty(allBidProjectHotelCountResponses)) {
                    for (ProjectHotelCountResponse allBidProjectHotelCountRespons : allBidProjectHotelCountResponses) {
                        QueryProjectOverviewResponse queryProjectOverviewResponse = queryProjectOverviewResponsMap.get(allBidProjectHotelCountRespons.getProjectId());
                        if(queryProjectOverviewResponse != null) {
                            queryProjectOverviewResponse.setTotalBidPriceCount(allBidProjectHotelCountRespons.getHotelCount());
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(underNegotiationProjectHotelCountResponses)) {
                    for (ProjectHotelCountResponse underNegotiationProjectHotelCountRespons : underNegotiationProjectHotelCountResponses) {
                        QueryProjectOverviewResponse queryProjectOverviewResponse = queryProjectOverviewResponsMap.get(underNegotiationProjectHotelCountRespons.getProjectId());
                        if(queryProjectOverviewResponse != null) {
                            queryProjectOverviewResponse.setUnderNegotiationPriceCount(underNegotiationProjectHotelCountRespons.getHotelCount());
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(bidWinningprojectHotelCountResponses)) {
                    for (ProjectHotelCountResponse bidWinningProjectHotelCountRespons : bidWinningprojectHotelCountResponses) {
                        QueryProjectOverviewResponse queryProjectOverviewResponse = queryProjectOverviewResponsMap.get(bidWinningProjectHotelCountRespons.getProjectId());
                        if(queryProjectOverviewResponse != null) {
                            queryProjectOverviewResponse.setBidWinningPriceCount(bidWinningProjectHotelCountRespons.getHotelCount());
                        }
                    }
                }
            }
        } else {
            projectIntentHotelGroupMapper.queryProjectBidInfo(page, queryProjectOverviewRequest);
            Map<Integer, QueryProjectOverviewResponse> queryProjectOverviewResponsMap = new HashMap<>();
            List<Integer> projectIntentHotelIds = new ArrayList<>();
            int languageId = AppUtility.getRequestHeaderLanguage(request);
            for (QueryProjectOverviewResponse queryProjectOverviewRespons : page.getRecords()) {
                //招标开始时间、招标结束时间、评标结果时间计算
                queryProjectOverviewRespons.setBidStartTime(queryProjectOverviewRespons.getFirstBidStartTime());
                //招标开始时间、招标结束时间、评标结果时间计算
                queryProjectOverviewRespons.setBidStartTime(queryProjectOverviewRespons.getFirstBidStartTime());
                Date bidEndTime = queryProjectOverviewRespons.getThirdBidEndTime() != null ?
                        queryProjectOverviewRespons.getThirdBidEndTime() : queryProjectOverviewRespons.getSecondBidEndTime() != null ?
                        queryProjectOverviewRespons.getSecondBidEndTime() : queryProjectOverviewRespons.getFirstBidEndTime();
                queryProjectOverviewRespons.setBidEndTime(bidEndTime);
                queryProjectOverviewRespons.setBidResultTime(DateUtil.getDate(bidEndTime, 1, 0));
                queryProjectOverviewResponsMap.put(queryProjectOverviewRespons.getProjectIntentHotelId(), queryProjectOverviewRespons);
                projectIntentHotelIds.add(queryProjectOverviewRespons.getProjectIntentHotelId());
                // 设置酒店名称
                queryProjectOverviewRespons.setHotelName(AppUtility.getHotelName(languageId, queryProjectOverviewRespons.getHotelId()));
                // 设置城市名称
                queryProjectOverviewRespons.setCityName(AppUtility.getCityName(languageId, queryProjectOverviewRespons.getCityCode()));
                //设置品牌名称
                if(queryProjectOverviewRespons.getHotelBrandId() != null){
                    queryProjectOverviewRespons.setHotelBrandName(AppUtility.getHotelBrandName(languageId, queryProjectOverviewRespons.getHotelBrandId()));
                }
                // 未报价显示酒店单店来源
                if(queryProjectOverviewRequest.getQuoteStatus() == 0){
                    queryProjectOverviewRespons.setBidOrgType(OrgTypeEnum.HOTEL.key);
                }
            }

            //最低价和早餐
            List<HotelMinPriceResponse> hotelMinPriceResponses = bidMapManager.queryHotelMinPrice(languageId, projectIntentHotelIds, false);
            if (CollectionUtils.isNotEmpty(hotelMinPriceResponses)) {
                for (HotelMinPriceResponse hotelMinPriceRespons : hotelMinPriceResponses) {
                    QueryProjectOverviewResponse queryProjectOverviewResponse = queryProjectOverviewResponsMap.get(hotelMinPriceRespons.getProjectIntentHotelId());
                    queryProjectOverviewResponse.setIsIncludeBreakfast(hotelMinPriceRespons.getIsIncludeBreakfast());
                    queryProjectOverviewResponse.setBasePrice(hotelMinPriceRespons.getMinPrice());
                    queryProjectOverviewResponse.setBreakfastNum(BreakfastNumEnum.getTextByKey(hotelMinPriceRespons.getBreakfastNum(), languageId));
                }
            }
        }
        return new PageVO<>((int) page.getTotal(), (int) page.getPages(), page.getRecords());
    }


    /**
     * 查询酒店集团报价模板
     */
    public QueryHotelGroupBidTemplateResponse queryHotelGroupBidTemplate(HttpServletRequest request, Integer id){
        ProjectEntity project = projectMapper.selectById(id);
        if(project == null){
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        // 定义返回值
        QueryHotelGroupBidTemplateResponse queryHotelGroupBidTemplateResponse = new QueryHotelGroupBidTemplateResponse();

        // 获取酒店集团用户信息
        UserSession userSession = UserSession.get();
        HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfoVO = AppUtility.getHotelGroupUserRelatedInfoVO(userSession);

        // 查询酒店集团意向报价信息（取最小酒店集团意向报价）
        ProjectIntentHotelGroupEntity projectIntentHotelGroup = projectIntentHotelGroupMapper.queryHotelGroupProjectIntentGroup(id, hotelGroupUserRelatedInfoVO.getHotelGroupOrgId());
        if(projectIntentHotelGroup == null){
            AppUtility.serviceError(ErrorCode.CANNOT_FOUND_PROJECT_INVITED_HOTEL_GROUP);
        }
        BeanUtils.copyProperties(projectIntentHotelGroup, queryHotelGroupBidTemplateResponse);
        // 设置酒店集团默认报价策略
        BidProjectStrategyVO defaultBidProjectStrategyVO = new BidProjectStrategyVO();
        BeanUtils.copyProperties(projectIntentHotelGroup, defaultBidProjectStrategyVO);
        queryHotelGroupBidTemplateResponse.setBidProjectStrategy(defaultBidProjectStrategyVO);

        // 如果联系人为空，设置用户为默认联系人
        if(!StringUtil.isValidString(queryHotelGroupBidTemplateResponse.getHotelGroupBidContactEmail()) &&
                !StringUtil.isValidString(queryHotelGroupBidTemplateResponse.getHotelGroupBidContactMobile()) &&
                !StringUtil.isValidString(queryHotelGroupBidTemplateResponse.getHotelGroupBidContactName())){
            queryHotelGroupBidTemplateResponse.setHotelGroupBidContactName(userSession.getUsername());
            queryHotelGroupBidTemplateResponse.setHotelGroupBidContactEmail(userSession.getEmail());
            queryHotelGroupBidTemplateResponse.setHotelGroupBidContactMobile(CommonUtil.generateContactMobile(userSession.getMobileAreaCode(), userSession.getMobile()));
        }

        // 查询项目自定义策略
        List<ProjectBidCustomTendStrategyVO> projectCustomTendStrategyList = projectManager.queryProjectCustomTendStrategyInfo(project.getProjectId());
        queryHotelGroupBidTemplateResponse.setProjectCustomTendStrategyList(projectCustomTendStrategyList);

        // 项目项目报价策略
        ProjectHotelTendStrategyVO projectHotelTendStrategyVO = projectHotelTendStrategyMapper.selectByProjectId(project.getProjectId());
        queryHotelGroupBidTemplateResponse.setProjectHotelTendStrategy(projectHotelTendStrategyVO);

        // 查询酒店集团其他承诺
        List<HGroupDefaultCusStrategyVO> hotelGroupDefaultCusStrategyList = bidMapManager.queryHotelGroupDefaultCustomStrategy(projectIntentHotelGroup.getProjectIntentHotelGroupId());
        queryHotelGroupBidTemplateResponse.setHotelGroupDefaultCusStrategyList(hotelGroupDefaultCusStrategyList);

        // 查询适用日期
        List<BidApplicableDayVO> applicableDayList = hotelGroupDefaultApplicableDayMapper.queryDefaultApplicableDayInfo(projectIntentHotelGroup.getProjectIntentHotelGroupId());
        queryHotelGroupBidTemplateResponse.setApplicableDayList(applicableDayList);
        boolean hasBasePrice = false;
        for(BidApplicableDayVO applicableDayVO : applicableDayList){
            if(applicableDayVO.getPriceType() == HotelPriceTypeEnum.BASE_PRICE.key){
                hasBasePrice = true;
            }
        }
        // 设置基础协议日期
        if(!hasBasePrice){
            BidApplicableDayVO bidApplicableDay = new BidApplicableDayVO();
            bidApplicableDay.setPriceType(HotelPriceTypeEnum.BASE_PRICE.key);
            bidApplicableDay.setStartDate(project.getPriceMonitorStartDate());
            bidApplicableDay.setEndDate(project.getPriceMonitorEndDate());
            queryHotelGroupBidTemplateResponse.getApplicableDayList().add(bidApplicableDay);

        }


        // 查询不可用日期
        List<HotelGroupDefaultUnApplicableDayVO> unApplicableDayList = hotelGroupDefaultUnapplicableDayMapper.queryDefaultUnApplicableDayInfo(projectIntentHotelGroup.getProjectIntentHotelGroupId());
        queryHotelGroupBidTemplateResponse.setUnapplicableDayList(unApplicableDayList);

        return queryHotelGroupBidTemplateResponse;
    }

    /**
     * 保存酒店集团报价模板
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateHotelGroupBidTemplate(HttpServletRequest request, UpdateHotelGroupBidTemplateRequest updateRequest) {
        // 更新酒店集团意向表
        ProjectIntentHotelGroupEntity projectIntentHotelGroup = projectIntentHotelGroupMapper.selectById(updateRequest.getProjectIntentHotelGroupId());
        if(projectIntentHotelGroup == null){
            AppUtility.serviceError(ErrorCode.HOTEL_INTENT_GROUP_NOT_EXIST);
        }
        BeanUtils.copyProperties(updateRequest, projectIntentHotelGroup);
        projectIntentHotelGroupMapper.updateById(projectIntentHotelGroup);

        UserSession userSession = UserSession.get();

        //自定义策略
        List<HGroupDefaultCusStrategyVO> hotelGroupDefaultCusStrategyList = updateRequest.getHotelGroupDefaultCusStrategyList();
        List<HGroupDefaultCusStrategyOptionEntity> hotelGroupDefaultCusStrategyOptionList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hotelGroupDefaultCusStrategyList)) {
            for (HGroupDefaultCusStrategyVO hGroupDefaultCusStrategyVO : hotelGroupDefaultCusStrategyList) {
                hGroupDefaultCusStrategyVO.setCreator(userSession.getUsername());
                hGroupDefaultCusStrategyVO.setModifier(userSession.getUsername());
                hGroupDefaultCusStrategyVO.setProjectIntentHotelGroupId(updateRequest.getProjectIntentHotelGroupId());
                hGroupDefaultCusStrategyVO.setProjectId(updateRequest.getProjectId());

                // 处理选项
                if (CustomStrategyTypeEnum.isOptionType(hGroupDefaultCusStrategyVO.getStrategyType()) && CollectionUtils.isNotEmpty(hGroupDefaultCusStrategyVO.getOptions())) {
                    for (HGroupDefaultCusStrategyOptionVO hGroupDefaultCusStrategyOptionVO : hGroupDefaultCusStrategyVO.getOptions()) {
                        HGroupDefaultCusStrategyOptionEntity hGroupDefaultCusStrategyOptionEntity = new HGroupDefaultCusStrategyOptionEntity();
                        BeanUtils.copyProperties(hGroupDefaultCusStrategyOptionVO, hGroupDefaultCusStrategyOptionEntity);
                        hGroupDefaultCusStrategyOptionEntity.setStrategyId(Long.valueOf(hGroupDefaultCusStrategyVO.getCustomTendStrategyId()));
                        hGroupDefaultCusStrategyOptionEntity.setCreator(userSession.getUsername());
                        hGroupDefaultCusStrategyOptionEntity.setModifier(userSession.getUsername());
                        hGroupDefaultCusStrategyOptionEntity.setProjectIntentHotelGroupId(hGroupDefaultCusStrategyVO.getProjectIntentHotelGroupId());
                        hGroupDefaultCusStrategyOptionEntity.setProjectId(hGroupDefaultCusStrategyVO.getProjectId());
                        hotelGroupDefaultCusStrategyOptionList.add(hGroupDefaultCusStrategyOptionEntity);
                    }
                }
            }
            hGroupDefaultCusStrategyMapper.batchMergeHotelGroupDefaultCustomStrategy(hotelGroupDefaultCusStrategyList);
            if (CollectionUtils.isNotEmpty(hotelGroupDefaultCusStrategyOptionList)) {
                hGroupDefaultCusStrategyOptionMapper.batchUpsert(hotelGroupDefaultCusStrategyOptionList);
            }
        }

        //不适用日期
        hotelGroupDefaultUnapplicableDayMapper.deleteByProjectIntentHotelGroupId(updateRequest.getProjectIntentHotelGroupId());
        List<HotelGroupDefaultUnApplicableDayVO> hotelGroupDefaultUnapplicableDays = updateRequest.getUnapplicableDayList();
        if (CollectionUtils.isNotEmpty(hotelGroupDefaultUnapplicableDays)) {
            List<HotelGroupDefaultUnapplicableDayEntity> insertHotelGroupDefaultUnapplicableDays = new ArrayList<>();
            for (HotelGroupDefaultUnApplicableDayVO hotelGroupDefaultUnApplicableDayVO : hotelGroupDefaultUnapplicableDays) {
                HotelGroupDefaultUnapplicableDayEntity hotelGroupDefaultUnapplicableDayEntity = new HotelGroupDefaultUnapplicableDayEntity();
                BeanUtils.copyProperties(hotelGroupDefaultUnApplicableDayVO, hotelGroupDefaultUnapplicableDayEntity);
                hotelGroupDefaultUnapplicableDayEntity.setProjectIntentHotelGroupId(updateRequest.getProjectIntentHotelGroupId());
                hotelGroupDefaultUnapplicableDayEntity.setProjectId(updateRequest.getProjectId());
                hotelGroupDefaultUnapplicableDayEntity.setCreator(userSession.getUsername());
                insertHotelGroupDefaultUnapplicableDays.add(hotelGroupDefaultUnapplicableDayEntity);
            }
            hotelGroupDefaultUnapplicableDayMapper.batchInsert(insertHotelGroupDefaultUnapplicableDays);
        }

        // 日期设置
        hotelGroupDefaultApplicableDayMapper.deleteHotelGroupDefaultApplicableDay(updateRequest.getProjectIntentHotelGroupId());
        List<HotelGroupDefaultApplicableDayVO> hotelGroupDefaultApplicableDays = updateRequest.getApplicableDayList();
        if (CollectionUtils.isNotEmpty(hotelGroupDefaultApplicableDays)) {
            List<HotelGroupDefaultApplicableDayEntity> entityList = new ArrayList<>();
            for (HotelGroupDefaultApplicableDayVO hotelGroupDefaultApplicableDay : hotelGroupDefaultApplicableDays) {
                HotelGroupDefaultApplicableDayEntity entity = new HotelGroupDefaultApplicableDayEntity();
                BeanUtils.copyProperties(hotelGroupDefaultApplicableDay, entity);
                entity.setProjectIntentHotelGroupId(updateRequest.getProjectIntentHotelGroupId());
                entity.setProjectId(updateRequest.getProjectId());
                entity.setCreator(userSession.getUsername());
                entity.setModifier(userSession.getUsername());
                entityList.add(entity);
            }
            hotelGroupDefaultApplicableDayMapper.batchInsert(entityList);
        }
    }


    public QueryHotelGroupHotelBidStatusResponse queryHotelGroupHotelBidStatus(QueryBidMapHotelInfoRequest req){
        // 获取酒店集团用户信息
        UserSession userSession = UserSession.get();
        HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfoVO = AppUtility.getHotelGroupUserRelatedInfoVO(userSession);

        // 定义返回值
        QueryHotelGroupHotelBidStatusResponse queryHotelGroupHotelBidStatusResponse = new QueryHotelGroupHotelBidStatusResponse();
        queryHotelGroupHotelBidStatusResponse.setProjectId(req.getProjectId());
        queryHotelGroupHotelBidStatusResponse.setHotelId(req.getHotelId());

        // 查询和检查酒店信息
        HotelEntity hotel = hotelMapper.selectById(req.getHotelId());
        if(hotel == null){
            AppUtility.serviceError(ErrorCode.HOTEL_NOT_EXIST);
        }
        // 查询酒店项目报价信息
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.queryByProjectAndHotelId(req.getProjectId(), req.getHotelId());

        // 未报价，未邀约
        if(projectIntentHotel == null){
            queryHotelGroupHotelBidStatusResponse.setIsBid(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoBid(RfpConstant.constant_1);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotel(RfpConstant.constant_1);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotelAgain(RfpConstant.constant_0);
        }
        // 若酒店已点击过"代报价"，相当于酒店集团机构建立了邀约信息，若未完成报价，下次进来按钮仅剩"代报价"，可以继续报价。
        else if(Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.NO_BID.bidState) && projectIntentHotel.getInviteStatus() == RfpConstant.constant_0){
            queryHotelGroupHotelBidStatusResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            queryHotelGroupHotelBidStatusResponse.setIsBid(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoBid(RfpConstant.constant_1);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotel(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotelAgain(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        }
        //  若酒店已点击过"通知酒店"，相当于酒店机构建立了邀约信息，若仍未报价，下次进来按钮仅剩"再次通知酒店"，点击可以重新发送邀约邮件邀约短信提醒。
        else if(Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.NO_BID.bidState) && projectIntentHotel.getInviteStatus() == RfpConstant.constant_1){
            queryHotelGroupHotelBidStatusResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            queryHotelGroupHotelBidStatusResponse.setIsBid(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoBid(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotel(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotelAgain(RfpConstant.constant_1);
            queryHotelGroupHotelBidStatusResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        }
        // 若已提交报价，这里无按钮，用文字提示"酒店已提交报价，不能重复提交"
        else if(!Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.NO_BID.bidState)){
            queryHotelGroupHotelBidStatusResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            queryHotelGroupHotelBidStatusResponse.setIsBid(RfpConstant.constant_1);
            queryHotelGroupHotelBidStatusResponse.setCanDoBid(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotel(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotelAgain(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        }

        // 查询是否有品牌限制
        queryHotelGroupHotelBidStatusResponse.setIsBrandLimit(RfpConstant.constant_1);
        if(hotel.getHotelBrandId() != null){
            // 根据酒店集团ID查询所属的酒店集团机构
            List<OrgRelatedHotelBrandEntity> orgRelatedHotelBrandEntities = orgRelatedHotelBrandMapper.queryHotelGroupOrgRelatedBrandList(userSession.getUserOrg().getOrgId());
            if(CollectionUtils.isNotEmpty(orgRelatedHotelBrandEntities)){
                ProjectIntentHotelGroupEntity projectIntentHotelGroup = projectIntentHotelGroupMapper.queryByProjectAndHotelGroupId(req.getProjectId(), orgRelatedHotelBrandEntities.get(0).getOrgId());
                queryHotelGroupHotelBidStatusResponse.setIsBrandLimit(projectIntentHotelGroup != null ? projectIntentHotelGroup.getIsBrandLimit() : 1);
            }
        }
       if(hotel.getHotelBrandId() == null || !hotelGroupUserRelatedInfoVO.getHotelBrandIdSet().contains(hotel.getHotelBrandId())){
            queryHotelGroupHotelBidStatusResponse.setIsHotelGroupBrand(RfpConstant.constant_0);
        } else {
            queryHotelGroupHotelBidStatusResponse.setIsHotelGroupBrand(RfpConstant.constant_1);
        }

        return queryHotelGroupHotelBidStatusResponse;
    }

    /**
     * 查询酒店集团下被邀请的单体酒店（意向单店）
     */
    public PageVO<InvitedSingleHotelResponse> queryInvitedSingleHotels(HttpServletRequest request, QueryInvitedSingleHotelRequest queryRequest) {
        UserSession userSession = UserSession.get();
        int languageId = AppUtility.getRequestHeaderLanguage(request);

        // 获取酒店集团用户相关信息
        HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfoVO = AppUtility.getHotelGroupUserRelatedInfoVO(userSession);
        queryRequest.setOrgId(hotelGroupUserRelatedInfoVO.getHotelGroupOrgId());
        queryRequest.setHotelGroupBrandIdList(hotelGroupUserRelatedInfoVO.getHotelBrandIdSet());

        // 执行分页查询（使用简洁版查询）
        Page<InvitedSingleHotelResponse> page = new Page<>(queryRequest.getPageIndex(), queryRequest.getPageSize());
        projectIntentHotelMapper.queryInvitedSingleHotelsSimple(page, queryRequest);

        // 处理国际化
        if(CollectionUtils.isNotEmpty(page.getRecords())){
            page.getRecords().forEach(item -> {
                // 处理酒店名称国际化
                item.setHotelName(AppUtility.getName(languageId, item.getHotelId(), item.getHotelNameEnUs(), item.getHotelNameZhCn()));
                
                // 处理城市名称国际化
                item.setCityName(AppUtility.getName(languageId, item.getCityCode(), item.getCityNameEnUs(), item.getCityNameZhCn()));
                
                // 处理签约类型国际化
                item.setTenderTypeDesc(TenderTypeEnum.geTextByKey(item.getTenderType(), languageId));
            });
        }

        return new PageVO<>(page.getTotal(), page.getPages(), page.getRecords());
    }

    /**
     * 查询酒店集团审核列表（待审核/审核驳回）
     */
    public PageVO<HotelGroupApprovalResponse> queryHotelGroupApprovalList(HttpServletRequest request, QueryHotelGroupApprovalRequest queryRequest) {
        UserSession userSession = UserSession.get();
        int languageId = AppUtility.getRequestHeaderLanguage(request);

        // 获取酒店集团用户相关信息
        HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfoVO = AppUtility.getHotelGroupUserRelatedInfoVO(userSession);
        queryRequest.setOrgId(hotelGroupUserRelatedInfoVO.getHotelGroupOrgId());
        queryRequest.setHotelGroupBrandIdList(hotelGroupUserRelatedInfoVO.getHotelBrandIdSet());

        // 执行分页查询
        Page<HotelGroupApprovalResponse> page = new Page<>(queryRequest.getPageIndex(), queryRequest.getPageSize());
        projectIntentHotelMapper.queryHotelGroupApprovalList(page, queryRequest);

        // 处理国际化和数据转换
        if(CollectionUtils.isNotEmpty(page.getRecords())){
            List<Integer> projectIntentHotelIds = page.getRecords().stream().map(HotelGroupApprovalResponse::getProjectIntentHotelId).collect(Collectors.toList());
            // 查询最低价格
            Map<Integer, HotelMinPriceResponse> minBasePriceMap = bidMapManager.queryHotelMinPrice(languageId, projectIntentHotelIds, false).stream().collect(Collectors.toMap(HotelMinPriceResponse::getProjectIntentHotelId, o->o, (o1, o2)-> o1));
            page.getRecords().forEach(item -> {
                // 处理酒店名称国际化
                item.setHotelName(AppUtility.getName(languageId, item.getHotelId(), item.getHotelNameEnUs(), item.getHotelNameZhCn()));
                
                // 处理城市名称国际化
                item.setCityName(AppUtility.getName(languageId, item.getCityCode(), item.getCityNameEnUs(), item.getCityNameZhCn()));
                
                // 处理签约类型国际化
                item.setTenderTypeDesc(TenderTypeEnum.geTextByKey(item.getTenderType(), languageId));
                
                // 处理项目状态国际化
                item.setProjectStatusDesc(ProjectStateEnum.geTextByKey(item.getProjectState(), languageId));
                
                // 处理报价状态国际化
                item.setBidStatusDesc(HotelBidStateEnum.getTextByKey(item.getBidState(), languageId));

                // 评标结果时间
                item.setBidResultTime(DateUtil.getDate(item.getBidEndTime(), 1, 0));

                // 设置最低价格
                HotelMinPriceResponse hotelMinPriceResponse = minBasePriceMap.get(item.getProjectIntentHotelId());
                if(hotelMinPriceResponse != null){
                    item.setBasePrice(hotelMinPriceResponse.getMinPrice());
                    item.setIsIncludeBreakfast(hotelMinPriceResponse.getIsIncludeBreakfast());
                    item.setBreakfastNum(BreakfastNumEnum.getTextByKey(hotelMinPriceResponse.getBreakfastNum(), languageId));
                }
            });


        }


        return new PageVO<>(page.getTotal(), page.getPages(), page.getRecords());
    }


    public void updateOpenGroupApprove(UpdateGroupApproveRequest req) {
        UserSession userSession = UserSession.get();
        ProjectIntentHotelGroupEntity record = new ProjectIntentHotelGroupEntity();
        record.setProjectIntentHotelGroupId(req.getProjectIntentHotelGroupId());
        record.setIsOpenGroupApprove(req.getIsOpenGroupApprove());
        record.setModifier(userSession.getUsername());
        projectIntentHotelGroupMapper.updateById( record);
    }

    /**
     * 导出报价
     */
    public void exportTenderPrice(@Valid ExportTenderPriceRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        // 查询项目信息
        ProjectEntity project = projectMapper.selectById(request.getProjectId());
        if (Objects.isNull(project)) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        // 构建导出文件名称
        String fileName = project.getProjectName() + "_" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);

        // 写入数据
        ExportExcelContext<ExportTenderPriceVO> exportContext = new ExportExcelContext<>();
        exportContext.setHttpServletResponse(httpServletResponse);
        exportContext.setLanguage(AppUtility.getRequestHeaderLanguage(httpServletRequest));
        exportContext.setRequestNo(request.getRequestSequenceNo());
        exportContext.setExportVOClass(ExportTenderPriceVO.class);
        exportContext.setFileName(fileName);
        exportContext.setDynamicHeaderMap(projectManager.buildCustomStrategyHeader(request.getProjectId()));
        exportContext.setExportData(queryTenderHotelPrice(request.getProjectId(), httpServletRequest));
        excelManager.generalExport(exportContext);
    }

    /**
     * 查询报价
     */
    private List<ExportTenderPriceVO> queryTenderHotelPrice(@NotNull Integer projectId, HttpServletRequest httpServletRequest) {
        UserSession userSession = UserSession.get();
        Integer orgId = userSession.getUserOrg().getOrgId();
        HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfoVO = AppUtility.getHotelGroupUserRelatedInfoVO(userSession);
        int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);

        // 除了"未报价"和"放弃报价"状态的, 全部导出
        ArrayList<Integer> validBidStates = Lists.newArrayList(
            HotelBidStateEnum.NEW_BID.bidState,
            HotelBidStateEnum.UNDER_NEGOTIATION.bidState,
            HotelBidStateEnum.BID_WINNING.bidState,
            HotelBidStateEnum.REJECTED.bidState,
            HotelBidStateEnum.UPDATED_BID.bidState,
            HotelBidStateEnum.REJECT_NEGOTIATION.bidState
        );

        // 查询报价
        List<TenderHotelPriceVO> projectIntentHotelList = projectIntentHotelMapper.queryHotelGroupTenderInfo(projectId, validBidStates, orgId, hotelGroupUserRelatedInfoVO.getHotelBrandIdSet());
        List<TenderHotelPriceVO> priceList = projectManager.queryHotelGroupTenderPriceList(language, projectId, projectIntentHotelList);

        // 转换成要导出的 vo
        return priceList.stream().map(this::convertToExportTenderPriceVO).collect(Collectors.toList());
    }

    /**
     * 对象转换
     */
    private ExportTenderPriceVO convertToExportTenderPriceVO(TenderHotelPriceVO tenderHotelPriceVO) {
        ExportTenderPriceVO exportTenderPriceVO = new ExportTenderPriceVO();
        BeanUtil.copyProperties(tenderHotelPriceVO, exportTenderPriceVO);
        return exportTenderPriceVO;
    }

    /**
     * 酒店集团导入标准报价
     */
    public IdVO<Long> uploadStandardBid(MultipartFile file, Integer projectId) throws IOException {
        // 校验项目是否存在
        ProjectEntity project = projectMapper.selectById(projectId);
        if (Objects.isNull(project)) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        ImportExcelContext<ImportStandardBidVO> context = new ImportExcelContext<>();
        context.getExtraParams().put("projectId", projectId);
        context.setFile(file);
        context.setBizType(ImportBizTypeEnum.IMPORT_POI);
        context.setImportVOClass(ImportStandardBidVO.class);
        context.setImportLogic(this::handleImportStandardBid);
        context.setHeadNum(2);
        return new IdVO<>(excelManager.generalImport(context));
    }

    /**
     * 酒店集团导入Lanyon报价
     */
    public IdVO<Long> uploadLanyonBid(MultipartFile file, Integer projectId) {
        // 校验项目是否存在
        ProjectEntity project = projectMapper.selectById(projectId);
        if (Objects.isNull(project)) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        UserSession userSession = UserSession.get();
        String ossKey = "";
        SysImportRecordEntity importRecord = new SysImportRecordEntity();

        try {
            // 上传到 oss
            ossKey = ossManager.putObjectPublic(FileTypeAndPathEnum.IMPORT_FILE_OSS_DIR.filePath, file.getInputStream(), file.getContentType(), file.getOriginalFilename());

            // 创建导入记录
            importRecord.setImportName(ImportBizTypeEnum.IMPORT_LANYON_BID.name());
            importRecord.setFileName(file.getOriginalFilename());
            importRecord.setImportPath(ossKey);
            importRecord.setStatus(ImportStatusEnum.IN_PROCESS.getKey());
            importRecord.setCreator(userSession.getUsername());
            sysImportRecordMapper.insert(importRecord);

            lanyonImportManager.asyncImportData(importRecord.getSysImportRecordId(), file, project.getProjectId(), userSession);
        } catch (Exception e) {
            log.error(ExceptionUtility.getDetailedExceptionString(e));
            importRecord.setStatus(ImportStatusEnum.FAIL.getKey());
            importRecord.setModifier(userSession.getUsername());
            sysImportRecordMapper.updateById(importRecord);
        }

        return new IdVO<>(importRecord.getSysImportRecordId());
    }


    /**
     * 酒店集团审核通过
     * */

    public void hotelGroupApproveBid(Integer projectIntentHotelId) {
        UserSession userSession = UserSession.get();
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.selectById(projectIntentHotelId);
        // 检查报价状态
        if(!Objects.equals(projectIntentHotel.getHotelGroupApproveStatus(), HotelGroupApproveStatusEnum.WAITING.key) &&
                !Objects.equals(projectIntentHotel.getHotelGroupApproveStatus(), HotelGroupApproveStatusEnum.REJECT_APPROVED.key)
        ){
            AppUtility.serviceError(ErrorCode.INVALIDATE_BID_APPROVE_STATUS);
        }
        // 当新报价 审核通过为 已报价状态
        projectIntentHotel.setHotelGroupApproveStatus(HotelGroupApproveStatusEnum.APPROVED.key);
        // 议价中审核通过为修改报价
        if(Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.NO_BID.bidState)){
            projectIntentHotel.setBidState(HotelBidStateEnum.NEW_BID.bidState);
        }
        projectIntentHotel.setModifier(userSession.getUsername());
        projectIntentHotelMapper.updateHotelGroupApprove(projectIntentHotel);

    }

    /**
     * 酒店集团审核驳回
     */
    public void hotelGroupRejectBid(Integer projectIntentHotelId, String rejectRemark) {
        UserSession userSession = UserSession.get();
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.selectById(projectIntentHotelId);
        // 检查报价状态
        if(!Objects.equals(projectIntentHotel.getHotelGroupApproveStatus(), HotelGroupApproveStatusEnum.WAITING.key)){
            AppUtility.serviceError(ErrorCode.INVALIDATE_BID_APPROVE_STATUS);
        }
        // 审核不通过为审核驳回（审核不通过填写审核不通过原因）
        projectIntentHotel.setHotelGroupApproveStatus(HotelGroupApproveStatusEnum.REJECT_APPROVED.key);
        projectIntentHotel.setHotelGroupRejectApproveReason(rejectRemark);
        projectIntentHotel.setModifier(userSession.getUsername());
        projectIntentHotelMapper.updateHotelGroupApprove(projectIntentHotel);
    }

    /**
     * 导入逻辑
     */
    private List<ImportRowErrorVO> handleImportStandardBid(ImportExcelContext<ImportStandardBidVO> context, List<ImportStandardBidVO> importList) {
        Integer projectId = context.getExtra("projectId", Integer.class);
        List<ImportRowErrorVO> errors = new ArrayList<>();

        // 填充 projectId
        importList.forEach(item -> item.setProjectId(projectId));

        // 校验并生成创建报价请求
        ValidateBidContext validateBidContext = new ValidateBidContext();
        validateBidContext.setLanguage(context.getLanguage());
        List<CreateBidRequest> createBidRequestList = bidManager.validateAndGenerateCreateBidRequest(validateBidContext, importList, errors);

        // 创建报价
        if (CollUtil.isNotEmpty(createBidRequestList)) {
            // 创建报价
            bidManager.batchCreateBid(createBidRequestList, null, context.getUserSession());
        }
        return errors;
    }


    public ProjectBidStatCountVO queryProjectInviteCityStat(HttpServletRequest request, Integer projectId){
        ProjectBidStatCountVO result = new ProjectBidStatCountVO();
        result.setProjectId(projectId);

        int languageId = GenericAppUtility.getRequestHeaderLanguage(request);

        UserSession userSession = UserSession.get();
        HotelGroupUserRelatedInfoVO hotelGroupUserRelatedInfoVO = GenericAppUtility.getHotelGroupUserRelatedInfoVO(userSession);
        List<ProjectBidBrandStatInfoVO>  projectBidBrandStatCountVOList =  projectIntentHotelMapper.queryProjectInviteCityInfo(projectId, hotelGroupUserRelatedInfoVO.getHotelGroupOrgId(), hotelGroupUserRelatedInfoVO.getHotelBrandIdSet());
        // 统计国家
        Map<String, ProjectBidStatCountItemVO> countryCountMap = new HashMap<>();
        Map<String, List<ProjectBidBrandStatInfoVO>> countryListMap = projectBidBrandStatCountVOList.stream().collect(Collectors.groupingBy(ProjectBidBrandStatInfoVO::getCountryCode));
        for(String countryCode : countryListMap.keySet()){
            if(!countryCountMap.containsKey(countryCode)){
                ProjectBidStatCountItemVO itemVO = new ProjectBidStatCountItemVO();
                itemVO.setCode(countryCode);
                itemVO.setName(countryCode);
                itemVO.setCount(0);
                CountryEntity countryEntity = cachedCountryService.getByCountryCode(countryCode);
                if(countryEntity != null){
                    itemVO.setName(GenericAppUtility.getName(languageId, countryEntity.getCountryCode(), countryEntity.getNameEnUs(), countryEntity.getNameZhCn()));
                }
                countryCountMap.put(countryCode, itemVO);
            }
            ProjectBidStatCountItemVO countryCountVO = countryCountMap.get(countryCode);
            for(ProjectBidBrandStatInfoVO record : countryListMap.get(countryCode)){
                countryCountVO.setCount(countryCountVO.getCount() + record.getCountryBidCount());
            }
        }
        result.setCountryList(new ArrayList<>(countryCountMap.values()));

        // 城市统计
        Map<String, ProjectBidStatCountItemVO> cityCountMap = new HashMap<>();
        Map<String, List<ProjectBidBrandStatInfoVO>> cityListMap = projectBidBrandStatCountVOList.stream().collect(Collectors.groupingBy(ProjectBidBrandStatInfoVO::getCityCode));
        for(String cityCode : cityListMap.keySet()){
            if(!cityCountMap.containsKey(cityCode)){
                ProjectBidStatCountItemVO itemVO = new ProjectBidStatCountItemVO();
                itemVO.setCode(cityCode);
                itemVO.setName(AppUtility.getCityName(languageId, cityCode));
                itemVO.setCount(0);
                cityCountMap.put(cityCode, itemVO);
            }
            ProjectBidStatCountItemVO cityCountVO = cityCountMap.get(cityCode);
            for(ProjectBidBrandStatInfoVO record : cityListMap.get(cityCode)){
                cityCountVO.setCount(cityCountVO.getCount() + record.getCityBidCount());
            }
        }
        result.setCityList(new ArrayList<>(cityCountMap.values()));
        // 排序
        result.getCountryList().sort(Comparator.comparingInt(ProjectBidStatCountItemVO::getCount).reversed());
        result.getCityList().sort(Comparator.comparingInt(ProjectBidStatCountItemVO::getCount).reversed());

        return result;
    }




}
