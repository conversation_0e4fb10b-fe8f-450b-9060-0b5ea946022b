<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HotelMapper">

    <select id="selectActiveHotelIds" resultType="java.lang.Long">
        select hotel_id
        from t_hotel
        <where>
            hotel_id in
            <foreach collection="hotelIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            and is_active = 1
        </where>
    </select>

    <insert id="batchUpsert">
        insert into t_hotel(hotel_id, country_code, province_code, city_code, name_en_us, name_zh_cn, address_en_us, address_zh_cn, introduce_en_us, introduce_zh_cn, telephone,
                            email, hotel_star, rating, opening_date, fitment_date, hotel_group_id, hotel_brand_id, main_pic_url, check_in_time, check_out_time, room_num,
                            has_meeting_room, has_swimming_pool, has_gym, has_laundry_room, lng_baidu, lat_baidu, lng_google, lat_google, lng_mars, lat_mars, currency_code,
                            is_active, creator, modifier)
        values
        <foreach collection="hotelEntities" item="item" separator=",">
            (#{item.hotelId}, #{item.countryCode}, #{item.provinceCode}, #{item.cityCode}, #{item.nameEnUs}, #{item.nameZhCn}, #{item.addressEnUs}, #{item.addressZhCn},
             #{item.introduceEnUs}, #{item.introduceZhCn}, #{item.telephone}, #{item.email}, #{item.hotelStar}, #{item.rating}, #{item.openingDate}, #{item.fitmentDate},
             #{item.hotelGroupId}, #{item.hotelBrandId}, #{item.mainPicUrl}, #{item.checkInTime}, #{item.checkOutTime}, #{item.roomNum}, #{item.hasMeetingRoom},
             #{item.hasSwimmingPool}, #{item.hasGym}, #{item.hasLaundryRoom}, #{item.lngBaidu}, #{item.latBaidu}, #{item.lngGoogle}, #{item.latGoogle}, #{item.lngMars},
             #{item.latMars}, #{item.currencyCode}, #{item.isActive}, #{item.creator}, #{item.modifier})
        </foreach>
        on duplicate key update country_code      = values(country_code),
                                province_code     = values(province_code),
                                city_code         = values(city_code),
                                name_en_us        = values(name_en_us),
                                name_zh_cn        = values(name_zh_cn),
                                address_en_us     = values(address_en_us),
                                address_zh_cn     = values(address_zh_cn),
                                introduce_en_us   = values(introduce_en_us),
                                introduce_zh_cn   = values(introduce_zh_cn),
                                telephone         = values(telephone),
                                email             = values(email),
                                hotel_star        = values(hotel_star),
                                rating            = values(rating),
                                opening_date      = values(opening_date),
                                fitment_date      = values(fitment_date),
                                hotel_group_id    = values(hotel_group_id),
                                hotel_brand_id    = values(hotel_brand_id),
                                main_pic_url      = values(main_pic_url),
                                check_in_time     = values(check_in_time),
                                check_out_time    = values(check_out_time),
                                room_num          = values(room_num),
                                has_meeting_room  = values(has_meeting_room),
                                has_swimming_pool = values(has_swimming_pool),
                                has_gym           = values(has_gym),
                                has_laundry_room  = values(has_laundry_room),
                                lng_baidu         = values(lng_baidu),
                                lat_baidu         = values(lat_baidu),
                                lng_google        = values(lng_google),
                                lat_google        = values(lat_google),
                                lng_mars          = values(lng_mars),
                                lat_mars          = values(lat_mars),
                                currency_code     = values(currency_code),
                                is_active         = values(is_active),
                                modifier          = values(modifier)
    </insert>

    <select id="listDataPage" resultType="com.fangcang.grfp.core.vo.ListHotelDataVO">
        SELECT
            h.*
        FROM
            t_hotel h
        <where>
            <if test="query.hotelId != null">
                AND h.hotel_id = #{query.hotelId}
            </if>
            <if test="query.hotelGroupId != null">
                AND h.hotel_group_id = #{query.hotelGroupId}
            </if>
            <if test="query.hotelBrandId != null">
                AND h.hotel_brand_id = #{query.hotelBrandId}
            </if>
            <if test="query.cityCode != null and query.cityCode != ''">
                AND h.city_code = #{query.cityCode}
            </if>
            <if test="query.hotelName != null and query.hotelName != '' ">
                    AND (h.name_en_us LIKE CONCAT('%', #{query.hotelName}, '%')
                    OR h.name_zh_cn LIKE CONCAT('%', #{query.hotelName}, '%'))
            </if>
            <if test="query.isActive != null">
                AND h.is_active = #{query.isActive}
            </if>
        </where>
    </select>

    <select id="selectHotelNameList" resultType="com.fangcang.grfp.core.vo.HotelNameVO">
        SELECT
            `hotel_id` AS hotelId,
            `hotel_brand_id` AS hotelBrandId,
            `hotel_group_id` AS hotelGroupId,
            `name_en_us` AS nameEnUs,
            `name_zh_cn` AS nameZhCn,
            `country_code` AS countryCode,
            `city_code` AS cityCode,
            `province_code` AS provinceCode
        FROM
            `t_hotel`
        WHERE `name_en_us`LIKE CONCAT('%', #{hotelName}, '%') OR `name_zh_cn` LIKE CONCAT('%', #{hotelName}, '%')
        <if test="languageId != null and languageId == 1">
            ORDER BY  `name_en_us` ASC
        </if>
        <if test="languageId != null and languageId == 2">
            ORDER BY `name_zh_cn` ASC
        </if>
        <if test="limitCount != null and limitCount > 0">
            LIMIT #{limitCount}
        </if>
    </select>

    <select id="selectHotelInfoByIds" resultType="com.fangcang.grfp.core.vo.response.HotelResponse">
        SELECT
        h.hotel_id AS hotelId,
        h.country_code AS country,
        h.province_code AS province,
        h.city_code AS city,
        h.hotel_star AS hotelStar,
        h.address_zh_cn AS addressZhCn,
        h.address_en_us AS addressEnUs,
        h.name_zh_cn AS hotelNameZhCn,
        h.name_en_us AS hotelNameEnUs,
        h.lng_baidu AS lngBaidu,
        h.lat_baidu AS latBaidu,
        h.lng_google AS lngGoogle,
        h.lat_google AS latGooGle,
        h.RATING AS ratting,
        rh.BRIGHT_SPOT AS brightSpot,
        h.hotel_group_id AS hotelGroup,
        h.hotel_brand_id AS hotelBrand
        FROM t_hotel h
        LEFT JOIN T_RECOMMEND_HOTEL rh ON h.hotel_id = rh.hotel_id
        WHERE h.hotel_id IN
        <foreach collection="hotelIds" item="hotelId" separator="," open="(" close=")">
            #{hotelId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectHotelInfoByCityAndRating" resultType="com.fangcang.grfp.core.vo.response.HotelResponse">
        SELECT
        h.hotel_id AS hotelId,
        h.CHN_NAME AS hotelName,
        h.COUNTRY AS country,
        h.PROVINCE AS province,
        h.CITY AS city,
        h.CITY_NAME AS cityName,
        h.HOTEL_STAR AS hotelStar,
        h.CHN_ADDRESS AS chnAddress,
        h.LNG_BAIDU AS lngBaidu,
        h.LAT_BAIDU AS latBaidu,
        h.LONGITUDE AS longitude,
        h.LATITUDE AS latitude,
        h.RATING AS ratting,
        h.opening_date AS praciceDate,
        h.FITMENT_DATE AS fitmentDate
        FROM T_HOTEL h
        WHERE h.city_code = #{city}
        <if test="rating != null and rating != ''">
            AND h.RATING IS NOT NULL AND h.RATING >= #{rating}
        </if>
    </select>

    <select id="queryNoDetailHotelIds" resultType="java.lang.Long">
        SELECT hotel_id AS hotelId
        FROM t_hotel
        WHERE hotel_id NOT IN (
            SELECT hotel_id
            FROM t_hotel_data
        )
    </select>
</mapper>
