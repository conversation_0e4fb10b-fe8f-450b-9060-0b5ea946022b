package com.fangcang.grfp.core.constant;

/**
 * <AUTHOR>
 * @ClassName RedisConstant
 * @Description redis缓存常量
 * @createTime 2022-08-26 18:51:00
 */
public class RedisConstant {

    public static final String REDIS_KEY_PREFIX = "grfp:";
    
    /**
     * Redis 分片(分区)，也可以在配置文件中配置
     */
    public static final int DB_INDEX = 4;

    public static final int SESSION_VAL_TIME_SPAN = 18000;

    public static final String REDIS_SHIRO_SESSION = REDIS_KEY_PREFIX + "user:shiro-session:";

    public static final String REDIS_KEY_USER_PREFIX = REDIS_KEY_PREFIX + "user:";
    
    //这里有个小BUG，因为Redis使用序列化后，Key反序列化回来发现前面有一段乱码，解决的办法是存储缓存不序列化
    public static final String REDIS_SHIRO_ALL = "*" + REDIS_SHIRO_SESSION + "*";


    /** 短信验证码 */
    //PC
    public static final String REGISTER_PRE = REDIS_KEY_USER_PREFIX + "regist:";//注册
    public static final String LOGIN_PRE = REDIS_KEY_USER_PREFIX + "login";//登录
    public static final String FORGETPW_PRE = REDIS_KEY_USER_PREFIX + "forgetpw:";//忘记密码
    public static final String MODIFY_PWD_PRE = REDIS_KEY_USER_PREFIX + "modifypw:";//修改密码
    public static final String BIND_PRE = REDIS_KEY_USER_PREFIX + "bind:";//手机绑定

    /**
     * 频率限制用
     */
    public static final String LIMIT = REDIS_KEY_USER_PREFIX + "limit:";

    public static final String RFP_ORG = REDIS_KEY_USER_PREFIX + "org:";//机构信息

    //添加意向酒店key
    public static final String ADDINTENTHOTEL = REDIS_KEY_PREFIX + "addintenthotel:org:";

    /**
     *  待生成合同队列
     */
    public static final String CONTRACT_GENERATING_QUEUE = REDIS_KEY_PREFIX + "contract:generating:queue";

    /**
     *  合同生成锁
     */
    public static final String CONTRACT_GENERATING_LOCK_KEY = REDIS_KEY_PREFIX + "contract:generating:lock:";

    /**
     *  通知投标人队列
     */
    public static final String NOTIFY_BIDDER = REDIS_KEY_PREFIX + "notify:bidder";

    /**
     * 邀请酒店队列
     */
    public static final String INVITE_HOTEL = REDIS_KEY_PREFIX + "invite:hotel";

    /**
     * 酒店首页代办事项key
     */
    public static final String HOME_SUPPLIER_BACKLOG = REDIS_KEY_PREFIX + "home:supplier:backlog";

    /**
     * 企业首页代办事项key
     */
    public static final String HOME_DIS_BACKLOG = REDIS_KEY_PREFIX + "home:dis:backlog";

    /**
     * 平台首页代办事项key
     */
    public static final String HOME_PLAT_BACKLOG = REDIS_KEY_PREFIX + "home:plat:backlog";

    /**
     * 平台首页电子签名key
     */
    public static final String HOME_PLAT_ELECTRONIC = REDIS_KEY_PREFIX + "home:plat:electronic";

    /**
     * rfp机构数量统计
     */
    public static final String RFP_COUNT_ORG = REDIS_KEY_PREFIX + "home:count:org";

    /**
     * rfp机构用户数量统计
     */
    public static final String RFP_ORG_USER_COUNT = REDIS_KEY_PREFIX + "home:user:count:";

    /**
     * rfp 机构数量统计缓存时间
     */
    public static final int COUNT_ORG_VAL_TIME = 86400;

    /**
     * 首页缓存过期时间
     */
    public static final int HOME_CACHE_EXPIRE = 1800;

    /**
     * 报价监控设置key
     */
    public static final String PRICE_MONITOR_CONFIG = REDIS_KEY_PREFIX + "price:monitor:config";

    /**
     * 待监控酒店IDkey
     */
    public static final String PRICE_MONITOR_HOTELIDS = REDIS_KEY_PREFIX + "price:monitor:hotelIds:";

    /**
     * 待重复满房监控酒店ID Key
     */
    public static final String PRICE_MONITOR_REPEAT_FULL_CHECK_HOTELIDS = REDIS_KEY_PREFIX + "price:monitor:repeatFull:hotelIds:";


    /**
     * 待重复满房监控机构key
     */
    public static final String PRICE_MONITOR_REPEAT_FULL_CHECK_ORGID = REDIS_KEY_PREFIX + "price:monitor:repeatFull:orgid";


    /**
     * 待监控机构key
     */
    public static final String PRICE_MONITOR_ORGID = REDIS_KEY_PREFIX + "price:monitor:orgid";

    /**
     *  在线签章锁
     */
    public static final String ONLINE_SIGNATURE_LOCK_KEY = REDIS_KEY_PREFIX + "online:signature:lock:";

    /**
     *  在线签章缓存队列
     */
    public static final String ONLINE_SIGNATURE = REDIS_KEY_PREFIX + "online:signature";

    /**
     * 首页成交排行key
     */
    public static final String HOME_TRANSACTION_RANKING_MONITOR = REDIS_KEY_PREFIX + "home:transactionRankingMonitor:";

    /**
     * 预定分布key
     */
    public static final String HOME_PREDETERMINE_DISTRIBUTION = REDIS_KEY_PREFIX + "home:predeterminedDistribution:";

    /**
     * 绑定项目查询推荐酒店缓存key
     */
    public static final String BIND_PROJECT_TO_QUERY_RECOMMEND_HOTEL = REDIS_KEY_PREFIX + "recommendHotel:bindProject:";

    /**
     * 邀请酒店集团队列
     */
    public static final String INVITE_HOTEL_GROUP = REDIS_KEY_PREFIX + "invite:hotelGroup";

    /**
     * 初始化酒店报价违规监控数据
     */
    public static final String HOTEL_PRICE_VIOLATIONS_MONITOR = REDIS_KEY_PREFIX + "hotelViolations:priceMonitor";

    /**
     * 违规复合项目酒店违规记录 HotelViolationsMonitor队列
     */
    public static final String HOTEL_VIOLATIONS_PRICE_REPEAT_MONITOR = REDIS_KEY_PREFIX + "hotelViolations:priceMonitor:repeat:";

    /**
     * 违规复合项目酒店违规记录 HotelViolationsMonitor队列
     */
    public static final String HOTEL_VIOLATIONS_PRICE_REPEAT_MONITOR_RETRY = REDIS_KEY_PREFIX + "hotelViolations:priceMonitor:repeatRetry:";

    /**
     * 初始化发送酒店报价违规监控数据
     */
    public static final String SEND_HOTEL_PRICE_VIOLATIONS_MONITOR = REDIS_KEY_PREFIX + "hotelViolations:send:priceMonitor";

    /**
     * 初始化酒店订单违规监控数据
     */
    public static final String HOTEL_ORDER_VIOLATIONS_MONITOR = REDIS_KEY_PREFIX + "hotelViolations:orderMonitor";

    /**
     * 初始化发送酒店订单违规监控数据
     */
    public static final String SEND_HOTEL_ORDER_VIOLATIONS_MONITOR = REDIS_KEY_PREFIX + "hotelViolations:send:orderMonitor";

    /**
     * 导出请求序号
     */
    public static final String EXPORT_DATA_REQUEST_SEQUENCE_NO = REDIS_KEY_PREFIX + "exportDataRequestSequenceNo:";
    /**
     * 导出超时时间
     */
    public static final int EXPORT_DATA_REQUEST_SEQUENCE_NO_EXPIRY_TIME = 1200;

    /**
     * 保持需要调用AI接口更新缓存酒店列表
     */
    public static final String AI_REVIEW_HOTEL_IDS = REDIS_KEY_PREFIX + "aiReview:hotelIds";

    /**
     * 生成项目 POI 统计分布式锁 key
     */
    public static final String GENERATE_HISTORY_PROJECT_STAT_LOCK_KEY = REDIS_KEY_PREFIX + "GenerateHistoryProjectStat:%s";


    public static final String GENERATE_HOTEL_HEXAGON_LOCK_KEY = REDIS_KEY_PREFIX + "GenerateHotelHexagon:%s";

    /**
     * 生成项目 POI 统计分布式锁 key
     */
    public static final String GENERATE_HOTEL_RECOMMEND_LEVEL_STAT_LOCK_KEY = REDIS_KEY_PREFIX + "GenerateHotelRecommendLevel:%s";
}
