package com.fangcang.grfp.core.vo.request;

import lombok.Data;

import java.io.File;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-7-13
 */
@Data
public class MailSendRequest implements Serializable {

    private static final long serialVersionUID = -8257840544600380496L;

    /**
     * 发给谁(必填)
     */
    private String[] to;
    /**
     * 抄送给谁(非必填)
     */
    private String[] cc;
    /**
     * 主题(必填)
     */
    private String subject;
    /**
     * 内容(必填)
     */
    private String content;
    /**
     * 用户名(非必填),用于展示 发送给谁的,填充在以下位置:
     * 尊敬的用户 {username}, 你好:
     */
    private String username;
    /**
     * 附件(非必填)
     */
    private File[] files;

}
