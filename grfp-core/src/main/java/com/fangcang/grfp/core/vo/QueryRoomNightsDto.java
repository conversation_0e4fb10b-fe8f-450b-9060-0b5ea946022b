package com.fangcang.grfp.core.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/28 21:02
 */
public class QueryRoomNightsDto {

    //酒店id
    private Long hotelId;

    //分销商编码
    private List<String> distributorCodes;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;


    //酒店id集合
    private List<Long> hotelIds;

    public Long getHotelId() {
        return hotelId;
    }

    public void setHotelId(Long hotelId) {
        this.hotelId = hotelId;
    }

    public List<String> getDistributorCodes() {
        return distributorCodes;
    }

    public void setDistributorCodes(List<String> distributorCodes) {
        this.distributorCodes = distributorCodes;
    }

    public String getStartTime() {
        return startTime;
    }

    public List<Long> getHotelIds() {
        return hotelIds;
    }

    public void setHotelIds(List<Long> hotelIds) {
        this.hotelIds = hotelIds;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
