package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.ProjectHotelPriceGroupEntity;
import com.fangcang.grfp.core.vo.ProjectHotelPriceGroupVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 项目酒店价格组 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface ProjectHotelPriceGroupMapper extends BaseMapper<ProjectHotelPriceGroupEntity> {

    default List<ProjectHotelPriceGroupEntity> selectByProjectIntentHotelId(int projectIntentHotelId){
        LambdaQueryWrapper<ProjectHotelPriceGroupEntity>queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProjectHotelPriceGroupEntity::getProjectIntentHotelId, projectIntentHotelId);
        queryWrapper.orderByAsc(ProjectHotelPriceGroupEntity::getHotelPriceGroupId);
        return selectList(queryWrapper);
    }

    default List<ProjectHotelPriceGroupEntity> selectByHotelPriceLevelId(int hotelPriceLevelId){
        LambdaQueryWrapper<ProjectHotelPriceGroupEntity>queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProjectHotelPriceGroupEntity::getHotelPriceLevelId, hotelPriceLevelId);
        return selectList(queryWrapper);
    }

    default List<ProjectHotelPriceGroupEntity> selectByProjectId(Integer projectId) {
        LambdaQueryWrapper<ProjectHotelPriceGroupEntity> queryWrapper = Wrappers.lambdaQuery(ProjectHotelPriceGroupEntity.class);
        queryWrapper.eq(ProjectHotelPriceGroupEntity::getProjectId, projectId);
        return selectList(queryWrapper);
    }

    List<ProjectHotelPriceGroupVO> selectInfoByProjectId(Integer projectId);

    default void deleteByProjectAndHotelId(Integer projectId, Long hotelId) {
        LambdaQueryWrapper<ProjectHotelPriceGroupEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHotelPriceGroupEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectHotelPriceGroupEntity::getHotelId, hotelId);
        delete(queryWrapper);
    }

    default int updateLocked(Integer projectPriceGroupId, Integer isLocked, String modifier){
        LambdaUpdateWrapper<ProjectHotelPriceGroupEntity> updateWrapper = new LambdaUpdateWrapper<ProjectHotelPriceGroupEntity>();
        updateWrapper.eq(ProjectHotelPriceGroupEntity::getHotelPriceGroupId, projectPriceGroupId);
        updateWrapper.set(ProjectHotelPriceGroupEntity::getIsLocked, isLocked);
        updateWrapper.set(ProjectHotelPriceGroupEntity::getModifier, modifier);
        return update(null, updateWrapper);
    }

    void batchInsert(@Param("list") List<ProjectHotelPriceGroupEntity> newPriceGroups);

    /**
     * 根据项目 ID 和酒店 ID 查询
     */
    default List<ProjectHotelPriceGroupEntity> selectByProjectIdAndHotelId(Integer projectId, Long hotelId) {
        LambdaQueryWrapper<ProjectHotelPriceGroupEntity> queryWrapper = Wrappers.lambdaQuery(ProjectHotelPriceGroupEntity.class);
        queryWrapper.eq(ProjectHotelPriceGroupEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectHotelPriceGroupEntity::getHotelId, hotelId);
        return selectList(queryWrapper);
    }

}
