package com.fangcang.grfp.core.cached.impl;

import com.fangcang.grfp.core.cached.CachedProjectIntentHotelService;
import com.fangcang.grfp.core.mapper.ProjectIntentHotelMapper;
import com.fangcang.grfp.core.util.CoordinateUtils;
import com.fangcang.grfp.core.util.GenericAppUtility;
import com.fangcang.grfp.core.vo.LngLatGoogleVO;
import com.fangcang.grfp.core.vo.response.project.BidHotelInfoQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class CachedProjectIntentHotelServiceImpl implements CachedProjectIntentHotelService {

    @Autowired
    private ProjectIntentHotelMapper projectIntentHotelMapper;

    @Override
    @Cacheable(value="cachedProjectIntentHotelService.queryProjectBidHotelInfo", key = "#projectId + '_' + #cityCode", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public List<BidHotelInfoQueryResponse> queryProjectBidHotelInfo(Integer projectId, String cityCode) {
        projectIntentHotelMapper.queryProjectBidHotelInfo(projectId, cityCode).forEach(item -> {
            LngLatGoogleVO lngLatGoogle =  CoordinateUtils.calculateNullGoogleLngLat(item.getLngGoogle(), item.getLatGoogle(), item.getLngBaiDu(), item.getLngBaiDu());
            item.setLngGoogle(lngLatGoogle.getLngGoogle());
            item.setLatGoogle(lngLatGoogle.getLatGoogle());
        });
        return projectIntentHotelMapper.queryProjectBidHotelInfo(projectId, cityCode);
    }
}
