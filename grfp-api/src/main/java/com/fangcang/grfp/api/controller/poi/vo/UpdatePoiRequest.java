package com.fangcang.grfp.api.controller.poi.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@ApiModel("更新 POI 请求")
@Getter
@Setter
public class UpdatePoiRequest extends BaseVO {

    private static final long serialVersionUID = 5198240374418997073L;

    @ApiModelProperty(value = "POI ID", required = true)
    @NotNull
    private Long poiId;

    @ApiModelProperty(value = "POI 名称", required = true)
    @NotBlank
    private String poiName;

    @ApiModelProperty(value = "POI 地址", required = true)
    @NotBlank
    private String poiAddress;

    @ApiModelProperty(value = "机构 ID", required = true)
    @NotNull
    private Integer orgId;

    @ApiModelProperty(value = "城市编码", required = true)
    @NotBlank
    private String cityCode;

    @ApiModelProperty(value = "地图 POI ID")
    private String mapPoiId;

    @ApiModelProperty(value = "谷歌经度", required = true)
    @NotNull
    private BigDecimal lngGoogle;

    @ApiModelProperty(value = "谷歌纬度", required = true)
    @NotNull
    private BigDecimal latGoogle;

}
