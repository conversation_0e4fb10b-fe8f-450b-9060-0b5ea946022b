package com.fangcang.grfp.core.vo.request;

import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("币种汇率列表分页查询")
@Getter
@Setter
public class ListCurrencyExchangeRateRequest extends PageQuery {

    @ApiModelProperty(value = "from币种")
    String fromCurrencyCode;

    @ApiModelProperty(value = "to币种")
    String toCurrencyCode;

}
