package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import lombok.Data;

@Data
public class ParkingInfo {

    /**
     * 位置，0-酒店内 1-酒店附近
     */
    private String parkingLocation;

    /**
     * 停车场类型，0-私人停车场，1-公共停车场
     */
    private String parkingType;

    /**
     * 是否需预订，0-需要预订，1-无需预订，2-无法提前预订
     */
    private String isReserved;

    /**
     * 是否收费，0-免费 1-部分住客可能收费 2-收费
     */
    private String isChargeable;

    /**
     * 收费金额
     */
    private String chargeAmount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 车位是否有限，0-否 1-是
     */
    private String isLimitParking;

}
