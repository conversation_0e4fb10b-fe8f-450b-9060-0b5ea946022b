package com.fangcang.grfp.core.vo.request;

import com.fangcang.grfp.core.base.PageQuery;
import com.fangcang.grfp.core.constant.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@ApiModel("城市列表分页查询")
@Getter
@Setter
public class ListCityDataRequest extends PageQuery {

    @ApiModelProperty(value = "英文名称")
    private String nameEnUs;

    @ApiModelProperty(value = "中文名称")
    private String nameZhCn;

    @ApiModelProperty(value = "国家编号")
    private String countryCode;

    @ApiModelProperty(value = "省份编号")
    private String provinceCode;

    @ApiModelProperty(value = "城市编号")
    private String cityCode;

    @ApiModelProperty(value = "父城市编号")
    private String parentCityCode;
    

}
