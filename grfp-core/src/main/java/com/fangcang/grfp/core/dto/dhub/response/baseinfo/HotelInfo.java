package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import lombok.Data;

import java.util.List;


@Data
public class HotelInfo {

    /**
     * 酒店 id
     */
    private Long hotelId;

    /**
     * 币种
     */
    private String currency;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店英文名称（后期废弃该字段）
     */
    private String hotelEngName;

    /**
     * 酒店地址
     */
    private String address;

    /**
     * 酒店主图地址
     */
    private String appearancePicUrl;

    /**
     * 酒店介绍
     */
    private String hotelIntroduce;

    /**
     * 酒店电话
     */
    private String telephone;

    /**
     * 酒店传真
     */
    private String fax;

    /**
     * 酒店星级，见字典项【酒店星级】
     */
    private Integer hotelStar;

    /**
     * 开业日期
     */
    private String praciceDate;

    /**
     * 最近装修日期
     */
    private String fitmentDate;

    /**
     * 集团id
     */
    private String parentHotelGroup;

    /**
     * 集团名称
     */
    private String parentHotelGroupName;

    /**
     * 品牌id
     */
    private String plateID;

    /**
     * 品牌名称
     */
    private String plateName;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 城市编码
     */
    private String city;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 行政区编码
     */
    private String distinct;

    /**
     * 行政区名称
     */
    private String distinctName;

    /**
     * 商业区编码
     */
    private String business;

    /**
     * 商业区名称
     */
    private String businessName;

    /**
     * 百度经度
     */
    private Double longitude;

    /**
     * 百度纬度
     */
    private Double latitude;

    /**
     * 谷歌经度
     */
    private Double lngGoogle;

    /**
     * 谷歌纬度
     */
    private Double latGoogle;

    /**
     * 酒店规定最早入住时间
     */
    private String checkInTime;

    /**
     * 酒店规定最晚入住时间
     */
    private String checkInLateTime;

    /**
     * 酒店规定最早离店时间
     */
    private String checkOutTime;

    /**
     * 酒店规定最晚离店时间
     */
    private String checkOutEarlyTime;

    /**
     * 客房总数
     */
    private String roomNum;

    /**
     * 可接待人群，3：仅接待大陆客人；4：仅接待大陆和港澳台客人；5：全球客人；
     */
    private String applicableGuest;

    /**
     * 酒店设施（含房型设施）
     */
    private List<HotelFacilityNewResponseDto> hotelFacilityNew;

    /**
     * 热门设施
     */
    private HotFacility hotFacility;

    /**
     * 早餐信息
     */
    private List<BreakfastResponseDto> breakfast;

    /**
     * 是否有停车场
     */
    private String isPark;

    /**
     * 停车场信息
     */
    private List<ParkingInfo> parking;

    /**
     * 是否有充电车位，0-否 1-是
     */
    private String isChargePark;

    /**
     * 充电停车场信息
     */
    private List<ChargingParking> chargingParking;

    /**
     * 重要通知，含防疫信息
     */
    private List<ImportantNotice> importantNotices;

    /**
     * 酒店资质，部分酒店有
     */
    private List<HotelCertificationDto> hotelCertificates;

    /**
     * 评分
     */
    private List<Comment> comment;

    /**
     * 酒店房型信息
     */
    private List<RoomInfo> roomInfos;

    /**
     * 酒店视频信息
     */
    private List<HotelVideoInfo> hotelVideoInfos;

    /**
     * 酒店会议室信息
     */
    private List<HotelMeetingInfo> hotelMeetingInfos;

    /**
     * 酒店结构化政策
     */
    private HotelStructuredPolicy hotelStructuredPolicies;

    /**
     * 酒店文本政策
     */
    private List<HotelTextPolicy> hotelTextPolicies;

    /**
     * 标签名称列表
     */
    private List<String> hotelLabelNameList;

    /**
     * 酒店分类
     */
    private String hotelCategory;

    /**
     * 酒店子分类
     */
    private String hotelSubCategory;

}
