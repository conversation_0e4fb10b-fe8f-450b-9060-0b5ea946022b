package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import lombok.Data;

@Data
public class HotelBreakfastOpenTimeDto {

    /**
     * 适用星期几，从周一到周日（如 0000011 表示适用用周六、周日）
     */
    private String weeklyIndex;

    /**
     * 是否营业 0-不营业 1-营业
     */
    private String isOpen;

    /**
     * 营业开始时间
     */
    private String openDayStartTime;

    /**
     * 营业结束时间
     */
    private String openDayEndTime;

}
