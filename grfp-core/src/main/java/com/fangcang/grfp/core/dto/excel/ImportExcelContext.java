package com.fangcang.grfp.core.dto.excel;

import com.fangcang.grfp.core.base.ImportRowErrorVO;
import com.fangcang.grfp.core.base.ImportVO;
import com.fangcang.grfp.core.enums.ImportBizTypeEnum;
import com.fangcang.grfp.core.usersession.UserSession;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;

@Getter
@Setter
public class ImportExcelContext<T extends ImportVO> {

    /**
     * 用户会话
     */
    private UserSession userSession;

    /**
     * 上传文件
     */
    private MultipartFile file;

    /**
     * 导入业务类型
     */
    private ImportBizTypeEnum bizType;

    /**
     * 导入记录 ID
     */
    private Long recordId;

    /**
     * 导入对象类
     */
    private Class<T> importVOClass;

    /**
     * 语言
     */
    private int language;

    /**
     * 没批处理大小
     */
    private Integer batchSize = 500;

    /**
     * 导入逻辑处理
     */
    private BiFunction<ImportExcelContext<T>, List<T>, List<ImportRowErrorVO>> importLogic;

    /**
     * 预期表头, key 是行数, value 是表头
     */
    private Map<Integer, List<String>> expectedHeaders;

    /**
     * 校验表头行数
     */
    private Integer validateHeadNum = 1;

    /**
     * 表头行数
     */
    private Integer headNum = 1;

    /**
     * 额外的参数
     */
    private Map<String, Object> extraParams = new HashMap<>();

    public void setExtra(String key, Object value) {
        this.extraParams.put(key, value);
    }

    public <T> T getExtra(String key, Class<T> clazz) {
        return clazz.cast(this.extraParams.get(key));
    }

}
