package com.fangcang.grfp.api.controller.account.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@ApiModel("更新账户信息")
@Getter
@Setter
public class UpdateAccountInfoRequest extends BaseVO {

    @ApiModelProperty(value = "用户姓名")
    @NotBlank
    private String userName;

    @ApiModelProperty(value = "用户电邮")
    @Email
    @NotBlank
    private String email;

    @ApiModelProperty(value = "手机号码区号")
    private String mobileAreaCode;

    @ApiModelProperty(value = "手机号码")
    private String mobile;


}
