package com.fangcang.grfp.core.base;

import com.fangcang.grfp.core.util.SessionUtility;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.session.FindByIndexNameSessionRepository;

import java.io.Serializable;

/**
 * session 中的对象基类
 *
 */
@Getter
@Setter
public abstract class BaseSession extends BaseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户 id
     */
    protected Integer userId;

    /**
     * 用户名
     */
    protected String username;

    /**
     * 会话 KEY
     */
    public abstract String sessionKey();

    /**
     * 会话登录
     */
    public void login() {
        SessionUtility.setAttribute(sessionKey(), this);
        if (userId != null) {
            SessionUtility.setAttribute(FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME, String.valueOf(userId));
        }
    }

}
