package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 根据目的地查询酒店列表
 */
@Data
@ApiModel("根据目的地查询酒店列表")
public class FindHotelListByDestinationRequest extends PageQuery {

    /**
     * 目的地ID
     */
    @ApiModelProperty("目的地ID")
    @NotBlank
    private String destinationId;

    /**
     * 入住日期 格式：yyyy-MM-dd 不能为空
     */
    @ApiModelProperty("入住日期 yyyy-MM-dd")
    @NotBlank
    private String checkInDate;

    /**
     * 离店日期 格式：yyyy-MM-dd 不能为空
     */
    @ApiModelProperty("离店日期 yyyy-MM-dd")
    @NotBlank
    private String checkOutDate;

    /**
     * 价格开始值
     */
    @ApiModelProperty("价格开始值")
    private Integer priceBegin;

    /**
     * 价格结束值
     */
    @ApiModelProperty("价格结束值")
    private Integer priceEnd;

    /**
     * 酒店标签id
     */
    @ApiModelProperty("酒店标签id")
    private List<Integer> hotelLabelIds;

    /**
     * 酒店品牌编号
     */
    @ApiModelProperty("酒店品牌编号")
    private List<String> hotelBrandCodes;

    /**
     * 酒店集团编号
     */
    @ApiModelProperty("酒店集团编号")
    private List<String> plateCodes;

    /**
     * 住宿类型
     */
    @ApiModelProperty("酒店子类型Id集合")
    private List<String> hotelSubCategoryIds;

    /**
     * 酒店星级
     */
    @ApiModelProperty("酒店星级")
    private List<Integer> hotelStars;

    /**
     * 酒店设施编码
     */
    @ApiModelProperty("酒店设施编码")
    private List<String> hotelFacilityCodes;

    /**
     * 房间设施编码
     */
    @ApiModelProperty("房间设施编码")
    private List<String> roomFacilityCodes;

    /**
     * 酒店关键字名称 用于酒店列表根据名称筛选指定酒店
     */
    @ApiModelProperty("酒店关键字名称")
    private String keyWord;


    @ApiModelProperty("排序方式：1默认排序 2价格由低到高 3价格由高到低 4星级从高到底 5星级从低到高 6距离由近到远 7评分由高到低。默认值：1")
    private Integer sortBy;


}
