package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.ProjectHotelTendWeightEntity;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelTendWeightVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 项目权重配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
public interface ProjectHotelTendWeightMapper extends BaseMapper<ProjectHotelTendWeightEntity> {

    /**
     * 插入或更新
     */
    void upsert(@Param("entity") ProjectHotelTendWeightEntity entity);

    /**
     * 根据项目 ID 查询
     */
    ProjectHotelTendWeightVO selectByProjectId(@Param("projectId") Integer projectId);

    /**
     * 根据项目 ID 查询
     */
    default ProjectHotelTendWeightEntity selectEntityByProjectId(@Param("projectId") Integer projectId){
        LambdaQueryWrapper<ProjectHotelTendWeightEntity>lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(ProjectHotelTendWeightEntity::getProjectId, projectId);
        return selectOne(lambdaQueryWrapper);
    }

}
