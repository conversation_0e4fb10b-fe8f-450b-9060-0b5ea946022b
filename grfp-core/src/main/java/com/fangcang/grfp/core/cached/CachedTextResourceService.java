package com.fangcang.grfp.core.cached;


import com.fangcang.grfp.core.vo.TextResourcesVO;

public interface CachedTextResourceService {

	// ---------------------------------------------------------------------------------------------------- Public Methods
	
	public String getWebValue(int languageId, String textResourceCode);

	public String getMsgValue(int languageId, String textResourceCode);

	// ----------------------------------------------------------------------------------------------------
	
	public String getLatestUpdatedTime(Integer textResourceType);

	public void clearTextResourcesVO(int languageId, Integer textResourceType, String lastUpdatedTime);

	public TextResourcesVO getTextResourcesVO(int languageId, Integer textResourceType, String lastUpdatedTime);
}
