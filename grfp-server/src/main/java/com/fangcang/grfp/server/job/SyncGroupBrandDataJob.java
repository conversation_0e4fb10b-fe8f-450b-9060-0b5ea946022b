package com.fangcang.grfp.server.job;

import cn.hutool.core.collection.CollUtil;
import com.fangcang.grfp.core.constant.SysConfig;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.QueryGroupBrandListRequest;
import com.fangcang.grfp.core.dto.dhub.response.Response;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.HotelPlate;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.QueryGroupBrandListResponse;
import com.fangcang.grfp.core.entity.HotelBrandEntity;
import com.fangcang.grfp.core.entity.HotelBrandLogEntity;
import com.fangcang.grfp.core.entity.HotelGroupEntity;
import com.fangcang.grfp.core.entity.HotelGroupLogEntity;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.enums.YesOrNoEnum;
import com.fangcang.grfp.core.mapper.HotelBrandLogMapper;
import com.fangcang.grfp.core.mapper.HotelBrandMapper;
import com.fangcang.grfp.core.mapper.HotelGroupLogMapper;
import com.fangcang.grfp.core.mapper.HotelGroupMapper;
import com.fangcang.grfp.core.remote.tmchub.manager.TmcHubApiManager;
import com.fangcang.grfp.core.remote.tmchub.request.SignatureHelpRequestDto;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.JsonUtil;
import com.fangcang.grfp.server.util.AppUtility;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Component
@Slf4j
@JobHandler(value = SyncGroupBrandDataJob.TASK_NAME)
public class SyncGroupBrandDataJob extends IJobHandler {

    public static final String TASK_NAME = "SyncGroupBrandDataJob";

    @Resource
    private TmcHubApiManager tmcHubApiManager;

    @Resource
    private HotelGroupMapper hotelGroupMapper;

    @Resource
    private HotelGroupLogMapper hotelGroupLogMapper;

    @Resource
    private HotelBrandMapper hotelBrandMapper;

    @Resource
    private HotelBrandLogMapper hotelBrandLogMapper;

    /**
     * Synchronize country data to the database
     */
    @Override
    public ReturnT<String> execute(String s) {
        log.info("Sync group brand data job start");
        try {
            Map<Long, HotelGroupEntity> groupIdGroupMap = new HashMap<>(3000);
            Map<Long, HotelBrandEntity> brandIdBrandMap = new HashMap<>(3000);

            Stream.of(LanguageEnum.values()).forEach(language -> {
                // Get group brand data from DHUB
                QueryGroupBrandListRequest queryGroupBrandListRequest = QueryGroupBrandListRequest.builder()
                    .language(language.value)
                    .build();
                Response<QueryGroupBrandListResponse> response = tmcHubApiManager.queryGroupBrandList(queryGroupBrandListRequest, signatureParam());
                if (Objects.isNull(response) || Objects.isNull(response.getBussinessResponse()) ||  CollUtil.isEmpty(response.getBussinessResponse().getHotelPlateList())) {
                    log.error("Query group brand data from DHUB is empty, language: {}", language);
                    return;
                }

                // Convert group data
                convertGroupData(language.value, response.getBussinessResponse().getHotelPlateList(), groupIdGroupMap);
                // Convert brand data
                convertBrandData(language.value, response.getBussinessResponse().getHotelPlateList(), brandIdBrandMap);
            });

            // Insert or update data into the database
            saveData(new ArrayList<>(groupIdGroupMap.values()), new ArrayList<>(brandIdBrandMap.values()));

            // The data not returned by DHUB needs to be set as invalid
            updateActiveStatus(groupIdGroupMap.keySet(), brandIdBrandMap.keySet());

            // Record the change log
            recordGroupChangeLog(groupIdGroupMap);
            recordBrandChangeLog(brandIdBrandMap);

        } catch (Exception e) {
            log.error("Sync group brand data job error", e);
            return ReturnT.FAIL;
        }
        log.info("Sync group brand data end");
        return ReturnT.SUCCESS;
    }

    /**
     * Create a signature parameter
     */
    private SignatureHelpRequestDto signatureParam() {
        SignatureHelpRequestDto signatureParam = new SignatureHelpRequestDto();
        signatureParam.setPartnerCode(AppUtility.getSysInfo(SysConfig.TMC_HUB_PARTNER_CODE));
        signatureParam.setSecurityKey(AppUtility.getSysInfo(SysConfig.TMC_HUB_SECURITY_KEY));
        return signatureParam;
    }

    private void recordGroupChangeLog(Map<Long, HotelGroupEntity> groupIdGroupMap) {
        if (CollUtil.isEmpty(groupIdGroupMap)) {
            return;
        }
        Set<Long> groupIds = groupIdGroupMap.keySet();

        // Get the latest log
        List<HotelGroupLogEntity> existedLogList = hotelGroupLogMapper.selectLatestByGroupIds(groupIds);
        Map<Long, HotelGroupLogEntity> existedLogMap = existedLogList.stream()
            .collect(Collectors.toMap(HotelGroupLogEntity::getHotelGroupId, hotelGroupLogEntity -> hotelGroupLogEntity, (o, n) -> n));

        List<HotelGroupLogEntity> changeLogList = new ArrayList<>();
        groupIdGroupMap.values().forEach(hotelGroup -> {
            HotelGroupLogEntity existedLogEntity = existedLogMap.get(hotelGroup.getHotelGroupId());

            // First time record log
            if (existedLogEntity == null) {
                HotelGroupLogEntity logEntity = new HotelGroupLogEntity();
                logEntity.setHotelGroupId(hotelGroup.getHotelGroupId());
                logEntity.setBeforeKvJson("");
                logEntity.setAfterKvJson(JsonUtil.objectToJson(hotelGroup));
                logEntity.setCreator(UserSession.SYSTEM_USER_NAME);
                changeLogList.add(logEntity);
                return;
            }

            // Compare the data
            String afterKvJson = existedLogEntity.getAfterKvJson();
            HotelGroupEntity existGroup = JsonUtil.jsonToBean(afterKvJson, HotelGroupEntity.class);
            if (!hotelGroup.equals(existGroup)) {
                HotelGroupLogEntity logEntity = new HotelGroupLogEntity();
                logEntity.setHotelGroupId(hotelGroup.getHotelGroupId());
                logEntity.setBeforeKvJson(afterKvJson);
                logEntity.setAfterKvJson(JsonUtil.objectToJson(hotelGroup));
                logEntity.setCreator(UserSession.SYSTEM_USER_NAME);
                changeLogList.add(logEntity);
            }
        });
        if (CollUtil.isNotEmpty(changeLogList)) {
            CollUtil.split(changeLogList, 1000).forEach(logList -> hotelGroupLogMapper.batchInsert(logList));
        }
    }

    private void recordBrandChangeLog(Map<Long, HotelBrandEntity> brandIdBrandMap) {
        if (CollUtil.isEmpty(brandIdBrandMap)) {
            return;
        }
        Set<Long> brandIds = brandIdBrandMap.keySet();

        // Get the latest log
        List<HotelBrandLogEntity> existedLogList = hotelBrandLogMapper.selectLatestByBrandIds(brandIds);
        Map<Long, HotelBrandLogEntity> existedLogMap = existedLogList.stream()
            .collect(Collectors.toMap(HotelBrandLogEntity::getHotelBrandId, hotelGroupLogEntity -> hotelGroupLogEntity, (o, n) -> n));

        List<HotelBrandLogEntity> changeLogList = new ArrayList<>();
        brandIdBrandMap.values().forEach(hotelBrand -> {
            HotelBrandLogEntity existedLogEntity = existedLogMap.get(hotelBrand.getHotelBrandId());

            // First time record log
            if (existedLogEntity == null) {
                HotelBrandLogEntity logEntity = new HotelBrandLogEntity();
                logEntity.setHotelBrandId(hotelBrand.getHotelBrandId());
                logEntity.setBeforeKvJson("");
                logEntity.setAfterKvJson(JsonUtil.objectToJson(hotelBrand));
                logEntity.setCreator(UserSession.SYSTEM_USER_NAME);
                changeLogList.add(logEntity);
                return;
            }

            // Compare the data
            String afterKvJson = existedLogEntity.getAfterKvJson();
            HotelBrandEntity oldBrand = JsonUtil.jsonToBean(afterKvJson, HotelBrandEntity.class);
            if (!hotelBrand.equals(oldBrand)) {
                HotelBrandLogEntity logEntity = new HotelBrandLogEntity();
                logEntity.setHotelBrandId(hotelBrand.getHotelBrandId());
                logEntity.setBeforeKvJson(afterKvJson);
                logEntity.setAfterKvJson(JsonUtil.objectToJson(hotelBrand));
                logEntity.setCreator(UserSession.SYSTEM_USER_NAME);
                changeLogList.add(logEntity);
            }
        });
        if (CollUtil.isNotEmpty(changeLogList)) {
            CollUtil.split(changeLogList, 1000).forEach(logList -> hotelBrandLogMapper.batchInsert(logList));
        }
    }

    private void updateActiveStatus(Set<Long> activeGroupIds, Set<Long> activeBrandIds) {
        // handle group
        Set<Long> existedActiveGroupIds = hotelGroupMapper.selectAllActiveGroupId();
        if (CollUtil.isNotEmpty(existedActiveGroupIds)) {
            existedActiveGroupIds.removeAll(activeGroupIds);
            // Set the group as invalid
            if (CollUtil.isNotEmpty(existedActiveGroupIds)) {
                hotelGroupMapper.updateActiveByGroupIds(existedActiveGroupIds, YesOrNoEnum.NO.getKey());
            }
        }

        // handle brand
        Set<Long> existedActiveBrandIds = hotelBrandMapper.selectAllActiveBrandId();
        if (CollUtil.isNotEmpty(existedActiveBrandIds)) {
            existedActiveBrandIds.removeAll(activeBrandIds);
            // Set the group as invalid
            if (CollUtil.isNotEmpty(existedActiveBrandIds)) {
                hotelBrandMapper.updateActiveByBrandIds(existedActiveBrandIds, YesOrNoEnum.NO.getKey());
            }
        }
    }

    /**
     * Save group and brand data to the database
     */
    private void saveData(Collection<HotelGroupEntity> groupEntityList, Collection<HotelBrandEntity> brandEntityList) {
            // Save group data
            if (CollUtil.isNotEmpty(groupEntityList)) {
                CollUtil.split(groupEntityList, 1000).forEach(groupList -> hotelGroupMapper.batchUpsert(groupList));
            }
            if (CollUtil.isNotEmpty(brandEntityList)) {
                CollUtil.split(brandEntityList, 1000).forEach(brandList -> hotelBrandMapper.batchUpsert(brandList));
            }
        }

    /**
     * convert group data to entity
     */
    private void convertGroupData(String language, List<HotelPlate> hotelPlateList, Map<Long, HotelGroupEntity> groupIdGroupMap) {
        hotelPlateList.forEach(hotelPlate -> {
            HotelGroupEntity hotelGroupEntity = groupIdGroupMap.computeIfAbsent(Long.parseLong(hotelPlate.getGroupId()), k -> new HotelGroupEntity());
            hotelGroupEntity.setHotelGroupId(Long.parseLong(hotelPlate.getGroupId()));
            hotelGroupEntity.setCreator(UserSession.SYSTEM_USER_NAME);
            hotelGroupEntity.setModifier(UserSession.SYSTEM_USER_NAME);
            if (LanguageEnum.ZH_CN.value.equals(language)) {
                hotelGroupEntity.setNameZhCn(StringUtils.defaultString(hotelPlate.getGroupName()));
                hotelGroupEntity.setNameEnUs(StringUtils.defaultString(hotelGroupEntity.getNameEnUs()));
            } else if (LanguageEnum.EN_US.value.equals(language)) {
                hotelGroupEntity.setNameEnUs(StringUtils.defaultString(hotelPlate.getGroupName()));
                hotelGroupEntity.setNameZhCn(StringUtils.defaultString(hotelGroupEntity.getNameZhCn()));
            }
            hotelGroupEntity.setIsActive(YesOrNoEnum.YES.getKey());
        });
    }

    /**
     * convert brand data to entity
     */
    private void convertBrandData(String language, List<HotelPlate> hotelPlateList, Map<Long, HotelBrandEntity> brandIdBrandMap) {
        hotelPlateList.forEach(hotelPlate -> {
            HotelBrandEntity brand = brandIdBrandMap.computeIfAbsent(Long.parseLong(hotelPlate.getBrandId()), k -> new HotelBrandEntity());
            brand.setHotelBrandId(Long.parseLong(hotelPlate.getBrandId()));
            brand.setHotelGroupId(hotelPlate.getGroupId());
            brand.setCreator(UserSession.SYSTEM_USER_NAME);
            brand.setModifier(UserSession.SYSTEM_USER_NAME);
            if (LanguageEnum.ZH_CN.value.equals(language)) {
                brand.setNameZhCn(StringUtils.defaultString(hotelPlate.getBrandName()));
                brand.setNameEnUs(StringUtils.defaultString(brand.getNameEnUs()));
            } else if (LanguageEnum.EN_US.value.equals(language)) {
                brand.setNameEnUs(StringUtils.defaultString(hotelPlate.getBrandName()));
                brand.setNameZhCn(StringUtils.defaultString(brand.getNameZhCn()));
            }
            brand.setIsActive(YesOrNoEnum.YES.getKey());
        });
    }
}

