package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 项目采购策略表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_hotel_tend_strategy")
public class ProjectHotelTendStrategyEntity extends BaseVO {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 项目 ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店是否需支持 VCC 公司统一支付: 1-是 0-否
     */
    @TableField("support_vcc_pay")
    private Integer supportVccPay;

    /**
     * 酒店是否须支持员工到店付款: 1-是 0-否
     */
    @TableField("support_pay_at_hotel")
    private Integer supportPayAtHotel;

    /**
     * 酒店是否须支持提供入住明细信息: 1-是 0-否
     */
    @TableField("support_checkin_info")
    private Integer supportCheckinInfo;

    /**
     * 酒店是否须支持到店付免担保: 1-是 0-否
     */
    @TableField("support_no_guarantee")
    private Integer supportNoGuarantee;

    /**
     * 酒店是否须支持提前离店按实际入住金额收款: 1-是 0-否
     */
    @TableField("support_pay_early_checkout")
    private Integer supportPayEarlyCheckout;

    /**
     * 报价是否需要包括税费和服务费: 1-是 0-否
     */
    @TableField("support_include_tax_service")
    private Integer supportIncludeTaxService;

    /**
     * 酒店房间是否需提供免费 WIFI 服务: 1-是 0-否
     */
    @TableField("support_wifi")
    private Integer supportWifi;

    /**
     * 酒店是否全部报价金额限制范围内报价：1-是 0-否
     */
    @TableField("support_price_limit")
    private Integer supportPriceLimit;

    /**
     * 限制最低报价，有报价限制时必须有值
     */
    @TableField("limit_min_price")
    private BigDecimal limitMinPrice;

    /**
     * 限制最高报价，有报价限制时必须有值
     */
    @TableField("limit_max_price")
    private BigDecimal limitMaxPrice;

    /**
     * 报价币种
     */
    @TableField("limit_price_currency_code")
    private String limitPriceCurrencyCode;

    /**
     * 酒店全部报价中是否支持N天M点前免费取消: 1-是 0-否
     */
    @TableField("support_cancel")
    private Integer supportCancel;

    /**
     * 免费取消限制天数
     */
    @TableField("support_cancel_day")
    private Integer supportCancelDay;

    /**
     * 免费取消限制时间
     */
    @TableField("support_cancel_time")
    private String supportCancelTime;

    /**
     * 酒店投标是否支持lra的报价: 1-是 0-否
     */
    @TableField("support_lra")
    private Integer supportLra;

    /**
     * 酒店投标是否须支持报价中产品不适用日期数总和不能超过N天: 1-是 0-否
     */
    @TableField("support_max_not_applicable_day")
    private Integer supportMaxNotApplicableDay;

    /**
     * 酒店报价中产品不适用日期数总和不能超过N天
     */
    @TableField("max_not_applicable_day")
    private Integer maxNotApplicableDay;

    /**
     * 酒店是否支持房档最大数限制: 1-是 0-否
     */
    @TableField("support_max_room_type_count")
    private Integer supportMaxRoomTypeCount;

    /**
     * 酒店房档最大数限制
     */
    @TableField("max_room_type_count")
    private Integer maxRoomTypeCount;

    /**
     * 是否支持SeasonDay限制: 1-是 0-否
     */
    @TableField("support_season_day_limit")
    private Integer supportSeasonDayLimit;

    /**
     * 最大 Season 日期数
     */
    @TableField("max_season_day")
    private Integer maxSeasonDay;

    /**
     * 是否必须提供无早单人价
     */
    @TableField("is_require_no_breakfast_single")
    private Integer isRequireNoBreakfastSingle;

    /**
     * 是否必须提供无早双人价
     */
    @TableField("is_require_no_breakfast_double")
    private Integer isRequireNoBreakfastDouble;

    /**
     * 是否必须提供含早单人价
     */
    @TableField("is_require_with_breakfast_single")
    private Integer isRequireWithBreakfastSingle;

    /**
     * 是否必须提供含早双人价
     */
    @TableField("is_require_with_breakfast_double")
    private Integer isRequireWithBreakfastDouble;

    /**
     * 是否建议提供无早单人价
     */
    @TableField("is_recommend_no_breakfast_single")
    private Integer isRecommendNoBreakfastSingle;

    /**
     * 是否建议提供无早双人价
     */
    @TableField("is_recommend_no_breakfast_double")
    private Integer isRecommendNoBreakfastDouble;

    /**
     * 是否建议提供含早单人价
     */
    @TableField("is_recommend_with_breakfast_single")
    private Integer isRecommendWithBreakfastSingle;

    /**
     * 是否建议提供含早双人价
     */
    @TableField("is_recommend_with_breakfast_double")
    private Integer isRecommendWithBreakfastDouble;

    /**
     * 是否提供全周适用价格: 1-是 0-否
     */
    @TableField("is_include_all_weekly_day")
    private Integer isIncludeAllWeeklyDay;

    /**
     * 酒店报价是否必须填写税费明细
     */
    @TableField("is_require_tax_details")
    private Integer isRequireTaxDetails;

    /**
     * 酒店报价是否必须包含全部税费
     */
    @TableField("is_require_include_all_taxes")
    private  Integer isRequireIncludeAllTaxes;

    /**
     * 酒店报价是否建议包含全部税费
     */
    @TableField("is_recommend_include_all_taxes")
    private Integer isRecommendIncludeAllTaxes;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
