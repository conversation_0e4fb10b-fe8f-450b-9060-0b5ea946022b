package com.fangcang.grfp.core.cached.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fangcang.grfp.core.cached.CachedHotelGroupService;
import com.fangcang.grfp.core.cached.CachedHotelService;
import com.fangcang.grfp.core.entity.HotelEntity;
import com.fangcang.grfp.core.entity.HotelGroupEntity;
import com.fangcang.grfp.core.entity.OrgRelatedHotelEntity;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.manager.HotelManager;
import com.fangcang.grfp.core.mapper.HotelGroupMapper;
import com.fangcang.grfp.core.mapper.HotelMapper;
import com.fangcang.grfp.core.mapper.OrgRelatedHotelMapper;
import com.fangcang.grfp.core.vo.HotelOrgRelatedInfoVO;
import com.fangcang.grfp.core.vo.response.hotel.RoomNameInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CachedHotelServiceImpl implements CachedHotelService {

    @Autowired
    private HotelMapper hotelMapper;
    @Autowired
    private HotelManager hotelManager;
    @Autowired
    private OrgRelatedHotelMapper orgRelatedHotelMapper;

    @Cacheable(value="cachedHotelService.getNameMap", key = "#languageId", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public Map<Long, String> getNameMap(int languageId) {
        Map<Long, String> nameMap = new HashMap<>();
        LambdaQueryWrapper<HotelEntity>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(languageId == LanguageEnum.EN_US.key){
            lambdaQueryWrapper.select(HotelEntity::getHotelId,HotelEntity::getNameEnUs);
        }
        if(languageId == LanguageEnum.ZH_CN.key){
            lambdaQueryWrapper.select(HotelEntity::getHotelId,HotelEntity::getNameZhCn);
        }
        List<HotelEntity> hotelList = hotelMapper.selectList(lambdaQueryWrapper);
        hotelList.forEach(item -> {
            if(languageId == LanguageEnum.EN_US.key){
                nameMap.put(item.getHotelId(), item.getNameEnUs());
            }
            if(languageId == LanguageEnum.ZH_CN.key){
                nameMap.put(item.getHotelId(), item.getNameZhCn());
            }
        });
        if(nameMap.isEmpty()){
            return null;
        }
        log.info("HotelNameMap size {}, {}", LanguageEnum.getValueByKey(languageId), nameMap.size());
        return nameMap;
    }

    @Override
    @CacheEvict(value="cachedHotelService.getNameMap", key = "#languageId", cacheManager = "ehCacheCacheManager")
    public void clearNameMap(int languageId) {
        log.info("clear Hotel NameMap {}", LanguageEnum.getValueByKey(languageId));
    }

    @Override
    @Cacheable(value="cachedHotelService.getId", key = "#id", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public HotelEntity getById(long id) {
        return hotelMapper.selectById(id);
    }

    @Override
    @Cacheable(value="cachedHotelService.queryOrgRelatedInfoVO", key = "#hotelOrgId", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public HotelOrgRelatedInfoVO queryOrgRelatedInfoVO(Integer hotelOrgId) {
        HotelOrgRelatedInfoVO hotelOrgRelatedInfoVO = new HotelOrgRelatedInfoVO();
        List<OrgRelatedHotelEntity> orgRelatedHotelList = orgRelatedHotelMapper.queryOrgRelatedHotelList(hotelOrgId);
        if(CollectionUtil.isNotEmpty(orgRelatedHotelList)){
            hotelOrgRelatedInfoVO.setHotelIdList(orgRelatedHotelList.stream().map(OrgRelatedHotelEntity::getHotelId).collect(Collectors.toList()));
        } else {
            hotelOrgRelatedInfoVO.setHotelIdList(new ArrayList<>());
        }
        return hotelOrgRelatedInfoVO;
    }

    @Override
    @Cacheable(value="cachedHotelService.queryRoomNameListByHotelIds", key = "#languageId + '_' + #hotelIds", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public List<RoomNameInfoVO> queryRoomNameListByHotelIds(int languageId, Collection<Long> hotelIds) {
       return hotelManager.queryRoomNameListByHotelIds(languageId, hotelIds);
    }

}
