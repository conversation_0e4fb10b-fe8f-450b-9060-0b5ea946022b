package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 报价操作日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_bid_operate_log")
public class BidOperateLogEntity extends BaseVO {


    /**
     * 报价操作日志ID
     */
    @TableId(value = "bid_operate_log_id", type = IdType.AUTO)
    private Long bidOperateLogId;

    /**
     * 项目酒店意向ID
     */
    @TableField("project_intent_hotel_id")
    private Integer projectIntentHotelId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 机构类型ID
     */
    @TableField("org_type_id")
    private Integer orgTypeId;

    /**
     * 操作内容
     */
    @TableField("operate_content")
    private String operateContent;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;


}
