package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("添加项目 POI 请求")
@Getter
@Setter
public class AddProjectPoiRequest extends BaseVO {

    private static final long serialVersionUID = -6383734032124001988L;

    @ApiModelProperty(value = "项目 ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "POI ID 列表", required = true)
    @NotEmpty
    private List<Long> poiIds;

}
