package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 州/省/邦
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_province")
public class ProvinceEntity extends BaseVO {

    /**
     * 国家编号
     */
    @TableId(value = "country_code", type = IdType.ASSIGN_ID)
    private String countryCode;

    /**
     * 省份编号
     */
    @TableField("province_code")
    private String provinceCode;

    /**
     * 英文名称
     */
    @TableField("name_en_us")
    private String nameEnUs;

    /**
     * 中文名称
     */
    @TableField("name_zh_cn")
    private String nameZhCn;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
