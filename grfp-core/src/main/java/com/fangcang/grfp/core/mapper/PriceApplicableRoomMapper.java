package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.PriceApplicableRoomEntity;
import com.fangcang.grfp.core.vo.BidApplicableRoomVO;
import com.fangcang.grfp.core.vo.PriceApplicableRoomVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 价格可用房型 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface PriceApplicableRoomMapper extends BaseMapper<PriceApplicableRoomEntity> {

    List<BidApplicableRoomVO> selectBidApplicableRoom(int projectIntentHotelId);

    default List<PriceApplicableRoomEntity> selectPriceApplicableRoomList(int hotelPriceLevelId){
        LambdaQueryWrapper<PriceApplicableRoomEntity>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PriceApplicableRoomEntity::getHotelPriceLevelId, hotelPriceLevelId);
        return selectList(lambdaQueryWrapper);
    }

    default List<PriceApplicableRoomEntity> selectPriceApplicableRoomList(List<Integer> hotelPriceLevelIds) {
        LambdaQueryWrapper<PriceApplicableRoomEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(PriceApplicableRoomEntity::getHotelPriceLevelId, hotelPriceLevelIds);
        lambdaQueryWrapper.orderByAsc(PriceApplicableRoomEntity::getDisplayOrder);
        return selectList(lambdaQueryWrapper);
    }

    default List<PriceApplicableRoomEntity> selectByProjectIntentHotelIds(List<Integer> projectIntentHotelIds) {
        LambdaQueryWrapper<PriceApplicableRoomEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(PriceApplicableRoomEntity::getProjectIntentHotelId, projectIntentHotelIds);
        return selectList(lambdaQueryWrapper);
    }

    default int deleteByHotelPriceLevelId(int hotelPriceLevelId){
        LambdaQueryWrapper<PriceApplicableRoomEntity>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PriceApplicableRoomEntity::getHotelPriceLevelId, hotelPriceLevelId);
        return delete(lambdaQueryWrapper);
    }

    default int deleteByPriceApplicableRoomIds(Collection<Integer> priceApplicableRoomIds){
        LambdaQueryWrapper<PriceApplicableRoomEntity>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(PriceApplicableRoomEntity::getPriceApplicableRoomId, priceApplicableRoomIds);
        return delete(lambdaQueryWrapper);
    }

    /**
     * 批量新增或更新
     */
    int batchInsertOrUpdate(@Param("list") List<PriceApplicableRoomEntity> list);


    /**
     * 根据项目 id 查询
     */
    List<PriceApplicableRoomVO> queryPriceApplicableRoomInfoByProjectId(Integer projectId);

    /**
     * 根据项目 id 和酒店 id 删除
     */
    default void deleteByProjectIdAndHotelId(Integer projectId, Long hotelId) {
        LambdaQueryWrapper<PriceApplicableRoomEntity> queryWrapper = Wrappers.lambdaQuery(PriceApplicableRoomEntity.class);
        queryWrapper.eq(PriceApplicableRoomEntity::getProjectId, projectId);
        queryWrapper.eq(PriceApplicableRoomEntity::getHotelId, hotelId);
        delete(queryWrapper);
    }

    /**
     * 批量写入
     */
    void batchInsert(@Param("list") List<PriceApplicableRoomEntity> entities);
}
