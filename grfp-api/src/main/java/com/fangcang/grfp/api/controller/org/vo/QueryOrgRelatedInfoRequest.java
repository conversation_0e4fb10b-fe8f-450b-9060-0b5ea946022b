package com.fangcang.grfp.api.controller.org.vo;

import com.fangcang.grfp.core.base.PageQuery;
import com.fangcang.grfp.core.constant.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
@ApiModel("机构关联信息查询")
@Getter
@Setter
public class QueryOrgRelatedInfoRequest extends PageQuery {

    @ApiModelProperty(value = "机构ID")
    @NotNull
    private Integer orgId;

    @ApiModelProperty(value = "酒店集团ID")
    private Long hotelGroupId;

    @ApiModelProperty(value = "酒店机构ID")
    private Long hotelBrandId;

}
