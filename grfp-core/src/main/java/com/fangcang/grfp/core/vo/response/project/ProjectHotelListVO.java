package com.fangcang.grfp.core.vo.response.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(description = "项目酒店列表项")
public class ProjectHotelListVO {

    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;

    @ApiModelProperty(value = "项目意向酒店ID")
    private Long projectIntentHotelId;

    @JsonIgnore
    private String cityCode;
    @JsonIgnore
    private BigDecimal latGoogle;
    @JsonIgnore
    private BigDecimal lngGoogle;

    @JsonIgnore
    private BigDecimal latBaidu;
    @JsonIgnore
    private BigDecimal lngBaidu;

    @ApiModelProperty(value = "酒店名称")
    private String hotelName;

    @ApiModelProperty(value = "酒店图片地址")
    private String hotelImageUrl;

    @ApiModelProperty(value = "酒店地址")
    private String hotelAddress;

    @ApiModelProperty(value = "开业日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date openingDate;

    @ApiModelProperty(value = "装修日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date fitmentDate;

    @ApiModelProperty(value = "客房总数")
    private Integer roomNum;

    @ApiModelProperty(value = "企业跟进人")
    private String distributorContactName;

    @ApiModelProperty(value = "平台跟进人")
    private String platformContactName;

    @ApiModelProperty(value = "酒店星级")
    private String hotelStar;
    @ApiModelProperty(value = "酒店星级描述")
    private String hotelStarDesc;

    @ApiModelProperty(value = "SSS级")
    private String sss;

    @ApiModelProperty(value = "评分")
    private BigDecimal ratingScore;

    @ApiModelProperty(value = "酒店特色")
    private List<String> hotelFeatures;

    @ApiModelProperty(value = "投标状态")
    private Integer bidState;
    @ApiModelProperty(value = "投标状态描述")
    private String bidStateDesc;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "酒店品牌名称")
    private String hotelBrandName;

    @ApiModelProperty(value = "酒店集团名称")
    private String hotelGroupName;

    @ApiModelProperty(value = "POI名称")
    private String poiName;

    @ApiModelProperty(value = "距离POI")
    private String distanceToPoi;

    @ApiModelProperty(value = "参考价")
    private BigDecimal referencePrice;

    @ApiModelProperty(value = "近1年我的采购（间夜）")
    private Integer lastYearRoomNight;

    @ApiModelProperty(value = "近1年我的采购均价")
    private BigDecimal myPurchaseLastYearAvgPrice;

    @ApiModelProperty(value = "最低产品签约价")
    private BigDecimal lowestContractPrice;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty(value = "项目状态 0：未启动，1：招标中(已启动)，2：招标完成，3：已废标")
    private Integer projectStatus;

    @ApiModelProperty(value = "项目状态描述")
    private String projectStatusDesc;

    @ApiModelProperty(value = "通知状态 1:已经通知, 0:待通知，null:不通知")
    private Integer notifyStatus;
    
    @ApiModelProperty(value = "通知状态描述")
    private String notifyStatusDesc;

    @ApiModelProperty(value = "通知人")
    private String notifyOperator;

    @ApiModelProperty(value = "通知时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date notifyTime;

    @ApiModelProperty(value = "报价机构类型 2:酒店，4:酒店集团")
    private Integer bidOrgType;

    @ApiModelProperty(value = "报价机构类型描述")
    private String bidOrgTypeDesc;

    @ApiModelProperty(value = "报价机构名称")
    private String bidOrgName;

    @ApiModelProperty(value = "平台报价跟进人姓名")
    private String hotelPriceFollowName;

    @ApiModelProperty(value = "备注，继续议价内容")
    private String remark;
} 