<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.OrgRelatedHotelBrandMapper">

    <select id="queryPageList" resultType="com.fangcang.grfp.core.vo.OrgRelatedHotelBrandVO">
            SELECT
                org_related_hotel_brand_id AS orgRelatedHotelBrandId,
                org_id AS orgId,
                hotel_group_id AS hotelGroupId,
                hotel_brand_id AS hotelBrandId
            FROM t_org_related_hotel_brand
            WHERE org_id = #{orgId}
            <if test="hotelGroupId != null">
              AND  hotel_group_id = #{hotelGroupId}
            </if>
            <if test="hotelBrandId != null">
                AND hotel_brand_id = #{hotelBrandId}
            </if>
    </select>

    <select id="queryHotelGroupBrandIds" resultType="java.lang.Long">
        SELECT
            hotel_brand_id
        FROM t_org_related_hotel_brand
        WHERE ORG_ID IN
        <foreach collection="orgIds" item="orgId" close=")" open="(" separator=",">
            #{orgId}
        </foreach>
    </select>
</mapper>
