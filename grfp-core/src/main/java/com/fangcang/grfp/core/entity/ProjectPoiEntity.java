package com.fangcang.grfp.core.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 项目 POI 表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_poi")
public class ProjectPoiEntity extends BaseVO {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 项目 ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * POI ID
     */
    @TableField("poi_id")
    private Long poiId;

    /**
     * 3公里范围总间夜数
     */
    @TableField("total_night_room_count")
    private Integer totalNightRoomCount;

    /**
     * 3公里范围总间夜数
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 3公里城市间夜数占比
     */
    @TableField("city_night_room_risk")
    private BigDecimal cityNightRoomRisk;

    /**
     * 5公里范围总间夜数
     */
    @TableField("total_night_room_count_5km")
    private Integer totalNightRoomCount5km;

    /**
     * 5公里范围总间夜数
     */
    @TableField("total_amount_5km")
    private BigDecimal totalAmount5km;

    /**
     * 5公里城市间夜数占比
     */
    @TableField("city_night_room_risk_5km")
    private BigDecimal cityNightRoomRisk5km;

    /**
     * 10公里范围总间夜数
     */
    @TableField("total_night_room_count_10km")
    private Integer totalNightRoomCount10km;

    /**
     * 10公里范围总间夜数
     */
    @TableField("total_amount_10km")
    private BigDecimal totalAmount10km;

    /**
     * 10公里城市间夜数占比
     */
    @TableField("city_night_room_risk_10km")
    private BigDecimal cityNightRoomRisk10km;

    /**
     * POI周边3公里酒店统计
     */
    @TableField("poi_hotel_stat_3km")
    private String poiHotelStat3km;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
