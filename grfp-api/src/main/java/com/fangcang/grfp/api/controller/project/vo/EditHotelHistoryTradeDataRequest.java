package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Getter
@Setter
@ApiModel(description = "删除项目酒店历史交易数据请求")
public class EditHotelHistoryTradeDataRequest extends BaseVO {

    @ApiModelProperty(value = "项目 ID", required = true)
    @NotNull
    private Integer projectId;

    @ApiModelProperty(value = "酒店 ID", required = true)
    @NotNull
    private Long hotelId;

    @ApiModelProperty(value = "成交间夜数", required = true)
    @NotNull
    private Integer roomNightCount;;

    @ApiModelProperty(value = "成交金额", required = true)
    @NotNull
    private BigDecimal totalAmount;
}
