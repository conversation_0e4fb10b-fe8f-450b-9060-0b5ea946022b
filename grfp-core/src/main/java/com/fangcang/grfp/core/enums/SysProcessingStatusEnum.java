package com.fangcang.grfp.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/12/28 17:19
 * 导入导出系统处理状态枚举
 */
@Getter
@AllArgsConstructor
public enum SysProcessingStatusEnum {


    /**
     * 处理中
     */
    INITIALIZE(0, "初始化"),

    /**
     * 处理中
     */
    PROCESSING(1, "处理中"),

    /**
     * 成功
     */
    SUCCESS(2, "成功"),

    /**
     * 失败
     */
    FAIL(3, "失败");

    /**
     * 代码
     */
    private final int code;

    /**
     * 描述
     */
    private final String desc;


}
