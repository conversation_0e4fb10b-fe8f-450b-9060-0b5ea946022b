package com.fangcang.grfp.core.vo.response.hotelgroup;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("酒店集团报价统计数据")
public class QueryBidStatCountVO extends BaseVO {

    // 意向单店数量
    @ApiModelProperty("意向单店数量")
    private int inviteHotelCount;

    // 未报价
    @ApiModelProperty("未报价")
    private int noBidCount;


    // 待审核数量
    @ApiModelProperty("待审核")
    private int waitingApproveCount;

    // 审核驳回
    @ApiModelProperty("审核驳回")
    private int rejectedApproveCount;


    // 已经报价数量
    @ApiModelProperty("已经报价数量")
    private int newBidCount;

    // 议价中数量
    @ApiModelProperty("议价中数量")
    private int underNegotiationCount;

    // 修订报价数量
    @ApiModelProperty("修订报价数量")
    private int updatedBidCount;

    // 拒绝议价
    @ApiModelProperty("拒绝议价")
    private int rejectNegotiationCount;

    // 已中签
    @ApiModelProperty("已中签")
    private int bidWinningCount;

    // 已否决
    @ApiModelProperty("已否决")
    private int rejectCount;

    // 放弃报价
    @ApiModelProperty("放弃报价")
    private int withdrawTheQuotationCount;


}
