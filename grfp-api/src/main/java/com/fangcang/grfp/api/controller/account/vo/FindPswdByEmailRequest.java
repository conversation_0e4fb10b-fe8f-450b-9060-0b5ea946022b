package com.fangcang.grfp.api.controller.account.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

@ApiModel("电邮找回密码")
@Getter
@Setter
public class FindPswdByEmailRequest extends BaseVO {

    @ApiModelProperty(value="电邮", required = true)
    @NotBlank
    @Email
    private String email;

    @ApiModelProperty(value="验证码", required = true)
    @Size (max = 6, min = 6)
    private String verifyCode;

    @ApiModelProperty(value="密码", required = true)
    @NotBlank
    private String newPassword;

}
