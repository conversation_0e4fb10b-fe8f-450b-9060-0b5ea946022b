package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.DisHotelDailyOrderEntity;
import com.fangcang.grfp.core.vo.HotelRoomNightCountDto;
import com.fangcang.grfp.core.vo.HotelSavedAmountDto;
import com.fangcang.grfp.core.vo.QueryRoomNightsDto;
import com.fangcang.grfp.core.vo.request.hotel.DisHotelDailyOrderRequest;
import com.fangcang.grfp.core.vo.response.hotel.DisHotelDailyOrderResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 分销商酒店每日订单统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
public interface DisHotelDailyOrderMapper extends BaseMapper<DisHotelDailyOrderEntity> {

    List<DisHotelDailyOrderResponse> selectHotelSavedAmountStatList(@Param("req") DisHotelDailyOrderRequest disHotelDailyOrderRequest);

    List<HotelRoomNightCountDto> selectHotelRoomNights(QueryRoomNightsDto queryRoomNightsDto);

    List<HotelSavedAmountDto> selectHotelSavedAmountGroupByOrg(DisHotelDailyOrderRequest disHotelDailyOrderRequest);

    List<HotelSavedAmountDto> selectHotelSavedAmount(DisHotelDailyOrderRequest disHotelDailyOrderRequest);
}
