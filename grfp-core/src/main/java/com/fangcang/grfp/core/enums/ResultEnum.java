package com.fangcang.grfp.core.enums;

public enum ResultEnum {

    NORESULT(0, "未处理"),
    SUCCESS(1, "成功"),
    FAILURE(2, "失败");

    public int key;
    public String value;

    private ResultEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static int getKeyByValue(String value) {
        int key = 0;

        for(ResultEnum modifyApplyResultEnum : values()) {
            if (modifyApplyResultEnum.value.equals(value)) {
                key = modifyApplyResultEnum.key;
                break;
            }
        }

        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;

        for(ResultEnum modifyApplyResultEnum : values()) {
            if (modifyApplyResultEnum.key == key) {
                value = modifyApplyResultEnum.value;
                break;
            }
        }

        return value;
    }

    public static ResultEnum getEnumByKey(int key) {
        ResultEnum modifyApplyResultEnum = null;

        for(ResultEnum modifyApplyResult : values()) {
            if (modifyApplyResult.key == key) {
                modifyApplyResultEnum = modifyApplyResult;
                break;
            }
        }

        return modifyApplyResultEnum;
    }

}
