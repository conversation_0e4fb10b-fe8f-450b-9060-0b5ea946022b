package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * 修改锁定价格组报价
 */
@ApiModel("锁定报价")
@Getter
@Setter
public class UpdatePriceGroupLockedRequest extends BaseVO {

    /**价格组ID */
    @ApiModelProperty("价格组ID")
    @NotNull
    private Integer hotelPriceGroupId;

    //是否锁定 1 锁定 0/null  未锁定
    @ApiModelProperty("是否锁定 1 锁定 0/null  未锁定")
    @NotNull
    private Integer isLocked;

}
