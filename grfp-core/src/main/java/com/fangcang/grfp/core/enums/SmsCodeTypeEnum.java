package com.fangcang.grfp.core.enums;

/**
 * <AUTHOR>
 * @ClassName SmsCodeTypeEnum
 * @Description 短信验证码类型 1-注册2-登录3-修改密码
 * @createTime 2022-09-01 10:34:43
 * @Param
 * @return
 */
public enum SmsCodeTypeEnum {

    REGISTER(1, "注册"), LOGIN(2, "登录"), MODIFY_PWD(3, "修改密码");
    public Integer key;
    public String value;

    SmsCodeTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static Integer getKeyByValue(String value) {
        Integer key = 0;
        for (SmsCodeTypeEnum smsCodeTypeEnum : SmsCodeTypeEnum.values()) {
            if (smsCodeTypeEnum.value.equals(value)) {
                key = smsCodeTypeEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (SmsCodeTypeEnum smsCodeTypeEnum : SmsCodeTypeEnum.values()) {
            if (smsCodeTypeEnum.key.equals(key)) {
                value = smsCodeTypeEnum.value;
                break;
            }
        }
        return value;
    }

    public static SmsCodeTypeEnum getEnumByKey(Integer key) {
        SmsCodeTypeEnum smsCodeTypeEnum = null;
        for (SmsCodeTypeEnum smsCodeType : SmsCodeTypeEnum.values()) {
            if (smsCodeType.key.equals(key)) {
                smsCodeTypeEnum = smsCodeType;
                break;
            }
        }
        return smsCodeTypeEnum;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
