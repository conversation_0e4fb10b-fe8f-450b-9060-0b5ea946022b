alter table grfp.t_price_applicable_room
    drop primary key,
    add primary key (price_applicable_room_id),
    drop index price_applicable_room_id,
    add unique index udx_room_type_id (project_intent_hotel_id, hotel_id, room_type_id);

alter table grfp.t_price_applicable_room
    modify room_type_id bigint null comment '房型ID',
    add column custom_room_type_name varchar(128) null comment '自定义房型名称',
    add unique index udx_custom_room_type_name (project_intent_hotel_id, hotel_id, custom_room_type_name);
