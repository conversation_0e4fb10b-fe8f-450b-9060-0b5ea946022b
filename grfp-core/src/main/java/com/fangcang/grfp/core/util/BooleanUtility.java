package com.fangcang.grfp.core.util;

public class BooleanUtility {
	
	// ---------------------------------------------------------------------------------------------------- Public Static Constant
	
	public static final int TRUE_INT = 1;
	
	public static final int FALSE_INT = 0;
	
	public static final String TRUE_STRING = String.valueOf(TRUE_INT);
	
	public static final String FALSE_STRING = String.valueOf(FALSE_INT);
	
	// ---------------------------------------------------------------------------------------------------- Public Static Method
	
	public static boolean intToBool(int value) {
		if (value == BooleanUtility.TRUE_INT) {
			return true;
		} else {
			return false;
		}
	}
	
	public static int boolToInt(boolean bool) {
		if (bool) {
			return BooleanUtility.TRUE_INT;
		} else {
			return BooleanUtility.FALSE_INT;
		}
	}
	
	public static String intToYN(Integer value) {
		if (value == null) {
			return "N";
		}
		return BooleanUtility.TRUE_INT == value ? "Y" : "N";
	}
	
	// ----------------------------------------------------------------------------------------------------
	
	public static boolean validateIntValue(int value) {
		if ((value == BooleanUtility.TRUE_INT) || (value == BooleanUtility.FALSE_INT)) {
			return true;
		} else {
			return false;
		}
	}
	
	public static boolean validateIntString(String text) {
		boolean result = false;
		int value = -1;
		
		try {
			value = Integer.parseInt(text);
			result = BooleanUtility.validateIntValue(value);
		} catch (NumberFormatException ex) {
			result = false;
		}
		
		return result;		
	}

}
