package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fangcang.grfp.core.entity.ImportBidTaskEntity;
import com.fangcang.grfp.core.vo.ImportBidTaskListVO;
import com.fangcang.grfp.core.vo.request.bid.ImportBidTaskListRequest;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 导入报价任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface ImportBidTaskMapper extends BaseMapper<ImportBidTaskEntity> {

    /**
     * 查询项目未生成报价任务
     */
    default List<ImportBidTaskEntity> selectByProjectIdAndGenerateBidStatus(Integer projectId, Integer generateBidStatus) {
        LambdaQueryWrapper<ImportBidTaskEntity> queryWrapper = Wrappers.lambdaQuery(ImportBidTaskEntity.class);
        queryWrapper.eq(ImportBidTaskEntity::getProjectId, projectId);
        queryWrapper.eq(ImportBidTaskEntity::getGenerateBidStatus, generateBidStatus);
        return selectList(queryWrapper);
    }

    /**
     * 更新生成报价状态
     */
    default void updateGenerateBidStatus(Integer generateBidStatus, String modifier, Collection<Long> ids) {
        LambdaUpdateWrapper<ImportBidTaskEntity> updateWrapper = Wrappers.lambdaUpdate(ImportBidTaskEntity.class);
        updateWrapper.in(ImportBidTaskEntity::getId, ids);
        updateWrapper.set(ImportBidTaskEntity::getGenerateBidStatus, generateBidStatus);
        updateWrapper.set(ImportBidTaskEntity::getModifier, modifier);
        update(null, updateWrapper);
    }

    /**
     * 根据条件查询
     */
    IPage<ImportBidTaskListVO> selectByCondition(@Param("page") IPage<ImportBidTaskListVO> page, @Param("request") ImportBidTaskListRequest request);

    /**
     * 根据条件查询数据详情
     */
    List<ImportBidTaskEntity> selectDataDetailByCondition(@Param("request") ImportBidTaskListRequest request);


}
