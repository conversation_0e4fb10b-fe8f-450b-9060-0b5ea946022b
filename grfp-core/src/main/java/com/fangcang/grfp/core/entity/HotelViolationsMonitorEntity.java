package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 酒店违规监控表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_hotel_violations_monitor")
public class HotelViolationsMonitorEntity extends BaseVO {


    /**
     * 违规监控id
     */
    @TableId(value = "violations_monitor_id", type = IdType.ASSIGN_ID)
    private Long violationsMonitorId;

    /**
     * 酒店id
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 项目id
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 违规项
     */
    @TableField("violation_item")
    private String violationItem;

    /**
     * 违规类型 1：报价监控；2：订单监控
     */
    @TableField("violation_type")
    private Integer violationType;

    /**
     * 扣除分数
     */
    @TableField("penalty_score")
    private BigDecimal penaltyScore;

    /**
     * 发送时间
     */
    @TableField("send_time")
    private Date sendTime;

    /**
     * 酒店回复信息
     */
    @TableField("hotel_reply_message")
    private String hotelReplyMessage;

    /**
     * 酒店回复时间
     */
    @TableField("hotel_reply_date")
    private Date hotelReplyDate;

    /**
     * 处理状态 0待解决；1已解决
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 处理信息
     */
    @TableField("processing_info")
    private String processingInfo;

    /**
     * 处理时间
     */
    @TableField("processing_date")
    private Date processingDate;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 间夜数量
     */
    @TableField("procurement_volume")
    private Integer procurementVolume;

    /**
     * 提醒等级 0：系统提醒；1：重点提醒；2：加力跟进；3：企业跟进
     */
    @TableField("reminder_level")
    private String reminderLevel;

    /**
     * 酒店服务分
     */
    @TableField("hotel_service_points")
    private BigDecimal hotelServicePoints;

    /**
     * 处理人
     */
    @TableField("`handler`")
    private String handler;

    /**
     * 酒店回复人
     */
    @TableField("hotel_reply")
    private String hotelReply;

    /**
     * 订单监控时间范围
     */
    @TableField("order_monitor_time")
    private String orderMonitorTime;

    /**
     * 发送状态 0未发送:1已发送
     */
    @TableField("send_status")
    private Integer sendStatus;

    /**
     * 满房扣分
     */
    @TableField("full_penalty_score")
    private Integer fullPenaltyScore;

    /**
     * 涨价扣分
     */
    @TableField("up_penalty_score")
    private Integer upPenaltyScore;

    /**
     * 是否补偿
     */
    @TableField("is_payback_score")
    private Integer isPaybackScore;

    /**
     * 补偿分数
     */
    @TableField("payback_score")
    private BigDecimal paybackScore;

    /**
     * 发送补偿电邮短信状态（0:不需要发送，1：初始化, 2: 发送中，3: 发送完成）
     */
    @TableField("send_payback_status")
    private Integer sendPaybackStatus;

    /**
     * 违规id
     */
    @TableField("violations_id")
    private Integer violationsId;

    /**
     * 违规房型id
     */
    @TableField("room_type_id")
    private Integer roomTypeId;

    /**
     * 违规早餐类型
     */
    @TableField("breakfast_num")
    private Integer breakfastNum;

    /**
     * 总违规天数
     */
    @TableField("total_violation_day_count")
    private Integer totalViolationDayCount;

    /**
     * 恢复原价天数
     */
    @TableField("restore_price_day_count")
    private Integer restorePriceDayCount;

    /**
     * 同档天数
     */
    @TableField("the_same_level_day_count")
    private Integer theSameLevelDayCount;

    /**
     * 关闭c端房态天数
     */
    @TableField("client_room_closed_day_count")
    private Integer clientRoomClosedDayCount;


}
