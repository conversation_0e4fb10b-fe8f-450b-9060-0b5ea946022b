package com.fangcang.grfp.core.enums;

/**
 * <AUTHOR>
 * @date 2022/8/26 11:24
 */
public enum ReturnResultEnum {

    SUCCESS(1, "SUCCESS", "成功"),
    FAILED(300, "FAILED", "失败"),
    PARAMETER_EXCEPTION(400, "PARAMETER_EXCEPTION", "参数异常"),
    PERMISSION_DENIED(401, "PERMISSION_DENIED", "该用户没有权限"),
    RESOURCE_NOT_FOUND(404, "RESOURCE_NOT_FOUND", "访问资源不存在"),
    BUSINESS_EXCEPTION(500, "BUSINESS_EXCEPTION", "业务异常"),

    /***********系统异常类错误代码 以9开头***********/
    SYSTEM_EXCEPTION(900, "SYSTEM_EXCEPTION", "系统异常"),
    OUTER_IF_EXCEPTION(901, "OUTER_IF_EXCEPTION", "外部接口调用异常"),
    UNKNOWN_EXCEPTION(902, "UNKNOWN_EXCEPTION", "未知异常"),
    CONNECT_TIME_OUT(903, "CONNECT_TIME_OUT", "连接超时"),
    READ_TIME_OUT(904, "READ_TIME_OUT", "访问超时"),
    INSERT_DATA_EXCEPTION(905, "INSERT_DATA_EXCEPTION", "插入数据异常"),
    NOT_LOGIN(906, "NOT_LOGIN", "该用户未登陆"),
    NOT_EXISTS_ORG(907, "NOT_EXISTS_ORG", "机构不存在"),
    FAIL_SIGNATURE(908, "FAIL_SIGNATURE", "token解析失败"),
    EXPIRED_SIGNATURE(909, "EXPIRED_SIGNATURE", "token过期"),
    EXCEPTION_SIGNATURE(910, "EXCEPTION_SIGNATURE", "token解析异常"),
    NOT_EXISTS_USER(911, "NOT_EXISTS_USER", "用户不存在"),
    ERROR_USER_OR_PASSWORD(912, "ERROR_USER_OR_PASSWORD", "账号或密码错误"),
    VERIFICATION_CODE(913, "VERIFICATION_CODE", "验证码错误"),
    KICKED_OUT_LOGIN(914, "KICKED_OUT_LOGIN", "您已经被踢出，请重新登录"),
    NOT_EXISTS_MOBILE(915, "NOT_EXISTS_MOBILE", "手机未注册"),
    SEND_SMS_CODE_ERROR(916, "SEND_SMS_CODE_ERROR", "发送验证码失败，请稍后重试"),
    INVALID_USER(917, "INVALID_USER", "无效用户"),
    INVALID_ORG(918, "INVALID_ORG", "无效机构"),
    AUDITING_USER(919, "AUDITING_USER", "用户审核中"),
    OTHER_USER_LOGINED(920, "OTHER_USER_LOGINED", "同一个浏览器同时只能一个账号登录，请退出前一个账号后再重试"),
    ERROR_SIGN(921, "ERROR_SIGN", "签名不正确"),
    NO_LOGIN_INFO(922,"NO_LOGIN_INFO","无登录信息"),
    INVALID_CLIENT_ORG(923, "INVALID_CLIENT_ORG", "无效外部机构，联登用户所属外部机构编码与协议通外部机构编码不一致，请联系管理员"),

    SEND_MOBILE_MESSAGE_ERROR(924,"SEND_MOBILE_MESSAGE_ERROR","发送手机短信消息失败"),

    CONVERT_EXCEL_CELL_DATA_ERROR(925,"CONVERT_EXCEL_CELL_DATA_ERROR","转换excel列数据失败"),
    
    
    /***********招投标业务异常：8开头*************/

    /***********项目简介(富文本)文件上传返回**********************/
    PROJECT_INTRODUCTION_SUCCESS(0, "SUCCESS", "成功"),
    PROJECT_INTRODUCTION_FAILED(1, "FAILED", "失败");

    public Integer errorNo;
    public String errorCode;
    public String message;

    private ReturnResultEnum(Integer errorNo, String errorCode, String message) {
        this.errorNo = errorNo;
        this.errorCode = errorCode;
        this.message = message;
    }

    public static Integer getKeyByValue(String errorCode) {
        Integer key = null;
        for (ReturnResultEnum errorCodeEnum : ReturnResultEnum.values()) {
            if (errorCodeEnum.errorCode.equals(errorCode)) {
                key = errorCodeEnum.errorNo;
                break;
            }
        }
        return key;
    }

    public static String getDescByErrorCode(String errorCode) {
        String message = "";
        for (ReturnResultEnum errorCodeEnum : ReturnResultEnum.values()) {
            if (errorCodeEnum.errorCode.equals(errorCode)) {
                message = errorCodeEnum.message;
                break;
            }
        }
        return message;
    }

    public static String getErrorCodeByKey(Integer key) {
        String errorCode = "";
        for (ReturnResultEnum errorCodeEnum : ReturnResultEnum.values()) {
            if (errorCodeEnum.errorNo.equals(key)) {
                errorCode = errorCodeEnum.errorCode;
                break;
            }
        }
        return errorCode;
    }


    public Integer getErrorNo() {
        return errorNo;
    }

    public void setErrorNo(Integer errorNo) {
        this.errorNo = errorNo;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
