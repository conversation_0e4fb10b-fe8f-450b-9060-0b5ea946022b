package com.fangcang.grfp.api.controller.common.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fangcang.grfp.api.GRfpApiApplication;
import com.fangcang.grfp.api.controller.common.vo.*;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.base.ImportErrorVO;
import com.fangcang.grfp.core.bo.RedisService;
import com.fangcang.grfp.core.cached.CachedCountryService;
import com.fangcang.grfp.core.cached.CachedSysConfigService;
import com.fangcang.grfp.core.cached.CachedTextResourceService;
import com.fangcang.grfp.core.constant.*;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.DestinationRequest;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.FindHotelListRequest;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.GetHotelSearchRequest;
import com.fangcang.grfp.core.dto.dhub.response.Response;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.Destination;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.DestinationResponse;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.FindHotelListResponse;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.GetHotelSearchResponse;
import com.fangcang.grfp.core.entity.AttachmentFileEntity;
import com.fangcang.grfp.core.entity.CountryEntity;
import com.fangcang.grfp.core.entity.CurrencyExchangeRateEntity;
import com.fangcang.grfp.core.entity.SysImportRecordEntity;
import com.fangcang.grfp.core.entity.UserEntity;
import com.fangcang.grfp.core.enums.*;
import com.fangcang.grfp.core.manager.ExcelManager;
import com.fangcang.grfp.core.manager.MailManager;
import com.fangcang.grfp.core.mapper.*;
import com.fangcang.grfp.core.oss.OssManager;
import com.fangcang.grfp.core.remote.tmchub.manager.TmcHubApiManager;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.*;
import com.fangcang.grfp.core.vo.*;
import com.fangcang.grfp.core.vo.request.common.QueryAttachmentInfoRequest;
import com.fangcang.grfp.core.vo.response.common.AttachmentInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CommonService {

    @Value("${web.session.key}")
    private String webSessionKey;

    public static int queryLimitCount = 20;

    @Autowired
    private CachedTextResourceService cachedTextResourceService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private MailManager mailManager;
    @Autowired
    private CountryMapper countryMapper;
    @Autowired
    private ProvinceMapper provinceMapper;
    @Autowired
    private OssManager ossManager;
    @Autowired
    private AttachmentFileMapper attachmentFileMapper;
    @Autowired
    private TmcHubApiManager tmcHubApiManager;
    @Resource
    private SysImportRecordMapper sysImportRecordMapper;
    @Autowired
    private HotelGroupMapper hotelGroupMapper;
    @Autowired
    private HotelBrandMapper hotelBrandMapper;
    @Autowired
    private HotelMapper hotelMapper;
    @Autowired
    private CurrencyExchangeRateMapper currencyExchangeRateMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ExcelManager excelManager;
    @Resource
    private CachedCountryService cachedCountryService;
    @Resource
    private CachedSysConfigService cachedSysConfigService;

    public TextResourcesVO getTextResources(int languageId, String lastUpdatedTime){
        if(!StringUtility.notNullAndNotEmpty(lastUpdatedTime)) {
            return cachedTextResourceService.getTextResourcesVO(languageId, TextResourceType.WEB,null);
        }
        Date localDateTime = DateUtil.offset(DateUtil.parseDateTime(lastUpdatedTime), DateField.MINUTE, -5);
        // Offset -5 min begin time
        String updateTimeFrom = DateUtil.format(localDateTime, DatePattern.NORM_DATETIME_MINUTE_PATTERN) + ":00";
        TextResourcesVO result = cachedTextResourceService.getTextResourcesVO(languageId, TextResourceType.WEB, updateTimeFrom);

        // If cached data is old then clear cache
        if(result.getLastUpdatedTime().compareTo(lastUpdatedTime) < 0) {
            cachedTextResourceService.clearTextResourcesVO(languageId, TextResourceType.WEB, updateTimeFrom);
            result = cachedTextResourceService.getTextResourcesVO(languageId, TextResourceType.WEB, updateTimeFrom);
        }

        return result;
    }

    public AppInfoVO getAppInfo(HttpServletRequest request) {
        AppInfoVO appInfo = new AppInfoVO();
        appInfo.setVersion(GRfpApiApplication.VERSION);
        appInfo.setTextUpdatedTime(cachedTextResourceService.getLatestUpdatedTime(TextResourceType.WEB));
        appInfo.setTextResources(cachedTextResourceService.getTextResourcesVO(AppUtility.getRequestHeaderLanguage(request), TextResourceType.WEB, null).getTextResources());
        appInfo.setNowDateTime(DateUtil.formatDateTime(new Date()));
        appInfo.setSmsFrequency(Integer.parseInt(AppUtility.getSysInfo(SysConfig.MAIL_SEND_SMS_FREQUENCY_IN_SECONDS)));
        appInfo.setAreaCodes(Arrays.asList(AppUtility.getSysInfo(SysConfig.AREA_CODES).split(",")));
        return appInfo;
    }

    @Cacheable(value="commonService.queryCountryByName", key = "#languageId + '_' + #name", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public List<CountryNameVO> queryCountryByName(int languageId,  String name){
        List<CountryNameVO> result = countryMapper.selectCountryNameList(languageId, name.trim(), queryLimitCount);
        result.forEach(item -> {
            item.setName(AppUtility.getName(languageId, item.getCountryCode(), item.getNameEnUs(), item.getNameZhCn()));
            item.setShortName(AppUtility.getName(languageId, item.getCountryCode(), item.getShortNameEnUs(), item.getShortNameZhCn()));
        });
        return result;
    }

    @Cacheable(value="commonService.queryLocalHotelByName", key = "#languageId + '_' + #name", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public List<HotelNameVO> queryLocalHotelByName(int languageId,  String name){
        List<HotelNameVO> result = hotelMapper.selectHotelNameList(languageId, name.trim(), queryLimitCount);
        result.forEach(item -> {
            item.setName(AppUtility.getName(languageId, item.getHotelId(), item.getNameEnUs(), item.getNameZhCn()));
            item.setCityName(AppUtility.getCityName(languageId, item.getCityCode()));
            CountryEntity countryEntity = cachedCountryService.getByCountryCode(item.getCountryCode());
            if(countryEntity != null) {
                item.setCountryName(AppUtility.getName(languageId, item.getCityCode(), countryEntity.getNameEnUs(), countryEntity.getNameZhCn()));
            }
        });
        return result;
    }

    @Cacheable(value="commonService.queryHotelGroupByName", key = "#languageId + '_' + #name", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public List<HotelGroupVO> queryHotelGroupByName(int languageId, String name){
        List<HotelGroupVO> result = hotelGroupMapper.selectHotelGroupNameList(languageId, name.trim(), queryLimitCount);
        result.forEach(item -> item.setName(AppUtility.getName(languageId, item.getHotelGroupId(), item.getNameEnUs(), item.getNameZhCn())));
        return result;
    }

    /**
     * 根据名称查询酒店品牌
     */
    @Cacheable(value="commonService.queryHotelBrandByName", key = "#languageId + '_' + #hotelGroupId + '_' + #name", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public List<HotelBrandVO> queryHotelBrandByName(int languageId, Long hotelGroupId, String name){
        List<HotelBrandVO> result = hotelBrandMapper.selectHotelBrandNameList(languageId,
                hotelGroupId,
                StringUtility.notNullAndNotEmpty(name) ? name.trim() : null, queryLimitCount);
        result.forEach(item -> item.setName(AppUtility.getName(languageId, item.getHotelBrandId(), item.getNameEnUs(), item.getNameZhCn())));
        return result;
    }


    @Cacheable(value="commonService.queryCityByName", key = "#key + '_' + #language", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public List<CityNameVO> queryCityByName(HttpServletRequest request, String key, String language){
        // 定义返回值
        List<CityNameVO> cityNameVOList = new ArrayList<>();
        // Call tmc hub api
        DestinationRequest destinationRequest = DestinationRequest.builder()
                .keyWord(key.trim())
                .dataType(2).build();
        Response<DestinationResponse> response = tmcHubApiManager.getDestination(destinationRequest, null);
        if(AppUtility.isTmcHubApiResponseSucceeded(response) && !CollectionUtils.isEmpty(response.getBussinessResponse().getDestinationDTOList())){
            response.getBussinessResponse().getDestinationDTOList().forEach(item -> {
                if(!CollectionUtils.isEmpty(item.getDestinationName())){
                    item.getDestinationName().forEach(destinationName -> {
                        CityNameVO cityNameVO = new CityNameVO();
                        cityNameVO.setCityCode(destinationName.getCityCode());
                        cityNameVO.setCityName(destinationName.getCityName());
                        cityNameVO.setProvinceCode(destinationName.getProvinceCode());
                        cityNameVO.setProvinceName(destinationName.getProvinceName());
                        cityNameVO.setCountryCode(destinationName.getCountryCode());
                        cityNameVO.setCountryName(destinationName.getCountryName());
                        cityNameVOList.add(cityNameVO);

                    });
                }
            });
        }
        return cityNameVOList;
    }


    @Cacheable(value="commonService.queryProvinceByName", key = "#languageId +'_'+ #countryCode +'_' + #name", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public List<ProvinceNameVO> queryProvinceByName(int languageId, String countryCode, String name){
        // 定义返回值
        List<ProvinceNameVO> result = provinceMapper.selectProvinceNameList(languageId, countryCode, name.trim(), queryLimitCount);
        result.forEach(item -> {
            item.setName(AppUtility.getName(languageId, item.getProvinceCode(), item.getNameEnUs(), item.getNameZhCn()));
        });
        return result;
    }

    /**
     * 根据城市与酒店名称选择酒店 调用tmc-hub接口
     */
    @Cacheable(value="commonService.queryHotelByName", key = "#cityCode + '_' + #hotelName + '_' + #language", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public List<DestinationHotelVO> queryHotelByName(HttpServletRequest request, String cityCode, String hotelName, String language){
        // 定义返回值
        List<DestinationHotelVO> destinationHotelVOList = new ArrayList<>();
        // Call tmc hub api
        DestinationRequest destinationRequest = DestinationRequest.builder()
                .cityCode(cityCode)
                .keyWord(hotelName.trim())
                .dataType(1).build();
        Response<DestinationResponse> response = tmcHubApiManager.getDestination(destinationRequest, null);
        if(AppUtility.isTmcHubApiResponseSucceeded(response) && !CollectionUtils.isEmpty(response.getBussinessResponse().getDestinationDTOList())){
            response.getBussinessResponse().getDestinationDTOList().forEach(item -> {
                if(!CollectionUtils.isEmpty(item.getDestinationName())){
                    item.getDestinationName().forEach(destinationName -> {
                        DestinationHotelVO destinationHotelVO = new DestinationHotelVO();
                        destinationHotelVO.setHotelId(Long.valueOf(destinationName.getHotelId()));
                        destinationHotelVO.setHotelName(destinationName.getName());
                        destinationHotelVO.setProvinceCode(destinationName.getProvinceCode());
                        destinationHotelVO.setProvinceName(destinationName.getProvinceName());
                        destinationHotelVO.setCityCode(destinationName.getCityCode());
                        destinationHotelVO.setCityName(destinationName.getCityName());
                        destinationHotelVO.setCountryCode(destinationName.getCountryCode());
                        destinationHotelVO.setCountryName(destinationName.getCountryName());
                        destinationHotelVO.setLatGoogle(destinationName.getLatGoogle());
                        destinationHotelVO.setLngGoogle(destinationName.getLngGoogle());
                        destinationHotelVOList.add(destinationHotelVO);
                    });
                }
            });
        }
        return destinationHotelVOList;
    }

    public void sendSMSCode(SendSmsCodeRequest sendSMSCodeRequest) {
        int type = sendSMSCodeRequest.getType();
        String email = sendSMSCodeRequest.getEmail();

        // 检查用户信息
        if (SmsCodeTypeEnum.REGISTER.key.equals(type)) {
            UserEntity user = userMapper.selectByEmail(email);
            if (user != null)
            {
                AppUtility.serviceError(ErrorCode.EMAIL_IS_REGISTERED);
            }
        } else if (SmsCodeTypeEnum.LOGIN.key.equals(type)) {
            UserEntity user = userMapper.selectByEmail(email);
            if (user == null) {
                log.info("EMAIL_NOT_REGISTERED: {} ", email);
                AppUtility.serviceError(ErrorCode.EMAIL_NOT_REGISTERED);
            }
            if (StateEnum.Effective.key != user.getState().intValue()) {
                if (StateEnum.Auditing.key == user.getState().intValue()) {
                    AppUtility.serviceError(ErrorCode.AUDITING_USER);
                }
            }
        } else if (SmsCodeTypeEnum.MODIFY_PWD.key.equals(type)) {
            UserEntity user = userMapper.selectByEmail(email);
            if (user == null) {
                AppUtility.serviceError(ErrorCode.EMAIL_NOT_REGISTERED);
            }
            if (StateEnum.Effective.key != user.getState().intValue()) {
                AppUtility.serviceError(ErrorCode.AUDITING_USER);
            }
        } else {
            AppUtility.serviceError(ErrorCode.NOT_SUPPORT_BUSSINESS_TYPE);
        }
        String key = UserUtility.getSmsCodeKey(email, type);
        String limitKey = RedisConstant.LIMIT + key;
        if (StringUtil.isValidString(redisService.get(limitKey))) {
            AppUtility.serviceError(ErrorCode.OPERATE_TOO_FREQUENCY);
        }
        String randomNumOld = redisService.get(key);
        boolean randomNumNeedRenew = !StringUtils.isNumeric(randomNumOld);//需要重新生成验证码
        int randomNum = randomNumNeedRenew ? CommonUtil.getRandom() : Integer.parseInt(randomNumOld);
        try {
            mailManager.sendSmsCodeMail(email, String.valueOf(randomNum));
        }  catch (MessagingException e) {
            log.error("发送验证码失败", e);
            AppUtility.serviceError(ErrorCode.SEND_SMS_CODE_EXCEPTION);
        }
        if (key != null) {
            if (randomNumNeedRenew) {
                redisService.setex(key, randomNum + "", 1800);
            }
            //限制60s不能重复发送
            redisService.setex(limitKey, randomNum + "", Integer.parseInt(AppUtility.getSysInfo(SysConfig.MAIL_SEND_SMS_FREQUENCY_IN_SECONDS)));
        } else {
            AppUtility.serviceError(ErrorCode.SEND_SMS_CODE_ERROR);
        }

    }

    public UploadFileResultVO uploadFile(MultipartFile uploadFile, Integer businessType, Long externalId, boolean isTemp) throws Exception {
        // 检查参数
        if(uploadFile == null){
            AppUtility.serviceError(ErrorCode.UPLOAD_FILE_CANNOT_BE_NULL);
        }
        // 判断图片大小小于50M
        if (uploadFile.getSize() > 1045876 * 50) {
            AppUtility.serviceError(ErrorCode.UPLOAD_FILE_SIZE_LIMIT);
        }
        // 文件业务类型检查
        String filePath = FileTypeAndPathEnum.getFilePathByBusinessType(businessType);
        if (!StringUtil.isValidString(filePath)) {
            AppUtility.serviceError(ErrorCode.UPLOAD_FILE_BUSSINESS_TYPE_ERROR);
        }

        // 定义值
        UserSession userSession = UserSession.get();
        String originalFilename = uploadFile.getOriginalFilename();
        InputStream inputStream = uploadFile.getInputStream();

        // 上传到临时桶/公开桶
        String fileKey = isTemp ? ossManager.putObjectTemp(filePath, inputStream, uploadFile.getContentType(), uploadFile.getOriginalFilename()) :
                    ossManager.putObjectPublic(filePath, inputStream, uploadFile.getContentType(), uploadFile.getOriginalFilename());

        AttachmentFileEntity attachmentFile = new AttachmentFileEntity();
        attachmentFile.setFileOriginalName(originalFilename);
        attachmentFile.setFileKey(fileKey);
        attachmentFile.setBusinessType(businessType);
        attachmentFile.setExternalId(externalId);
        attachmentFile.setIsActive(RfpConstant.constant_1);

        //合同需要将文件转成base64字符串，防止服务器上文件丢失，备份
        byte[] fileBase64 = null;
        if (FileTypeAndPathEnum.CONTRACT.businessType.intValue() == businessType.intValue()) {
            ByteArrayOutputStream swapStream = null;
            InputStream inputStreamInfo = null;
            try {
                inputStreamInfo = isTemp ? ossManager.getObjectTemp(fileKey) : ossManager.getObjectPublic(fileKey);
                swapStream = new ByteArrayOutputStream();
                byte[] buff = new byte[1024];
                int rc = 0;
                while ((rc = inputStreamInfo.read(buff, 0, 1024)) > 0) {
                    swapStream.write(buff, 0, rc);
                }
                fileBase64 = Base64.encodeBase64(swapStream.toByteArray());
            } catch (Exception e) {
                log.error("上传文件转换base64异常", e);
            } finally {
                if (swapStream != null) {
                    swapStream.close();
                }
                if (inputStreamInfo != null) {
                    inputStreamInfo.close();
                }
            }
        }
        attachmentFile.setFileStream(fileBase64);
        attachmentFile.setFileSize(uploadFile.getSize());
        attachmentFile.setCreator(userSession.getUsername());
        // 保存附件信息
        int insertResult = attachmentFileMapper.insert(attachmentFile);
        if(insertResult == 0){
            AppUtility.serviceError(ErrorCode.UPLOAD_FILE_FILED);
        }

        // 定义返回数据
        UploadFileResultVO uploadFileResultVO = new UploadFileResultVO();
        uploadFileResultVO.setFileUrl(isTemp ? ossManager.generateUrlTemp(fileKey) : ossManager.generateUrlPublic(fileKey));
        uploadFileResultVO.setFileKey(fileKey);
        return uploadFileResultVO;

    }

    public void deleteAttachmentFile(Long fileId) {
        UserSession userSession = UserSession.get();
        AttachmentFileEntity attachmentFileEntity = attachmentFileMapper.selectById(fileId);
        attachmentFileEntity.setAttachmentFileId(fileId);
        attachmentFileEntity.setIsActive(RfpConstant.constant_0);
        attachmentFileEntity.setModifier(userSession.getUsername());
        attachmentFileEntity.setModifyTime(new Date());
        attachmentFileMapper.updateById(attachmentFileEntity);

    }

    public String getDownlandTemplateFileUrl(String templateFileName){
        return ossManager.generateUrlPublic(FileTypeAndPathEnum.EXPORT_EXCEL_TEMPLATE.filePath +"/" + templateFileName);
    }

    public List<AttachmentInfoResponse> queryAttachmentInfo(QueryAttachmentInfoRequest queryAttachmentInfoRequest) {
        // 参数检查
        if(queryAttachmentInfoRequest.getFileId() == null){
            if(queryAttachmentInfoRequest.getExternalId() == null || queryAttachmentInfoRequest.getBusinessType() == null){
                AppUtility.serviceError(ErrorCode.REQUEST_PARAMETER_ERROR);
            }
        }
        List<AttachmentFileEntity> attachments = attachmentFileMapper.queryAttachment(queryAttachmentInfoRequest);
        List<AttachmentInfoResponse> attachmentInfoResponseList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(attachments)) {
            attachmentInfoResponseList = new ArrayList<>();
            for (AttachmentFileEntity attachment : attachments) {
                AttachmentInfoResponse attachmentInfoResponse = new AttachmentInfoResponse();
                BeanUtils.copyProperties(attachment, attachmentInfoResponse);
                if (attachment.getFileStream() != null) {
                    attachmentInfoResponse.setFileBase64String(new String(attachment.getFileStream()));
                }
                attachmentInfoResponse.setFileUrl(ossManager.generateUrlPublic(attachment.getFileKey()));
                attachmentInfoResponseList.add(attachmentInfoResponse);
            }
        }
        return attachmentInfoResponseList;
    }



    /**
     * 查询导入记录信息
     */
    public ImportRecordVO queryImportRecord(Long id, HttpServletRequest httpServletRequest) {
        SysImportRecordEntity sysImportRecordEntity = sysImportRecordMapper.selectById(id);
        if (Objects.isNull(sysImportRecordEntity)) {
            AppUtility.serviceError(ErrorCode.IMPORT_RECORD_NOT_EXIST);
        }

        // 转换结果
        ImportRecordVO recordVO = new ImportRecordVO();
        BeanUtils.copyProperties(sysImportRecordEntity, recordVO);

        // 处理失败原因
        if (sysImportRecordEntity.getStatus().equals(ImportStatusEnum.FAIL.getKey()) && StringUtils.isNotBlank(sysImportRecordEntity.getFailRemark())) {
            int languageId = GenericAppUtility.getRequestHeaderLanguage(httpServletRequest);
            ImportErrorVO importErrorVO = JsonUtil.jsonToBean(sysImportRecordEntity.getFailRemark(), ImportErrorVO.class);
            if (Objects.nonNull(importErrorVO)) {
                importErrorVO.setErrorMsg(cachedTextResourceService.getMsgValue(languageId, importErrorVO.getErrorCode()));
            }
            excelManager.fillRowErrorMessage(languageId, importErrorVO.getRowErrors());
            recordVO.setFailRemark(importErrorVO);
        }
        return recordVO;
    }

    public GetHotelSearchResponse getHotelSearchByDestination(HttpServletRequest request, GetHotelSearchByDestinationRequest getHotelSearchByDestinationRequest){
        GetHotelSearchRequest getHotelSearchRequest = GetHotelSearchRequest.builder().build();
        getHotelSearchRequest.setDestinationId(getHotelSearchByDestinationRequest.getDestinationId());
        getHotelSearchRequest.setLanguage(LanguageEnum.getValueByKey(GenericAppUtility.getRequestHeaderLanguage(request)));
        return tmcHubApiManager.getHotelSearch(getHotelSearchRequest, null).getBussinessResponse();
    }

    public DestinationResponse searchDestination(HttpServletRequest request, SearchDestinationRequest searchDestinationRequest){
        DestinationRequest destinationRequest = DestinationRequest.builder().build();
        destinationRequest.setKeyWord(searchDestinationRequest.getKeyWord());
        DestinationResponse destinationResponse = tmcHubApiManager.getDestination(destinationRequest, null).getBussinessResponse();
        if(destinationResponse != null && !CollectionUtils.isEmpty(destinationResponse.getDestinationDTOList())){
            List<Destination> hotelAndCiryDestinationDTOList = destinationResponse.getDestinationDTOList().stream().filter(destination -> destination.getDataType() == 1 || destination.getDataType() == 2).collect(Collectors.toList());
            destinationResponse.setDestinationDTOList(hotelAndCiryDestinationDTOList);
        }
        return tmcHubApiManager.getDestination(destinationRequest, null).getBussinessResponse();
    }

    public FindHotelListResponse findHotelListByDestination(HttpServletRequest request, FindHotelListByDestinationRequest findHotelListByDestinationRequest){
        FindHotelListRequest findHotelListRequest = FindHotelListRequest.builder().build();
        BeanUtils.copyProperties(findHotelListByDestinationRequest, findHotelListRequest);
        findHotelListRequest.setLanguage(LanguageEnum.getValueByKey(GenericAppUtility.getRequestHeaderLanguage(request)));
        findHotelListRequest.setCurrentPage(findHotelListByDestinationRequest.getPageIndex());
        findHotelListRequest.setPageSize(findHotelListByDestinationRequest.getPageSize());
        return tmcHubApiManager.findHotelList(findHotelListRequest, null).getBussinessResponse();
    }

    public List<CurrencyNameVO> queryCurrencyByCodeRequest(HttpServletRequest request, QueryCurrencyByCodeRequest queryCurrencyByCodeRequest){
            return currencyExchangeRateMapper.currencyNameList(queryCurrencyByCodeRequest.getCurrencyCode(), null);
    }

    /**
     * 获取枚举类型数据
     */
    public Map<String, List<EnumItemVO>> getEnumTypes(List<String> enumTypes, int languageId) {
        Map<String, List<EnumItemVO>> result = new HashMap<>();
        
        // 如果没有传递枚举类型，返回所有支持的枚举类型
        if (CollectionUtils.isEmpty(enumTypes)) {
            enumTypes = Arrays.asList(
                "HotelBidStateEnum",
                "HotelStarEnum", 
                "BidUploadSourceEnum",
                "HotelGroupApproveStatusEnum",
                "ProjectStateEnum",
                "ProjectTypeEnum",
                "RecommendLevelEnum",
                "YesOrNoEnum",
                "OrgTypeEnum",
                "HotelStarGroupEnum",
                "HotelBidNotifyStatusEnum",
                "BidOrgTypeEnum"
            );
        }
        
        for (String enumType : enumTypes) {
            List<EnumItemVO> enumItems = new ArrayList<>();
            
            switch (enumType) {
                case "HotelBidStateEnum":
                    for (HotelBidStateEnum item : HotelBidStateEnum.values()) {
                        String textCode = "BID_STATE_" + item.bidState;
                        String text = GenericAppUtility.getText(languageId, textCode);
                        enumItems.add(new EnumItemVO(item.bidState, item.name(), textCode, text));
                    }
                    break;
                    
                case "HotelStarEnum":
                    for (HotelStarEnum item : HotelStarEnum.values()) {
                        String textCode = "HOTEL_STAR_" + item.name();
                        String text = GenericAppUtility.getText(languageId, textCode);
                        enumItems.add(new EnumItemVO(item.key, item.name(), textCode, text));
                    }
                    break;

                case "BidUploadSourceEnum":
                    for (BidUploadSourceEnum item : BidUploadSourceEnum.values()) {
                        String textCode = "BID_SOURCE_" + item.name();
                        String text = GenericAppUtility.getText(languageId, textCode);
                        enumItems.add(new EnumItemVO(item.key, item.name(), textCode, text));
                    }
                    break;
                    
                case "HotelGroupApproveStatusEnum":
                    for (HotelGroupApproveStatusEnum item : HotelGroupApproveStatusEnum.values()) {
                        String textCode = "APPROVE_STATUS_" + item.name();
                        String text = GenericAppUtility.getText(languageId, textCode);
                        enumItems.add(new EnumItemVO(item.key, item.name(), textCode, text));
                    }
                    break;
                    
                case "ProjectStateEnum":
                    for (ProjectStateEnum item : ProjectStateEnum.values()) {
                        String textCode = "PROJECT_STATE_" + item.key;
                        String text = GenericAppUtility.getText(languageId, textCode);
                        enumItems.add(new EnumItemVO(item.key, item.name(), textCode, text));
                    }
                    break;
                    
                case "ProjectTypeEnum":
                    for (ProjectTypeEnum item : ProjectTypeEnum.values()) {
                        String textCode = "PROJECT_TYPE_" + item.name();
                        String text = GenericAppUtility.getText(languageId, textCode);
                        enumItems.add(new EnumItemVO(item.key, item.name(), textCode, text));
                    }
                    break;
                    
                case "RecommendLevelEnum":
                    for (RecommendLevelEnum item : RecommendLevelEnum.values()) {
                        String textCode = "RECOMMEND_LEVEL_" + item.name();
                        String text = GenericAppUtility.getText(languageId, textCode);
                        enumItems.add(new EnumItemVO(item.key, item.name(), textCode, text));
                    }
                    break;
                    
                case "YesOrNoEnum":
                    for (YesOrNoEnum item : YesOrNoEnum.values()) {
                        String textCode = "YES_NO_" + item.name();
                        String text = GenericAppUtility.getText(languageId, textCode);
                        enumItems.add(new EnumItemVO(item.getKey(), item.name(), textCode, text));
                    }
                    break;
                    
                case "OrgTypeEnum":
                    for (OrgTypeEnum item : OrgTypeEnum.values()) {
                        String textCode = "ORG_TYPE_" + item.key;
                        String text = GenericAppUtility.getText(languageId, textCode);
                        enumItems.add(new EnumItemVO(item.key, item.name(), textCode, text));
                    }
                    break;

                case "HotelStarGroupEnum":
                    for (HotelStarGroupEnum item : HotelStarGroupEnum.values()) {
                        String textCode = "HOTEL_STAR_GROUP_" + item.name();
                        String text = GenericAppUtility.getText(languageId, textCode);
                        enumItems.add(new EnumItemVO(item.key, item.name(), textCode, text));
                    }
                    break;

                case "HotelBidNotifyStatusEnum":
                    for (HotelBidNotifyStatusEnum item : HotelBidNotifyStatusEnum.values()) {
                        String textCode = "NOTIFY_STATUS_" + item.key;
                        String text = GenericAppUtility.getText(languageId, textCode);
                        enumItems.add(new EnumItemVO(item.key, item.name(), textCode, text));
                    }
                    break;

                case "BidOrgTypeEnum":
                    for (BidOrgTypeEnum item : BidOrgTypeEnum.values()) {
                        String textCode = "BID_ORG_TYPE_" + item.key;
                        String text = GenericAppUtility.getText(languageId, textCode);
                        enumItems.add(new EnumItemVO(item.key, item.name(), textCode, text));
                    }
                    break;

                case "GeneratedBidStatusEnum":
                    for (GeneratedBidStatusEnum item : GeneratedBidStatusEnum.values()) {
                        String textCode = "GENERATED_BID_STATUS_" + item.key;
                        String text = GenericAppUtility.getText(languageId, textCode);
                        enumItems.add(new EnumItemVO(item.key, item.value, textCode, text));
                    }
                    break;

                default:
                    // 如果没有匹配的枚举类型，跳过
                    continue;
            }
            
            if (!enumItems.isEmpty()) {
                result.put(enumType, enumItems);
            }
        }
        
        return result;
    }

    /**
     * 查询导出状态
     */
    public QueryExportStatusResponse queryExportStatus(@Valid QueryExportStatusRequest request) {
        // 查询状态
        String key = RedisConstant.EXPORT_DATA_REQUEST_SEQUENCE_NO + request.getRequestSequenceNo();
        String status = redissonClient.<String>getBucket(key).get();

        // 导出
        QueryExportStatusResponse response = new QueryExportStatusResponse();
        response.setStatus(Integer.parseInt(status));
        return response;
    }

    /**
     * 查询常用币种列表
     */
    public PopCurrencyVO queryPopCurrency() {
        PopCurrencyVO res = new PopCurrencyVO();

        // 查询配置的常用币种, 配置是逗号隔开的多个三字码
        String configCurrencyCodes = cachedSysConfigService.getValue(SysConfig.POP_CURRENCY);
        if (StringUtils.isEmpty(configCurrencyCodes)) {
            res.setPopCurrencyList(Collections.emptyList());
            return res;
        }

        // 查询币种信息
        List<String> currencyCodes = StrUtil.split(configCurrencyCodes, ",");
        List<CurrencyExchangeRateEntity> popCurrencyEntityList = currencyExchangeRateMapper.selectByCurrencyCodes(currencyCodes);
        if (CollUtil.isNotEmpty(popCurrencyEntityList)) {
            Map<String, String> currencyMap = popCurrencyEntityList.stream()
                .collect(Collectors.toMap(CurrencyExchangeRateEntity::getCurrencyCode, CurrencyExchangeRateEntity::getCurrencyName, (v1, v2) -> v2));
            List<CurrencyNameVO> popCurrencyList = new ArrayList<>(popCurrencyEntityList.size());
            // 按配置的顺序转换
            currencyCodes.forEach(item -> {
                CurrencyNameVO currencyNameVO = new CurrencyNameVO();
                currencyNameVO.setCurrencyCode(item);
                currencyNameVO.setCurrencyName(currencyMap.get(item));
                popCurrencyList.add(currencyNameVO);
            });
            res.setPopCurrencyList(popCurrencyList);
        }

        return res;
    }
}
