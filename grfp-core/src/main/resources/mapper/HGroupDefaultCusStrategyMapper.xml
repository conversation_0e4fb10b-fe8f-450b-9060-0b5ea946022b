<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HGroupDefaultCusStrategyMapper">

    <select id="queryHGroupDefaultCusStrategy" resultType="com.fangcang.grfp.core.vo.HGroupDefaultCusStrategyVO">
        SELECT
            `custom_tend_strategy_id`,
            `project_intent_hotel_group_id`,
            `project_id`,
            `strategy_type`,
            `strategy_name`,
            `support_strategy_name`,
            `support_strategy_text`,
            creator,
            modifier
        FROM
            t_h_group_default_cus_strategy
        WHERE
            project_intent_hotel_group_id = #{projectIntentHotelGroupId}
    </select>

    <insert id="batchMergeHotelGroupDefaultCustomStrategy" parameterType="java.util.List">
        INSERT INTO t_h_group_default_cus_strategy (
                                                    custom_tend_strategy_id,
                                                    project_intent_hotel_group_id,
                                                    project_id, strategy_name,
            support_strategy_name,strategy_type, support_strategy_text, creator, modifier)
            VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.customTendStrategyId}, #{item.projectIntentHotelGroupId},
            #{item.projectId}, #{item.strategyName,jdbcType=VARCHAR}, #{item.supportStrategyName,jdbcType=DECIMAL},#{item.strategyType,jdbcType=INTEGER},
            #{item.supportStrategyText,jdbcType=VARCHAR},
            #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
        </foreach>
        on duplicate key update
            strategy_name = values(strategy_name),
            support_strategy_name = values(support_strategy_name),
            strategy_type = values(strategy_type),
            support_strategy_text = values(support_strategy_text),
            modifier = values(modifier)
    </insert>
</mapper>
