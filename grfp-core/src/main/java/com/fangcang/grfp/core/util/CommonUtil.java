/**
 * @Title: CommonUtil.java
 * @Package com.fangcang.platform.util
 * @Description: TODO
 * <AUTHOR>
 * @date 2014-12-19 上午10:09:44
 * @version V1.0
 */
package com.fangcang.grfp.core.util;

import cn.hutool.core.util.NumberUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * @类 名:CommonUtil
 * @简单描述: 公共工具类
 * @详细描述：
 * @作 者:ya<PERSON><PERSON><PERSON>
 * @日 期：2016年11月22日下午5:24:13
 *
 */
public class CommonUtil {

    final static Class<? extends Object> SELF = CommonUtil.class;

    /**
     * 生成机构编码
     *
     * @param organization
     * @return
     */
    public static Integer orgInt = 0;    // 全局数

    private static ThreadPoolTaskExecutor logDataExecutor = null;

    public static String GenerateOrder() {
        StringBuffer sb = new StringBuffer();
        ;
        orgInt++;// 自增
        Integer countInteger = 4 - orgInt.toString().length();// 算补位
        String bu = "";
        for (int i = 0; i < countInteger; i++) {// 补字符串
            bu += "0";
        }
        bu += orgInt.toString();
        if (orgInt > 9999) {
            orgInt = 0;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmmss");// 时间戳
        String str = sdf.format(new Date());
        sb.append(str).append(bu);
        return sb.toString();
    }


    /**
     * 生成随机数字和字母组合
     * @param length
     * @return
     */
    public static String generatePassword(int length) {
        String val = "";
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            // 输出字母还是数字
            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            // 字符串
            if ("char".equalsIgnoreCase(charOrNum)) {
                // 取得大写字母还是小写字母
                int choice = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val += (char) (choice + random.nextInt(26));
            } else if ("num".equalsIgnoreCase(charOrNum)) { // 数字
                val += String.valueOf(random.nextInt(10));
            }
        }
        return val;
    }

    /**
     * @Title: separateDates
     * @Description: 根据开始日期、结束日期和适用星期得到日期列表
     * @param @param fromDate
     * @param @param toDate
     * @param @param weeks=1,2,3,4,5,6,7
     * @param @return
     * @return List<Map < String , Date>> 返回类型
     * @throws
     */
    public static List<Map<String, Date>> separateDates(Date fromDate, Date toDate, String weeks) {
        List<Map<String, Date>> resultList = new ArrayList<Map<String, Date>>();
        if (null == fromDate || null == toDate || !StringUtils.isNotEmpty(weeks)) {
            return resultList;
        }
        Map<String, Date> map = null;
        if (7 == weeks.split(",").length) {
            map = new HashMap<String, Date>();
            map.put("startDate", fromDate);
            map.put("endDate", toDate);
            resultList.add(map);
        } else {
            Date beginDate = null;
            Date endDate = null;
            Date tempDate = fromDate;
            Boolean isBegin = true;
            String week = "";

            while (tempDate.compareTo(toDate) <= 0) {
                week = "" + DateUtil.getWeekOfDate(tempDate);
                if (weeks.contains(week)) {
                    if (isBegin) {
                        isBegin = false;
                        beginDate = tempDate;
                        endDate = tempDate;
                    } else {
                        endDate = tempDate;
                    }
                } else {
                    if (!isBegin) {
                        isBegin = true;
                        map = new HashMap<String, Date>();
                        map.put("startDate", beginDate);
                        map.put("endDate", endDate);
                        resultList.add(map);

                        beginDate = null;
                    }
                }
                tempDate = DateUtil.getDate(tempDate, 1, 0);
            }
            week = "" + DateUtil.getWeekOfDate(toDate);
            if (null != beginDate && weeks.contains(week)) {
                map = new HashMap<String, Date>();
                map.put("startDate", beginDate);
                map.put("endDate", toDate);
                resultList.add(map);
            }
        }
        return resultList;
    }

    //获取6为随机数
    public static int getRandom() {
        Random r = new Random();
        return r.nextInt(899999) + 100000;
    }

    //获取8为随机数
    public static int getRandomEight() {
        Random r = new Random();
        return r.nextInt(89999999) + 10000000;
    }

    public static String getEncoding(String str) {
        String encode = "ISO-8859-1";
        try {
            if (str.equals(new String(str.getBytes(encode), encode))) {
                String s = encode;
                return s;
            }
        } catch (Exception exception) {
        }
        encode = "UTF-8";
        try {
            if (str.equals(new String(str.getBytes(encode), encode))) {
                String s2 = encode;
                return s2;
            }
        } catch (Exception exception2) {
        }
        encode = "GBK";
        try {
            if (str.equals(new String(str.getBytes(encode), encode))) {
                String s3 = encode;
                return s3;
            }
        } catch (Exception exception3) {
        }
        encode = "GB2312";
        try {
            if (str.equals(new String(str.getBytes(encode), encode))) {
                String s1 = encode;
                return s1;
            }
        } catch (Exception exception1) {
        }
        return null;
    }

    /**
     * @[简要描述]根据开始日期、结束日期和适用星期得到日期列表
     * @[详细描述]
     * @作 者:yangjunjun
     * @日 期：2017年3月30日下午8:41:12
     * @param @param fromDate
     * @param @param toDate
     * @param @param weeks=1,2,3,4,5,6,7
     * @param @return
     * @return
     * @return: List<Date>
     */
    public static List<Date> geteDates(Date fromDate, Date toDate, String weeks) {
        List<Date> dataList = new ArrayList<>();
        String week = "";
        while (fromDate.compareTo(toDate) <= 0) {
            week = "" + DateUtil.getWeekOfDate(fromDate);
            if (weeks.contains(week)) {
                dataList.add(fromDate);
            }
            fromDate = DateUtil.getDate(fromDate, 1, 0);
        }
        return dataList;
    }

    /**
     * 根据项目ID，合同业务类型，项目业务ID生成缓存key
     * @param projectId
     * @param contractBizType
     * @param projectBussinessId
     * @return
     */
    public static String getContractGeneratingKey(Long projectId, Integer contractBizType, Long projectBussinessId) {
        return projectId + "_" + contractBizType + "_" + projectBussinessId;
    }

    /**
     * 根据缓存key获取项目ID
     * @param contractGeneratingKey
     * @return
     */
    public static Long getProjectIdFromContractGeneratingKey(String contractGeneratingKey) {
        return (StringUtils.isNotBlank(contractGeneratingKey) ? Long.valueOf(contractGeneratingKey.split("_")[0]) : null);
    }

    /**
     * 根据缓存key获取合同业务类型
     * @param contractGeneratingKey
     * @return
     */
    public static Integer getContractBizTypeFromContractGeneratingKey(String contractGeneratingKey) {
        return (StringUtils.isNotBlank(contractGeneratingKey) ? Integer.valueOf(contractGeneratingKey.split("_")[1]) : null);
    }

    /**
     * 根据缓存key获取项目业务ID
     * @param contractGeneratingKey
     * @return
     */
    public static Long getProjectBussinessIdFromContractGeneratingKey(String contractGeneratingKey) {
        return (StringUtils.isNotBlank(contractGeneratingKey) ? Long.valueOf(contractGeneratingKey.split("_")[2]) : null);
    }

    //将set集合分割成多个set
    public static <T> List<Set<T>> splitSet(Set<T> set, int chunkSize) {
        List<Set<T>> result = new ArrayList<>();
        Iterator<T> iterator = set.iterator();
        while (iterator.hasNext()) {
            Set<T> chunk = new HashSet<>(chunkSize);
            for (int i = 0; i < chunkSize && iterator.hasNext(); i++) {
                chunk.add(iterator.next());
            }
            result.add(chunk);
        }
        return result;
    }

    public static String readHtmlFile(String path) throws Exception {
        StringBuffer script = new StringBuffer();
        InputStream is = null;
        BufferedReader bufferreader = null;
        try {
            is = CommonUtil.class.getClassLoader().getResourceAsStream(path);
            bufferreader = new BufferedReader(new InputStreamReader(is));
            String tempString = null;
            while ((tempString = bufferreader.readLine()) != null) {
                script.append(tempString).append("\n");
            }
        } catch (Exception e) {
            throw e;
        } finally {
            if (bufferreader != null) {
                bufferreader.close();
            }
            if (is != null) {
                is.close();
            }
        }
        return script.toString();
    }

    /**
     * 压缩字符串
     * @param str 要压缩的字符串
     * @return 压缩后的字节数组
     * @throws IOException 异常
     */
    public static byte[] compress(String str) throws IOException {
        if (str == null || str.isEmpty()) {
            return new byte[0];
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(outputStream)) {
            gzipOutputStream.write(str.getBytes("UTF-8"));
        }
        return outputStream.toByteArray();
    }

    /**
     * 解压缩字节数组为字符串
     * @param compressed 压缩的字节数组
     * @return 解压缩后的字符串
     * @throws IOException 异常
     */
    public static String decompress(byte[] compressed) throws IOException {
        if (compressed == null || compressed.length == 0) {
            return "";
        }
        try (GZIPInputStream gzipInputStream = new GZIPInputStream(new ByteArrayInputStream(compressed));
             InputStreamReader inputStreamReader = new InputStreamReader(gzipInputStream, "UTF-8");
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                output.append(line);
            }
            return output.toString();
        }
    }

    /**
     * 根据区号和手机号生成联系方式
     */
    public static String generateContactMobile(String mobileAreaCode, String mobile) {
        if(StringUtils.isEmpty(mobileAreaCode)){
            return mobile;
        }
        return mobileAreaCode + mobile;
    }

    public static String getCellValue(Cell cell){
        // 获取单元格的值
        String cellValue = null;
        if(cell == null){
            return null;
        }
        switch (cell.getCellType()) {
            case STRING :
                cellValue = cell.getStringCellValue();
                break;
            case NUMERIC:
                double numberCellValue = cell.getNumericCellValue();
                BigDecimal round0Value = NumberUtil.round(numberCellValue, 0);
                // 整数直接返回不带小数值
                if(round0Value.compareTo(BigDecimal.valueOf(numberCellValue)) == 0){
                    cellValue = round0Value.toString();
                } else {
                    // 返回保留2位小数值
                    cellValue = String.valueOf(NumberUtil.round(numberCellValue, 2));
                }
                break;
            case BOOLEAN:
                cellValue =String.valueOf(cell.getBooleanCellValue());
                break;
            default:
                cellValue =String.valueOf(cell.getStringCellValue());
                break;
            // 其他类型的单元格可以根据需要进行添加
        }
        return cellValue;
    }


    // 日期格式化
    public static String getCellValue2(Cell cell){
        // 获取单元格的值
        String cellValue = null;
        if(cell == null){
            return null;
        }
        switch (cell.getCellType()) {
            case STRING:
                cellValue = cell.getStringCellValue();
                break;
            case NUMERIC:
                if (HSSFDateUtil.isCellDateFormatted(cell)){
                    return cn.hutool.core.date.DateUtil.format(cell.getDateCellValue(), "yyyy/MM/dd")  ;
                }
                double numberCellValue = cell.getNumericCellValue();
                BigDecimal round0Value = NumberUtil.round(numberCellValue, 0);
                // 整数直接返回不带小数值
                if(round0Value.compareTo(BigDecimal.valueOf(numberCellValue)) == 0){
                    cellValue = round0Value.toString();
                } else {
                    // 返回保留2位小数值
                    cellValue = String.valueOf(NumberUtil.round(numberCellValue, 2));
                }
                break;
            case BOOLEAN:
                cellValue =String.valueOf(cell.getBooleanCellValue());
                break;
            default:
                cellValue =String.valueOf(cell.getStringCellValue());
                break;
            // 其他类型的单元格可以根据需要进行添加
        }
        return cellValue;
    }
}