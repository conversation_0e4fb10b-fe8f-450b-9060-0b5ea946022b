<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.UserMapper">

    <select id="queryUserVOPageList" resultType="com.fangcang.grfp.core.vo.ListUserVO">
        SELECT
            u.user_id AS userId,
            u.org_id AS orgId,
            u.user_name AS  userName,
            u.role_code AS roleCode,
            u.mobile_area_code AS mobileAreaCode,
            u.mobile AS  mobile,
            u.email AS  email,
            u.state AS  state,
            o.org_name AS orgName,
            o.org_type AS orgType
        FROM
            t_user u
        LEFT JOIN t_org o ON u.org_id = o.org_id
        WHERE
            1 = 1
        <if test="query.orgName != null and query.orgName != ''">
            AND o.org_name like CONCAT('%',#{query.orgName}, '%')
        </if>
        <if test="query.userName != null and query.userName != ''">
            AND u.user_name LIKE CONCAT('%',#{query.userName}, '%')
        </if>
        <if test="query.mobile != null and query.mobile != ''">
            AND u.mobile = #{query.mobile}
        </if>
        <if test="query.email != null and query.email != ''">
            AND u.email = #{query.email}
        </if>
        <if test="query.state != null">
            AND u.state = #{query.state}
        </if>
        <if test="query.userId != null and query.userId > 0">
            AND u.user_id = #{query.userId}
        </if>
        <if test="query.orgType != null and query.orgType != ''">
            AND o.org_type = #{query.orgType}
        </if>
        <if test="query.hotelId != null and query.hotelId > 0">
            AND o.org_id IN (SELECT org_id FROM t_org_related_hotel WHERE hotel_id = #{query.hotelId})
        </if>
        <if test="query.relatedOrgIdList != null and query.relatedOrgIdList.size() > 0">
            AND o.org_id IN
            <foreach item="item" collection="query.relatedOrgIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        ORDER BY u.create_time DESC
        </select>


</mapper>
