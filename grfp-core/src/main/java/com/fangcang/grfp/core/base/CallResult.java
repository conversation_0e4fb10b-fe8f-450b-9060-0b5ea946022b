package com.fangcang.grfp.core.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * This class holds the primary return code, secondary return code and also other objects returned by a call on BO or DAO.
 * 
 * The primary return code is a code summarizing the result of the call.
 * 
 * The secondary return code could be a code to support the primary return code, or an English written description supporting the primary code. The use of
 * secondary return code is optional, and depending on the owner of the BO and DAO.
 *
 *
 */
public class CallResult implements Serializable {
	
	// ---------------------------------------------------------------------------------------------------- Private Static Variables
	
	private static final long serialVersionUID = 1L;
	
	// ---------------------------------------------------------------------------------------------------- Public Static Variables

	public static final String SUCCEEDED = "SUCCEEDED"; 
	
	public static final String FAILED = "FAILED";
	
	public static final String EXCEPTION = "EXCEPTION";
	
	// ---------------------------------------------------------------------------------------------------- Private Static Constant
	
	protected static final String LIST = "List(";
	
	protected static final String COLLECTION = "Collection(";
	
	protected static final String CLOSE_BRACKET = ")";
	
	protected static final String COMMA = ",";
	
	// ---------------------------------------------------------------------------------------------------- Private Variables
	
	protected String returnCode;
	
	protected String returnCodeDetail;
	
	protected Object returnObject;
	
	// ---------------------------------------------------------------------------------------------------- Constructor

	public CallResult() {
		this.returnCode = null;
		this.returnCodeDetail = null;
		this.returnObject = null; 
	}	
	
	// ---------------------------------------------------------------------------------------------------- Public Static Methods

	public static CallResult newForSucceeded() {
		CallResult result = new CallResult();
		result.setReturnCode(CallResult.SUCCEEDED);
		return result;
	}

	public static CallResult newForSucceeded(String returnCodeDetail) {
		CallResult result = new CallResult();
		result.setReturnCode(CallResult.SUCCEEDED);
		result.setReturnCodeDetail(returnCodeDetail);
		return result;
	}

	public static CallResult newForSucceeded(String returnCodeDetail, Object returnObject) {
		CallResult result = new CallResult();
		result.setReturnCode(CallResult.SUCCEEDED);
		result.setReturnCodeDetail(returnCodeDetail);
		result.setReturnObject(returnObject);
		return result;
	}

	// ----------------------------------------------------------------------------------------------------

	public static CallResult newForFailed() {
		CallResult result = new CallResult();
		result.setReturnCode(CallResult.FAILED);
		return result;
	}

	public static CallResult newForFailed(String returnCodeDetail) {
		CallResult result = new CallResult();
		result.setReturnCode(CallResult.FAILED);
		result.setReturnCodeDetail(returnCodeDetail);
		return result;
	}

	public static CallResult newForFailed(String returnCodeDetail, Object returnObject) {
		CallResult result = new CallResult();
		result.setReturnCode(CallResult.FAILED);
		result.setReturnCodeDetail(returnCodeDetail);
		result.setReturnObject(returnObject);
		return result;
	}

	// ----------------------------------------------------------------------------------------------------

	public static CallResult newForException(Exception ex) {
		CallResult result = new CallResult();
		result.setReturnCode(CallResult.EXCEPTION);
		result.setReturnCodeDetail(ex.getMessage());
		return result;
	}
	

	public static CallResult newForException(String exceptionMessage) {
		CallResult result = new CallResult();
		result.setReturnCode(CallResult.EXCEPTION);
		result.setReturnCodeDetail(exceptionMessage);
		return result;
	}

	public static CallResult newForException(Exception ex, Object returnObject) {
		CallResult result = new CallResult();
		result.setReturnCode(CallResult.EXCEPTION);
		result.setReturnCodeDetail(ex.getMessage());
		result.setReturnObject(returnObject);
		return result;
	}

	// ----------------------------------------------------------------------------------------------------
	
	// *** THERE IS NO isSucceeded() static method for code safety, please use the member method instead *** 
	
	public static boolean isFailed(String returnCode) {
		return CallResult.FAILED.equalsIgnoreCase(returnCode);
	}
	
	public static boolean isException(String returnCode) {
		return CallResult.EXCEPTION.equalsIgnoreCase(returnCode);
	}
	
	// ---------------------------------------------------------------------------------------------------- Public Methods
	
	public String getReturnCode() {
		return returnCode;
	}

	public void setReturnCode(String returnCode) {
		this.returnCode = returnCode;
	}

	public String getReturnCodeDetail() {
		return returnCodeDetail;
	}

	public void setReturnCodeDetail(String returnCodeDetail) {
		this.returnCodeDetail = returnCodeDetail;
	}

	public Object getReturnObject() {
		return returnObject;
	}

	public void setReturnObject(Object returnObject) {
		this.returnObject = returnObject;
	}

	// ----------------------------------------------------------------------------------------------------
	
	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder();
		
		sb.append(returnCode);
		if (!StringUtils.isEmpty(returnCodeDetail)) {
			sb.append(COMMA);
			sb.append(returnCodeDetail);
		}
		if (returnObject != null) {
			if (returnObject instanceof List) {
				List<?> list = (List<?>)returnObject;
				sb.append(COMMA);
				sb.append(LIST);
				sb.append(list.size());
				sb.append(CLOSE_BRACKET);
				
			} else if (returnObject instanceof Map) {
				Map<?, ?> map = (Map<?, ?>)returnObject;
				sb.append(COMMA);
				sb.append(LIST);
				sb.append(map.size());
				sb.append(CLOSE_BRACKET);
			
			} else if (returnObject instanceof Collection) {
				Collection<?> collection = (Collection<?>)returnObject;
				sb.append(COMMA);
				sb.append(COLLECTION);
				sb.append(collection.size());
				sb.append(CLOSE_BRACKET);
				
			} else {
				sb.append(COMMA);
				sb.append(returnObject.toString());
				
			}
		}
		
		return sb.toString();
	}

	@Override
	public boolean equals(Object object) {
		if (object instanceof String) {
			// Print out the error message
			Throwable throwable = new Throwable();
			StackTraceElement stackTraceElement = throwable.getStackTrace()[1];
			System.out.println("Unexpected the use of CallResult.equals() at " + stackTraceElement.getClassName() + "." + stackTraceElement.getMethodName() + ":" + stackTraceElement.getLineNumber());
			
			// Use return code to compare
			return this.getReturnCode().equals((String)object);
		} else {
			return object.equals(this);
		}
	}
	
	// ---------------------------------------------------------------------------------------------------- Public Methods

	@JsonIgnore
	public boolean isSucceeded() {
		return CallResult.SUCCEEDED.equalsIgnoreCase(returnCode);
	}
	
	// *** THERE IS NO isFailed() and isException() member methods for code safety, please use the static methods instead *** 
	
}
