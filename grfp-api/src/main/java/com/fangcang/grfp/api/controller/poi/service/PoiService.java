package com.fangcang.grfp.api.controller.poi.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.api.controller.poi.vo.AddPoiRequest;
import com.fangcang.grfp.api.controller.poi.vo.AddPoiResponse;
import com.fangcang.grfp.api.controller.poi.vo.ImportPoiVO;
import com.fangcang.grfp.api.controller.poi.vo.UpdatePoiRequest;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.base.IdVO;
import com.fangcang.grfp.core.base.ImportRowErrorVO;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.dto.excel.ImportExcelContext;
import com.fangcang.grfp.core.entity.CityEntity;
import com.fangcang.grfp.core.entity.OrgEntity;
import com.fangcang.grfp.core.entity.OrgPoiEntity;
import com.fangcang.grfp.core.enums.ImportBizTypeEnum;
import com.fangcang.grfp.core.enums.OrgTypeEnum;
import com.fangcang.grfp.core.enums.StateEnum;
import com.fangcang.grfp.core.manager.ExcelManager;
import com.fangcang.grfp.core.mapper.CityMapper;
import com.fangcang.grfp.core.mapper.OrgMapper;
import com.fangcang.grfp.core.mapper.OrgPoiMapper;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.vo.request.poi.ListPoiRequest;
import com.fangcang.grfp.core.vo.response.city.CityVO;
import com.fangcang.grfp.core.vo.response.poi.ListPoiVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PoiService {

    @Resource
    private OrgPoiMapper orgPoiMapper;

    @Resource
    private OrgMapper orgMapper;

    @Resource
    private CityMapper cityMapper;

    @Resource
    private ExcelManager excelManager;

    @Resource
    private HttpServletRequest httpServletRequest;

    /**
     * 添加 POI
     */
    public AddPoiResponse addPoi(AddPoiRequest addPoiRequest) {
        UserSession userSession = UserSession.get();

        // 企业机构只能创建本机构的 POI
        if (OrgTypeEnum.DISTRIBUTOR.key.equals(userSession.getUserOrg().getOrgType()) && !addPoiRequest.getOrgId().equals(userSession.getUserOrg().getOrgId())) {
            AppUtility.serviceError(ErrorCode.POI_ORG_ID_ILLEGAL);
        }

        // 查询省份和国家代码
        CityEntity city = cityMapper.selectByCityCode(addPoiRequest.getCityCode());
        if (Objects.isNull(city)) {
            AppUtility.serviceError(ErrorCode.POI_CITY_NOT_EXIST);
        }

        // 构建 entity
        OrgPoiEntity orgPoiEntity = new OrgPoiEntity();
        BeanUtils.copyProperties(addPoiRequest, orgPoiEntity);
        orgPoiEntity.setCreator(userSession.getUsername());
        orgPoiEntity.setModifier(userSession.getUsername());
        orgPoiEntity.setProvinceCode(city.getProvinceCode());
        orgPoiEntity.setCountryCode(city.getCountryCode());
        orgPoiEntity.setState(StateEnum.Effective.getKey());

        // 如果传了 map poi id, 则更新
        if (StringUtils.isNotBlank(orgPoiEntity.getMapPoiId())) {
            OrgPoiEntity existEntity = orgPoiMapper.selectByOrgIdAndGooglePoiId(addPoiRequest.getOrgId(), addPoiRequest.getMapPoiId());
            if (Objects.nonNull(existEntity)) {
                orgPoiEntity.setPoiId(existEntity.getPoiId());
                orgPoiMapper.updateById(orgPoiEntity);
                return new AddPoiResponse(orgPoiEntity.getPoiId());
            }
        }

        // 否则插入
        // 可能传了空串, 会导致唯一索引错误
        orgPoiEntity.setMapPoiId(null);
        orgPoiMapper.insert(orgPoiEntity);
        return new AddPoiResponse(orgPoiEntity.getPoiId());
    }

    /**
     * 删除 POI
     */
    public void deletePoi(Long orgPoiId) {
        UserSession userSession = UserSession.get();

        // 查询 POI 信息
        OrgPoiEntity orgPoiEntity = orgPoiMapper.selectById(orgPoiId);
        if (Objects.isNull(orgPoiEntity)) {
            AppUtility.serviceError(ErrorCode.POI_NOT_EXIST);
        }

        // 企业机构只能删除本机构的 POI
        if (OrgTypeEnum.DISTRIBUTOR.key.equals(userSession.getUserOrg().getOrgType()) && !orgPoiEntity.getOrgId().equals(userSession.getUserOrg().getOrgId())) {
            AppUtility.serviceError(ErrorCode.POI_NO_PERMISSION);
        }

        // 删除 POI
        OrgPoiEntity updateEntity = new OrgPoiEntity();
        updateEntity.setPoiId(orgPoiId);
        updateEntity.setModifier(userSession.getUsername());
        updateEntity.setState(StateEnum.Invalid.key);
        orgPoiMapper.updateById(updateEntity);
    }

    /**
     * 更新 POI
     */
    public void updatePoi(UpdatePoiRequest req) {
        UserSession userSession = UserSession.get();

        // 企业机构只能删除本机构的 POI
        if (OrgTypeEnum.DISTRIBUTOR.key.equals(userSession.getUserOrg().getOrgType()) && !req.getOrgId().equals(userSession.getUserOrg().getOrgId())) {
            AppUtility.serviceError(ErrorCode.POI_NO_PERMISSION);
        }

        // 查询省份和国家代码
        CityEntity city = cityMapper.selectByCityCode(req.getCityCode());
        if (Objects.isNull(city)) {
            AppUtility.serviceError(ErrorCode.POI_CITY_NOT_EXIST);
        }

        // 更新 POI 信息
        OrgPoiEntity updateEntity = new OrgPoiEntity();
        BeanUtils.copyProperties(req, updateEntity);
        updateEntity.setMapPoiId(StringUtils.isBlank(req.getMapPoiId()) ? null : req.getMapPoiId());
        updateEntity.setProvinceCode(city.getProvinceCode());
        updateEntity.setCountryCode(city.getCountryCode());
        updateEntity.setModifier(userSession.getUsername());
        orgPoiMapper.updateById(updateEntity);
    }

    /**
     * 查询 POI 列表
     */
    public PageVO<ListPoiVO> queryPoiList(ListPoiRequest req) {
        UserSession userSession = UserSession.get();

        // 为企业角色时，只能查询当前登录机构的poi
        if (!OrgTypeEnum.PLATFORM.key.equals(userSession.getUserOrg().getOrgType())) {
            req.setOrgId(userSession.getUserOrg().getOrgId());
        }

        // 查询数据
        Page<ListPoiVO> pageRes = orgPoiMapper.selectByCondition(new Page<>(req.getPageIndex(), req.getPageSize()), req);
        List<ListPoiVO> records = pageRes.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageVO<>(0, 0, records);
        }

        // 查询城市对应的国家和省份信息
        int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);
        Set<String> cityCodes = records.stream().map(ListPoiVO::getCityCode).collect(Collectors.toSet());
        List<CityVO> cityList = cityMapper.selectCityVOList(cityCodes);
        Map<String, CityVO> cityCodeCityVOMap = cityList.stream().collect(Collectors.toMap(CityVO::getCityCode, Function.identity(), (o, v) -> v));

        // 组装返回结果
        records.forEach(item -> {
            CityVO cityVO = cityCodeCityVOMap.get(item.getCityCode());
            Optional.ofNullable(cityVO).ifPresent(e -> {
                item.setCityName(AppUtility.getName(language, e.getCityCode(), e.getCityNameEnUs(), e.getCityNameZhCn()));
                item.setProvinceName(AppUtility.getName(language, e.getProvinceCode(), e.getProvinceNameEnUs(), e.getProvinceNameZhCn()));
                item.setCountryName(AppUtility.getName(language, e.getCountryCode(), e.getCountryNameEnUs(), e.getCountryNameZhCn()));
            });
        });

        return new PageVO<>(pageRes.getTotal(), pageRes.getPages(), records);
    }

    /**
     * 导入 POI
     */
    public IdVO<Long> importPoi(MultipartFile file) throws IOException {
        ImportExcelContext<ImportPoiVO> context = new ImportExcelContext<>();
        context.setFile(file);
        context.setBizType(ImportBizTypeEnum.IMPORT_POI);
        context.setImportVOClass(ImportPoiVO.class);
        context.setImportLogic(this::handleImportPoi);
        return new IdVO<>(excelManager.generalImport(context));
    }

    /**
     * 导入逻辑
     */
    private List<ImportRowErrorVO> handleImportPoi(ImportExcelContext<ImportPoiVO> context, List<ImportPoiVO> importList) {
        List<ImportRowErrorVO> errors = new ArrayList<>();

        // 查询机构
        List<String> orgNames = importList.stream().map(ImportPoiVO::getOrgName).collect(Collectors.toList());
        List<OrgEntity> orgList = orgMapper.selectByOrgNames(orgNames);
        Map<String, OrgEntity> orgNameOrgMap = orgList.stream().collect(Collectors.toMap(OrgEntity::getOrgName, Function.identity(), (o, v) -> v));

        // 查询城市
        List<String> cityNames = importList.stream().map(ImportPoiVO::getCityName).collect(Collectors.toList());
        List<CityEntity> cityList = cityMapper.selectByCityName(cityNames);
        Map<String, CityEntity> cityNameEnUsMap = cityList.stream().collect(Collectors.toMap(CityEntity::getNameEnUs, Function.identity(), (o, v) -> v));
        Map<String, CityEntity> cityNameZhCnMap = cityList.stream().collect(Collectors.toMap(CityEntity::getNameZhCn, Function.identity(), (o, v) -> v));

        // 校验机构是否存在
        List<OrgPoiEntity> insertList = new ArrayList<>(importList.size());
        importList.forEach(item -> {
            ImportRowErrorVO rowError = new ImportRowErrorVO(item.getRowNum());

            // 校验
            validImportPoi(item, rowError, orgNameOrgMap);

            // 填充城市
            CityEntity city = null;
            if (StringUtils.isNotEmpty(item.getCityName())) {
                city = cityNameEnUsMap.getOrDefault(item.getCityName(), cityNameZhCnMap.get(item.getCityName()));
                if (Objects.isNull(city)) {
                    rowError.addError(ErrorCode.CITY_NOT_EXIST);
                }
            }

            // 校验不通过, 不处理
            if (rowError.hasError()) {
                errors.add(rowError);
                return;
            }

            // 转换 entity
            OrgPoiEntity orgPoiEntity = new OrgPoiEntity();
            BeanUtils.copyProperties(item, orgPoiEntity);
            orgPoiEntity.setOrgId(orgNameOrgMap.get(item.getOrgName()).getOrgId());
            // todo 需要根据经纬度查询城市, GLink API 未给出
            orgPoiEntity.setCityCode(city.getCityCode());
            orgPoiEntity.setProvinceCode(city.getProvinceCode());
            orgPoiEntity.setCountryCode(city.getCountryCode());
            orgPoiEntity.setCreator(context.getUserSession().getUsername());
            insertList.add(orgPoiEntity);
        });

        // 插入 POI
        if (CollUtil.isNotEmpty(insertList)) {
            orgPoiMapper.batchInsert(insertList);
        }
        return errors;
    }

    /**
     * 校验行数据
     */
    private static void validImportPoi(ImportPoiVO item, ImportRowErrorVO error, Map<String, OrgEntity> orgNameOrgMap) {
        if (StringUtils.isBlank(item.getOrgName())) {
            error.addError(ErrorCode.INVALID_ORG);
        }
        OrgEntity orgEntity = orgNameOrgMap.get(StringUtils.trim(item.getOrgName()));
        if (Objects.isNull(orgEntity)) {
            error.addError(ErrorCode.NOT_EXIST_ORG);
        }
        if (StringUtils.isEmpty(item.getCityName())) {
            error.addError(ErrorCode.CITY_NOT_EXIST);
        }
        if (StringUtils.isEmpty(item.getPoiName())) {
            error.addError(ErrorCode.POI_NAME_VALID);
        }
        if (StringUtils.isEmpty(item.getPoiAddress())) {
            error.addError(ErrorCode.POI_ADDRESS_VALID);
        }
        if (Objects.isNull(item.getLngGoogle())) {
            error.addError(ErrorCode.POI_LONGITUDE_INVALID);
        }
        if (Objects.isNull(item.getLatGoogle())) {
            error.addError(ErrorCode.POI_LATITUDE_INVALID);
        }
    }
}
