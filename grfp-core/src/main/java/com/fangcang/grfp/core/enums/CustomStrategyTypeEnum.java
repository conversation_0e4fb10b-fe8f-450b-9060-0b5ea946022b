package com.fangcang.grfp.core.enums;

/**
 * 自定义策略回答类型
 */
public enum CustomStrategyTypeEnum {

    YSE_OR_NO(1, "是或否"),

    TEXT(2, "文本"),

    CHECKBOX(3, "多选"),

    RADIO(4, "单选");

    public int key;

    public String value;

    CustomStrategyTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    /**
     * 是否选项类型(单选或多选)
     */
    public static boolean isOptionType(int key) {
        return CHECKBOX.key == key || RADIO.key == key;
    }


}
