CREATE DATABASE `grfp` CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_unicode_ci';

USE  `grfp`;

CREATE TABLE `t_user` (
   `user_id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
   `org_id` INT(11) NOT NULL COMMENT '机构ID',
   `user_name` VARCHAR(200) NOT NULL COMMENT '用户姓名',
   `nick_name` VARCHAR(200) COMMENT '用户昵称',
   `role_code` varchar(100) NOT NULL COMMENT '角色编码类型 ADMIN：管理员，EMPLOYEE：员工，HEAD_ORGANIZATION：机构负责人，CHANNEL_ADMIN:渠道合作管理员',
   `mobile_area_code` VARCHAR(10) NOT NULL COMMENT '手机号码区号',
   `mobile` VARCHAR(50) NOT NULL COMMENT '手机号码',
   `email` VARCHAR(100) NOT NULL COMMENT '邮箱',
   `password` VARCHAR(100)  COMMENT '密码',
   `last_login_time` datetime COMMENT '最近登录时间',
   `last_ip_address` VARCHAR(100) COMMENT '最近登录IP',
   `state` INT(5) NOT NULL COMMENT '用户状态 1:有效,0:无效，2:待审核',
   `approve_time` datetime  COMMENT '审批时间',
   `channel_partner_id` INT(11)  COMMENT '渠道ID',
   `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `modifier` varchar(100) DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改人',
   `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
   PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
ALTER TABLE `t_user` ADD INDEX `idx_org_id` (`org_id` ASC);
ALTER TABLE `t_user` ADD UNIQUE INDEX `uidx_email` (`email` ASC);


CREATE TABLE `t_user_login_log` (
     `user_login_log_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户登录日志ID',
     `user_id` INT(11) NOT NULL COMMENT '用户ID',
     `login_date_time` datetime NOT NULL COMMENT '登录时间',
     `user_token` varchar(1000) DEFAULT NULL COMMENT '登录token',
     `ip_address` varchar(100) DEFAULT NULL COMMENT 'IP地址',
     `user_agent` varchar(500) DEFAULT NULL COMMENT '登录user-agent',
     `logout_date_time` datetime DEFAULT NULL COMMENT '登出时间',
     `logout_reason` varchar(100) DEFAULT NULL COMMENT '登出原因',
     `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
     `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
     PRIMARY KEY (`user_login_log_id`),
     KEY `idx_create_time` (`create_time`),
     KEY `idx_usr` (`user_id`,`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8 COMMENT='登录日志';

CREATE TABLE `t_sys_config` (
    `sys_config_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '系统配置ID',
    `sys_config_code` varchar(100) NOT NULL COMMENT '配置code',
    `sys_config_value` varchar(1000) DEFAULT NULL COMMENT '配置Value',
    `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`sys_config_code`),
    UNIQUE KEY `uk_id` (`sys_config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置';

CREATE TABLE `t_sys_config_log` (
       `sys_config_log_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '系统配置修改日志ID',
       `operate_code` varchar(100) NOT NULL COMMENT '操作编号',
       `sys_config_code` varchar(100) NOT NULL COMMENT '系统配置编号',
       `before_kv_json` varchar(3000) DEFAULT NULL COMMENT '修改前值',
       `after_kv_json`  varchar(3000) DEFAULT NULL COMMENT '修改后值',
       `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
       `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       PRIMARY KEY (`sys_config_log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置修改日志表';


CREATE TABLE `t_text_resource` (
    `text_resource_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '文字资源ID',
    `text_resource_code` varchar(100) NOT NULL COMMENT '文字资源编号',
    `text_resource_type` int(2) NOT NULL COMMENT '文字资源类型 1:网页文本，2：提示信息',
    `value_en_us` varchar(1000) DEFAULT NULL COMMENT '英文文字翻译',
    `value_zh_cn` varchar(1000) DEFAULT NULL COMMENT '中文文字翻译',
    `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`text_resource_type`,`text_resource_code`),
    UNIQUE KEY `uk_id` (`text_resource_id`),
    UNIQUE KEY `uk_code_type` (`text_resource_code`,`text_resource_type`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文字资源表';


CREATE TABLE `t_user_permission` (
       `user_permission_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户权限ID',
       `org_type` INT(2) NOT NULL COMMENT '机构类型 1平台，2酒店，3企业，4酒店集团',
       `role_code` varchar(100) NOT NULL COMMENT '角色编码类型 ADMIN：管理员，EMPLOYEE：员工，HEAD_ORGANIZATION：机构负责人，CHANNEL_ADMIN:渠道合作管理员',
       `permission` varchar(100) DEFAULT NULL COMMENT '权限',
       `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
       `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
       `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
       PRIMARY KEY (`user_permission_id`),
       UNIQUE KEY `uk_id` (`user_permission_id`),
       UNIQUE KEY `uk_code_type_permission` (`org_type`,`role_code`,`permission`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限管理表';


CREATE TABLE `t_country`
(
    `country_id`       varchar(100) NOT NULL COMMENT '国家ID',
    `country_code`     varchar(100) NOT NULL COMMENT '国家编号',
    `name_en_us`       varchar(300) NOT NULL DEFAULT '' COMMENT '英文名称',
    `short_name_en_us` varchar(300) NOT NULL DEFAULT '' COMMENT '英文名称简称',
    `name_zh_cn`       varchar(300) NOT NULL DEFAULT '' COMMENT '中文名称',
    `short_name_zh_cn` varchar(300) NOT NULL DEFAULT '' COMMENT '中文简称名称',
    `creator`          varchar(100) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`         varchar(100) NULL COMMENT '修改人',
    `modify_time`      datetime     NULL on update CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`country_code`),
    UNIQUE KEY `uk_id` (`country_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='国家';


CREATE TABLE `t_province`
(
    `country_code`  varchar(100) NOT NULL COMMENT '国家编号',
    `province_code` varchar(100) NOT NULL COMMENT '省份编号',
    `name_en_us`    varchar(300) NOT NULL DEFAULT '' COMMENT '英文名称',
    `name_zh_cn`    varchar(300) NOT NULL DEFAULT '' COMMENT '中文名称',
    `creator`       varchar(100) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`      varchar(100) NULL COMMENT '修改人',
    `modify_time`   datetime     NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`country_code`, `province_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='州/省/邦';

CREATE TABLE `t_city`
(
    `country_code`     varchar(100) NOT NULL COMMENT '国家编号',
    `province_code`    varchar(100) NOT NULL COMMENT '省份编号',
    `city_code`        varchar(100) NOT NULL COMMENT '城市编号',
    `name_en_us`       varchar(300) NOT NULL DEFAULT '' COMMENT '英文名称',
    `name_zh_cn`       varchar(300) NOT NULL DEFAULT '' COMMENT '中文名称',
    `parent_city_code` varchar(100) NOT NULL COMMENT '父城市编号',
    `creator`          varchar(100) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`         varchar(100) NULL COMMENT '修改人',
    `modify_time`      datetime     NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`country_code`, `province_code`, `city_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='城市';

create index idx_citycode on t_city(city_code);

CREATE TABLE `t_hotel_group`
(
    `hotel_group_id` bigint       NOT NULL COMMENT '酒店集团ID',
    `name_en_us`     varchar(300) NOT NULL default '' COMMENT '英文名称',
    `name_zh_cn`     varchar(300) NOT NULL default '' COMMENT '中文名称',
    `is_active`      tinyint(1)   NOT NULL COMMENT '是否有效 1:是，0:否',
    `creator`        varchar(100) NULL COMMENT '创建人',
    `create_time`    datetime     NOT NULL DEFAULT current_timestamp COMMENT '创建时间',
    `modifier`       varchar(100) NULL COMMENT '修改人',
    `modify_time`    datetime     NULL on update CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`hotel_group_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='酒店集团';

CREATE TABLE `t_hotel_group_log`
(
    `hotel_group_log_id` bigint        NOT NULL AUTO_INCREMENT COMMENT '酒店集团日志ID',
    `hotel_group_id`     bigint        NOT NULL COMMENT '酒店品牌ID',
    `before_kv_json`     varchar(3000) NULL COMMENT '修改前值',
    `after_kv_json`      varchar(3000) NULL COMMENT '修改后值',
    `creator`            varchar(100)  NULL COMMENT '创建人',
    `create_time`        datetime      NULL DEFAULT current_timestamp COMMENT '创建时间',
    PRIMARY KEY (`hotel_group_log_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='酒店集团日志表';

CREATE TABLE `t_hotel_brand`
(
    `hotel_brand_id` bigint       NOT NULL COMMENT '酒店品牌ID',
    `hotel_group_id` bigint       NOT NULL COMMENT '酒店集团ID',
    `name_en_us`     varchar(300) NOT NULL DEFAULT '' COMMENT '英文名称',
    `name_zh_cn`     varchar(300) NOT NULL DEFAULT '' COMMENT '中文名称',
    `is_active`      int(1)       NOT NULL COMMENT '是否有效 1:是，0:否',
    `creator`        varchar(100) NULL COMMENT '创建人',
    `create_time`    datetime     NULL default current_timestamp COMMENT '创建时间',
    `modifier`       varchar(100) NULL COMMENT '修改人',
    `modify_time`    datetime     NULL on update CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`hotel_brand_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='酒店品牌';

CREATE TABLE `t_hotel_brand_log`
(
    `hotel_brand_log_id` bigint        NOT NULL AUTO_INCREMENT COMMENT '酒店品牌日志ID',
    `hotel_brand_id`     bigint        NOT NULL COMMENT '酒店品牌ID',
    `before_kv_json`     varchar(3000) NULL COMMENT '修改前值',
    `after_kv_json`      varchar(3000) NULL COMMENT '修改后值',
    `creator`            varchar(100)  NULL COMMENT '创建人',
    `create_time`        datetime      NOT NULL DEFAULT current_timestamp COMMENT '创建时间',
    PRIMARY KEY (`hotel_brand_log_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='酒店品牌日志表';

CREATE TABLE `t_hotel`
(
    `hotel_id`          bigint        NOT NULL COMMENT '酒店ID',
    `country_code`      varchar(100) COMMENT '国家编号',
    `province_code`     varchar(100) COMMENT '省/州/邦编码',
    `city_code`         varchar(100) COMMENT '城市编码',
    `name_en_us`        varchar(300)  NULL COMMENT '英文名称',
    `name_zh_cn`        varchar(300)  NULL COMMENT '中文名称',
    `address_en_us`     varchar(600)  NULL COMMENT '地址（英文）',
    `address_zh_cn`     varchar(600)  NULL COMMENT '地址（中文）',
    `introduce_en_us`   varchar(5000) NULL COMMENT '介绍（英文）',
    `introduce_zh_cn`   varchar(5000) NULL COMMENT '介绍（中文）',
    `telephone`         varchar(200)  NULL COMMENT '电话',
    `email`             varchar(200)  NULL COMMENT '电邮',
    `hotel_star`        varchar(200)  NULL COMMENT '星级',
    `rating`            varchar(200)  NULL COMMENT '酒店评分',
    `opening_date`      date          NULL COMMENT '开业日期',
    `fitment_date`      date          NULL COMMENT '装修日期',
    `hotel_group_id`    bigint        NULL COMMENT '酒店集团ID',
    `hotel_brand_id`    bigint        NULL COMMENT '酒店品牌ID',
    `main_pic_url`      VARCHAR(200)  NULL COMMENT '主图URL地址',
    `check_in_time`     VARCHAR(100)  NULL COMMENT '酒店入住时间',
    `check_out_time`    VARCHAR(100)  NULL COMMENT '酒店离店时间',
    `room_num`          int(5)        NULL COMMENT '客房总数',
    `has_meeting_room`  tinyint(1)    NULL COMMENT '是否包含会议室 1:是，0:否',
    `has_swimming_pool` tinyint(1)    NULL COMMENT '是否包含游泳池 1:是，0:否',
    `has_gym`           tinyint(1)    NULL COMMENT '是否包含健身房 1:是，0:否',
    `has_laundry_room`  tinyint(1)    NULL COMMENT '是否包含洗衣房 1:是，0:否',
    `lng_baidu`         decimal(15, 10)    DEFAULT NULL COMMENT '百度经度',
    `lat_baidu`         decimal(15, 10)    DEFAULT NULL COMMENT '百度纬度',
    `lng_google`        decimal(15, 10)    DEFAULT NULL COMMENT 'Google经度',
    `lat_google`        decimal(15, 10)    DEFAULT NULL COMMENT 'Google纬度',
    `lng_mars`          decimal(15, 10)    DEFAULT NULL COMMENT '火星经度',
    `lat_mars`          decimal(15, 10)    DEFAULT NULL COMMENT '火星纬度',
    `currency_code`     varchar(32)   NULL COMMENT '币种',
    `is_active`         tinyint(1)    NOT NULL COMMENT '是否有效 1:是，0:否',
    `creator`           varchar(100)       DEFAULT NULL COMMENT '创建人',
    `create_time`       datetime      NULL default current_timestamp COMMENT '创建时间',
    `modifier`          varchar(100)  NULL COMMENT '修改人',
    `modify_time`       datetime      NULL on update current_timestamp COMMENT '修改时间',
    PRIMARY KEY (`hotel_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='酒店';


CREATE TABLE `t_hotel_log`
(
    `hotel_log_id`   bigint   NOT NULL AUTO_INCREMENT COMMENT '酒店日志ID',
    `hotel_id`       bigint   NOT NULL COMMENT '酒店ID',
    `before_kv_json` varchar(7000) DEFAULT NULL COMMENT '修改前值',
    `after_kv_json`  varchar(7000) DEFAULT NULL COMMENT '修改后值',
    `creator`        varchar(100)  DEFAULT NULL COMMENT '创建人',
    `create_time`    datetime NULL default current_timestamp COMMENT '创建时间',
    PRIMARY KEY (`hotel_log_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='酒店日志表';


CREATE TABLE `t_hotel_data`
(
    `hotel_id`    bigint   NOT NULL COMMENT '酒店ID',
    `data_en_us`  MEDIUMTEXT COMMENT '数据(英文)',
    `data_zh_cn`  MEDIUMTEXT COMMENT '数据(中文)',
    `create_time` datetime NOT NULL default current_timestamp COMMENT '创建时间',
    `modify_time` datetime NULL on update current_timestamp COMMENT '修改时间',
    PRIMARY KEY (`hotel_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='酒店数据';

create table `t_hotel_image`
(
    `hotel_id`    bigint   NOT NULL COMMENT '酒店ID',
    `data`        MEDIUMTEXT COMMENT '数据',
    `create_time` datetime NOT NULL default current_timestamp COMMENT '创建时间',
    `modify_time` datetime NULL on update current_timestamp COMMENT '修改时间',
    PRIMARY KEY (`hotel_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='酒店图片数据';


CREATE TABLE `t_org`
(
    `org_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '机构ID',
    `org_name` varchar(128) NOT NULL COMMENT '机构名称',
    `org_type` int(2) NOT NULL COMMENT '机构类型 1平台，2酒店，3企业，4酒店集团',
    `contact_name` varchar(64) NOT NULL COMMENT '联系人',
    `contact_mobile_area_code` VARCHAR(10) DEFAULT NULL COMMENT '联系人电话区号',
    `contact_mobile` varchar(16) DEFAULT NULL COMMENT '联系人电话',
    `contact_email` varchar(64) NOT NULL COMMENT '联系人电邮',
    `financial_contact_name` varchar(64) DEFAULT NULL COMMENT '财务联系人',
    `financial_contact_mobile_area_code` VARCHAR(10) DEFAULT NULL COMMENT '财务联系人电话区号',
    `financial_contact_mobile` varchar(16) DEFAULT NULL COMMENT '财务联系人电话',
    `financial_contact_email` varchar(64) NOT NULL COMMENT '联系人电邮',
    `address_detail` varchar(256) DEFAULT NULL COMMENT '详细地址',
    `company_profile` varchar(1024) DEFAULT NULL COMMENT '公司简介',
    `logo_url` varchar(128) DEFAULT NULL COMMENT '机构logo地址',
    `state` int(1) NOT NULL COMMENT '状态(1：有效，0：无效)',
    `partner_channel_code` VARCHAR(128)  COMMENT '合作商渠道编码',
    `channel_partner_id` int(11)  COMMENT '渠道合作商ID',
    `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`org_id`)
) ENGINE=InnoDB  AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='机构';


CREATE TABLE `t_sys_audit_log` (
           `id` bigint NOT NULL AUTO_INCREMENT,
           `user_id` int(11) DEFAULT NULL COMMENT '成员 id',
            `request_time` datetime NOT NULL COMMENT '请求时间',
            `response_time` datetime DEFAULT NULL COMMENT '响应时间',
            `client_ip` varchar(128) DEFAULT NULL COMMENT '请求 ip',
            `user_agent` varchar(200)  DEFAULT NULL COMMENT '请求 user agent',
            `request_uri` varchar(128) DEFAULT NULL COMMENT '请求 uri',
            `request_params` varchar(500) COMMENT '请求参数. 超长截断',
            `response` varchar(500) DEFAULT NULL COMMENT '响应结果. 超长截断',
            `response_code` varchar(32) DEFAULT NULL COMMENT '响应结果 code',
            `function_name` varchar(128) DEFAULT NULL COMMENT '功能. 格式: 菜单-动作',
            `extend_param` varchar(500) DEFAULT NULL COMMENT '自定义扩展参数',
             `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录的创建时间',
              PRIMARY KEY (`id`),
            KEY `idx_requesttime` (`request_time`),
            KEY `idx_userid` (`user_id`),
           KEY `idx_function_name` (`function_name`)
) ENGINE=InnoDB  AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户审计日志表';

CREATE TABLE `t_org_related_hotel`(
                                      `org_related_hotel_id` int(11) NOT NULL AUTO_INCREMENT,
                                      `org_id` int(11) NOT NULL COMMENT '机构ID',
                                      `hotel_id` bigint NOT NULL COMMENT '酒店ID',
                                      `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      PRIMARY KEY (`org_related_hotel_id`),
                                      UNIQUE KEY `uidx_org_hotel_id` (`org_id`,`hotel_id`),
                                      UNIQUE KEY `u_hotelId`(`hotel_id`)
)ENGINE=InnoDB  AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='机构关联酒店';

CREATE TABLE `t_org_related_hotel_brand`(
                                            `org_related_hotel_brand_id` int(11) NOT NULL AUTO_INCREMENT,
                                            `org_id` int(11) NOT NULL COMMENT '机构ID',
                                            `hotel_group_id` bigint  NOT NULL COMMENT '酒店集团ID',
                                            `hotel_brand_id` bigint  NOT NULL COMMENT '酒店品牌ID',
                                            `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
                                            `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            PRIMARY KEY (`org_related_hotel_brand_id`),
                                            UNIQUE KEY `uidx_org_brand_id` (`org_id`,`hotel_group_id`,`hotel_brand_id`)
)ENGINE=InnoDB  AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='机构关联酒店品牌';


CREATE TABLE `t_attachment_file`(
    `attachment_file_id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件ID',
    `file_original_name` varchar(256) NOT NULL COMMENT '文件原名',
    `file_key` varchar(128) NOT NULL COMMENT '文件Key',
    `business_type` int(2) NOT NULL COMMENT '类型 1-机构logo 2-机构签约主体营业执照 3-正文合同模板(不含技术定位符)，供投标等预览时用  4-机构签约主体授权书  5-合同  6-合同正式模板',
    `is_active` int(1) NOT NULL COMMENT '是否有效 1-有效 0-无效',
    `external_id` bigint DEFAULT NULL COMMENT '关联ID',
    `file_stream` blob DEFAULT NULL  COMMENT '文件流',
    `file_size` BIGINT DEFAULT NULL  COMMENT '文件大小',
    `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`attachment_file_id`),
    UNIQUE KEY `uidx_file_key` (`business_type`,`file_key`)
)ENGINE=InnoDB  AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件附件';

CREATE TABLE `t_sys_export_record`(
    `sys_export_record_id` bigint NOT NULL AUTO_INCREMENT COMMENT '系统导出记录ID',
    `export_name` varchar(256) NOT NULL COMMENT '导出记录名称',
    `file_name` varchar(256) NOT NULL COMMENT '文件名称',
    `export_path`  varchar(250)   null comment '导出路径, 只存 key (方便换桶)',
    `status`       varchar(32)    not null comment '状态(0:初始化话 1:处理中 2:成功, 3:失败)',
    `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier` varchar(100) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `remark`    varchar(128)    null comment '备注',
     PRIMARY KEY (`sys_export_record_id`)
)ENGINE=InnoDB  AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='导出记录表';

CREATE TABLE `t_sys_import_record`
(
    `sys_import_record_id` bigint        NOT NULL AUTO_INCREMENT COMMENT '系统导入记录 ID',
    `import_name`          varchar(256)  NOT NULL COMMENT '导入名称, 区别不同业务, 如导入POI',
    `file_name`            varchar(256)  NOT NULL COMMENT '文件名称',
    `import_path`          varchar(250)  NULL COMMENT '导入 oss 路径',
    `status`               int           NOT NULL COMMENT '状态 1:处理中 2:成功, 3:失败',
    `fail_remark`          varchar(5000) NULL COMMENT '失败原因, 注意截断, 防止超长',
    `creator`              varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time`          datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`             varchar(100) DEFAULT NULL COMMENT '修改人',
    `modify_time`          datetime     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`sys_import_record_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='导入记录表';

CREATE TABLE `t_currency_exchange_rate`  (
                                             `currency_code` varchar(100)  NOT NULL COMMENT '币种编码',
                                             `currency_name` varchar(100)  NOT NULL COMMENT  '币种名称',
                                             `exchange_rate` decimal(20, 10) NOT NULL COMMENT '美元对币种汇率',
                                             `inverse_exchange_rate` decimal(20, 10) NOT NULL COMMENT '币种对美元汇率',
                                             `display_order` int(11) NULL DEFAULT NULL COMMENT '显示顺序',
                                             `is_auto_sync` int(1) NOT NULL DEFAULT 1 COMMENT '是否自动同步',
                                             `creator` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人',
                                             `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                             `modifier` varchar(100)  DEFAULT NULL COMMENT '修改人',
                                             `modify_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                             PRIMARY KEY (`currency_code` ) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '币种汇率表' ROW_FORMAT = Compact;

CREATE TABLE `t_currency_exchange_rate_log`  (
                                                 `currency_exchange_rate_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '币种汇率日志ID',
                                                 `currency_code` varchar(100) NOT NULL COMMENT '币种编码',
                                                 `before_kv_json` varchar(7000) DEFAULT NULL COMMENT '修改前值',
                                                 `after_kv_json`  varchar(7000) DEFAULT NULL COMMENT '修改后值',
                                                 `creator`        varchar(100)  DEFAULT NULL COMMENT '创建人',
                                                 `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                 PRIMARY KEY (`currency_exchange_rate_log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 151 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '币种汇率日志表' ROW_FORMAT = Compact;

CREATE TABLE `t_project` (
     `project_id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '项目ID',
     `project_name` VARCHAR(100) NOT NULL COMMENT '项目名称',
     `project_type` INT(1) NOT NULL COMMENT '项目类型(1：酒店)，跟合同模板业务类型保持一致',
     `project_state` INT(1)  NOT NULL COMMENT '项目状态(0：未启动，1：招标中(已启动)，2：招标完成，3：已废标)',
     `tender_org_id` INT(11) NOT NULL COMMENT '招标机构id',
     `tender_type` INT(11) NOT NULL COMMENT '招标方式(1-公开招标，2-邀请招标)',
     `contact_name` VARCHAR(200) NOT NULL COMMENT '招标方项目联系人',
     `contact_mobile` VARCHAR(200) NOT NULL COMMENT '招标方项目人手机号码',
     `bid_start_time` DATE NOT NULL COMMENT '报价开始时间',
     `bid_end_time` DATE NOT NULL COMMENT '报价结束时间',
     `first_bid_start_time` DATE NOT NULL COMMENT '第一轮报价开始时间',
     `first_bid_end_time` DATE NOT NULL COMMENT '第一轮报价结束时间',
     `second_bid_start_time` DATE DEFAULT NULL COMMENT '第二轮报价开始时间',
     `second_bid_end_time` DATE DEFAULT NULL COMMENT '第二轮报价结束时间',
     `third_bid_start_time` DATE DEFAULT NULL COMMENT '第三轮报价开始时间',
     `third_bid_end_time` DATE DEFAULT NULL COMMENT '第三轮报价结束时间',
     `tender_count` INT(11) NOT NULL COMMENT '预估采购数量，项目类型为酒店时即为酒店数量',
     `price_monitor_start_date` DATE NOT NULL COMMENT '协议报价开始日期',
     `price_monitor_end_date` DATE NOT NULL COMMENT '协议报价结束日期',
     `bid_state_updated_notify_mode` INT(1)  NOT NULL COMMENT '报价状态更新通知方式(0-手工，1-自动)',
     `introduction` MEDIUMTEXT DEFAULT NULL COMMENT '项目简介',
     `related_project_id` INT(11) DEFAULT NULL COMMENT '关联项目 ID',
     `display_order` INT(11) DEFAULT NULL COMMENT '排序(大到小)',
     `creator`        varchar(100)  NOT NULL COMMENT '创建人',
     `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `modifier`        varchar(100)  NOT NULL COMMENT '修改人',
     `modify_time`    DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
     `diff_min_amount`     decimal(12, 2) null comment '公司差标最小值',
     `diff_max_amount`     decimal(12, 2) null comment '公司差标最大值',
     `budget_total_amount` decimal(12, 2) null comment '招标预算总价',
     PRIMARY KEY  (`project_id`)
)ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '项目' ROW_FORMAT = Compact;



CREATE TABLE `t_org_poi`
(
    `poi_id`        bigint          NOT NULL AUTO_INCREMENT COMMENT 'POI ID',
    `poi_name`      varchar(128)    NOT NULL COMMENT 'POI 名称',
    `poi_address`   varchar(256)    NOT NULL COMMENT 'POI 地址',
    `org_id`        int             NOT NULL COMMENT '机构ID',
    `city_code`     varchar(128)    NOT NULL COMMENT '城市编码',
    `province_code` varchar(128)    NOT NULL COMMENT '省份编码',
    `country_code`  varchar(128)    NOT NULL COMMENT '国家编码',
    `map_poi_id`    varchar(128)    NULL COMMENT '地图 POI ID',
    `lng_google`    decimal(15, 10) NOT NULL COMMENT 'Google 经度',
    `lat_google`    decimal(15, 10) NOT NULL COMMENT 'Google 纬度',
    `state`         tinyint(1)      NOT NULL DEFAULT 1 COMMENT '状态 1:有效,0:无效',
    `creator`       varchar(100)             DEFAULT NULL COMMENT '创建人',
    `create_time`   datetime        NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`      varchar(100)             DEFAULT NULL COMMENT '修改人',
    `modify_time`   datetime                 DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`poi_id`),
    UNIQUE INDEX `udx_orgid_mappoiid` (`org_id`, `map_poi_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='机构 POI';

create table t_project_poi
(
    `id`          bigint       not null auto_increment comment '主键',
    `project_id`  int          not null comment '项目 ID',
    `poi_id`      bigint       not null comment 'POI ID',
    `total_night_room_count` int comment '3公里范围总间夜数',
    `total_amount` decimal(15,5) comment '3公里范围总间夜数',
    `city_night_room_risk` decimal(15,5) comment '3公里城市间夜数占比',
    `total_night_room_count_5km` int comment '5公里范围总间夜数',
    `total_amount_5km` decimal(15,5) comment '5公里范围总间夜数',
    `city_night_room_risk_5km` decimal(15,5) comment '5公里城市间夜数占比',
    `total_night_room_count_10km` int comment '10公里范围总间夜数',
    `total_amount_10km` decimal(15,5) comment '10公里范围总间夜数',
    `city_night_room_risk_10km` decimal(15,5) comment '10公里城市间夜数占比',
    `poi_hotel_stat_3km` varchar(1024) comment 'POI周边3公里酒店统计',
    `creator`     varchar(100) not null comment '创建人',
    `create_time` datetime     not null default CURRENT_TIMESTAMP comment '创建时间',
    `modifier`    varchar(100) null comment '修改人',
    `modify_time` datetime     null on update CURRENT_TIMESTAMP comment '修改时间',
    primary key (id),
    unique udx_projectid_poiid (project_id, poi_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '项目 POI 表';

create table t_project_custom_tend_strategy
(
    `id`                      bigint         not null auto_increment comment '主键',
    `project_id`              int            not null comment '项目 ID',
    `strategy_name`           varchar(1024)  not null comment '策略名称',
    `strategy_type`           int            not null comment '策略类型: 1-是或否 2-文本',
    `display_order`           int            not null comment '排序(小到大)',
    `support_strategy_name`   int            not null comment '是否支持策略: 1-是 0-否',
    `wht_strategy_name_state` int            not null comment '是否启用权重: 1-是 0-否',
    `wht_strategy_name`       decimal(10, 2) not null comment '权重分值',
    `creator`                 varchar(100)   not null comment '创建人',
    `create_time`             datetime       not null default CURRENT_TIMESTAMP comment '创建时间',
    `modifier`                varchar(100)   null comment '修改人',
    `modify_time`             datetime       null on update CURRENT_TIMESTAMP comment '修改时间',
    primary key (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '项目自定义采购策略表';

create table t_project_hotel_tend_strategy
(
    `id`                                     bigint         not null auto_increment comment '主键',
    `project_id`                             int            not null comment '项目 ID',
    `support_vcc_pay`                        int            not null comment '酒店是否需支持 VCC 公司统一支付: 1-是 0-否',
    `support_pay_at_hotel`                   int            not null comment '酒店是否须支持员工到店付款: 1-是 0-否',
    `support_checkin_info`                   int            not null comment '酒店是否须支持提供入住明细信息: 1-是 0-否',
    `support_no_guarantee`                   int            not null comment '酒店是否须支持到店付免担保: 1-是 0-否',
    `support_pay_early_checkout`             int            not null comment '酒店是否须支持提前离店按实际入住金额收款: 1-是 0-否',
    `support_include_tax_service`            int            not null comment '报价是否需要包括税费和服务费: 1-是 0-否',
    `support_wifi`                           int            not null comment '酒店房间是否需提供免费 WIFI 服务: 1-是 0-否',
    `support_price_limit`                    int            not null comment '酒店是否全部报价金额限制范围内报价：1-是 0-否',
    `limit_min_price`                        decimal(12, 2) null comment '限制最低报价，有报价限制时必须有值',
    `limit_max_price`                        decimal(12, 2) null comment '限制最高报价，有报价限制时必须有值',
    `support_cancel`                         int            not null comment '酒店全部报价中是否支持N天M点前免费取消: 1-是 0-否',
    `support_cancel_day`                     int            null comment '免费取消限制天数',
    `support_cancel_time`                    varchar(32)    null comment '免费取消限制时间',
    `support_cancel_first_night_charge`      int            not null comment '酒店全部报价中是否支持N天M点前免费取消,之后收取首晚房费: 1-是 0-否',
    `support_cancel_day_first_night_charge`  int            null comment '免费取消限制天数(之后收取首晚房费)',
    `support_cancel_time_first_night_charge` varchar(32)    null comment '免费取消限制时间(之后收取首晚房费)',
    `support_lra`                            int            not null comment '酒店投标是否支持lra的报价: 1-是 0-否',
    `include_breakfast`                      int            null comment '酒店投标中是否有早餐的报价：1-是，0-否',
    `support_max_not_applicable_day`         int            not null comment '酒店投标是否须支持报价中产品不适用日期数总和不能超过N天: 1-是 0-否',
    `max_not_applicable_day`                 int            null comment '酒店报价中产品不适用日期数总和不能超过N天',
    `support_max_room_type_count`            int            not null comment '酒店是否支持房档最大数限制: 1-是 0-否',
    `max_room_type_count`                    int            null comment '酒店房档最大数限制',
    `support_season_day_limit`               int            not null comment '是否支持SeasonDay限制: 1-是 0-否',
    `max_season_day`                         int            null comment '最大 Season 日期数',
    `is_suggest_no_breakfast`                int            not null comment '是否建议提供无早: 1-是 0-否',
    `is_include_no_breakfast`                int            not null comment '是否必须提供无早: 1-是 0-否',
    `is_suggest_one_breakfast`               int            not null comment '是否建议提供单早: 1-是 0-否',
    `is_include_one_breakfast`               int            not null comment '是否必须提供单早: 1-是 0-否',
    `is_suggest_two_breakfast`               int            not null comment '是否提供双早: 1-是 0-否',
    `is_include_two_breakfast`               int            not null comment '是否必须包括双早: 1-是 0-否',
    `is_include_all_weekly_day`              int            not null comment '是否提供全周适用价格: 1-是 0-否',
    `creator`                                varchar(100)   not null comment '创建人',
    `create_time`                            datetime       not null default CURRENT_TIMESTAMP comment '创建时间',
    `modifier`                               varchar(100)   null comment '修改人',
    `modify_time`                            datetime       null on update CURRENT_TIMESTAMP comment '修改时间',
    primary key (id),
    unique key (project_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '项目采购策略表';

create table t_project_hotel_tend_weight
(
    `id`                           bigint         not null auto_increment comment '主键',
    `project_id`                   int            not null comment '项目 id',
    `wht_room_night`               decimal(10, 2) null comment '间夜量权重（不少于120间夜）',
    `wht_room_night_state`         int            not null default 0 comment '是否启用间夜量权重',
    `wht_room_night_ex`            decimal(10, 2) null comment '间夜量(额外加分项)权重（不少于600间夜）',
    `wht_room_night_ex_state`      int            not null comment '是否启用间夜量(额外加分项)权重',
    `wht_city`                     decimal(10, 2) null comment '城市权重',
    `wht_city_state`               int            not null comment '是否启用城市权重',
    `wht_location`                 decimal(10, 2) null comment '位置权重',
    `wht_location_state`           int            not null comment '是否启用位置权重',
    `wht_price_advantage`          decimal(10, 2) null comment '价格优势权重（低于15%）',
    `wht_price_advantage_state`    int            not null comment '是否启用价格优势权重',
    `wht_price_advantage_ex`       decimal(10, 2) null comment '价格优势(额外加分项)权重（低于25%）',
    `wht_price_advantage_ex_state` int            not null comment '是否启用价格优势(额外加分项)权重',
    `wht_ota_score`                decimal(10, 2) null comment 'ota评分权重',
    `wht_ota_score_state`          int            not null comment '是否启用ota评分权重',
    `wht_co_pay`                   decimal(10, 2) null comment '公司统一支付权重',
    `wht_co_pay_state`             int            not null comment '是否启用公司统一支付权重',
    `wht_breakfast`                decimal(10, 2) null comment '早餐权重',
    `wht_breakfast_state`          int            not null comment '是否启用早餐权重',
    `wht_lra`                      decimal(10, 2) null comment 'lra权重',
    `wht_lra_state`                int            not null comment '是否启用lra权重',
    `wht_cancel`                   decimal(10, 2) null comment '退改规则权重',
    `wht_cancel_state`             int            not null comment '是否启用退改规则权重（注意字段名拼写一致性）',
    `wht_total_weight`             decimal(10, 2) not null comment '总权重(满分)，各项启用权重总和',
    `creator`                      varchar(100)   not null comment '创建人',
    `create_time`                  datetime       not null default current_timestamp comment '创建时间',
    `modifier`                     varchar(100)   null comment '修改人',
    `modify_time`                  datetime       null on update current_timestamp comment '修改时间',
    primary key (id),
    unique key (project_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '项目权重配置表';


CREATE INDEX index_name_en_us ON `t_country` (`name_en_us`);
CREATE INDEX index_name_zh_cn ON `t_country` (`name_zh_cn`);
CREATE INDEX index_name_en_us ON `t_province` (`name_en_us`);
CREATE INDEX index_name_zh_cn ON `t_province` (`name_zh_cn`);
CREATE INDEX index_name_en_us ON `t_city` (`name_en_us`);
CREATE INDEX index_name_zh_cn ON `t_city` (`name_zh_cn`);
CREATE INDEX index_name_en_us ON `t_hotel_group` (`name_en_us`);
CREATE INDEX index_name_zh_cn ON `t_hotel_group` (`name_zh_cn`);
CREATE INDEX index_name_en_us ON `t_hotel_brand` (`name_en_us`);
CREATE INDEX index_name_zh_cn ON `t_hotel_brand` (`name_zh_cn`);
CREATE INDEX index_name_en_us ON `t_hotel` (`name_en_us`);
CREATE INDEX index_name_zh_cn ON `t_hotel` (`name_zh_cn`);

CREATE TABLE `t_project_intent_hotel`(
    `project_intent_hotel_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '项目酒店意向ID',
    `project_id` int(11) NOT NULL  COMMENT '项目ID',
    `hotel_id` bigint NOT NULL  COMMENT '酒店ID',
    `hotel_org_id` int(11) NOT NULL  COMMENT '酒店机构ID',
    `hotel_contact_uid` int(11)  COMMENT '酒店指派销售跟进人id(招投标项目时指派的)',
    `hotel_contact_name` varchar(100)  COMMENT '酒店指派销售跟进人姓名(招投标项目时指派的)',
    `distributor_contact_uid` int(11)  COMMENT '企业(分销商)跟进人电话(招投标项目时指派的)',
    `distributor_contact_name` varchar(100)  COMMENT '企业(分销商)跟进人电话(招投标项目时指派的)',
    `platform_contact_uid` int(11)  COMMENT '平台跟进人id',
    `platform_contact_name` varchar(100)  COMMENT '平台跟进人姓名',
    `hotel_sales_contact_name` varchar(100)  COMMENT '酒店销售联系人姓名(默认取推荐酒店表数据)',
    `hotel_sales_contact_mobile` varchar(100) COMMENT '酒店销售联系人电话',
    `hotel_sales_contact_email` varchar(100)  COMMENT '酒店销售联系人电邮',
    `hotel_bid_contact_name` varchar(100)  COMMENT '酒店报价联系人',
    `hotel_bid_contact_mobile` varchar(100) COMMENT '酒店报价联系人手机号码',
    `hotel_bid_contact_email` varchar(100)  COMMENT '酒店报价联系人电邮',
    `hotel_price_follow_name` varchar(100)  COMMENT '平台报价跟进人姓名',
    `online_follow_name` varchar(100)  COMMENT '平台线上跟进人姓名',
    `monitor_follow_name` varchar(100)  COMMENT '平台履约跟进人姓名',
    `hotel_group_bid_contact_name` varchar(100) COMMENT '酒店报价联系人',
    `hotel_group_bid_contact_mobile` varchar(100) COMMENT '酒店报价联系人手机号码',
    `hotel_group_bid_contact_email` varchar(100) COMMENT '酒店报价联系人电邮',
    `send_mail_status` int(1) NOT NULL DEFAULT 0 COMMENT '邀约邮件发送状态(0：未发送邀约邮件，1：已发送邀约邮件)',
    `invite_status` int(1) NOT NULL DEFAULT 0 COMMENT '邀约状态(0：未邀请，1：已邀请)，企业主动邀请的记录默认就是已邀请，非企业邀请的酒店指派销售人时默认就是未邀请',
    `last_invite_time` datetime  COMMENT '最近一次邀约时间',
    `bid_state` int(1) NOT NULL DEFAULT 0 COMMENT '标书状态(0：未投标，1：新标(酒店提交报价，企业还未处理)，2：议价中，3：已中标，4：已否决，5：放弃报价)',
    `last_year_room_night` int(11)  COMMENT '近一年采购间夜数',
    `tender_avg_price` decimal(12,2)  COMMENT '采购均价',
    `currency_code`   varchar(100)       comment '币种',
    `bid_weight` decimal(12,2)  COMMENT '投标总权重(投标时根据项目权重和酒店投标策略计算生成)',
    `hotel_service_points` decimal(6,2) NOT NULL DEFAULT 100 COMMENT '酒店服务分',
    `is_upload` int(1) NOT NULL DEFAULT 0 COMMENT '是否上传报价(0：否，1：是)',
    `bid_upload_source` int(1)  COMMENT '报价导入源数据 1:加力,2:Lanyon',
    `bid_org_id` int(11)  COMMENT '报价机构ID',
    `bid_org_type` int(1)  COMMENT '报价机构类型 2:酒店，4:酒店集团',
    `hotel_group_approve_status` int(1) COMMENT '酒店既然审核状态 0:不需要审核，1:等待审核，2:审核通过，3:审核未通过',
    `hotel_group_reject_approve_reason` varchar(1000) COMMENT '酒店集团拒绝原因',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `employee_right`  varchar(1024) COMMENT '员工权益',
    `employee_right_file_url`  varchar(1024) COMMENT '员工权益上传文件',
    `creator`                      varchar(100)   not null comment '创建人',
    `create_time`                  datetime       not null default current_timestamp comment '创建时间',
    `modifier`                     varchar(100)   null comment '修改人',
    `modify_time`                  datetime       null on update current_timestamp comment '修改时间',
    `notify_status` int(1)  null comment '通知状态 1:已经通知, 0为待通知，null不通知',
    `notify_operator`                      varchar(100)    null comment '通知人',
    `notify_time`                  datetime        null comment '通知时间',
    primary key (`project_intent_hotel_id`),
    unique key (`project_id`, `hotel_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '项目酒店意向表';


create table `t_project_intent_hotel_group`(
       `project_intent_hotel_group_id` int(11) not null auto_increment comment '意向酒店集团ID',
       `project_id`                     int(11) not null comment '项目ID',
       `hotel_group_org_id`                     int(11) default not null comment '酒店集团机构ID',
       `hotel_group_contact_uid`               int(11) default null comment '酒店集团指派销售跟进人id(招投标项目时指派的)',
       `hotel_group_contact_name`               int(11) default null comment '酒店集团指派销售跟进人姓名(招投标项目时指派的)',
       `hotel_group_bid_contact_name`       varchar(100)           default null comment '酒店集团销售联系人姓名',
       `hotel_group_bid_contact_mobile`      varchar(100)           default null comment '酒店集团销售联系人电话',
       `hotel_group_bid_contact_email`       varchar(100)           default null comment '酒店集团销售联系人邮箱',
       `invite_send_email_status`       int(1) not null comment '邀约邮件发送状态(0：未发送邀约邮件，1：已发送邀约邮件)',
       `is_brand_limit` int(1) not null comment '邀约邮件发送状态(0：未发送邀约邮件，1：已发送邀约邮件)',
       `is_open_group_approve` int(1)  default 0 not null comment '是否开启集团审核 1:开启，0:开启',
       `is_active` int(1)  not null default 1 comment '状态(1：有效，0：无效)',
       `support_pay_at_hotel`           int            comment '酒店是否须支持员工到店付款: 1-是 0-否',
       `support_vcc_pay`                int            comment '酒店是否需支持 VCC 公司统一支付: 1-是 0-否',
       `support_checkin_info`           int            comment '酒店是否须支持提供入住明细信息: 1-是 0-否',
       `support_no_guarantee`           int            comment '酒店是否须支持到店付免担保: 1-是 0-否',
       `support_include_tax_service`    int           comment '报价是否需要包括税费和服务费: 1-是 0-否',
       `support_wifi`                   int            comment '酒店房间是否需提供免费 WIFI 服务: 1-是 0-否',
       `is_include_breakfast`           int            comment '是否包含早餐: 1-是 0-否',
       `currency_code`                 varchar(100)       comment '币种',
       `late_reserve_time`                 varchar(100)   comment '到店付免担保最晚保留时间',
       `do_after_late_reserve_time`    int           comment '超出最晚保留时间采取措施：1-酒店直接与入住人或预订联系人联系，确认入住时间；0-取消订单',
       `support_cancel_day`             int            null comment '免费取消限制天数',
       `support_cancel_time`            varchar(32)    null comment '免费取消限制时间',
       `has_commission`             int            null comment '有无佣金 1-有 0-否',
       `commission`             decimal(10,2)            null comment '佣金比例',
       `creator`                        varchar(100)  NOT NULL COMMENT '创建人',
       `create_time`                    DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       `modifier`                       varchar(100)  NOT NULL COMMENT '修改人',
       `modify_time`                    DATETIME               DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
       primary key (`project_intent_hotel_group_id`),
       unique key `uk_project_id_hotel_group_id` (`project_id`, `hotel_group_org_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '项目酒店集团默认报价策略';

create table t_project_hotel_white
(
    `id`               bigint       not null auto_increment comment '主键',
    `project_id`       int          not null comment '项目 id',
    `hotel_id`         bigint       not null comment '酒店 id',
    `hotel_white_type` int          not null comment '白名单类型: 1-报价免控白名单 2-周末节假日免履约监控白名单 3-仅每周一履约监控白名单',
    `creator`          varchar(100) not null comment '创建人',
    `create_time`      datetime     not null default current_timestamp comment '创建时间',
    `modifier`         varchar(100) null comment '修改人',
    `modify_time`      datetime     null on update current_timestamp comment '修改时间',
    primary key (id),
    unique key (project_id, hotel_id, hotel_white_type)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '项目酒店白名单表';



create table t_recommend_hotel
(
    `hotel_id`         bigint       not null comment '酒店ID',
    `breakfast_num`       int          not null comment '早餐数量',
    `reference_price`   decimal(12, 2)   not null comment '签约参考价',
    `recommend_score`  int        comment '酒店推荐值',
    `bright_spot`  varchar(256)        comment '推荐亮点',
    `contact_name`  varchar(256)        comment '酒店联系人',
    `contact_mobile`  varchar(256)        comment '酒店联系手机号码',
    `contact_email`  varchar(256)        comment '酒店联系电邮',
    `last_room_available` int(1)         comment '是否可以预订酒店最后一间未售出的客房(1:是，0：否)',
    `required_room_night` varchar(256) comment '签约要求月均采购间夜数',
    `platform_contact_uid` int(11) comment '平台跟进人',
    `platform_contact_name` varchar(256) comment '平台运营跟进人姓名',
    `state` int(1) not null comment '状态(1：有效，0：无效)',
    `creator`          varchar(100) not null comment '创建人',
    `create_time`      datetime     not null default current_timestamp comment '创建时间',
    `modifier`         varchar(100) null comment '修改人',
    `modify_time`      datetime     null on update current_timestamp comment '修改时间',
    primary key (hotel_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '酒店推荐表';



create table t_project_invite_hotel
(
    `project_id`         int       not null comment '项目ID',
    `hotel_id`       bigint          not null comment '酒店ID',
    `creator`          varchar(100) not null comment '创建人',
    `create_time`      datetime     not null default current_timestamp comment '创建时间',
    `modifier`         varchar(100) null comment '修改人',
    `modify_time`      datetime     null on update current_timestamp comment '修改时间',
    primary key (`project_id`,`hotel_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '项目邀请酒店';

create table t_project_hotel_history_data
(
    `id`                     bigint         not null auto_increment comment '主键',
    `project_id`             int            not null comment '项目 id',
    `hotel_id`               bigint         not null comment '酒店 id',
    `city_code`              varchar(128)   null comment '城市编码',
    `city_order`             int            null comment '城市排名',
    `saved_amount`           decimal(12, 4) null     default 0 comment '节省金额',
    `saved_amount_rate`      decimal(12, 2) null     default 0 comment '节省率',
    `poi_id`                 bigint         null     default 0 comment '酒店同城最近 POI ID',
    `poi_distance`           decimal(12, 4) null     default 0 comment '酒店距离 POI 距离',
    `total_violations_count` int            null comment '去年总违规数',
    `service_point`          decimal(12, 4) null comment '去年服务分',
    `ota_min_price`          decimal(12, 4) null comment 'OTA最低价格',
    `ota_max_price`          decimal(12, 4) null comment 'OTA最高价格',
    `lowest_price`           decimal(12, 4) null comment '商旅价格',
    `adjust_lowest_price`    decimal(12, 4) null comment '签约混合价',
    `lowest_price_item_info` varchar(3000)  null comment '差旅价格信息',
    `lowest_price_date`      date           null comment '商旅价格生成日期',
    `min_max_ota_price_date` date           null comment 'OTA最低最高价格生成日期',
    `last_stat_reference_no` varchar(128)   null comment '最近统计参考编号',
    `price_level_room_night` int            null comment '周边3公里同价位酒店',
    `room_night_count`       int            not null comment '成交间夜数',
    `total_amount`           decimal(12, 4) not null comment '成交金额',
    `recommend_level`        varchar(32)    null comment '推荐等级: 1-SSS 2-SS 3-S 4-A 5-B 6-C',
    `is_uploaded`            int            not null default 1 comment '是否上传: 1-是 0-否',
    `creator`                varchar(100)   not null comment '创建人',
    `create_time`            datetime       not null default current_timestamp comment '创建时间',
    `modifier`               varchar(100)   null comment '修改人',
    `modify_time`            datetime       null on update current_timestamp comment '修改时间',
    primary key (id),
    unique key `udx_project_hotel` (project_id, hotel_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '项目酒店历史交易表';

create table `t_hotel_group_default_applicable_day`(
    `default_applicable_day_id` int not null auto_increment comment '默认适用日期ID',
    `project_intent_hotel_group_id` int not null comment '酒店集团意向ID',
    `project_id` int not null  comment '项目ID',
    `price_type` int(2) not null comment '价格类型 1:协议价,2:Season1,3:Season2',
    `start_date` date not null comment '开始时间',
    `end_date` date not null comment '结束时间',
    `creator`                varchar(100)   not null comment '创建人',
    `create_time`            datetime       not null default current_timestamp comment '创建时间',
    `modifier`               varchar(100)   null comment '修改人',
    `modify_time`            datetime       null on update current_timestamp comment '修改时间',
    primary key (default_applicable_day_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '项目意向酒店集团默认适用日期';


create table `t_hotel_group_default_unapplicable_day`(
       `default_unapplicable_day_id` int not null auto_increment comment '默认适用日期ID',
       `project_intent_hotel_group_id` int not null comment '酒店集团意向ID',
       `project_id` int not null  comment '项目ID',
       `start_date` date not null comment '开始时间',
       `end_date` date not null comment '结束时间',
       `creator`                varchar(100)   not null comment '创建人',
       `create_time`            datetime       not null default current_timestamp comment '创建时间',
       `modifier`               varchar(100)   null comment '修改人',
       `modify_time`            datetime       null on update current_timestamp comment '修改时间',
       primary key (default_unapplicable_day_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '项目意向酒店集团默认不适用日期';

create table `t_h_group_default_cus_strategy` (
    `custom_tend_strategy_id`  int not null comment '自定义策略ID',
    `project_intent_hotel_group_id` int not null comment '项目意向酒店集团ID',
    `project_id` int not null comment '项目ID',
    `strategy_type` int not null comment '策略类型: 1-是或否 2-文本',
    `strategy_name` varchar(200) not null comment '策略名称',
    `support_strategy_name`   int  comment '是否支持策略: 1-是 0-否',
    `support_strategy_text` varchar(512) comment '策略回复文本',
    `creator`                varchar(100)   not null comment '创建人',
    `create_time`            datetime       not null default current_timestamp comment '创建时间',
    `modifier`               varchar(100)   null comment '修改人',
    `modify_time`            datetime       null on update current_timestamp comment '修改时间',
    primary key (custom_tend_strategy_id,project_intent_hotel_group_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '酒店集团默认报价自定义策略';

CREATE TABLE `t_project_last_year_city_stat`(
    `project_id` int not null comment '项目ID',
    `city_code` varchar(100) not null comment '城市编号',
    `total_room_night` int not null  comment '总间夜数',
    `total_sales_amount` decimal(15,2) not null comment '总销售金额',
    `total_room_night_500` int not null comment '总间夜数',
    `total_room_night_400_to_500` int not null comment '400-500 间夜',
    `total_room_night_300_to_400` int not null comment '300-400 间夜',
    `total_room_night_200_to_300` int not null comment '200-300 间夜',
    `total_room_night_200` int not null comment '200以内间夜',
    `creator`                varchar(100)   not null comment '创建人',
    `create_time`            datetime       not null default current_timestamp comment '创建时间',
    `modifier`               varchar(100)   null comment '修改人',
    `modify_time`            datetime       null on update current_timestamp comment '修改时间',
    primary key  (project_id ASC, city_code ASC)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '项目去年城市统计表';


CREATE TABLE `t_price_applicable_room`(
        `price_applicable_room_id` int not null auto_increment comment '价格可用房型ID',
        `project_intent_hotel_id` int not null comment '项目意向酒店ID',
        `project_id` int not null  comment '项目ID',
        `hotel_id` bigint not null comment '酒店ID',
        `hotel_price_level_id` int not null comment '价格房档ID',
        `room_type_id` bigint not null comment '房型ID',
        `display_order` int  comment '排序',
        `creator`                varchar(100)   not null comment '创建人',
        `create_time`            datetime       not null default current_timestamp comment '创建时间',
        `modifier`               varchar(100)   null comment '修改人',
        `modify_time`            datetime       null on update current_timestamp comment '修改时间',
        primary key  (project_intent_hotel_id ASC, hotel_id ASC, room_type_id ASC),
        unique key (price_applicable_room_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '价格可用房型';

ALTER TABLE `t_hotel` ADD INDEX `index_city_code`(`city_code`) USING BTREE;

create table t_project_custom_bid_strategy
(
    `custom_tend_strategy_id` int           not null comment '项目自定义采购策略id',
    `project_intent_hotel_id` int           not null comment '项目意向酒店id',
    `hotel_id`                bigint        not null comment '酒店id',
    `project_id`              int           not null comment '项目id',
    `strategy_name`           varchar(1024) not null comment '策略名称',
    `strategy_type`           int           not null comment '策略回类型：1：是或否,2:文本',
    `support_strategy_name`   int(1) comment '是否支持策略，1-是，0-否',
    `support_strategy_text`   varchar(512) comment '支持策略文本',
    `creator`                 varchar(100)  not null comment '创建人',
    `create_time`             datetime      not null default current_timestamp comment '创建时间',
    `modifier`                varchar(100)  null comment '修改人',
    `modify_time`             datetime      null on update current_timestamp comment '修改时间',
    primary key (project_id ASC, hotel_id ASC, custom_tend_strategy_id ASC)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '项目自定义报价策略';

create table t_price_applicable_day
(
    `price_applicable_day_id` int          not null auto_increment comment '项目可用日期ID',
    `project_intent_hotel_id` int          not null comment '项目意向酒店ID',
    `project_id`              int          not null comment '项目id',
    `hotel_id`                bigint       not null comment '酒店id',
    `price_type` int(1) not null comment '价格类型 1:协议价,2:Season1,3:Season2',
    `start_date` date not null comment '开始时间',
    `end_date` date not null comment '结束时间',
    `creator`                 varchar(100) not null comment '创建人',
    `create_time`             datetime     not null default current_timestamp comment '创建时间',
    `modifier`                varchar(100) null comment '修改人',
    `modify_time`             datetime     null on update current_timestamp comment '修改时间',
    primary key  (price_applicable_day_id ASC)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '价格可用日期';

create table t_price_unapplicable_day
(
    `price_unapplicable_day_id` int          not null auto_increment comment '项目不可以日期ID',
    `project_intent_hotel_id` int          not null comment '项目意向酒店ID',
    `project_id`              int          not null comment '项目id',
    `hotel_id`                bigint       not null comment '酒店id',
    `start_date` date not null comment '开始时间',
    `end_date` date not null comment '结束时间',
    `creator`                 varchar(100) not null comment '创建人',
    `create_time`             datetime     not null default current_timestamp comment '创建时间',
    `modifier`                varchar(100) null comment '修改人',
    `modify_time`             datetime     null on update current_timestamp comment '修改时间',
    primary key  (price_unapplicable_day_id ASC)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment '价格不可用日期';



CREATE TABLE `t_dis_hotel_daily_order`
(
    `id`                         bigint      not null auto_increment comment '主键',
    `distributor_code`           VARCHAR(32) NOT NULL COMMENT '分销商编码',
    `hotel_id`                   BIGINT      NOT NULL COMMENT '酒店ID',
    `city_code`                  VARCHAR(64)    DEFAULT NULL COMMENT '城市编码',
    `book_date`                  DATE        NOT NULL COMMENT '预订日期',
    `book_count`                 INT            DEFAULT NULL COMMENT '订单数量',
    `room_night_count`           INT            DEFAULT NULL COMMENT '间夜数量',
    `order_amount`               DECIMAL(15, 4) DEFAULT NULL COMMENT '订单金额',
    `saved_amount`               DECIMAL(15, 4) DEFAULT '0' COMMENT '节省金额',
    `hotel_group`                BIGINT         DEFAULT '0' COMMENT '酒店集团',
    `saved_room_night_count`     INT            DEFAULT '0' COMMENT '有节省对比数据的订单间夜数',
    `has_ota_price_order_amount` DECIMAL(15, 2) DEFAULT NULL COMMENT '有OTA价格的总预订金额',
    `total_ota_price`            DECIMAL(15, 2) DEFAULT NULL COMMENT 'OTA的总价格',
    `saved_amount_order_count`   INT            DEFAULT NULL COMMENT '有节省金额的订单数量',
    `creator`                    VARCHAR(64)    DEFAULT NULL COMMENT '创建人',
    `create_time`                DATETIME       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`                   VARCHAR(64)    DEFAULT NULL COMMENT '修改人',
    `modify_time`                DATETIME       DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    primary key (`id`),
    unique key `udx_distributor_hotel_book` (`distributor_code`, `hotel_id`, `book_date`),
    KEY `idx_book_date` (`book_date`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='分销商酒店每日订单统计表';

CREATE TABLE `t_order_monitor_config`
(
    `id`               bigint      not null auto_increment comment '主键',
    `org_id`           BIGINT      NOT NULL COMMENT '机构id',
    `distributor_code` VARCHAR(16) NOT NULL COMMENT '分销商编码',
    `distributor_name` VARCHAR(128) DEFAULT NULL COMMENT '分销商名称',
    `state`            TINYINT     NOT NULL COMMENT '有效状态：0-无效，1-有效',
    `creator`          VARCHAR(64)  DEFAULT NULL COMMENT '创建人',
    `create_time`      DATETIME     DEFAULT NULL COMMENT '创建时间',
    `modifier`         VARCHAR(64)  DEFAULT NULL COMMENT '修改人',
    `modify_time`      DATETIME     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    primary key (`id`),
    unique KEY `udx_org_distributor` (`org_id`, `distributor_code`),
    KEY `idx_org` (`org_id`),
    KEY `idx_state` (`state`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='订单监控配置';

CREATE TABLE `t_hotel_hexagon_lng_lat`
(
    `hotel_id`           BIGINT          NOT NULL PRIMARY KEY COMMENT '酒店ID',
    `lng_google`         DECIMAL(15, 10) NOT NULL COMMENT '谷歌经度',
    `lat_google`         DECIMAL(15, 10) NOT NULL COMMENT '谷歌纬度',
    `hexagon_lng_lat_10` LONGTEXT        NOT NULL COMMENT '酒店10公里范围六边形坐标',
    `creator`            VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    `create_time`        DATETIME    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`           VARCHAR(64) DEFAULT NULL COMMENT '修改人',
    `modify_time`        DATETIME    DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT = '酒店六边形坐标';

create table t_project_hotel_bid_strategy
(
    `project_intent_hotel_id`     int            not null comment '项目意向酒店ID',
    `project_id`                  int            not null comment '项目id',
    `hotel_id`                    bigint         not null comment '酒店id',
    `support_pay_at_hotel`        int comment '酒店是否须支持员工到店付款: 1-是 0-否',
    `support_vcc_pay`             int comment '酒店是否需支持 VCC 公司统一支付: 1-是 0-否',
    `support_checkin_info`        int comment '酒店是否须支持提供入住明细信息: 1-是 0-否',
    `support_no_guarantee`        int comment '酒店是否须支持到店付免担保: 1-是 0-否',
    `support_include_tax_service` int comment '报价是否需要包括税费和服务费: 1-是 0-否',
    `support_wifi`                int comment '酒店房间是否需提供免费 WIFI 服务: 1-是 0-否',
    `is_include_breakfast`        int comment '是否包含早餐: 1-是 0-否',
    `currency_code`               varchar(100) comment '币种',
    `late_reserve_time`           varchar(100) comment '到店付免担保最晚保留时间',
    `do_after_late_reserve_time`  int comment '超出最晚保留时间采取措施：1-酒店直接与入住人或预订联系人联系，确认入住时间；0-取消订单',
    `support_cancel_day`          int            null comment '免费取消限制天数',
    `support_cancel_time`         varchar(32)    null comment '免费取消限制时间',
    `has_commission`              int            null comment '有无佣金 1-有 0-否',
    `commission`                  decimal(10, 2) null comment '佣金比例',
    `creator`                     VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    `create_time`                 DATETIME    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`                    VARCHAR(64) DEFAULT NULL COMMENT '修改人',
    `modify_time`                 DATETIME    DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    primary key (`project_intent_hotel_id`),
    unique key (`project_id`,`hotel_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT = '项目酒店报价策略';


create table t_project_hotel_tax_settings
(
    `project_intent_hotel_id`     int            not null comment '项目意向酒店ID',
    `project_id`                  int            not null comment '项目id',
    `hotel_id`                    bigint         not null comment '酒店id',
    `earlyck_fee_type`        int comment '提前入住税计算方式 1:百分比，2:固定值',
    `earlyck_fee_value`             decimal(10,5) comment '提前入住税',
    `lodgtx_fee_type`        int comment '入住税计算方式 1:百分比，2:固定值',
    `lodgtx_fee_value`             decimal(10,5) comment '入住税',
    `statetx_fee_type`        int comment '国家税 计算方式 1:百分比，2:固定值',
    `statetx_fee_value`             decimal(10,5) comment '国家税 ',
    `citytx_fee_type`        int comment '城市税 计算方式 1:百分比，2:固定值',
    `citytx_fee_value`             decimal(10,5) comment '城市税 ',
    `vatgstrm_fee_type`        int comment '客房增值税 计算方式 1:百分比，2:固定值',
    `vatgstrm_fee_value`             decimal(10,5) comment '客房增值税 ',
    `vatgstrm_fee_is_include`        int comment '是否包含客房增值税',
    `vatgstfb_fee_type`        int comment '餐饮增值税 计算方式 1:百分比，2:固定值',
    `vatgstfb_fee_value`             decimal(10,5) comment '餐饮增值税 ',
    `vatgstfb_fee_is_include`        int comment '是否包含餐饮增值税',
    `service_fee_type`        int comment '服务费 计算方式 1:百分比，2:固定值',
    `service_fee_value`             decimal(10,5) comment '服务费 ',
    `service_fee_is_include`        int comment '是否包含服务费',
    `occ_fee_type`        int comment '占用费 计算方式 1:百分比，2:固定值',
    `occ_fee_value`             decimal(10,5) comment '占用费 ',
    `occ_fee_is_include`        int comment '是否包占用费',
    `othertx1_fee_type`        int comment '其他税费1 计算方式 1:百分比，2:固定值',
    `othertx1_fee_desc`             varchar(256) comment '其他税费1描述 ',
    `othertx1_fee_value`             decimal(10,5) comment '其他税费1 ',
    `othertx1_fee_is_include`        int comment '是否包其他税费1',
    `othertx2_fee_type`        int comment '其他税费2 计算方式 1:百分比，2:固定值',
    `othertx2_fee_desc`             varchar(256) comment '其他税费2描述 ',
    `othertx2_fee_value`             decimal(10,5) comment '其他税费2',
    `othertx2_fee_is_include`        int comment '是否包其他税费2',
    `othertx3_fee_type`        int comment '其他税费3 计算方式 1:百分比，2:固定值',
    `othertx3_fee_desc`             varchar(256) comment '其他税费3描述 ',
    `othertx3_fee_value`             decimal(10,5) comment '其他税费3',
    `othertx3_fee_is_include`        int comment '是否包其他税费3',
    `creator`                     VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    `create_time`                 DATETIME    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`                    VARCHAR(64) DEFAULT NULL COMMENT '修改人',
    `modify_time`                 DATETIME    DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    primary key (`project_intent_hotel_id`),
    unique key (`project_id`,`hotel_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
 COMMENT = '项目酒店报价税费设定';


create table t_project_hotel_price_level
(	`hotel_price_level_id` int not null auto_increment comment '项目酒店价格档次ID',
     `project_intent_hotel_id` int not null comment '项目酒店意向ID',
     `project_id`  int not null comment '项目ID',
     `hotel_id` bigint not null comment '酒店ID',
     `room_level_no` int not null comment '房档编号 1,2,3,4',
     `big_bed_room_count` int comment '大床房数量',
     `double_bed_room_count` int comment '双床房数量',
     `total_room_count` int comment '总房数量',
     `creator`                     VARCHAR(64) DEFAULT NULL COMMENT '创建人',
     `create_time`                 DATETIME    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `modifier`                    VARCHAR(64) DEFAULT NULL COMMENT '修改人',
     `modify_time`                 DATETIME    DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
     primary key (`hotel_price_level_id`),
     unique key (`project_intent_hotel_id`,`room_level_no`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT = '项目酒店价格档次';

CREATE INDEX  `idx_u_project_hotel` ON t_project_hotel_price_level(project_id, hotel_id,room_level_no);

create table t_project_hotel_price_group
(	`hotel_price_group_id` int not null auto_increment comment '项目酒店价格组ID',
    `project_intent_hotel_id` int not null comment '项目酒店意向ID',
     `project_id`  int not null comment '项目ID',
     `hotel_id` bigint not null comment '酒店ID',
     `hotel_price_level_id` int not null comment '项目酒店价格档次ID',
     `applicable_weeks` varchar(16) not null comment '适用星期，按国际规范，星期天是第1天',
     `lra` int(1) comment 'lra承诺：1-是，0-否',
     `remark` varchar(1024) comment '备注，最多500汉字',
     `is_locked` int(1) comment '是否被锁定',
     `creator`                     VARCHAR(64) DEFAULT NULL COMMENT '创建人',
     `create_time`                 DATETIME    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `modifier`                    VARCHAR(64) DEFAULT NULL COMMENT '修改人',
     `modify_time`                 DATETIME    DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
     primary key (`hotel_price_group_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  COMMENT = '项目酒店价格组';

CREATE INDEX  `idx_project_hotel` ON t_project_hotel_price_group(project_id, hotel_id);
CREATE INDEX  `idx_project_intent_hotel` ON t_project_hotel_price_group(project_intent_hotel_id);

create table t_project_hotel_price
(	`hotel_price_id` int not null auto_increment comment '项目酒店价格ID',
     `project_intent_hotel_id` int not null comment '项目酒店意向ID',
     `project_id`  int not null comment '项目ID',
     `hotel_id` bigint not null comment '酒店ID',
     `hotel_price_level_id` int not null comment '项目酒店价格档次ID',
     `hotel_price_group_id` int not null comment '项目酒店价格组ID',
     `price_type` int(1) comment '价格类型 1:协议价,2:Season1价,3:Season2价',
     `one_person_price` decimal(15,2) comment '单人价格',
     `two_person_price` decimal(15,2) comment '双人价格',
     `last_one_person_price` decimal(15,2) comment '单人价格 (最近议价价格)',
     `last_two_person_price` decimal(15,2) comment '双人价格 (最近议价价格)',
     `creator`                     VARCHAR(64) DEFAULT NULL COMMENT '创建人',
     `create_time`                 DATETIME    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `modifier`                    VARCHAR(64) DEFAULT NULL COMMENT '修改人',
     `modify_time`                 DATETIME    DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
     primary key (`hotel_price_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  COMMENT = '项目酒店价格';
CREATE UNIQUE INDEX  `idx_u_price_group_type` ON t_project_hotel_price(hotel_price_group_id, price_type);
CREATE INDEX  `idx_project_hotel` ON t_project_hotel_price(project_id, hotel_id);
CREATE INDEX  `idx_project_intent_hotel` ON t_project_hotel_price(project_intent_hotel_id);

CREATE TABLE t_bid_operate_log(
    `bid_operate_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '报价操作日志ID',
    `project_intent_hotel_id` int(11) NOT NULL COMMENT '项目酒店意向ID',
    `project_id` int(11) NOT NULL COMMENT '项目ID',
    `hotel_id` bigint NOT NULL COMMENT '酒店ID',
    `org_type_id` int(11) NOT NULL COMMENT '机构类型ID',
    `operate_content` varchar(2048) NOT NULL COMMENT '操作内容',
    `operate_time` DATETIME NOT NULL COMMENT '操作内容',
    `creator`                     VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    `create_time`                 DATETIME    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    primary key (`bid_operate_log_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  COMMENT = '报价操作日志';

CREATE TABLE t_project_hotel_bid_temp_info(
    `project_intent_hotel_id` int(11) NOT NULL COMMENT '项目酒店意向ID',
    `bid_info_json` LONGBLOB NOT NULL COMMENT '报价信息JSON',
    `creator`                     VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    `create_time`                 DATETIME    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`                    VARCHAR(64) DEFAULT NULL COMMENT '修改人',
    `modify_time`                 DATETIME    DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    primary key (`project_intent_hotel_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  COMMENT = '项目意向报价临时信息';

CREATE TABLE t_project_recommend_stat_log
(
    project_id        int NOT NULL COMMENT '项目ID',
    stat_reference_no VARCHAR(128) COMMENT '统计参考编号',
    stat_name         VARCHAR(128) COMMENT '统计名称',
    begin_time        datetime COMMENT '统计开始时间',
    end_time          datetime COMMENT '统计结束时间',
    is_finished       int COMMENT '是否结束',
    result            int COMMENT '统计结果',
    result_msg        VARCHAR(1024) COMMENT '统计结果信息',
    creator           VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    create_time       DATETIME    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT ='项目历史数据推荐统计日志';

CREATE INDEX idx_project_id ON t_project_recommend_stat_log (project_id);

CREATE TABLE t_hotel_violations_monitor
(
    violations_monitor_id        bigint           not null comment '违规监控id',
    hotel_id                     bigint           not null comment '酒店id',
    project_id                   int           not null comment '项目id',
    violation_item               varchar(1024) not null comment '违规项',
    violation_type               tinyint       not null comment '违规类型 1：报价监控；2：订单监控',
    penalty_score                decimal(6, 2) not null comment '扣除分数',
    send_time                    datetime comment '发送时间',
    hotel_reply_message          varchar(1024) comment '酒店回复信息',
    hotel_reply_date             datetime comment '酒店回复时间',
    status                       int       not null comment '处理状态 0待解决；1已解决',
    processing_info              varchar(1024) comment '处理信息',
    processing_date              datetime comment '处理时间',
    creator                      varchar(64) comment '创建人',
    create_time                  datetime comment '创建时间',
    modifier                     varchar(64) comment '修改人',
    modify_time                  datetime comment '修改时间',
    procurement_volume           int comment '间夜数量',
    reminder_level               varchar(10) comment '提醒等级 0：系统提醒；1：重点提醒；2：加力跟进；3：企业跟进',
    hotel_service_points         decimal(6, 2) not null comment '酒店服务分',
    handler                      varchar(64) comment '处理人',
    hotel_reply                  varchar(64) comment '酒店回复人',
    order_monitor_time           varchar(64) comment '订单监控时间范围',
    send_status                  int comment '发送状态 0未发送:1已发送',
    full_penalty_score           int       default 0 comment '满房扣分',
    up_penalty_score             int       default 0 comment '涨价扣分',
    is_payback_score             int       default 0 comment '是否补偿',
    payback_score                decimal(6, 2) default 0 comment '补偿分数',
    send_payback_status          tinyint       default 0 comment '发送补偿电邮短信状态（0:不需要发送，1：初始化, 2: 发送中，3: 发送完成）',
    violations_id                int           default 0 comment '违规id',
    room_type_id                 int comment '违规房型id',
    breakfast_num                smallint comment '违规早餐类型',
    total_violation_day_count    smallint      default 0 comment '总违规天数',
    restore_price_day_count      smallint      default 0 comment '恢复原价天数',
    the_same_level_day_count     smallint      default 0 comment '同档天数',
    client_room_closed_day_count smallint      default 0 comment '关闭c端房态天数',
    primary key (violations_monitor_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='酒店违规监控表';

-- 创建索引
create index query_violations_monitor_idx on t_hotel_violations_monitor (hotel_id, project_id);
create index idx_vm_create_time on t_hotel_violations_monitor (create_time);

CREATE TABLE t_ota_hotel_daily_min_price
(
    hotel_id      bigint not null comment '酒店id',
    sales_date    date   not null comment '销售日期',
    min_price     decimal(15, 2) comment '最低价格',
    `creator`     VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    `create_time` DATETIME    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`    VARCHAR(64) DEFAULT NULL COMMENT '修改人',
    `modify_time` DATETIME    DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='酒店每日OTA价格';

create unique index udx_hotel_sales_date
    on t_ota_hotel_daily_min_price (hotel_id, sales_date);

CREATE TABLE `t_project_history_recommend`
(
    `project_id`                     INT NOT NULL COMMENT '项目ID',
    `hotel_id`                       BIGINT NOT NULL COMMENT '酒店ID',
    `is_frequency`                   int    NOT NULL DEFAULT 0 COMMENT '是否高频预订 1:是 0:不是',
    `is_frequency_recommend`         int    NOT NULL DEFAULT 0 COMMENT '是否高频预订推荐 1:是 0:不是',
    `frequency_recommends`           VARCHAR(64) COMMENT '是否高频预订推荐 1:高产酒店 2:重点高产酒店 3:超高产酒店',
    `is_same_level_freq`             int    NOT NULL DEFAULT 0 COMMENT '是否高频预订同档 1:是 0:不是',
    `is_same_level_freq_recommend`   int    NOT NULL DEFAULT 0 COMMENT '是否高频预订同档推荐 1:是 0:不是',
    `same_level_freq_hotel_id`       BIGINT COMMENT '附近高产酒店ID',
    `same_level_freq_hotel_distance` DECIMAL(15, 5) COMMENT '附近高产酒店距离',
    `same_level_freq_hotel_info`     VARCHAR(256) COMMENT '附近高产酒店信息',
    `same_level_freq_recommends`     VARCHAR(64) COMMENT '高频预订同档推荐 1:OTA评分4.9分及以上 2:开业时间在近2年 3:商旅常订酒店 4:成交均价低于10% 5:成交均价低',
    `is_poi_near_hotel`              int    NOT NULL DEFAULT 0 COMMENT '是否POI周边酒店 1:是 0:不是',
    `is_poi_near_hotel_recommend`    int    NOT NULL DEFAULT 0 COMMENT '是否POI周边酒店推荐 1:是 0:不是',
    `poi_near_hotel_recommends`      VARCHAR(64) COMMENT 'POI周边酒店推荐 1:高产酒店，建议邀约 2:同档员工最常订酒店 3:超高评分酒店 4:新开业酒店 5:商旅常订酒店',
    `poi_near_hotel_poi_id`          BIGINT COMMENT 'POI ID',
    `is_no_poi_hot_area`             int    NOT NULL DEFAULT 0 COMMENT '是否非POI热点区域 1:是 0:不是',
    `is_no_poi_hot_area_recommend`   int    NOT NULL DEFAULT 0 COMMENT '是否非POI热点区域推荐 1:是 0:不是',
    `no_poi_hot_area_recommends`     VARCHAR(64) COMMENT '非POI热点区域推荐 1:高产酒店 2:同档员工最常订酒店 3:超高评分酒店 4:新开业酒店 5:商旅常订酒店',
    `no_poi_hotel_id`                BIGINT COMMENT '非POI热点区域ID',
    `no_poi_hotel_info`              VARCHAR(1024) COMMENT '非POI热点信息 (酒店id,酒店名称，酒店距离，区域总间夜,周边采购价格统计分布,星级统计分布)',
    `is_area_gather`                 int    NOT NULL DEFAULT 0 COMMENT '是否散布聚量 1:是 0:不是',
    `is_area_gather_recommend`       int    NOT NULL DEFAULT 0 COMMENT '是否散布聚量推荐',
    `area_gather_hotel_id`           BIGINT COMMENT '聚量酒店ID',
    `area_gather_hotel_info`         VARCHAR(1024) COMMENT '聚量酒店信息 (酒店ID,酒店名称,距离，同档总间夜数，同档最高价格，最低价格)',
    `area_gather_recommends`         VARCHAR(64) COMMENT '散布聚量推荐 1:推荐去年员工预订最多酒店 2:推荐去年高性价比酒店 3:城市去年员工预订最多酒店 4:城市去年高性价比酒店',
    `is_high_quality`                int    NOT NULL DEFAULT 0 COMMENT '是否优质酒店 1:是 0:不是',
    `is_high_quality_recommend`      int COMMENT '是否优质酒店推荐:节省明星酒店推荐 (优质商旅酒店推荐等级XX)',
    `is_saved_hotel`                 int    NOT NULL DEFAULT 0 COMMENT '是否节省明星酒店 1:是 0:不是',
    `is_saved_hotel_recommend`       int    NOT NULL DEFAULT 0 COMMENT '是否节省明星酒店推荐 1:是 0:不是 (节省明星酒店推荐)',
    `price_level_room_night`         BIGINT COMMENT '周边3KM同价位酒店间夜',
    `creator`                        VARCHAR(64) COMMENT '创建人',
    `create_time`                    DATETIME        DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`                       VARCHAR(64) COMMENT '修改人',
    `modify_time`                    DATETIME        DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`project_id`, `hotel_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='项目历史数据推荐';

-- 创建唯一索引
create unique index udx_project_hotel on t_project_history_recommend (project_id, hotel_id);

ALTER TABLE `grfp`.`t_project_hotel_price_group`
    MODIFY COLUMN `lra` int(1) NOT NULL COMMENT 'lra承诺：1-是，0-否' AFTER `applicable_weeks`;

CREATE TABLE t_project_hotel_remark(
    `project_hotel_remark_id` int NOT NULL AUTO_INCREMENT COMMENT '项目酒店备注ID',
    `project_intent_hotel_id` int NOT NULL COMMENT '项目ID',
    `project_id` int NOT NULL COMMENT '项目ID',
    `hotel_id` bigint NOT NULL COMMENT '酒店ID',
    `remark_type` varchar(128) NOT NULL COMMENT '备注类型 UNDER_NEGOTIATION:议价中',
    `remark` varchar(1024) NOT NULL COMMENT '备注内容',
    `creator` varchar(64) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`project_hotel_remark_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='项目酒店备注';


ALTER TABLE `grfp`.`t_project_intent_hotel`
    ADD COLUMN `reject_negotiation_remark` varchar(1024) COMMENT '拒绝议价备注' AFTER `remark`;


CREATE TABLE t_lanyon_import_column(
       `column_index` int NOT NULL COMMENT '序列号',
       `data_type` varchar(200) NOT NULL COMMENT '数据类型',
       `column_code` varchar(200) NOT NULL COMMENT '列编码',
       `column_name` varchar(200) NOT NULL COMMENT '列名称',
       `display_order` int NOT NULL COMMENT '排序',
       `data_category` varchar(200) NOT NULL COMMENT '分类',
       PRIMARY KEY (`column_index`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='Lanyon导入列';

CREATE TABLE t_lanyon_import_data(
       `lanyon_import_data_id` int NOT NULL AUTO_INCREMENT COMMENT '导入数据ID',
       `project_id` int NOT NULL COMMENT '项目ID',
       `hotel_id` bigint NOT NULL COMMENT '酒店ID',
       `json_data` LONGBLOB NOT NULL COMMENT '导入JSON数据',
       `creator`                        VARCHAR(64) COMMENT '创建人',
       `create_time`                    DATETIME        DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       `modifier`                       VARCHAR(64) COMMENT '修改人',
       `modify_time`                    DATETIME        DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
       PRIMARY KEY (`lanyon_import_data_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='Lanyon导入数据';

CREATE TABLE t_project_lanyon_view_keys(
     `project_lanyon_view_keys_id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     `project_id` int NOT NULL COMMENT '项目ID',
     `base_info` LONGBLOB  COMMENT '基础信息',
     `hotel_verify` LONGBLOB COMMENT '酒店认证',
     `hotel_facilities` LONGBLOB  COMMENT '酒店基础设施',
     `bid_info` LONGBLOB  COMMENT '报价信息',
     `meeting_room_bid_info` LONGBLOB  COMMENT '会议室报价信息',
     `long_bid_info` LONGBLOB  COMMENT '长报价信息',
     `hotel_service` LONGBLOB  COMMENT '酒店服务',
     `user_defined` LONGBLOB  COMMENT '用户自定义',
     `mtg_user_defined` LONGBLOB  COMMENT 'mtg用户自定义',
     `lar_un_applicable_day_info` LONGBLOB  COMMENT 'LRY不可以信息',
     `base_service_fee` LONGBLOB  COMMENT '基础服务费',
     `creator`                        VARCHAR(64) COMMENT '创建人',
     `create_time`                    DATETIME        DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `modifier`                       VARCHAR(64) COMMENT '修改人',
     `modify_time`                    DATETIME        DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
     PRIMARY KEY (`project_lanyon_view_keys_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='项目lanyon显示key';

alter table t_project_hotel_price_level
    add column lanyon_room_desc varchar(64) null comment 'Lanyon 房档描述';

alter table grfp.t_project_hotel_tend_strategy
    add column limit_price_currency_code                      varchar(32) not null default 'USD' comment '报价币种',
    add column is_require_no_breakfast_single     int         not null default 0 comment '是否必须提供无早单人价',
    add column is_require_no_breakfast_double     int         not null default 0 comment '是否必须提供无早双人价',
    add column is_recommend_no_breakfast_single   int         not null default 0 comment '是否建议提供无早单人价',
    add column is_recommend_no_breakfast_double   int         not null default 0 comment '是否建议提供无早双人价',
    add column is_require_with_breakfast_single   int         not null default 0 comment '是否必须提供含早单人价',
    add column is_require_with_breakfast_double   int         not null default 0 comment '是否必须提供含早双人价',
    add column is_recommend_with_breakfast_single int         not null default 0 comment '是否建议提供含早单人价',
    add column is_recommend_with_breakfast_double int         not null default 0 comment '是否建议提供含早双人价',
    add column is_require_tax_details             int         not null default 0 comment '酒店报价是否必须填写税费明细',
    add column is_require_include_all_taxes       int         not null default 0 comment '酒店报价是否必须包含全部税费',
    add column is_recommend_include_all_taxes     int         not null default 0 comment '酒店报价是否建议包含全部税费';

alter table grfp.t_project_hotel_tend_strategy
    drop column support_cancel_first_night_charge,
    drop column support_cancel_day_first_night_charge,
    drop column support_cancel_time_first_night_charge,
    drop column include_breakfast,
    drop column is_suggest_no_breakfast,
    drop column is_include_no_breakfast,
    drop column is_suggest_one_breakfast,
    drop column is_include_one_breakfast,
    drop column is_suggest_two_breakfast,
    drop column is_include_two_breakfast;

