package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 项目酒店备注
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_hotel_remark")
public class ProjectHotelRemarkEntity extends BaseVO {


    /**
     * 项目酒店备注ID
     */
    @TableId(value = "project_hotel_remark_id", type = IdType.AUTO)
    private Integer projectHotelRemarkId;

    /**
     * 项目ID
     */
    @TableField("project_intent_hotel_id")
    private Integer projectIntentHotelId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店ID
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 备注类型 UNDER_NEGOTIATION:议价中
     */
    @TableField("remark_type")
    private String remarkType;

    /**
     * 备注内容
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;


}
