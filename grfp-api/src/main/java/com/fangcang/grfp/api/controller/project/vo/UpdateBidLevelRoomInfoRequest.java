package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.vo.BidApplicableRoomVO;
import com.fangcang.grfp.core.vo.BidHotelPriceLevelInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("修改房档房型请求")
@Getter
@Setter
public class UpdateBidLevelRoomInfoRequest extends BaseVO {

    @ApiModelProperty("项目酒店ID")
    @NotNull
    private Integer projectIntentHotelId;

    @ApiModelProperty("酒店价格房档ID, 新增为空，修改传ID")
    @NotNull
    private Integer hotelPriceLevelId;

    @ApiModelProperty("大床房数量")
    private Integer bigBedRoomCount;

    @ApiModelProperty("双床房数量")
    private Integer doubleBedRoomCount;

    @ApiModelProperty("总房数量")
    private Integer totalRoomCount;

    @ApiModelProperty("房档房型列表")
    @NotNull
    private List<BidApplicableRoomVO> roomList;


}
