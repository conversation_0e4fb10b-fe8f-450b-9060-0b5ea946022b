package com.fangcang.grfp.core.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.cache.CacheManagerCustomizer;
import org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers;
import org.springframework.boot.autoconfigure.cache.CacheProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.ApplicationContextException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 
 * Cache autoConfiguration
 * <AUTHOR>
 *
 * 2022-10-28
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(CacheProperties.class)
@EnableCaching
@ImportAutoConfiguration({RedisCacheAutoConfiguration.class, CaffeineCacheAutoConfiguration.class, EhCacheCacheAutoConfiguration.class})
public class CacheAutoConfiguration {

	// --------------------------------------------------------------------------------- Bean
	
	@Bean
	@ConditionalOnMissingBean
	public CacheManagerCustomizers cacheManagerCustomizers(ObjectProvider<CacheManagerCustomizer<?>> customizers) {
        return new CacheManagerCustomizers(customizers.orderedStream().collect(Collectors.toList()));
	}
	
	@Bean
	public KeyGenerator keyGenerator() {
		return new KeyGenerator() {
			@Override
			public Object generate(Object target, Method method, Object... params) {
				StringBuffer sb = new StringBuffer();
				sb.append(target.getClass().getSimpleName()).append(":");
				sb.append(method.getName());
				
				// Append param key
				if(params != null && params.length > 0) {
					for(Object param : params) {
						sb.append(":");
						if(param == null) {
							sb.append("null");
						// Map is not allowed as a cache key
						} else if(param instanceof Map){
							throw new ApplicationContextException("Map type can not be cache key " + param);
						} else {
							sb.append(param.toString());
						}
					}
				}
				
				// Return result
                return sb.toString();
			}
		};
		
	}
	
}
