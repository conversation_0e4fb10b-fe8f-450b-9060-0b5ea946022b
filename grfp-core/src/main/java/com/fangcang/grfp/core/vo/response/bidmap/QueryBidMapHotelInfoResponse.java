package com.fangcang.grfp.core.vo.response.bidmap;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.Image;
import com.fangcang.grfp.core.vo.BidApplicableDayVO;
import com.fangcang.grfp.core.vo.BidHotelPriceLevelInfoVO;
import com.fangcang.grfp.core.vo.BidUnApplicableDayVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@ApiModel("地图酒店详细信息")
@Getter
@Setter
public class QueryBidMapHotelInfoResponse extends BaseVO {

    // 去年城市成交总间夜
    @ApiModelProperty("去年城市成交总间夜")
    private String lastYearTotalRoomNight;

    // 去年城市销售总金额
    @ApiModelProperty("去年城市销售总金额")
    private String lastYearTotalSalesAmount;

    /**
     * 关联项目 ID
     */
    @ApiModelProperty("去年项目 ID")
    private Integer relatedProjectId;

    @ApiModelProperty("500 以上间夜")
    private String totalRoomNight500;

    @ApiModelProperty("400-500间夜数")
    private String totalRoomNight400To500;

    @ApiModelProperty("300-400间夜数")
    private String totalRoomNight300To400;

    @ApiModelProperty("200-300间夜数")
    private String totalRoomNight200To300;

    @ApiModelProperty("100-200间夜数")
    private String totalRoomNight100To200;

    @ApiModelProperty("100以内")
    private String totalRoomNight100;

    // 酒店信息
    @ApiModelProperty("酒店ID")
    private Long hotelId;

    @ApiModelProperty("酒店名称")
    private String hotelName;

    // 城市名称
    @ApiModelProperty("城市编号")
    private String cityCode;

    // 城市名称
    @ApiModelProperty("城市名称")
    private String cityName;

    //星级
    @ApiModelProperty("星级")
    private String hotelStar;

    //星级显示
    @ApiModelProperty("星级名称")
    private String hotelStarName;

    //星级显示
    @ApiModelProperty("早餐")
    private String breakfastNum;

    @ApiModelProperty("币种")
    private String currencyCode;

    //百度经度
    @ApiModelProperty("Google经度")
    private BigDecimal lngGoogle;

    //百度纬度
    @ApiModelProperty("Google纬度")
    private BigDecimal latGoogle;

    //近一年采购间夜数
    @ApiModelProperty("近一年采购间夜数")
    private int latestYearRoomNight;

    //近一年排名
    @ApiModelProperty("近一年排名")
    private int latestYearOrder;

    // 同档排名
    @ApiModelProperty("同档排名")
    private int theSameLevelOrder;

    // 是否推荐
    @ApiModelProperty("是否推荐")
    private Integer isRecommendHotel;

    // 是否意向酒店
    @ApiModelProperty("是否意向酒店")
    private Integer isInvitedHotel;

    /**
     * 投标总权重(投标时根据项目权重和酒店投标策略计算生成)
     */
    @ApiModelProperty("投标总权重(投标时根据项目权重和酒店投标策略计算生成)")
    private BigDecimal bidWeight;

    @ApiModelProperty("酒店评分")
    private String rating;

    @ApiModelProperty("项目总权重")
    private BigDecimal projectWeight;

    // 是否意向酒店
    @ApiModelProperty("酒店服务分")
    private Integer hotelServicePoint;

    @ApiModelProperty("酒店图片列表")
    private List<Image> hotelImageList;

    @ApiModelProperty("项目酒店报价ID")
    private Integer projectIntentHotelId;

    @ApiModelProperty("酒店主图")
    private String mainPicUrl;

    @ApiModelProperty("最近poi名称")
    private String poiName;

    @ApiModelProperty("最近poi名称 KM")
    private String poiDistance;

    @ApiModelProperty("最低价格")
    private BigDecimal minPrice;

    @ApiModelProperty("房型")
    private String roomTypeDesc;

    @ApiModelProperty("报价状态")
    private Integer bidState;

    /**
     * 范围内酒店信息
     */
    @ApiModelProperty("范围内酒店信息")
    private List<HotelBidMapHotelInfoResponse> bidHotelInfoQueryResponseList;

    @ApiModelProperty("可用日期列表")
    private List<BidApplicableDayVO> bidApplicableDayList;

    @ApiModelProperty("不可用日期列表")
    private List<BidUnApplicableDayVO> bidUnApplicableDayList;

    @ApiModelProperty("报价房档信息")
    private List<BidHotelPriceLevelInfoVO> hotelPriceLevelInfoVOList;

    @ApiModelProperty("去年报价房档信息")
    private List<BidHotelPriceLevelInfoVO> lastYearHotelPriceLevelInfoVOList;

    /**
     * 范围内POI信息
     */
    @ApiModelProperty("范围内POI信息")
    private List<ProjectPoiInfoResponse> hotelProjectPoiInfoResponseList;

    /**
     * 3公里统计信息
     */
    @ApiModelProperty("3公里统计信息")
    private QueryProjectHotelBidStatResponse threeQueryProjectHotelBidStatResponse;

    /**
     * 城市同档统计信息
     */
    @ApiModelProperty("城市同档统计信息")
    private QueryProjectHotelBidStatResponse cityTheSameLevelQueryProjectHotelBidStatResponse;

    /**
     * 城市统计信息
     */
    @ApiModelProperty("城市统计信息")
    private QueryProjectHotelBidStatResponse cityQueryProjectHotelBidStatResponse;


    @ApiModelProperty("项目酒店报价备注信息")
    private List<ProjectHotelRemarkResponse> projectHotelRemarkResponseList;

    @ApiModelProperty("展示币种")
    private String viewCurrencyCode;

    @ApiModelProperty("报价币种对展示币种汇率")
    private BigDecimal viewCurrencyExchangeRate;
}
