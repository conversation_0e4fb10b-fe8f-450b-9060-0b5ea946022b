package com.fangcang.grfp.core.cached.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fangcang.grfp.core.cached.CachedCityService;
import com.fangcang.grfp.core.entity.CityEntity;
import com.fangcang.grfp.core.entity.HotelGroupEntity;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.mapper.CityMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CachedCityServiceImpl implements CachedCityService {

    @Autowired
    private CityMapper cityMapper;

    @Override
    @Cacheable(value="cachedCityService.getByCityCode", key = "#cityCode", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public CityEntity getByCityCode(String cityCode) {
        return cityMapper.selectByCityCode(cityCode);
    }

    @Override
    @Cacheable(value="cachedCityService.getNameMap", key = "#languageId", unless = "#result == null", cacheManager = "ehCacheCacheManager")
    public Map<String, String> getNameMap(int languageId) {
        Map<String, String> nameMap = new HashMap<>();
        LambdaQueryWrapper<CityEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(languageId == LanguageEnum.EN_US.key){
            lambdaQueryWrapper.select(CityEntity::getCityCode, CityEntity::getNameEnUs);
        }
        if(languageId == LanguageEnum.ZH_CN.key){
            lambdaQueryWrapper.select(CityEntity::getCityCode, CityEntity::getNameZhCn);
        }
        List<CityEntity> cityEntityList = cityMapper.selectList(lambdaQueryWrapper);
        cityEntityList.forEach(item -> {
            if(languageId == LanguageEnum.EN_US.key){
                nameMap.put(item.getCityCode(), item.getNameEnUs());
            }
            if(languageId == LanguageEnum.ZH_CN.key){
                nameMap.put(item.getCityCode(), item.getNameZhCn());
            }
        });
        if(nameMap.isEmpty()){
            return null;
        }
        log.info("City Map size {}, {}", LanguageEnum.getValueByKey(languageId), nameMap.size());
        return nameMap;
    }

    @Override
    @CacheEvict(value="cachedCityService.getNameMap", key = "#languageId", cacheManager = "ehCacheCacheManager")
    public void clearNameMap(int languageId) {
        log.info("clear City NameMap {}", LanguageEnum.getValueByKey(languageId));
    }
}
