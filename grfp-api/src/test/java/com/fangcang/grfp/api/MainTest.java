package com.fangcang.grfp.api;

import cn.hutool.core.date.DateUtil;

import java.util.Date;

public class MainTest {

    public static void main(String[] args) {
        Date day1 = com.fangcang.grfp.core.util.DateUtil.stringToDate("2025-06-01");
        Date day2 = com.fangcang.grfp.core.util.DateUtil.stringToDate("2025-06-02");
        int limit = (int) DateUtil.betweenDay(day1, day2, false);

        System.out.println(limit);
    }
}
