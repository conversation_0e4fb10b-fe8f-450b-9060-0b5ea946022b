package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fangcang.grfp.core.entity.HotelImageEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 * 酒店图片数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface HotelImageMapper extends BaseMapper<HotelImageEntity> {

    /**
     * 批量插入
     */
    void batchUpsert(@Param("list") Collection<HotelImageEntity> hotelImageEntities);

}
