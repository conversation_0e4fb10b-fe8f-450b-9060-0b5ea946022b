package generator;


import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.keywords.MySqlKeyWordsHandler;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.IOException;
import java.util.Properties;
import java.util.Scanner;
import java.util.function.Consumer;

/**
 * 生成 entity, mapper 和 xml. xml 手工放到 resource/mapper 下
 *
 */
public class CodeGenerator {

    public static void generator(CodeGeneratorConfig config, Consumer<AutoGenerator> postConfig) {
        // 代码生成器
        AutoGenerator mpg = new AutoGenerator();

        // 全局配置
        GlobalConfig gc = new GlobalConfig();
        gc.setOutputDir(config.getOutput());
        gc.setFileOverride(true);
        gc.setBaseResultMap(false);
        gc.setBaseColumnList(false);
        gc.setDateType(DateType.ONLY_DATE);
        gc.setEntityName("%sEntity");
        gc.setMapperName("%sMapper");
        gc.setXmlName("%sMapper");
        gc.setServiceImplName("%sService");
        gc.setIdType(config.getIdType());
        mpg.setGlobalConfig(gc);

        // 数据源配置
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setUrl(config.getDbUrl());
        dsc.setDriverName(config.getDbDriverName());
        dsc.setUsername(config.getDbUser());
        dsc.setPassword(config.getDbPassword());
        if (org.apache.commons.lang3.StringUtils.containsIgnoreCase(dsc.getUrl(), "mysql")) {
            dsc.setKeyWordsHandler(new MySqlKeyWordsHandler());
        }
        mpg.setDataSource(dsc);

        // 包配置
        PackageConfig pc = new PackageConfig();
        pc.setParent(config.getBasePackage());
        pc.setEntity("entity");
        pc.setMapper("mapper");
        pc.setXml("mapper");
        mpg.setPackageInfo(pc);

        // 配置模板
        TemplateConfig templateConfig = new TemplateConfig();
        templateConfig.setController(null);
        templateConfig.setService(null);
        templateConfig.setServiceImpl(null);
        mpg.setTemplate(templateConfig);

        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        strategy.setNaming(NamingStrategy.underline_to_camel);
        strategy.setColumnNaming(NamingStrategy.underline_to_camel);
        strategy.setTablePrefix("t_");
        strategy.setSuperEntityClass(config.getSuperEntityClass());
        strategy.setEntityLombokModel(true);
        strategy.setEntityTableFieldAnnotationEnable(true);
        strategy.setInclude(scanner("表名, 多个英文逗号分割").split(","));
        strategy.setEntitySerialVersionUID(false);
        mpg.setStrategy(strategy);

        if (postConfig != null) {
            postConfig.accept(mpg);
        }

        mpg.execute();
    }

    public static void generator(CodeGeneratorConfig config) {
        generator(config, null);
    }

    public static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in, "UTF-8");
        System.out.println("请输入" + tip + ": ");
        if (scanner.hasNext()) {
            String ipt = scanner.next();
            if (StringUtils.isNotBlank(ipt)) {
                return ipt;
            }
        }
        throw new MybatisPlusException("请输入正确的" + tip + "！");
    }

    public static void main(String[] args) throws IOException {
        Properties properties = PropertiesLoaderUtils.loadAllProperties("application-dev.properties");
        System.out.println(JSONUtil.toJsonStr(properties));
        CodeGeneratorConfig config = new CodeGeneratorConfig();
        config.setDbUrl(properties.getProperty("spring.datasource.url"));
        config.setDbUser(properties.getProperty("spring.datasource.username"));
        config.setDbPassword(properties.getProperty("spring.datasource.password"));
        config.setBasePackage("com.fangcang.grfp.core");

        CodeGenerator.generator(config);
    }

}