2025-05-22 22:54:03.528|main|ERROR|org.springframework.boot.SpringApplication.reportFailure:834|Application run failed java.lang.IllegalStateException: Error processing condition on com.fangcang.grfp.core.config.RedisAutoConfiguration.redisTemplate
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:60)
	at org.springframework.context.annotation.ConditionEvaluator.shouldSkip(ConditionEvaluator.java:108)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForBeanMethod(ConfigurationClassBeanDefinitionReader.java:184)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:144)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:120)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:332)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:237)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:280)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:96)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:707)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:533)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:120)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:123)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:244)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:98)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$5(ClassBasedTestDescriptor.java:341)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:346)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$6(ClassBasedTestDescriptor.java:341)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:312)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:743)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:742)
	at java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:580)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:340)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:263)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$2(ClassBasedTestDescriptor.java:256)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$3(ClassBasedTestDescriptor.java:255)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:29)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:108)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:107)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:71)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$1(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:75)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:232)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.fangcang.grfp.core.config.RedisAutoConfiguration] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:481)
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:358)
	at org.springframework.util.ReflectionUtils.getUniqueDeclaredMethods(ReflectionUtils.java:414)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.lambda$getTypeForFactoryMethod$2(AbstractAutowireCapableBeanFactory.java:742)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryMethod(AbstractAutowireCapableBeanFactory.java:741)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:680)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:648)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isFactoryBean(AbstractBeanFactory.java:1614)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:523)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:495)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.collectBeanNamesForType(OnBeanCondition.java:238)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:231)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:221)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchingBeans(OnBeanCondition.java:169)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchOutcome(OnBeanCondition.java:144)
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:47)
	... 82 common frames omitted
Caused by: java.lang.NoClassDefFoundError: org/redisson/client/codec/Codec
	at java.lang.Class.getDeclaredMethods0(Native Method)
	at java.lang.Class.privateGetDeclaredMethods(Class.java:2701)
	at java.lang.Class.getDeclaredMethods(Class.java:1975)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:463)
	... 98 common frames omitted
Caused by: java.lang.ClassNotFoundException: org.redisson.client.codec.Codec
	at java.net.URLClassLoader.findClass(URLClassLoader.java:387)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:418)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:355)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:351)
	... 102 common frames omitted
 [grfp-api,] ------
2025-05-22 22:54:03.536|main|ERROR|o.springframework.test.context.TestContextManager.prepareTestInstance:248|Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@76a4ebf2] to prepare test instance [com.fangcang.grfp.core.remote.tmchub.manager.TmcHubApiManagerTest@4422dd48] java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:132)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:123)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:244)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:98)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$5(ClassBasedTestDescriptor.java:341)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:346)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$6(ClassBasedTestDescriptor.java:341)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:312)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:743)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:742)
	at java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:580)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:340)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:263)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$2(ClassBasedTestDescriptor.java:256)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$3(ClassBasedTestDescriptor.java:255)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:29)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:108)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:107)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:71)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$1(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:75)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:232)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: java.lang.IllegalStateException: Error processing condition on com.fangcang.grfp.core.config.RedisAutoConfiguration.redisTemplate
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:60)
	at org.springframework.context.annotation.ConditionEvaluator.shouldSkip(ConditionEvaluator.java:108)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForBeanMethod(ConfigurationClassBeanDefinitionReader.java:184)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:144)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:120)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:332)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:237)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:280)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:96)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:707)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:533)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:120)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	... 65 common frames omitted
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.fangcang.grfp.core.config.RedisAutoConfiguration] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:481)
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:358)
	at org.springframework.util.ReflectionUtils.getUniqueDeclaredMethods(ReflectionUtils.java:414)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.lambda$getTypeForFactoryMethod$2(AbstractAutowireCapableBeanFactory.java:742)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryMethod(AbstractAutowireCapableBeanFactory.java:741)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:680)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:648)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isFactoryBean(AbstractBeanFactory.java:1614)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:523)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:495)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.collectBeanNamesForType(OnBeanCondition.java:238)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:231)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:221)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchingBeans(OnBeanCondition.java:169)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchOutcome(OnBeanCondition.java:144)
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:47)
	... 82 common frames omitted
Caused by: java.lang.NoClassDefFoundError: org/redisson/client/codec/Codec
	at java.lang.Class.getDeclaredMethods0(Native Method)
	at java.lang.Class.privateGetDeclaredMethods(Class.java:2701)
	at java.lang.Class.getDeclaredMethods(Class.java:1975)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:463)
	... 98 common frames omitted
Caused by: java.lang.ClassNotFoundException: org.redisson.client.codec.Codec
	at java.net.URLClassLoader.findClass(URLClassLoader.java:387)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:418)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:355)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:351)
	... 102 common frames omitted
 [grfp-api,] ------
2025-05-22 22:55:50.933|main|ERROR|org.springframework.boot.SpringApplication.reportFailure:834|Application run failed java.lang.IllegalStateException: Error processing condition on com.fangcang.grfp.core.config.RedisAutoConfiguration.redisTemplate
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:60)
	at org.springframework.context.annotation.ConditionEvaluator.shouldSkip(ConditionEvaluator.java:108)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForBeanMethod(ConfigurationClassBeanDefinitionReader.java:184)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:144)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:120)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:332)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:237)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:280)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:96)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:707)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:533)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:120)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:123)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:244)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:98)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$5(ClassBasedTestDescriptor.java:341)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:346)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$6(ClassBasedTestDescriptor.java:341)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:312)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:743)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:742)
	at java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:580)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:340)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:263)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$2(ClassBasedTestDescriptor.java:256)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$3(ClassBasedTestDescriptor.java:255)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:29)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:108)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:107)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:71)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$1(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:75)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:232)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.fangcang.grfp.core.config.RedisAutoConfiguration] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:481)
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:358)
	at org.springframework.util.ReflectionUtils.getUniqueDeclaredMethods(ReflectionUtils.java:414)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.lambda$getTypeForFactoryMethod$2(AbstractAutowireCapableBeanFactory.java:742)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryMethod(AbstractAutowireCapableBeanFactory.java:741)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:680)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:648)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isFactoryBean(AbstractBeanFactory.java:1614)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:523)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:495)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.collectBeanNamesForType(OnBeanCondition.java:238)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:231)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:221)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchingBeans(OnBeanCondition.java:169)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchOutcome(OnBeanCondition.java:144)
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:47)
	... 82 common frames omitted
Caused by: java.lang.NoClassDefFoundError: org/redisson/client/codec/Codec
	at java.lang.Class.getDeclaredMethods0(Native Method)
	at java.lang.Class.privateGetDeclaredMethods(Class.java:2701)
	at java.lang.Class.getDeclaredMethods(Class.java:1975)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:463)
	... 98 common frames omitted
Caused by: java.lang.ClassNotFoundException: org.redisson.client.codec.Codec
	at java.net.URLClassLoader.findClass(URLClassLoader.java:387)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:418)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:355)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:351)
	... 102 common frames omitted
 [grfp-api,] ------
2025-05-22 22:55:50.940|main|ERROR|o.springframework.test.context.TestContextManager.prepareTestInstance:248|Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@76a4ebf2] to prepare test instance [com.fangcang.grfp.core.remote.tmchub.manager.TmcHubApiManagerTest@4554de02] java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:132)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:123)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:244)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:98)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$5(ClassBasedTestDescriptor.java:341)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:346)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$6(ClassBasedTestDescriptor.java:341)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:312)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:743)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:742)
	at java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:580)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:340)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:263)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$2(ClassBasedTestDescriptor.java:256)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$3(ClassBasedTestDescriptor.java:255)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:29)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:108)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:107)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:71)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$1(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:75)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:232)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: java.lang.IllegalStateException: Error processing condition on com.fangcang.grfp.core.config.RedisAutoConfiguration.redisTemplate
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:60)
	at org.springframework.context.annotation.ConditionEvaluator.shouldSkip(ConditionEvaluator.java:108)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForBeanMethod(ConfigurationClassBeanDefinitionReader.java:184)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:144)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:120)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:332)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:237)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:280)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:96)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:707)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:533)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:120)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	... 65 common frames omitted
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.fangcang.grfp.core.config.RedisAutoConfiguration] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:481)
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:358)
	at org.springframework.util.ReflectionUtils.getUniqueDeclaredMethods(ReflectionUtils.java:414)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.lambda$getTypeForFactoryMethod$2(AbstractAutowireCapableBeanFactory.java:742)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryMethod(AbstractAutowireCapableBeanFactory.java:741)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:680)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:648)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isFactoryBean(AbstractBeanFactory.java:1614)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:523)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:495)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.collectBeanNamesForType(OnBeanCondition.java:238)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:231)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:221)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchingBeans(OnBeanCondition.java:169)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchOutcome(OnBeanCondition.java:144)
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:47)
	... 82 common frames omitted
Caused by: java.lang.NoClassDefFoundError: org/redisson/client/codec/Codec
	at java.lang.Class.getDeclaredMethods0(Native Method)
	at java.lang.Class.privateGetDeclaredMethods(Class.java:2701)
	at java.lang.Class.getDeclaredMethods(Class.java:1975)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:463)
	... 98 common frames omitted
Caused by: java.lang.ClassNotFoundException: org.redisson.client.codec.Codec
	at java.net.URLClassLoader.findClass(URLClassLoader.java:387)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:418)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:355)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:351)
	... 102 common frames omitted
 [grfp-api,] ------
2025-05-22 23:05:43.747|main|ERROR|c.fangcang.grfp.server.job.SyncProvinceCityDataJob.lambda$null$0:81|Query province city data from DHUB is empty, language: zh-CN  [grfp-api,] ------
2025-05-22 23:06:20.727|main|ERROR|com.fangcang.grfp.server.job.SyncGroupBrandDataJob.execute:99|Sync group brand data job error org.springframework.dao.RecoverableDataAccessException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 106 milliseconds ago. The last packet sent successfully to the server was 248 milliseconds ago.
### The error may exist in file [D:\RFP\grfp-project\grfp-core\target\classes\mapper\HotelGroupMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: insert into t_hotel_group(hotel_group_id, name_zh_cn, name_en_us, is_active)         values                        (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)          ,              (?, ?, ?, ?)                   on duplicate key update hotel_group_id = values(hotel_group_id),                                 name_zh_cn     = values(name_zh_cn),                                 name_en_us     = values(name_en_us),                                 is_active      = values(is_active)
### Cause: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 106 milliseconds ago. The last packet sent successfully to the server was 248 milliseconds ago.
; Communications link failure

The last packet successfully received from the server was 106 milliseconds ago. The last packet sent successfully to the server was 248 milliseconds ago.; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 106 milliseconds ago. The last packet sent successfully to the server was 248 milliseconds ago.
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:100)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy94.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:271)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:60)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy101.batchUpsert(Unknown Source)
	at com.fangcang.grfp.server.job.SyncGroupBrandDataJob.lambda$saveData$9(SyncGroupBrandDataJob.java:227)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at com.fangcang.grfp.server.job.SyncGroupBrandDataJob.saveData(SyncGroupBrandDataJob.java:227)
	at com.fangcang.grfp.server.job.SyncGroupBrandDataJob.execute(SyncGroupBrandDataJob.java:89)
	at com.fangcang.grfp.server.job.SyncGroupBrandDataJobTest.testExecute(SyncGroupBrandDataJobTest.java:21)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:686)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84)
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:212)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:208)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:71)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:135)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:232)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 106 milliseconds ago. The last packet sent successfully to the server was 248 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy133.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:56)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.update(MybatisCachingExecutor.java:85)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 76 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 106 milliseconds ago. The last packet sent successfully to the server was 248 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:582)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:762)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:701)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:1052)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:657)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:893)
	... 97 common frames omitted
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576)
	... 102 common frames omitted
 [grfp-api,] ------
