package com.fangcang.grfp.core.dto.dhub.response.baseinfo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class BreakfastResponseDto {

    /**
     * 是否提供早餐
     */
    private String isBreakfast;

    /**
     * 早餐形式，1-单点，2-自助餐...见字典项
     */
    private String breakfastForm;

    /**
     * 固定套餐金额
     */
    private BigDecimal fixedAmount;

    /**
     * 盒装食品/包装食品金额
     */
    private BigDecimal operateAmount;

    /**
     * 自助餐金额
     */
    private BigDecimal buffetAmount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 早餐类型，1-西式，2-中式...见字典项
     */
    private String breakfastType;

    /**
     * 早餐种类，1-面包，2-点心...见字典项
     */
    private String breakfastVariety;

    /**
     * 早餐开放时间类型，0-每日开放 1-指定日期开放
     */
    private String breakfastOpenType;

    /**
     * 营业时间段
     */
    private List<HotelBreakfastOpenTimeDto> breakfastOpenTimes;

}
