package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.core.entity.ProjectCustomTendStrategyEntity;
import com.fangcang.grfp.core.vo.request.project.QueryCustomTendStrategyRequest;
import com.fangcang.grfp.core.vo.response.project.CustomTendStrategyVO;
import com.fangcang.grfp.core.vo.response.project.ProjectBidCustomTendStrategyVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 项目自定义采购策略表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
public interface ProjectCustomTendStrategyMapper extends BaseMapper<ProjectCustomTendStrategyEntity> {

    /**
     * 根据项目 id count
     */
    default Integer countByProjectId(Integer projectId) {
        LambdaQueryWrapper<ProjectCustomTendStrategyEntity> queryWrapper = Wrappers.lambdaQuery(ProjectCustomTendStrategyEntity.class);
        queryWrapper.eq(ProjectCustomTendStrategyEntity::getProjectId, projectId);
        return selectCount(queryWrapper);
    }

    /**
     * 根据项目 ID 和策略名称查询
     */
    default ProjectCustomTendStrategyEntity selectByProjectIdAndStrategyName(Integer projectId, String strategyName) {
        LambdaQueryWrapper<ProjectCustomTendStrategyEntity> queryWrapper = Wrappers.lambdaQuery(ProjectCustomTendStrategyEntity.class);
        queryWrapper.eq(ProjectCustomTendStrategyEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectCustomTendStrategyEntity::getStrategyName, strategyName);
        queryWrapper.last("limit 1");
        return selectOne(queryWrapper);
    }

    default List<ProjectCustomTendStrategyEntity> selectByProjectId(Integer projectId) {
        LambdaQueryWrapper<ProjectCustomTendStrategyEntity> queryWrapper = Wrappers.lambdaQuery(ProjectCustomTendStrategyEntity.class);
        queryWrapper.eq(ProjectCustomTendStrategyEntity::getProjectId, projectId);
        queryWrapper.orderByAsc(ProjectCustomTendStrategyEntity::getDisplayOrder, ProjectCustomTendStrategyEntity::getProjectId);
        return selectList(queryWrapper);
    }

    /**
     * 根据项目 ID 查询最大的 displayOrder
     */
    Integer selectMaxDisplayOrder(@Param("projectId") Integer projectId);

    /**
     * 根据项目 ID 和自定义策略 ID 删除
     */
    default void deleteByProjectIdAndStrategyId(Integer projectId, Integer strategyId) {
        LambdaQueryWrapper<ProjectCustomTendStrategyEntity> queryWrapper = Wrappers.lambdaQuery(ProjectCustomTendStrategyEntity.class);
        queryWrapper.eq(ProjectCustomTendStrategyEntity::getProjectId, projectId);
        queryWrapper.eq(ProjectCustomTendStrategyEntity::getId, strategyId);
        delete(queryWrapper);
    }

    /**
     * 根据条件查询
     */
    Page<CustomTendStrategyVO> selectByCondition(Page<CustomTendStrategyVO> page, @Param("req") QueryCustomTendStrategyRequest req);


    /**
     * 查询项目报价自定义策略
     */
    List<ProjectBidCustomTendStrategyVO> selectBidCustomTendStrategyInfoByProjectId(int projectId);

    /**
     * 批量更新
     */
    int batchUpdate(@Param("list") List<ProjectCustomTendStrategyEntity> list);

    /**
     * 根据项目 id 批量查询
     */
    default List<ProjectCustomTendStrategyEntity> selectByProjectIds(Collection<Integer> projectIds) {
        LambdaQueryWrapper<ProjectCustomTendStrategyEntity> queryWrapper = Wrappers.lambdaQuery(ProjectCustomTendStrategyEntity.class);
        queryWrapper.in(ProjectCustomTendStrategyEntity::getProjectId, projectIds);
        queryWrapper.orderByAsc(ProjectCustomTendStrategyEntity::getProjectId, ProjectCustomTendStrategyEntity::getDisplayOrder);
        return selectList(queryWrapper);
    }
}
