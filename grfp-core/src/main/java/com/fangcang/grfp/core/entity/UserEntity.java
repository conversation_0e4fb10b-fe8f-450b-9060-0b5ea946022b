package com.fangcang.grfp.core.entity;

import com.fangcang.grfp.core.base.BaseVO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_user")
public class UserEntity extends BaseVO {


    /**
     * 用户ID
     */
    @TableId(value = "user_id", type = IdType.AUTO)
    private Integer userId;

    /**
     * 机构ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 用户姓名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 用户昵称
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 角色编码类型 ADMIN：管理员，EMPLOYEE：员工，HEAD_ORGANIZATION：机构负责人，CHANNEL_ADMIN:渠道合作管理员
     */
    @TableField("role_code")
    private String roleCode;

    /**
     * 手机号码区号
     */
    @TableField("mobile_area_code")
    private String mobileAreaCode;

    /**
     * 手机号码
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 密码
     */
    @TableField("`password`")
    private String password;

    /**
     * 最近登录时间
     */
    @TableField("last_login_time")
    private Date lastLoginTime;

    /**
     * 最近登录IP
     */
    @TableField("last_ip_address")
    private String lastIpAddress;

    /**
     * 用户状态 1:有效,0:无效，2:待审核
     */
    @TableField("state")
    private Integer state;

    /**
     * 审批时间
     */
    @TableField("approve_time")
    private Date approveTime;

    /**
     * 渠道ID
     */
    @TableField("channel_partner_id")
    private Integer channelPartnerId;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
