package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("项目报价数量统计")
@Getter
@Setter
public class ProjectBidStatCountVO extends BaseVO {

    @ApiModelProperty("编号")
    private String code;

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("数量")
    private Integer count;

}
