<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectCustomTendStrategyMapper">
    <select id="selectMaxDisplayOrder" resultType="java.lang.Integer">
        select max(display_order)
        from t_project_custom_tend_strategy
        where project_id = #{projectId}
    </select>

    <select id="selectByCondition" resultType="com.fangcang.grfp.core.vo.response.project.CustomTendStrategyVO">
        select id,
               strategy_name,
               strategy_type,
               display_order,
               support_strategy_name,
               wht_strategy_name,
               wht_strategy_name_state,
               creator,
               create_time
        from t_project_custom_tend_strategy
        <where>
            project_id = #{req.projectId}
            <if test="req.strategyName != null and req.strategyName != ''">
                and strategy_name like concat('%', #{req.strategyName}, '%')
            </if>
            <if test="req.strategyType != null">
                and strategy_type = #{req.strategyType}
            </if>
        </where>
        order by display_order, id
    </select>

    <select id="selectBidCustomTendStrategyInfoByProjectId" resultType="com.fangcang.grfp.core.vo.response.project.ProjectBidCustomTendStrategyVO">
        select id,
            strategy_name,
            strategy_type,
            display_order,
            support_strategy_name
        from t_project_custom_tend_strategy
        WHERE project_id = #{projectId}
        order by display_order, id
    </select>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update t_project_custom_tend_strategy
            set wht_strategy_name = #{item.whtStrategyName},
                wht_strategy_name_state = #{item.whtStrategyNameState},
                modifier = #{item.modifier}
            where id = #{item.id}
        </foreach>
    </update>
</mapper>
