<?xml version="1.0" encoding="UTF-8"?>
<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://ehcache.org/ehcache.xsd">

	<diskStore path="java.io.tmpdir" />
	
	<!-- The defaultCache does not provide "defaults" for every cache, but its just a way of specifying configuration for caches that can/are added dynamically -->
	<defaultCache maxElementsInMemory="10000" eternal="false" timeToIdleSeconds="600" timeToLiveSeconds="600" overflowToDisk="false"></defaultCache>

	<!-- ******************************************************************* -->

	<cache name="cachedSysConfigService.getValue"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

	<cache name="cachedTextResourceService.getWebValue"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

	<cache name="cachedTextResourceService.getMsgValue"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

	<cache name="cachedTextResourceService.getLatestUpdatedTime"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

	<cache name="cachedTextResourceService.getTextResourcesVO"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

	<cache name="cachedCityService.getByCityCode"
		   maxElementsInMemory="20000" eternal="false" timeToIdleSeconds="8640" timeToLiveSeconds="8640" maxElementsOnDisk="100000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

	<cache name="cachedProjectService.queryProjectEmailAttachmentList"
		   maxElementsInMemory="2000000" eternal="false" timeToIdleSeconds="60" timeToLiveSeconds="60" maxElementsOnDisk="2000000" overflowToDisk="false" memoryStoreEvictionPolicy="LRU" />

</ehcache>
