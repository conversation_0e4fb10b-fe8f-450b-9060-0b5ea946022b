<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HotelGroupLogMapper">
    <select id="selectLatestByGroupIds" resultType="com.fangcang.grfp.core.entity.HotelGroupLogEntity">
        SELECT t1.*
        FROM t_hotel_group_log t1
            JOIN (
        SELECT hotel_group_id, MAX(hotel_group_log_id) AS max_id
        FROM t_hotel_group_log
        WHERE hotel_group_id IN
        <foreach collection="hotelGroupIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY hotel_group_id
        ) t2 ON t1.hotel_group_id = t2.hotel_group_id AND t1.hotel_group_log_id = t2.max_id
    </select>

    <insert id="batchInsert">
        INSERT INTO t_hotel_group_log(hotel_group_id, before_kv_json, after_kv_json, creator)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.hotelGroupId}, #{item.beforeKvJson}, #{item.afterKvJson}, #{item.creator})
        </foreach>
    </insert>
</mapper>
