<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectHotelPriceLevelMapper">
    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="hotelPriceLevelId">
        insert into t_project_hotel_price_level (project_intent_hotel_id, project_id, hotel_id, room_level_no, big_bed_room_count, double_bed_room_count, total_room_count, lanyon_room_desc, creator,
                                                 modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectIntentHotelId}, #{item.projectId}, #{item.hotelId}, #{item.roomLevelNo}, #{item.bigBedRoomCount}, #{item.doubleBedRoomCount}, #{item.totalRoomCount}, #{item.lanyonRoomDesc},
             #{item.creator},
             #{item.modifier})
        </foreach>
    </insert>

    <update id="updateRoomCount" parameterType="com.fangcang.grfp.core.entity.ProjectHotelPriceLevelEntity">
       UPDATE t_project_hotel_price_level
       SET big_bed_room_count = #{bigBedRoomCount},
           double_bed_room_count = #{doubleBedRoomCount},
           total_room_count = #{totalRoomCount},
           modifier = #{modifier}
       WHERE hotel_price_level_id = #{hotelPriceLevelId}
    </update>


</mapper>
