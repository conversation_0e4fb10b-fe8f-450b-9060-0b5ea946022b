package com.fangcang.grfp.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fangcang.grfp.core.entity.BidOperateLogEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 报价操作日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface BidOperateLogMapper extends BaseMapper<BidOperateLogEntity> {

    default List<BidOperateLogEntity> queryByProjectIntentHotelId(Integer projectIntentHotelId){
        LambdaQueryWrapper<BidOperateLogEntity>lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(BidOperateLogEntity::getProjectIntentHotelId, projectIntentHotelId);
        lambdaQueryWrapper.orderByDesc(BidOperateLogEntity::getBidOperateLogId);
        return selectList(lambdaQueryWrapper);
    }
}
