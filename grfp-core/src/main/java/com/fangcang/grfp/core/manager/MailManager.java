package com.fangcang.grfp.core.manager;

import com.fangcang.grfp.core.util.ExceptionUtility;
import com.fangcang.grfp.core.vo.AttachmentFileVO;
import com.fangcang.grfp.core.vo.request.MailSendRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamSource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.util.List;

/**
 */
@Component
@Slf4j
public class MailManager {

    @Value("${spring.mail.username}")
    private String from;
    @Resource
    private JavaMailSender javaMailSender;
    @Resource
    private TemplateEngine templateEngine;

    public void sendNotifyMail(String subject, String emailTo, String content, List<AttachmentFileVO> fileList) throws MessagingException {
        MimeMessage mimeMailMessage;
        mimeMailMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMailMessage, true);
        mimeMessageHelper.setFrom(from);
        mimeMessageHelper.setTo(emailTo);
        mimeMessageHelper.setSubject(subject);
        mimeMessageHelper.setText(content, true);

        if (!CollectionUtils.isEmpty(fileList)) {
            for (AttachmentFileVO file : fileList) {
                InputStreamSource inputStreamSource = () -> new ByteArrayInputStream(file.getData());
                mimeMessageHelper.addAttachment(file.getFileName(), inputStreamSource);
            }
        }
        javaMailSender.send(mimeMailMessage);
    }

    public void sendSmsCodeMail(String emailTo, String smsCode) throws MessagingException {
        log.info("sendSmsCodeMail emailTo={}, smsCode={}", emailTo, smsCode);
        MimeMessage mimeMailMessage;
        mimeMailMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMailMessage, true);
        mimeMessageHelper.setFrom(from);
        mimeMessageHelper.setTo(emailTo);
        mimeMessageHelper.setSubject("SmsCode");

        Context context = new Context();
        context.setVariable("smsCode", smsCode);
        String template = templateEngine.process("sendSmsCodeTemplate", context);
        mimeMessageHelper.setText(template, true);

        javaMailSender.send(mimeMailMessage);
        log.info("sendSmsCodeMail end emailTo={}, smsCode={}", emailTo, smsCode);
    }


    /**
     * 发送模板邮件 不带附件
     */
    public void sendTemplateMail(String subject, String emailTo, String templateName, Context context, List<AttachmentFileVO> fileList) throws MessagingException {
        log.info("sendTemplateMail begin emailTo: {}, templateName: {}", emailTo, templateName);
        try {
            MimeMessage mimeMailMessage;
            mimeMailMessage = javaMailSender.createMimeMessage();
            MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMailMessage, true);
            mimeMessageHelper.setFrom(from);
            mimeMessageHelper.setTo(emailTo);
            mimeMessageHelper.setSubject(subject);

            String template = templateEngine.process(templateName, context);
            mimeMessageHelper.setText(template, true);
            if (!CollectionUtils.isEmpty(fileList)) {
                for (AttachmentFileVO file : fileList) {
                    InputStreamSource inputStreamSource = () -> new ByteArrayInputStream(file.getData());
                    mimeMessageHelper.addAttachment(file.getFileName(), inputStreamSource);
                }
            }
            javaMailSender.send(mimeMailMessage);
        } catch (Exception ex) {
            log.info("sendTemplateMail exception: {}, templateName: {}", emailTo, templateName);
            log.error(ExceptionUtility.getDetailedExceptionString(ex));
        }
        log.info("sendTemplateMail end emailTo: {}", emailTo);
    }
}
