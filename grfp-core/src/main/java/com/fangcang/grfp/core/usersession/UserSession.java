package com.fangcang.grfp.core.usersession;

import com.fangcang.grfp.core.base.BaseSession;
import com.fangcang.grfp.core.util.SessionUtility;
import com.fangcang.grfp.core.vo.UserOrgVO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel("用户信息")
public class UserSession extends BaseSession {

    private static final long serialVersionUID = 1L;

    public static final String SYSTEM_USER_NAME = "+system+";

    public static final String SESSION_KEY = "GRFP_SESSION_KEY";

    /**
     * 用户登录邮箱
     */
    @ApiModelProperty("电邮")
    private String email;

    /**
     * 手机号码区号
     */
    @ApiModelProperty("手机号码区号")
    private String mobileAreaCode;

    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String mobile;

    /**
     *  角色编码类型 ADMIN：管理员，EMPLOYEE：员工，HEAD_ORGANIZATION：机构负责人，CHANNEL_ADMIN:渠道合作管理员
     */
    @ApiModelProperty("角色编码类型 ADMIN：管理员，EMPLOYEE：员工，HEAD_ORGANIZATION：机构负责人，CHANNEL_ADMIN:渠道合作管理员")
    private String roleCode;

    /**
     *  登录类型
     */
    @ApiModelProperty("登录类型 登录类型 3:邮箱和验证码登录,4:邮箱和黁登录")
    private Integer loginType;

    /**
     * 权限列表
     */
    @ApiModelProperty("权限列表")
    private Set<String> permissions;

    @ApiModelProperty("用户登录日志ID")
    private Integer userLoginLogId;

    /**
     * 用户机构信息
     */
    @ApiModelProperty("用户机构信息")
    private UserOrgVO userOrg;

    public UserSession(){

    }

    @Override
    public String sessionKey() {
        return SESSION_KEY;
    }

    /**
     * 当前登录的用户
     */
    public static UserSession get() {
        return SessionUtility.getAttribute(SESSION_KEY);
    }

}
