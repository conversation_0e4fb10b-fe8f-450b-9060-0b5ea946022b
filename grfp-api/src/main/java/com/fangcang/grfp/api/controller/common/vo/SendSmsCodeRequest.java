package com.fangcang.grfp.api.controller.common.vo;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel("发送验证码")
@Getter
@Setter
public class SendSmsCodeRequest extends BaseVO {

    @ApiModelProperty(value="发送类型 1-注册2-登录3-修改密码", required = true)
    @NotNull
    private Integer type;

    @ApiModelProperty(value = "电邮", required = true)
    @NotBlank
    @Email
    private String email;


}
