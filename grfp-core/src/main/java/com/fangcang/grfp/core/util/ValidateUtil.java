package com.fangcang.grfp.core.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ValidateUtil {


	/***************************************************************************
	 * 正整数验证
	 * 
	 * @param str
	 *            待验证字符串
	 * @return true 验证通过 false 验证失败
	 */
	public static boolean isValidInteger(String str) {
		boolean flag = false;
		if (str != null) {
			Pattern p = Pattern.compile("^\\d*$");
			Matcher match = p.matcher(str);
			flag = match.matches();
		}
		return flag;
	}

	/***************************************************************************
	 * 整数验证(包括正整数与 负整数)
	 * 
	 * @param str
	 *            待验证字符串
	 * @return true 验证通过 false 验证失败
	 */
	public static boolean isValidNo(String str) {
		boolean flag = false;
		Pattern p = Pattern.compile("^-?\\d*$");
		if (str != null) {
			Matcher match = p.matcher(str);
			flag = match.matches();
		}
		return flag;
	}

	/**
	 * 验证非负整数(正整数+0)
	 * 
	 * @param str
	 *            待验证字符串
	 * @return true 验证通过 false 验证失败
	 */
	public static boolean isValidNonNegative(String str) {
		boolean flag = false;
		Pattern p = Pattern.compile("^\\d+$");
		if (str != null) {
			Matcher match = p.matcher(str);
			flag = match.matches();
		}
		return flag;
	}

	/***************************************************************************
	 * 验证电话号码 后可接分机号 区号3位或者4位 电话7位或者8位后 后面可加3位或者4位分机号
	 * 
	 * @param telephoeNo
	 *            电话号码字符串
	 * @return
	 */
	public static boolean isValidTelephoeNo(String telephoeNo) {
		// 1、\\d{3,4} 区号 3位或者4位的匹配
		// 2、\\d{7,8} 号码 7味或者8位的匹配
		// 3、(\\d{3,4})? 分机号3位或者4位的匹配 ？可匹配一次或者两次
		boolean flag = false;
		Pattern p = Pattern.compile("^\\d{3,4}\\d{7,8}(\\d{3,4})?$");
		Matcher match = p.matcher(telephoeNo);
		if (telephoeNo != null) {
			flag = match.matches();
		}
		return flag;
	}

	/***************************************************************************
	 * 验证手机号码
	 * 
	 * @param mobileNo 电话号码字符串
	 * @return
	 */
	public static boolean isValidMobileNo(String mobileNo) {
		if(!StringUtil.isValidString(mobileNo)){
			return false;
		}
		if(Pattern.matches("^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$",mobileNo)){
			//手机号码格式正确
			return true;
		}else {
			//手机号码格式错误
			return false;
		}
	}

	/***************************************************************************
	 * 验证是否是正确的邮箱格式
	 * 
	 * @param email
	 * @return true表示是正确的邮箱格式,false表示不是正确邮箱格式
	 */
	public static boolean isValidEmail(String email) {
		// 1、\\w+表示@之前至少要输入一个匹配字母或数字或下划线 \\w 单词字符：[a-zA-Z_0-9]
		// 2、(\\w+\\.)表示域名. 如新浪邮箱域名是sina.com.cn
		// {1,3}表示可以出现一次或两次或者三次. 
		String reg = "^(\\w-*\\.*)+@(\\w-?)+(\\.\\w{2,})+$";
		Pattern pattern = Pattern.compile(reg);
		boolean flag = false;
		if (email != null) {
			Matcher matcher = pattern.matcher(email);
			flag = matcher.matches();
		}
		return flag;
	}

	public static void main(String[] args) {
		System.out.println(isValidEmail("13456789"));
	}


	/**
	 * 验证用户名注册是否合法-----------由6-16位英文字母、数字或符号组成的字符串
	 * 
	 * @param userName
	 * @return
	 */
	public static boolean isRegUserName(String userName) {

		String str = "^\\w+$";
		boolean flag = true;
		if (userName != null) {
			Pattern p = Pattern.compile(str);
			Matcher match = p.matcher(userName);
			flag = match.matches();
		}
		return flag;
	}
	

	/**
	 * 验证IP地址
	 * 
	 * @param 待验证的字符串
	 * @return 如果是符合格式的字符串,返回 <b>true </b>,否则为 <b>false </b>
	 */
	public static boolean isIP(String str) {
		String num = "(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)";
		String regex = "^" + num + "\\." + num + "\\." + num + "\\." + num
				+ "$";
		return match(regex, str);
	}

	/**
	 * 验证网址Url
	 * 
	 * @param 待验证的字符串
	 * @return 如果是符合格式的字符串,返回 <b>true </b>,否则为 <b>false </b>
	 */
	public static boolean isUrl(String str) {
		String regex = "http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?";
		return match(regex, str);
	}


	/**
	 * 验证输入两位小数
	 * 
	 * @param 待验证的字符串
	 * @return 如果是符合格式的字符串,返回 <b>true </b>,否则为 <b>false </b>
	 */
	public static boolean isDecimal(String str) {
		String regex = "^[0-9]+(.[0-9]{2})?$";
		return match(regex, str);
	}

	/**
	 * 验证大写字母
	 * 
	 * @param 待验证的字符串
	 * @return 如果是符合格式的字符串,返回 <b>true </b>,否则为 <b>false </b>
	 */
	public static boolean isUpChar(String str) {
		String regex = "^[A-Z]+$";
		return match(regex, str);
	}
	
	/**
	 * 验证密码(6-16位，英文、数字、符号组合的密码，要求至少包含两类信息)
	 * 
	 * @param 待验证的字符串
	 * @return 如果是符合格式的字符串,返回 <b>true </b>,否则为 <b>false </b>
	 */
	public static boolean isValidPassWord(String str) {
		String regex = "/(?!^[0-9]+$)(?!^[A-z]+$)(?!^[^A-z0-9]+$)^.{6,16}$/";
		return match(regex, str);
	}
	

	
	public static boolean match(String regex, String str) {
		Pattern p = Pattern.compile(str);
		Matcher match = p.matcher(regex);
		return match.matches();
	}
}
