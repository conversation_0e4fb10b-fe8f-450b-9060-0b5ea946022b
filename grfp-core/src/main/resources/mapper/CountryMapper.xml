<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.CountryMapper">
    <insert id="batchUpsert">
        insert into t_country(country_id, country_code, name_zh_cn, short_name_zh_cn, name_en_us, short_name_en_us, creator, modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.countryId}, #{item.countryCode}, #{item.nameZhCn}, #{item.shortNameZhCn}, #{item.nameEnUs}, #{item.shortNameEnUs}, #{item.creator}, #{item.modifier})
        </foreach>
        on duplicate key update country_id       = values(country_id),
                                country_code     = values(country_code),
                                name_zh_cn       = values(name_zh_cn),
                                short_name_zh_cn = values(short_name_zh_cn),
                                name_en_us       = values(name_en_us),
                                short_name_en_us = values(short_name_en_us),
                                modifier         = values(modifier)
    </insert>

    <select id="selectCountryNameList" resultType="com.fangcang.grfp.core.vo.CountryNameVO">
        SELECT
            country_code AS countryCode,
            name_zh_cn AS  nameZhCn,
            short_name_zh_cn AS shortNameZhCn,
            name_en_us AS nameEnUs,
            short_name_en_us AS shortNameEnUs
        FROM
            t_country
          WHERE  `name_en_us` LIKE CONCAT('%', #{countryName}, '%') OR `name_zh_cn` LIKE CONCAT('%', #{countryName}, '%')
        <if test="languageId != null and languageId == 1">
            ORDER BY `name_en_us` ASC
        </if>
        <if test="languageId != null and languageId == 2">
            ORDER BY `name_zh_cn` ASC
        </if>
        <if test="limitCount != null and limitCount > 0">
            LIMIT #{limitCount}
        </if>
    </select>

    <select id="listDataPage" resultType="com.fangcang.grfp.core.entity.CountryEntity">
        SELECT *
        FROM
            t_country
        <where>
            <if test="query.nameEnUs != null and query.nameEnUs != ''">
                AND `name_en_us` = #{query.nameEnUs}
            </if>
            <if test="query.nameZhCn != null and query.nameZhCn != ''">
                AND `name_zh_cn` = #{query.nameZhCn}
            </if>
            <if test="query.countryCode != null and query.countryCode != ''">
                AND `country_code` = #{query.countryCode}
            </if>
        </where>
        ORDER BY `name_en_us` ASC
    </select>
</mapper>
