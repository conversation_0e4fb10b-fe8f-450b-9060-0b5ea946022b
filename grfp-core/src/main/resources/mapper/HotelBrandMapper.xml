<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.HotelBrandMapper">

    <insert id="batchUpsert">
        insert into t_hotel_brand(hotel_brand_id, hotel_group_id, name_zh_cn, name_en_us, is_active, creator, modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.hotelBrandId}, #{item.hotelGroupId}, #{item.nameZhCn}, #{item.nameEnUs}, #{item.isActive}, #{item.creator}, #{item.modifier})
        </foreach>
        on duplicate key update hotel_group_id = values(hotel_group_id),
                                hotel_brand_id = values(hotel_brand_id),
                                name_zh_cn     = values(name_zh_cn),
                                name_en_us     = values(name_en_us),
                                is_active      = values(is_active),
                                modifier       = values(modifier)
    </insert>

    <select id="selectHotelBrandNameList" resultType="com.fangcang.grfp.core.vo.HotelBrandVO">
        SELECT
            `hotel_brand_id` AS hotelBrandId,
            `hotel_group_id` AS hotelGroupId,
            `name_en_us` AS nameEnUs,
            `name_zh_cn` AS nameZhCn
        FROM
            `t_hotel_brand`
        <where>
            <if test="hotelBrandName != null and hotelBrandName != ''">
                (`name_en_us` LIKE CONCAT('%', #{hotelBrandName}, '%') OR `name_zh_cn` LIKE CONCAT('%', #{hotelBrandName}, '%'))
            </if>
            <if test="hotelGroupId != null and hotelGroupId > 0">
                AND `hotel_group_id` = #{hotelGroupId}
            </if>
        </where>
        <if test="languageId != null and languageId == 1">
            ORDER BY  `name_en_us` ASC
        </if>
        <if test="languageId != null and languageId == 2">
            ORDER BY `name_zh_cn` ASC
        </if>
        <if test="limitCount != null and limitCount > 0">
            LIMIT #{limitCount}
        </if>
    </select>

    <select id="listDataPage" parameterType="com.fangcang.grfp.core.vo.request.ListHotelBrandDataRequest"
            resultType="com.fangcang.grfp.core.vo.ListHotelBrandDataVO">
        SELECT
            b.`hotel_brand_id` AS hotelBrandId,
            b.`hotel_group_id` AS hotelGroupId,
            b.`name_en_us` AS nameEnUs,
            b.`name_zh_cn` AS nameZhCn,
            b.`is_active` AS isActive,
            g.`name_en_us` AS hotelGroupNameEnUs,
            g.`name_zh_cn` AS hotelGroupNameZhCn,
            b.`creator` AS creator,
            b.`modifier` AS modifier,
            b.`create_time` AS createTime,
            b.`modify_time` AS modifyTime
        FROM
            `t_hotel_brand` b
        LEFT JOIN t_hotel_group g ON b.`hotel_group_id` = g.`hotel_group_id`
        <where>
            <if test="query.hotelBrandId != null and query.hotelBrandId > 0">
                AND b.`hotel_brand_id` = #{query.hotelBrandId}
            </if>
            <if test="query.hotelGroupId != null and query.hotelGroupId > 0">
                AND b.`hotel_group_id` = #{query.hotelGroupId}
            </if>
            <if test="query.isActive != null">
                AND b.`is_active` = #{query.isActive}
            </if>
            <if test="query.hotelBrandName != null and query.hotelBrandName != ''">
                    AND (b.`name_en_us` LIKE CONCAT('%', #{query.hotelBrandName}, '%')
                             OR b.`name_zh_cn` LIKE CONCAT('%', #{query.hotelBrandName}, '%'))
            </if>
        </where>
        ORDER BY b.`hotel_group_id` ASC , b.`hotel_brand_id` ASC

    </select>
</mapper>
