package com.fangcang.grfp.api.config;

import io.swagger.annotations.ApiOperation;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpHeaders;
import springfox.bean.validators.configuration.BeanValidatorPluginsConfiguration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.builders.RequestParameterBuilder;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ParameterType;
import springfox.documentation.service.RequestParameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger.web.UiConfiguration;
import springfox.documentation.swagger.web.UiConfigurationBuilder;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.List;


@SuppressWarnings("deprecation")
@Configuration(proxyBeanMethods = false)
@EnableSwagger2WebMvc
@Import({BeanValidatorPluginsConfiguration.class})
@ConditionalOnProperty(name = SwaggerAutoConfiguration.PROPERTY_ENABLED, matchIfMissing = false)
public class SwaggerAutoConfiguration {
	
	public static final String PROPERTY_ENABLED = "web.swagger.enabled";
	
    @Bean
    @ConditionalOnMissingBean
    public UiConfiguration uiConfiguration() {
        return UiConfigurationBuilder.builder().defaultModelsExpandDepth(-1).build();
    }
    
	@Bean
	@ConditionalOnProperty(name = SwaggerAutoConfiguration.PROPERTY_ENABLED, matchIfMissing = false)
    @ConditionalOnMissingBean(name = "apiDocket")
	public Docket apiDocket() {
		Docket apiDocket = SwaggerAutoConfiguration.docket("API", "/grfp-api/Api/.*");
		RequestParameterBuilder builder = new RequestParameterBuilder();
        RequestParameter tokenParameter = builder.name(HttpHeaders.AUTHORIZATION).description("Authorization").in(ParameterType.HEADER).required(false).build();
        RequestParameter languageParameter = builder.name("Language").description("Language").in(ParameterType.HEADER).required(false).build();
		List<RequestParameter> globalRequestParameters = new ArrayList<>();
        globalRequestParameters.add(tokenParameter);
        globalRequestParameters.add(languageParameter);
        apiDocket.globalRequestParameters(globalRequestParameters);
		return apiDocket;
	}
	
    /**
     * Add for sub api docket
     */
    public static Docket docket(String groupName, String pathRegex) {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName(groupName)
                .apiInfo(apiInfo(groupName))
                .useDefaultResponseMessages(false)
                .select()
                .paths(PathSelectors.regex(pathRegex))
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .build()
                .ignoredParameterTypes(HttpSession.class);
    }

    private static ApiInfo apiInfo(String siteName) {
        return new ApiInfoBuilder()
                .title(siteName)
                .description("")
                .version("")
                .build();
    }
}
