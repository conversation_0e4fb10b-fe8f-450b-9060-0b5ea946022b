package com.fangcang.grfp.core.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fangcang.grfp.core.base.BaseVO;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 项目酒店报价税费设定
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_hotel_tax_settings")
public class ProjectHotelTaxSettingsEntity extends BaseVO {


    /**
     * 项目意向酒店ID
     */
    @TableId(value = "project_intent_hotel_id", type = IdType.ASSIGN_ID)
    private Integer projectIntentHotelId;

    /**
     * 项目id
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 酒店id
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 提前入住税计算方式 1:百分比，2:固定值
     */
    @TableField("earlyck_fee_type")
    private Integer earlyckFeeType;

    /**
     * 提前入住税
     */
    @TableField("earlyck_fee_value")
    private BigDecimal earlyckFeeValue;

    /**
     * 是否包含提前入住税
     */
    @TableField("earlyck_fee_is_include")
    private Integer earlyckFeeIsInclude;

    /**
     * 入住税计算方式 1:百分比，2:固定值
     */
    @TableField("lodgtx_fee_type")
    private Integer lodgtxFeeType;

    /**
     * 入住税
     */
    @TableField("lodgtx_fee_value")
    private BigDecimal lodgtxFeeValue;

    /**
     * 是否包含入住税
     */
    @TableField("lodgtx_fee_is_include")
    private Integer lodgtxFeeIsInclude;

    /**
     * 国家税 计算方式 1:百分比，2:固定值
     */
    @TableField("statetx_fee_type")
    private Integer statetxFeeType;

    /**
     * 国家税 
     */
    @TableField("statetx_fee_value")
    private BigDecimal statetxFeeValue;

    /**
     * 是否包含国家税
     */
    @TableField("statetx_fee_is_include")
    private Integer statetxFeeIsInclude;


    /**
     * 城市税 计算方式 1:百分比，2:固定值
     */
    @TableField("citytx_fee_type")
    private Integer citytxFeeType;

    /**
     * 城市税 
     */
    @TableField("citytx_fee_value")
    private BigDecimal citytxFeeValue;


    /**
     * 是否包含城市税
     */
    @TableField("citytx_fee_is_include")
    private Integer citytxFeeIsInclude;

    /**
     * 客房增值税 计算方式 1:百分比，2:固定值
     */
    @TableField("vatgstrm_fee_type")
    private Integer vatgstrmFeeType;

    /**
     * 客房增值税 
     */
    @TableField("vatgstrm_fee_value")
    private BigDecimal vatgstrmFeeValue;


    /**
     * 是否包含客房增值税
     */
    @TableField("vatgstrm_fee_is_include")
    private Integer vatgstrmFeeIsInclude;


    /**
     * 餐饮增值税 计算方式 1:百分比，2:固定值
     */
    @TableField("vatgstfb_fee_type")
    private Integer vatgstfbFeeType;

    /**
     * 餐饮增值税 
     */
    @TableField("vatgstfb_fee_value")
    private BigDecimal vatgstfbFeeValue;

    /**
     * 是否包含餐饮增值税
     */
    @TableField("vatgstfb_fee_is_include")
    private Integer vatgstfbFeeIsInclude;

    /**
     * 服务费 计算方式 1:百分比，2:固定值
     */
    @TableField("service_fee_type")
    private Integer serviceFeeType;

    /**
     * 服务费 
     */
    @TableField("service_fee_value")
    private BigDecimal serviceFeeValue;

    /**
     * 是否包含服务费
     */
    @TableField("service_fee_is_include")
    private Integer serviceFeeIsInclude;

    /**
     * 占用费 计算方式 1:百分比，2:固定值
     */
    @TableField("occ_fee_type")
    private Integer occFeeType;

    /**
     * 占用费
     */
    @TableField("occ_fee_value")
    private BigDecimal occFeeValue;

    /**
     * 是否包含占用费
     */
    @TableField("occ_fee_is_include")
    private Integer occFeeIsInclude;

    /**
     * 其他税费1 计算方式 1:百分比，2:固定值
     */
    @TableField("othertx1_fee_type")
    private Integer othertx1FeeType;

    /**
     * 其他税费1描述 
     */
    @TableField("othertx1_fee_desc")
    private String othertx1FeeDesc;

    /**
     * 其他税费1 
     */
    @TableField("othertx1_fee_value")
    private BigDecimal othertx1FeeValue;

    /**
     * 是否包含其他税费1
     */
    @TableField("othertx1_fee_is_include")
    private Integer othertx1FeeIsInclude;

    /**
     * 其他税费2 计算方式 1:百分比，2:固定值
     */
    @TableField("othertx2_fee_type")
    private Integer othertx2FeeType;

    /**
     * 其他税费2描述 
     */
    @TableField("othertx2_fee_desc")
    private String othertx2FeeDesc;

    /**
     * 其他税费2
     */
    @TableField("othertx2_fee_value")
    private BigDecimal othertx2FeeValue;


    /**
     * 是否包含其他税费2
     */
    @TableField("othertx2_fee_is_include")
    private Integer othertx2FeeIsInclude;

    /**
     * 其他税费3 计算方式 1:百分比，2:固定值
     */
    @TableField("othertx3_fee_type")
    private Integer othertx3FeeType;

    /**
     * 其他税费3描述 
     */
    @TableField("othertx3_fee_desc")
    private String othertx3FeeDesc;

    /**
     * 其他税费3
     */
    @TableField("othertx3_fee_value")
    private BigDecimal othertx3FeeValue;

    /**
     * 是否包含其他税费3
     */
    @TableField("othertx3_fee_is_include")
    private Integer othertx3FeeIsInclude;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
