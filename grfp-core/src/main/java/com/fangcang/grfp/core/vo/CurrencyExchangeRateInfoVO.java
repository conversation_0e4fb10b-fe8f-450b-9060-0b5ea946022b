package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@ApiModel("币种美元汇率")
@Getter
@Setter
public class CurrencyExchangeRateInfoVO extends BaseVO {

    @ApiModelProperty(value = "币种编号")
    private String currencyCode;

    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "反向汇率")
    private BigDecimal inverseExchangeRate;

    @ApiModelProperty(value = "排序")
    private Integer displayOrder;

    @ApiModelProperty(value = "是否自动同步")
    private Integer isAutoSync;

}
