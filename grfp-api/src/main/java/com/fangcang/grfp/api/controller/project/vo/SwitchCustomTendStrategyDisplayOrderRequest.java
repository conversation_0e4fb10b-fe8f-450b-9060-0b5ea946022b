package com.fangcang.grfp.api.controller.project.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(description = "切换自定义采购策略排序")
public class SwitchCustomTendStrategyDisplayOrderRequest extends BaseVO {

    private static final long serialVersionUID = 4376704933441449172L;

    @ApiModelProperty(value = "自定义采购策略ID from")
    @NotNull
    private Long fromCustomTendStrategyId;

    @ApiModelProperty(value = "自定义采购策略ID to")
    @NotNull
    private Long toCustomTendStrategyId;

}
