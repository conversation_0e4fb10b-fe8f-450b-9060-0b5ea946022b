package com.fangcang.grfp.api.controller.org.vo;

import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("删除关联酒店品牌请求")
@Getter
@Setter
public class DeleteRelatedHotelBrandRequest extends BaseVO {

    @ApiModelProperty("酒店集团机构Id")
    @NotNull
    private Integer hotelGroupOrgId;

    @ApiModelProperty("酒店集团Id")
    @NotNull
    private Long hotelGroupId;

    @ApiModelProperty("酒店品牌Id")
    @NotNull
    private Long hotelBrandId;
}
