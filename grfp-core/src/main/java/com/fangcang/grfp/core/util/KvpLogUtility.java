package com.fangcang.grfp.core.util;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.fangcang.grfp.core.vo.KvpLogBeforeAfterValuePairVO;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

public class KvpLogUtility {

	// ---------------------------------------------------------------------------------------------------- Public Static Methods

	public static List<KvpLogBeforeAfterValuePairVO> constructKvpLogBeforeAfterValuePairVOList(String beforeKvp, String afterKvp) {
		List<KvpLogBeforeAfterValuePairVO> beforeAfterValuePairVOList = new ArrayList<KvpLogBeforeAfterValuePairVO>();

		Map<String, KvpLogBeforeAfterValuePairVO> beforeAfterValuePairVOMap = new HashMap<String, KvpLogBeforeAfterValuePairVO>();

		if (StringUtility.notNullAndNotEmpty(beforeKvp) || StringUtility.notNullAndNotEmpty(afterKvp)) {
			// Parse beforeKvp
			Gson gson = new Gson();
			Type beforeKvpType = new TypeToken<Map<String, String>>() {}.getType();
			
			Map<String, String> beforeKvpMap = new HashMap<String, String>();
			if (StringUtility.notNullAndNotEmpty(beforeKvp)) {
				beforeKvpMap = gson.fromJson(beforeKvp, beforeKvpType);
			}

			// Parse afterKvp
			gson = new Gson();
			Type afterKvpType = new TypeToken<Map<String, String>>() {}.getType();

			// Parse the afterKvp
			Map<String, String> afterKvpMap = new HashMap<String, String>();
			if (StringUtility.notNullAndNotEmpty(afterKvp)) {
				afterKvpMap = gson.fromJson(afterKvp, afterKvpType);
			}

			if (beforeKvpMap.isEmpty() == false) {
				for (String key : beforeKvpMap.keySet()) {
					String field = key;

					String beforeValue = "";
					String afterValue = "";
					
					if (StringUtility.notNullAndNotEmpty(beforeKvpMap.get(key)) == true) {
						beforeValue = beforeKvpMap.get(key);
					}

					KvpLogBeforeAfterValuePairVO beforeAfterValuePairVO = new KvpLogBeforeAfterValuePairVO(
							field,
							beforeValue,
							afterValue
							);
					
					beforeAfterValuePairVOList.add(beforeAfterValuePairVO);
					beforeAfterValuePairVOMap.put(key, beforeAfterValuePairVO);
				}
			}

			if (afterKvpMap.isEmpty() == false) {
				for (String key : afterKvpMap.keySet()) {
					String afterValue = afterKvpMap.get(key);

					if (Objects.nonNull(beforeAfterValuePairVOMap.get(key))) {
						beforeAfterValuePairVOMap.get(key).setAfterValue(afterValue);
					} else {
						KvpLogBeforeAfterValuePairVO beforeAfterValuePairVO = new KvpLogBeforeAfterValuePairVO(
								key,
								"",
								afterValue
								);

						beforeAfterValuePairVOList.add(beforeAfterValuePairVO);
						beforeAfterValuePairVOMap.put(key, beforeAfterValuePairVO);
					}
				}
			}
		}

		return beforeAfterValuePairVOList;
	}
	
	// ----------------------------------------------------------------------------------------------------
	
	public static void main(String args[]) {
		String kvpString = "{\"prize_name\": \"Last Five Digit Prize \", \"prize_type_id\": 59, \"num_of_winning\": 1, \"prize_payout_idr\": 41934, \"prize_payout_khr\": 12000, \"prize_payout_myr\": 12800.00000, \"prize_payout_thb\": 100000, \"prize_payout_usd\": 2750, \"prize_payout_vnd\": 66000}";
			
		System.out.println("kvpString:"+kvpString);
			
		// Parse beforeKvp
		Gson gson = new Gson();
		Type beforeKvpType = new TypeToken<Map<String, String>>(){}.getType();
						
		Map<String, String> beforeKvpMap = new HashMap<String, String>();
		if (StringUtility.notNullAndNotEmpty(kvpString)) {
			beforeKvpMap = gson.fromJson(kvpString, beforeKvpType);
		}
			
		System.out.println("beforeKvpMap:"+beforeKvpMap);
			
		for (String key : beforeKvpMap.keySet()) {
			System.out.println("key:"+key);
		}
	}

}
