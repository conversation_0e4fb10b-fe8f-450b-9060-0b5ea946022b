package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 酒店品牌
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_hotel_brand")
public class HotelBrandEntity extends BaseVO {


    /**
     * 酒店品牌ID
     */
    @TableId(value = "hotel_brand_id", type = IdType.ASSIGN_ID)
    private Long hotelBrandId;

    /**
     * 酒店集团ID
     */
    @TableField("hotel_group_id")
    private String hotelGroupId;

    /**
     * 英文名称
     */
    @TableField("name_en_us")
    private String nameEnUs;

    /**
     * 中文名称
     */
    @TableField("name_zh_cn")
    private String nameZhCn;

    /**
     * 是否有效 1:是，0:否
     */
    @TableField("is_active")
    private Integer isActive;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @JsonFormat(timezone = RfpConstant.TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("modify_time")
    private Date modifyTime;


}
