package com.fangcang.grfp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fangcang.grfp.core.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 导入报价任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_import_bid_task")
public class ImportBidTaskEntity extends BaseVO {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 酒店id
     */
    @TableField("hotel_id")
    private Long hotelId;

    /**
     * 项目id
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 批次 ID
     */
    @TableField("batch_id")
    private String batchId;

    /**
     * 导入集团内部酒店编码
     */
    @TableField("prop_code")
    private String propCode;

    /**
     * 导入AMADEUS集团编码
     */
    @TableField("amadeus_chain_code")
    private String amadeusChainCode;

    /**
     * 导入AMADEUS酒店编码
     */
    @TableField("amadeus_hotel_code")
    private String amadeusHotelCode;

    /**
     * 导入酒店名称
     */
    @TableField("prop_name")
    private String propName;

    /**
     * 导入酒店电话
     */
    @TableField("prop_phone")
    private String propPhone;

    /**
     * 导入酒店地址
     */
    @TableField("prop_add")
    private String propAdd;

    /**
     * 是否校验错误
     */
    @TableField("is_validate_error")
    private Integer isValidateError;

    /**
     * 校验错误详情
     */
    @TableField("validate_error_detail")
    private String validateErrorDetail;

    /**
     * 生成报价状态: 1:待生成，2:已生成
     */
    @TableField("generate_bid_status")
    private Integer generateBidStatus;

    /**
     * 导入数据详情
     */
    @TableField("data_detail")
    private String dataDetail;

    /**
     * oss 文件 key
     */
    @TableField("file_key")
    private String fileKey;

    /**
     * oss 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;


}
